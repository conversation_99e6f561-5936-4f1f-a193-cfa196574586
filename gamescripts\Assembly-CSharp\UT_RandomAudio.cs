﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020000D9 RID: 217
public class UT_RandomAudio : MonoBehaviour
{
	// Token: 0x060006D5 RID: 1749 RVA: 0x00036100 File Offset: 0x00034300
	private void Awake()
	{
		if (this.aud == null)
		{
			this.aud = base.GetComponent<AudioSource>();
		}
		if (this.playOnStart)
		{
			this.Play();
		}
	}

	// Token: 0x060006D6 RID: 1750 RVA: 0x0003612C File Offset: 0x0003432C
	public void Play()
	{
		this.aud.clip = this.clips[Random.Range(0, this.clips.Count)];
		if (this.pitchModulation > 0f)
		{
			this.aud.pitch = 1f + Random.Range(-this.pitchModulation, this.pitchModulation);
		}
		this.aud.Play();
	}

	// Token: 0x04000858 RID: 2136
	public List<AudioClip> clips = new List<AudioClip>();

	// Token: 0x04000859 RID: 2137
	public AudioSource aud;

	// Token: 0x0400085A RID: 2138
	public float pitchModulation;

	// Token: 0x0400085B RID: 2139
	public bool playOnStart;
}
