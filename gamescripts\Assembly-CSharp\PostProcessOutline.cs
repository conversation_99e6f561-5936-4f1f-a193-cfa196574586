﻿using System;
using UnityEngine;
using UnityEngine.Rendering.PostProcessing;

// Token: 0x020001B8 RID: 440
[PostProcess(typeof(PostProcessOutlineRenderer), PostProcessEvent.BeforeStack, "Roystan/Post Process Outline", true)]
[Serializable]
public sealed class PostProcessOutline : PostProcessEffectSettings
{
	// Token: 0x04000C7E RID: 3198
	[Tooltip("Number of pixels between samples that are tested for an edge. When this value is 1, tested samples are adjacent.")]
	public IntParameter scale = new IntParameter
	{
		value = 1
	};

	// Token: 0x04000C7F RID: 3199
	public ColorParameter color = new ColorParameter
	{
		value = Color.white
	};

	// Token: 0x04000C80 RID: 3200
	[Tooltip("Difference between depth values, scaled by the current depth, required to draw an edge.")]
	public FloatParameter depthThreshold = new FloatParameter
	{
		value = 1.5f
	};

	// Token: 0x04000C81 RID: 3201
	[Range(0f, 1f)]
	[Tooltip("The value at which the dot product between the surface normal and the view direction will affect the depth threshold. This ensures that surfaces at right angles to the camera require a larger depth threshold to draw an edge, avoiding edges being drawn along slopes.")]
	public FloatParameter depthNormalThreshold = new FloatParameter
	{
		value = 0.5f
	};

	// Token: 0x04000C82 RID: 3202
	[Tooltip("Scale the strength of how much the depthNormalThreshold affects the depth threshold.")]
	public FloatParameter depthNormalThresholdScale = new FloatParameter
	{
		value = 7f
	};

	// Token: 0x04000C83 RID: 3203
	[Range(0f, 1f)]
	[Tooltip("Larger values will require the difference between normals to be greater to draw an edge.")]
	public FloatParameter normalThreshold = new FloatParameter
	{
		value = 0.4f
	};
}
