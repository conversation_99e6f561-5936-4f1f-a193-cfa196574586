﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020000CD RID: 205
public class UT_EventOnActive : MonoBehaviour
{
	// Token: 0x060006AD RID: 1709 RVA: 0x00035A13 File Offset: 0x00033C13
	private void Start()
	{
		if (this.onEnable)
		{
			return;
		}
		if (this.runOnce && this.hasRun)
		{
			return;
		}
		this.startEvent.Invoke();
		if (this.runOnce)
		{
			this.hasRun = true;
		}
	}

	// Token: 0x060006AE RID: 1710 RVA: 0x00035A49 File Offset: 0x00033C49
	private void OnEnable()
	{
		if (!this.onEnable)
		{
			return;
		}
		if (this.runOnce && this.hasRun)
		{
			return;
		}
		this.startEvent.Invoke();
		if (this.runOnce)
		{
			this.hasRun = true;
		}
	}

	// Token: 0x0400082E RID: 2094
	public bool runOnce;

	// Token: 0x0400082F RID: 2095
	public UnityEvent startEvent;

	// Token: 0x04000830 RID: 2096
	private bool hasRun;

	// Token: 0x04000831 RID: 2097
	public bool onEnable = true;
}
