﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

// Token: 0x020000F2 RID: 242
public class ContextMenu_Manager : MonoBehaviour
{
	// Token: 0x06000767 RID: 1895 RVA: 0x00038980 File Offset: 0x00036B80
	private void OnEnable()
	{
		ContextMenu_Manager.OnClick = (Action<string, Vector2, List<global::ContextMenu.ContextOption>>)Delegate.Combine(ContextMenu_Manager.OnClick, new Action<string, Vector2, List<global::ContextMenu.ContextOption>>(this.ShowMenu));
	}

	// Token: 0x06000768 RID: 1896 RVA: 0x000389A2 File Offset: 0x00036BA2
	private void OnDisable()
	{
		ContextMenu_Manager.OnClick = (Action<string, Vector2, List<global::ContextMenu.ContextOption>>)Delegate.Remove(ContextMenu_Manager.OnClick, new Action<string, Vector2, List<global::ContextMenu.ContextOption>>(this.ShowMenu));
	}

	// Token: 0x06000769 RID: 1897 RVA: 0x000389C4 File Offset: 0x00036BC4
	private void Start()
	{
		this.contextMenu.gameObject.SetActive(false);
	}

	// Token: 0x0600076A RID: 1898 RVA: 0x000389D8 File Offset: 0x00036BD8
	private void ShowMenu(string tip, Vector2 mousePos, List<global::ContextMenu.ContextOption> options)
	{
		this.contextMenu.gameObject.SetActive(true);
		OS_Manager.soundPlayer.PlaySound("tap");
		foreach (object obj in this.contextMenu)
		{
			Object.Destroy(((Transform)obj).gameObject);
		}
		for (int i = 0; i < options.Count; i++)
		{
			global::ContextMenu.ContextOption option = options[i];
			Button button = Object.Instantiate<Button>(this.contextOptionPrefab, this.contextMenu.position, Quaternion.identity, this.contextMenu.transform);
			button.transform.localPosition = new Vector3(0f, (float)(i * -19), 0f);
			button.transform.GetChild(0).GetComponent<Text>().text = option.text;
			button.onClick.AddListener(delegate
			{
				UnityEvent clickEvent = option.clickEvent;
				if (clickEvent == null)
				{
					return;
				}
				clickEvent.Invoke();
			});
			UnityAction unityAction = new UnityAction(this.HideMenu);
			button.onClick.AddListener(unityAction);
		}
		this.contextMenu.gameObject.SetActive(true);
		Vector3 vector = new Vector2(mousePos.x, mousePos.y);
		if (OS_Manager.mousePosition.x > 512f - this.contextMenu.sizeDelta.x)
		{
			vector.x -= this.contextMenu.sizeDelta.x * 0.7f;
		}
		if (OS_Manager.mousePosition.y < this.contextMenu.sizeDelta.y / 2f)
		{
			vector.y += this.contextMenu.sizeDelta.y / 2f;
		}
		this.contextMenu.transform.position = vector;
	}

	// Token: 0x0600076B RID: 1899 RVA: 0x00038BD8 File Offset: 0x00036DD8
	private void HideMenu()
	{
		OS_Manager.soundPlayer.PlaySound("click");
		this.contextMenu.gameObject.SetActive(false);
	}

	// Token: 0x040008E3 RID: 2275
	public Text tipText;

	// Token: 0x040008E4 RID: 2276
	public RectTransform contextMenu;

	// Token: 0x040008E5 RID: 2277
	public static Action<string, Vector2, List<global::ContextMenu.ContextOption>> OnClick;

	// Token: 0x040008E6 RID: 2278
	public Button contextOptionPrefab;
}
