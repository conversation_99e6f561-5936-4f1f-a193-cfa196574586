﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000163 RID: 355
public class UT_Counter : MonoBehaviour
{
	// Token: 0x06000A05 RID: 2565 RVA: 0x000435B4 File Offset: 0x000417B4
	public virtual void AddCount(int i)
	{
		this.count = Mathf.Min(this.count + i, this.maxCount);
		foreach (UT_Counter.CounterEvent counterEvent in this.counterEvents)
		{
			if (!counterEvent.hasRun && counterEvent.activateCount <= this.count)
			{
				counterEvent.countEvent.Invoke();
				counterEvent.hasRun = true;
			}
		}
	}

	// Token: 0x06000A06 RID: 2566 RVA: 0x00043644 File Offset: 0x00041844
	public virtual void Reset()
	{
		foreach (UT_Counter.CounterEvent counterEvent in this.counterEvents)
		{
			counterEvent.hasRun = false;
		}
		this.count = 0;
	}

	// Token: 0x04000B1E RID: 2846
	public int maxCount = 5;

	// Token: 0x04000B1F RID: 2847
	public int count;

	// Token: 0x04000B20 RID: 2848
	public List<UT_Counter.CounterEvent> counterEvents;

	// Token: 0x020002D7 RID: 727
	[Serializable]
	public class CounterEvent
	{
		// Token: 0x04001232 RID: 4658
		public string name;

		// Token: 0x04001233 RID: 4659
		public int activateCount = 1;

		// Token: 0x04001234 RID: 4660
		public UnityEvent countEvent;

		// Token: 0x04001235 RID: 4661
		public bool hasRun;
	}
}
