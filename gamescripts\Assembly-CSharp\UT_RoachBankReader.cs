﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020001B2 RID: 434
public class UT_RoachBankReader : MonoBehaviour
{
	// Token: 0x06000B5E RID: 2910 RVA: 0x00048A40 File Offset: 0x00046C40
	private void Update()
	{
		if (this.lastRoachAmount != CL_GameManager.roaches)
		{
			this.lastRoachAmount = CL_GameManager.roaches;
			if (this.resetOnChange)
			{
				this.Reset();
			}
			this.Check();
		}
	}

	// Token: 0x06000B5F RID: 2911 RVA: 0x00048A70 File Offset: 0x00046C70
	public void Check()
	{
		foreach (UT_RoachBankReader.CheckEvent checkEvent in this.checkEvents)
		{
			checkEvent.Check();
		}
	}

	// Token: 0x06000B60 RID: 2912 RVA: 0x00048AC0 File Offset: 0x00046CC0
	public void Reset()
	{
		foreach (UT_RoachBankReader.CheckEvent checkEvent in this.checkEvents)
		{
			checkEvent.Reset();
		}
	}

	// Token: 0x04000C4B RID: 3147
	public List<UT_RoachBankReader.CheckEvent> checkEvents;

	// Token: 0x04000C4C RID: 3148
	private int lastRoachAmount;

	// Token: 0x04000C4D RID: 3149
	public bool resetOnChange;

	// Token: 0x020002E7 RID: 743
	[Serializable]
	public class CheckEvent
	{
		// Token: 0x06000F65 RID: 3941 RVA: 0x0005AF18 File Offset: 0x00059118
		public void Check()
		{
			if (this.hasRun)
			{
				return;
			}
			bool flag = false;
			if (this.type == UT_RoachBankReader.CheckEvent.CheckType.greater && CL_GameManager.roaches > this.checkAmount)
			{
				flag = true;
			}
			else if (this.type == UT_RoachBankReader.CheckEvent.CheckType.equal && CL_GameManager.roaches == this.checkAmount)
			{
				flag = true;
			}
			else if (this.type == UT_RoachBankReader.CheckEvent.CheckType.equal && CL_GameManager.roaches < this.checkAmount)
			{
				flag = true;
			}
			if (flag)
			{
				this.succeedEvent.Invoke();
				this.hasRun = true;
			}
		}

		// Token: 0x06000F66 RID: 3942 RVA: 0x0005AF90 File Offset: 0x00059190
		public void Reset()
		{
			this.hasRun = false;
		}

		// Token: 0x04001277 RID: 4727
		public string name;

		// Token: 0x04001278 RID: 4728
		public UT_RoachBankReader.CheckEvent.CheckType type;

		// Token: 0x04001279 RID: 4729
		public int checkAmount;

		// Token: 0x0400127A RID: 4730
		public UnityEvent succeedEvent;

		// Token: 0x0400127B RID: 4731
		private bool hasRun;

		// Token: 0x0200031E RID: 798
		public enum CheckType
		{
			// Token: 0x04001344 RID: 4932
			greater,
			// Token: 0x04001345 RID: 4933
			lesser,
			// Token: 0x04001346 RID: 4934
			equal
		}
	}
}
