﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

// Token: 0x0200012E RID: 302
public class UI_MenuButton : MonoBehaviour
{
	// Token: 0x060008E9 RID: 2281 RVA: 0x0003EC90 File Offset: 0x0003CE90
	public void OpenScreen()
	{
		this.parentMenu.DisableMenus(this);
		this.screen.Open();
	}

	// Token: 0x060008EA RID: 2282 RVA: 0x0003ECA9 File Offset: 0x0003CEA9
	public void DisableScreen()
	{
		this.screen.Disable();
	}

	// Token: 0x060008EB RID: 2283 RVA: 0x0003ECB6 File Offset: 0x0003CEB6
	public void Initialize(UI_Menu menu)
	{
		this.parentMenu = menu;
		this.screen.Initialize(this, this.parentMenu);
		base.GetComponent<Button>().onClick.AddListener(new UnityAction(this.OpenScreen));
	}

	// Token: 0x04000A3B RID: 2619
	public UI_MenuScreen screen;

	// Token: 0x04000A3C RID: 2620
	private UI_Menu parentMenu;
}
