﻿using System;

// Token: 0x02000048 RID: 72
public class PerkModule_ItemRemover : PerkModule
{
	// Token: 0x06000324 RID: 804 RVA: 0x0001FE94 File Offset: 0x0001E094
	public override void Update()
	{
		base.Update();
		Item itemByTag = Inventory.instance.GetItemByTag(this.itemTagToRemove);
		if (itemByTag != null)
		{
			if (itemByTag.GetHandItem() != null)
			{
				Inventory.instance.DropItemFromHand(ENT_Player.playerObject.transform.position, itemByTag.GetHandItem().hand.id, false);
				return;
			}
			Inventory.instance.DropItemIntoWorld(itemByTag, ENT_Player.playerObject.transform.position);
		}
	}

	// Token: 0x04000450 RID: 1104
	public string itemTagToRemove;
}
