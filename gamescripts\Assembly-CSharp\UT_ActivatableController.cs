﻿using System;
using System.Collections.Generic;
using Drawing;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200015C RID: 348
public class UT_ActivatableController : MonoBehaviourGizmos, Activatable
{
	// Token: 0x060009DD RID: 2525 RVA: 0x00042D56 File Offset: 0x00040F56
	public void ActivateGroups(string groupName)
	{
		this.SetGroupStates(groupName, true, false);
	}

	// Token: 0x060009DE RID: 2526 RVA: 0x00042D61 File Offset: 0x00040F61
	public void DeactivateGroups(string groupName)
	{
		this.SetGroupStates(groupName, false, false);
	}

	// Token: 0x060009DF RID: 2527 RVA: 0x00042D6C File Offset: 0x00040F6C
	public void SoloActivateGroups(string groupName)
	{
		this.SetGroupStates(groupName, true, true);
	}

	// Token: 0x060009E0 RID: 2528 RVA: 0x00042D77 File Offset: 0x00040F77
	public void DeactivateAllGroups()
	{
		this.SetGroupStates("all", false, true);
	}

	// Token: 0x060009E1 RID: 2529 RVA: 0x00042D88 File Offset: 0x00040F88
	public void ActivateAllGroups()
	{
		if (this.locked)
		{
			return;
		}
		foreach (UT_ActivatableController.ActivatableGroup activatableGroup in this.activatableGroups)
		{
			foreach (GameObject gameObject in activatableGroup.activatables)
			{
				gameObject.GetComponent<Activatable>().Activate();
			}
		}
	}

	// Token: 0x060009E2 RID: 2530 RVA: 0x00042E20 File Offset: 0x00041020
	public void SetGroupStates(string groupName, bool b, bool deactivateOthers = false)
	{
		if (this.locked)
		{
			return;
		}
		foreach (UT_ActivatableController.ActivatableGroup activatableGroup in this.activatableGroups)
		{
			if (activatableGroup.name.Contains(groupName))
			{
				using (List<GameObject>.Enumerator enumerator2 = activatableGroup.activatables.GetEnumerator())
				{
					while (enumerator2.MoveNext())
					{
						GameObject gameObject = enumerator2.Current;
						if (b)
						{
							gameObject.GetComponent<Activatable>().Activate();
						}
						else
						{
							gameObject.GetComponent<Activatable>().Deactivate();
						}
					}
					continue;
				}
			}
			if (deactivateOthers)
			{
				foreach (GameObject gameObject2 in activatableGroup.activatables)
				{
					gameObject2.GetComponent<Activatable>().Deactivate();
				}
			}
		}
	}

	// Token: 0x060009E3 RID: 2531 RVA: 0x00042F28 File Offset: 0x00041128
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			float num = 0.25f;
			foreach (UT_ActivatableController.ActivatableGroup activatableGroup in this.activatableGroups)
			{
				foreach (GameObject gameObject in activatableGroup.activatables)
				{
					Draw.DashedLine(base.transform.position, gameObject.transform.position, num, num, activatableGroup.color);
				}
			}
		}
	}

	// Token: 0x060009E4 RID: 2532 RVA: 0x00042FF0 File Offset: 0x000411F0
	public void Deactivate()
	{
		this.deactivateEvent.Invoke();
		this.active = false;
	}

	// Token: 0x060009E5 RID: 2533 RVA: 0x00043004 File Offset: 0x00041204
	public void Activate()
	{
		this.activateEvent.Invoke();
		this.active = true;
	}

	// Token: 0x060009E6 RID: 2534 RVA: 0x00043018 File Offset: 0x00041218
	public void ToggleActivated()
	{
		if (this.active)
		{
			this.Deactivate();
			return;
		}
		this.Activate();
	}

	// Token: 0x060009E7 RID: 2535 RVA: 0x0004302F File Offset: 0x0004122F
	public GameObject GetGameObject()
	{
		return base.gameObject;
	}

	// Token: 0x060009E8 RID: 2536 RVA: 0x00043037 File Offset: 0x00041237
	public void SetLocked(bool l)
	{
		this.locked = l;
	}

	// Token: 0x04000AFF RID: 2815
	public List<UT_ActivatableController.ActivatableGroup> activatableGroups;

	// Token: 0x04000B00 RID: 2816
	public bool active;

	// Token: 0x04000B01 RID: 2817
	public bool locked;

	// Token: 0x04000B02 RID: 2818
	public UnityEvent activateEvent;

	// Token: 0x04000B03 RID: 2819
	public UnityEvent deactivateEvent;

	// Token: 0x020002D6 RID: 726
	[Serializable]
	public class ActivatableGroup
	{
		// Token: 0x0400122F RID: 4655
		public string name;

		// Token: 0x04001230 RID: 4656
		public List<GameObject> activatables;

		// Token: 0x04001231 RID: 4657
		public Color color;
	}
}
