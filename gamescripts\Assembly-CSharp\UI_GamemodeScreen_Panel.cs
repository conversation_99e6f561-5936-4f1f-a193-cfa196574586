﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

// Token: 0x02000145 RID: 325
public class UI_GamemodeScreen_Panel : MonoBehaviour, GamemodeHolder
{
	// Token: 0x06000958 RID: 2392 RVA: 0x00040759 File Offset: 0x0003E959
	public void Initialize()
	{
		this.stats = base.GetComponentsInChildren<UT_StatText>();
	}

	// Token: 0x06000959 RID: 2393 RVA: 0x00040767 File Offset: 0x0003E967
	public UT_StatText[] GetStats()
	{
		return this.stats;
	}

	// Token: 0x0600095A RID: 2394 RVA: 0x0004076F File Offset: 0x0003E96F
	public void LoadGamemode()
	{
		UI_GamemodeScreen.instance.LoadGamemode();
	}

	// Token: 0x0600095B RID: 2395 RVA: 0x0004077B File Offset: 0x0003E97B
	public void LoadGamemodeSave()
	{
		UI_GamemodeScreen.instance.LoadGamemodeSave();
	}

	// Token: 0x0600095C RID: 2396 RVA: 0x00040787 File Offset: 0x0003E987
	public void DeleteGamemodeSave()
	{
		UI_GamemodeScreen.instance.DeleteGamemodeSave();
	}

	// Token: 0x0600095D RID: 2397 RVA: 0x00040793 File Offset: 0x0003E993
	public void CloseWindow()
	{
		UI_GamemodeScreen.instance.CloseWindow();
	}

	// Token: 0x0600095E RID: 2398 RVA: 0x0004079F File Offset: 0x0003E99F
	public M_Gamemode GetGamemode()
	{
		return UI_GamemodeScreen.instance.GetGamemode();
	}

	// Token: 0x04000A9E RID: 2718
	public string id;

	// Token: 0x04000A9F RID: 2719
	public Leaderboard_Panel leaderboard;

	// Token: 0x04000AA0 RID: 2720
	public Image background;

	// Token: 0x04000AA1 RID: 2721
	public UI_LerpOpen lerpOpen;

	// Token: 0x04000AA2 RID: 2722
	public Selectable firstSelect;

	// Token: 0x04000AA3 RID: 2723
	public UnityEvent openEvent;

	// Token: 0x04000AA4 RID: 2724
	public TMP_Text descriptionField;

	// Token: 0x04000AA5 RID: 2725
	public TMP_Text continueButtonText;

	// Token: 0x04000AA6 RID: 2726
	public List<GameObject> hasSaveObjects;

	// Token: 0x04000AA7 RID: 2727
	public List<GameObject> noSaveObjects;

	// Token: 0x04000AA8 RID: 2728
	private UT_StatText[] stats;
}
