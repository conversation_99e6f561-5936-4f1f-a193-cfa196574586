﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020001A8 RID: 424
public class UT_DebugController : MonoBehaviour
{
	// Token: 0x06000B2F RID: 2863 RVA: 0x00048194 File Offset: 0x00046394
	public void ToggleNoclip()
	{
		ENT_Player.playerObject.noclip = !ENT_Player.playerObject.noclip;
	}

	// Token: 0x06000B30 RID: 2864 RVA: 0x000481B0 File Offset: 0x000463B0
	public void GiveItem(Item_Object o)
	{
		List<Item> list = new List<Item>();
		Item clone = o.itemData.GetClone();
		clone.bagPosition = new Vector3(0f, 0f, 1f) + Random.insideUnitSphere * 0.01f;
		clone.bagRotation = Quaternion.LookRotation(clone.upDirection);
		list.Add(clone);
		Inventory.instance.LoadItemsIntoBag(list);
	}

	// Token: 0x06000B31 RID: 2865 RVA: 0x00048220 File Offset: 0x00046420
	public void TeleportPlayerToLevel(string s)
	{
		WorldLoader.instance.TeleportPlayerToTargetLevel(new string[] { s });
	}

	// Token: 0x06000B32 RID: 2866 RVA: 0x00048236 File Offset: 0x00046436
	public void KillPlayer()
	{
		ENT_Player.playerObject.Kill("");
	}

	// Token: 0x06000B33 RID: 2867 RVA: 0x00048247 File Offset: 0x00046447
	public void ToggleFullbright()
	{
		FXManager.Fullbright(new string[0]);
	}

	// Token: 0x06000B34 RID: 2868 RVA: 0x00048254 File Offset: 0x00046454
	public void ToggleMass()
	{
		DEN_DeathFloor.instance.DeathGooToggle(new string[0]);
	}
}
