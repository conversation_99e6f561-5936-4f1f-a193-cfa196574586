﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200019A RID: 410
public class UT_StatChecker : MonoBehaviour
{
	// Token: 0x06000AF9 RID: 2809 RVA: 0x000473FC File Offset: 0x000455FC
	public void Check()
	{
		int num;
		if (this.session)
		{
			num = StatManager.GetStatisticInt(StatManager.sessionStats, this.statName);
		}
		else
		{
			num = StatManager.GetTotalStatisticInt(this.statName);
		}
		if (this.evaluation == UT_StatChecker.Evaluation.equals)
		{
			if (num == this.evaluationInteger)
			{
				this.trueEvent.Invoke();
				return;
			}
			this.falseEvent.Invoke();
			return;
		}
		else
		{
			if (this.evaluation != UT_StatChecker.Evaluation.greaterThan)
			{
				if (this.evaluation == UT_StatChecker.Evaluation.lesserThan)
				{
					if (num < this.evaluationInteger)
					{
						this.trueEvent.Invoke();
						return;
					}
					this.falseEvent.Invoke();
				}
				return;
			}
			if (num > this.evaluationInteger)
			{
				this.trueEvent.Invoke();
				return;
			}
			this.falseEvent.Invoke();
			return;
		}
	}

	// Token: 0x04000BF8 RID: 3064
	public string statName;

	// Token: 0x04000BF9 RID: 3065
	public bool session = true;

	// Token: 0x04000BFA RID: 3066
	public UT_StatChecker.Evaluation evaluation;

	// Token: 0x04000BFB RID: 3067
	public int evaluationInteger;

	// Token: 0x04000BFC RID: 3068
	public UnityEvent trueEvent;

	// Token: 0x04000BFD RID: 3069
	public UnityEvent falseEvent;

	// Token: 0x020002E3 RID: 739
	public enum Evaluation
	{
		// Token: 0x04001265 RID: 4709
		equals,
		// Token: 0x04001266 RID: 4710
		lesserThan,
		// Token: 0x04001267 RID: 4711
		greaterThan
	}
}
