﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x0200012A RID: 298
public class UI_GamemodeText : MonoBehaviour
{
	// Token: 0x060008CC RID: 2252 RVA: 0x0003E2D5 File Offset: 0x0003C4D5
	private void Awake()
	{
		this.text = base.GetComponent<TMP_Text>();
	}

	// Token: 0x060008CD RID: 2253 RVA: 0x0003E2E3 File Offset: 0x0003C4E3
	private void OnEnable()
	{
		this.Refresh();
	}

	// Token: 0x060008CE RID: 2254 RVA: 0x0003E2EC File Offset: 0x0003C4EC
	public void Refresh()
	{
		if (CL_GameManager.gamemode != null)
		{
			this.text.text = this.append + CL_GameManager.GetGamemodeName(this.includeGamemodeOptions, false);
			return;
		}
		this.text.text = "";
	}

	// Token: 0x04000A16 RID: 2582
	public string append = "";

	// Token: 0x04000A17 RID: 2583
	private TMP_Text text;

	// Token: 0x04000A18 RID: 2584
	public bool includeGamemodeOptions = true;
}
