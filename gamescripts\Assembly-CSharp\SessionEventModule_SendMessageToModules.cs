﻿using System;
using System.Collections.Generic;

// Token: 0x02000068 RID: 104
[Serializable]
public class SessionEventModule_SendMessageToModules : SessionEventModule
{
	// Token: 0x060003D5 RID: 981 RVA: 0x00023780 File Offset: 0x00021980
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		this.Activate();
	}

	// Token: 0x060003D6 RID: 982 RVA: 0x00023790 File Offset: 0x00021990
	public override void Activate()
	{
		foreach (SessionEventModule_SendMessageToModules.Message message in this.messages)
		{
			this.sessionEvent.SendMessageToModule(message.targetID, message.message);
		}
		base.Activate();
	}

	// Token: 0x04000532 RID: 1330
	public List<SessionEventModule_SendMessageToModules.Message> messages;

	// Token: 0x0200024D RID: 589
	[Serializable]
	public class Message
	{
		// Token: 0x04000F45 RID: 3909
		public string targetID;

		// Token: 0x04000F46 RID: 3910
		public string message;
	}
}
