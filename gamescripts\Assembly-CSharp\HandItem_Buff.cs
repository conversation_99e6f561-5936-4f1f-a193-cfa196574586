﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200008B RID: 139
public class HandItem_Buff : HandItem
{
	// Token: 0x060004B0 RID: 1200 RVA: 0x00028CF6 File Offset: 0x00026EF6
	private void OnEnable()
	{
		this.used = false;
	}

	// Token: 0x060004B1 RID: 1201 RVA: 0x00028D00 File Offset: 0x00026F00
	public override void Use()
	{
		base.Use();
		if (this.used)
		{
			return;
		}
		this.used = true;
		this.anim.SetTrigger("Use");
		CL_CameraControl.Shake(0.015f);
		if (this.audioClip != null)
		{
			AudioManager.PlaySound(this.audioClip, base.transform.position, this.audioVolume, 1f, 0f, false, 1f, null);
		}
		this.useEvent.Invoke();
	}

	// Token: 0x060004B2 RID: 1202 RVA: 0x00028D88 File Offset: 0x00026F88
	public override void Activate()
	{
		CL_CameraControl.Shake(0.115f);
		this.buff.Initialize();
		CL_GameManager.gMan.localPlayer.Buff(this.buff);
		if (this.useSecondaryBuffs)
		{
			foreach (BuffContainer buffContainer in this.secondaryBuffs)
			{
				buffContainer.Initialize();
				CL_GameManager.gMan.localPlayer.Buff(buffContainer);
			}
		}
		this.UpdateStats();
	}

	// Token: 0x060004B3 RID: 1203 RVA: 0x00028E28 File Offset: 0x00027028
	public override bool CanDrop()
	{
		return !this.used;
	}

	// Token: 0x04000632 RID: 1586
	public BuffContainer buff;

	// Token: 0x04000633 RID: 1587
	public bool useSecondaryBuffs;

	// Token: 0x04000634 RID: 1588
	public List<BuffContainer> secondaryBuffs;

	// Token: 0x04000635 RID: 1589
	public AudioClip audioClip;

	// Token: 0x04000636 RID: 1590
	public float audioVolume;

	// Token: 0x04000637 RID: 1591
	public UnityEvent useEvent;
}
