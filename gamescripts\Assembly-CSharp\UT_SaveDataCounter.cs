﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200018B RID: 395
public class UT_SaveDataCounter : MonoBehaviour
{
	// Token: 0x06000AC5 RID: 2757 RVA: 0x00046994 File Offset: 0x00044B94
	public void LogData()
	{
		StatManager.sessionStats.UpdateStatistic("counter-" + this.saveFlagName + "-amount", 1, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Add, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
		int totalStatisticInt = StatManager.GetTotalStatisticInt("counter-" + this.saveFlagName + "-amount");
		Debug.Log("Counting: " + totalStatisticInt.ToString());
		if (this.unityEventOnReachAmount)
		{
			if (totalStatisticInt >= this.targetCounterAmount)
			{
				this.succeedCounterEvent.Invoke();
				return;
			}
			this.failedCounterEvent.Invoke();
		}
	}

	// Token: 0x06000AC6 RID: 2758 RVA: 0x00046A23 File Offset: 0x00044C23
	public void ResetCounterData()
	{
		StatManager.sessionStats.UpdateStatistic("counter-" + this.saveFlagName + "-amount", 0, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
	}

	// Token: 0x04000BC9 RID: 3017
	public string saveFlagName;

	// Token: 0x04000BCA RID: 3018
	public bool unityEventOnReachAmount;

	// Token: 0x04000BCB RID: 3019
	public int targetCounterAmount = 1;

	// Token: 0x04000BCC RID: 3020
	public UnityEvent succeedCounterEvent;

	// Token: 0x04000BCD RID: 3021
	public UnityEvent failedCounterEvent;
}
