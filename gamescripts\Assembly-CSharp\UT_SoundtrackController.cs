﻿using System;
using UnityEngine;

// Token: 0x020000DE RID: 222
public class UT_SoundtrackController : MonoBeh<PERSON>our
{
	// Token: 0x060006EB RID: 1771 RVA: 0x00036497 File Offset: 0x00034697
	public void PlaySong(string songName)
	{
		AudioManager.PlayTrack(songName);
	}

	// Token: 0x060006EC RID: 1772 RVA: 0x0003649F File Offset: 0x0003469F
	public void StopMusic()
	{
		AudioManager.StopMusic();
	}

	// Token: 0x060006ED RID: 1773 RVA: 0x000364A6 File Offset: 0x000346A6
	public void FadeOut()
	{
		AudioManager.FadeOutMusic();
	}
}
