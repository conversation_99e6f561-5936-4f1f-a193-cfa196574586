﻿using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000B7 RID: 183
public class CommandConsole : MonoBehaviour
{
	// Token: 0x060005FF RID: 1535 RVA: 0x00031D68 File Offset: 0x0002FF68
	private void Awake()
	{
		CommandConsole.hasCheated = false;
		if (CommandConsole.instance == null)
		{
			CommandConsole.instance = this;
			Object.DontDestroyOnLoad(base.gameObject);
			this.RegisterCommand("help", new Action<string[]>(this.HelpCommand), false);
			this.RegisterCommand("cheats", new Action<string[]>(this.EnableCheatsCommand), false);
		}
		else
		{
			Object.Destroy(base.gameObject);
		}
		CommandConsole.HideConsole();
	}

	// Token: 0x06000600 RID: 1536 RVA: 0x00031DDC File Offset: 0x0002FFDC
	private void LateUpdate()
	{
		if (Input.GetKey(KeyCode.BackQuote) && Input.GetKey(KeyCode.LeftShift) && !this.buttonPressed)
		{
			this.inputField.text = string.Empty;
			this.buttonPressed = true;
			this.ToggleConsole();
		}
		if (!Input.GetKey(KeyCode.BackQuote) || (!Input.GetKey(KeyCode.LeftShift) && this.buttonPressed))
		{
			this.buttonPressed = false;
		}
		if (this.isConsoleVisible)
		{
			if (Input.GetKeyDown(KeyCode.Return))
			{
				this.ExecuteCommand(this.inputField.text);
				this.inputField.text = string.Empty;
				this.inputField.ActivateInputField();
				return;
			}
			if (Input.GetKeyDown(KeyCode.UpArrow))
			{
				this.NavigateCommandHistory(-1);
				return;
			}
			if (Input.GetKeyDown(KeyCode.DownArrow))
			{
				this.NavigateCommandHistory(1);
				return;
			}
			if (Input.GetKeyDown(KeyCode.Tab))
			{
				this.AutocompleteCommand();
				return;
			}
			if (Input.GetKeyDown(KeyCode.Escape))
			{
				CommandConsole.HideConsole();
			}
		}
	}

	// Token: 0x06000601 RID: 1537 RVA: 0x00031ECB File Offset: 0x000300CB
	public static void Log(string message, bool printToConsole = false)
	{
		if (CommandConsole.instance != null)
		{
			CommandConsole.instance.AddMessageToHistory(message);
			Debug.Log(message);
		}
	}

	// Token: 0x06000602 RID: 1538 RVA: 0x00031EEB File Offset: 0x000300EB
	public static void LogError(string message)
	{
		if (CommandConsole.instance != null)
		{
			CommandConsole.instance.AddMessageToHistory("<color=red>" + message + "</color>");
			Debug.LogError(message);
		}
	}

	// Token: 0x06000603 RID: 1539 RVA: 0x00031F1A File Offset: 0x0003011A
	public static void AddCommand(string command, Action<string[]> callback, bool cheat = true)
	{
		command = command.ToLower();
		if (CommandConsole.instance != null)
		{
			CommandConsole.instance.RegisterCommand(command, callback, cheat);
		}
	}

	// Token: 0x06000604 RID: 1540 RVA: 0x00031F40 File Offset: 0x00030140
	public static void RemoveCommand(string command)
	{
		command = command.ToLower();
		if (CommandConsole.instance != null && CommandConsole.instance.commands.ContainsKey(command))
		{
			CommandConsole.instance.commandList.Remove(CommandConsole.instance.commands[command]);
			CommandConsole.instance.commands.Remove(command);
		}
	}

	// Token: 0x06000605 RID: 1541 RVA: 0x00031FA8 File Offset: 0x000301A8
	public void RegisterCommand(string command, Action<string[]> callback, bool cheat = true)
	{
		CommandConsole.Command command2 = new CommandConsole.Command();
		command2.command = command;
		command2.callback = callback;
		command2.cheat = cheat;
		command = command.ToLower();
		if (!this.commands.ContainsKey(command))
		{
			this.commands.Add(command, command2);
		}
		else
		{
			CommandConsole.Command command3 = command2;
			command3.callback = (Action<string[]>)Delegate.Combine(command3.callback, callback);
			CommandConsole.Command command4 = this.commands[command];
			command4.callback = (Action<string[]>)Delegate.Combine(command4.callback, callback);
		}
		if (!this.commandList.Contains(command2))
		{
			this.commandList.Add(command2);
		}
		this.commandList.Sort((CommandConsole.Command x, CommandConsole.Command y) => x.command.CompareTo(y.command));
	}

	// Token: 0x06000606 RID: 1542 RVA: 0x00032074 File Offset: 0x00030274
	private void ToggleConsole()
	{
		this.isConsoleVisible = !this.isConsoleVisible;
		this.consoleUI.SetActive(this.isConsoleVisible);
		if (this.isConsoleVisible)
		{
			this.inputField.ActivateInputField();
			Cursor.lockState = CursorLockMode.None;
		}
		else if (!CL_GameManager.isDead() && !CL_GameManager.gMan.isPaused)
		{
			Cursor.lockState = CursorLockMode.Locked;
		}
		CL_GameManager.gMan.lockPlayerInput = this.isConsoleVisible;
	}

	// Token: 0x06000607 RID: 1543 RVA: 0x000320E5 File Offset: 0x000302E5
	public static void HideConsole()
	{
		if (CommandConsole.instance.isConsoleVisible)
		{
			CommandConsole.instance.ToggleConsole();
		}
	}

	// Token: 0x06000608 RID: 1544 RVA: 0x000320FD File Offset: 0x000302FD
	public static bool IsConsoleVisible()
	{
		return !(CommandConsole.instance == null) && CommandConsole.instance.isConsoleVisible;
	}

	// Token: 0x06000609 RID: 1545 RVA: 0x00032118 File Offset: 0x00030318
	public void ExecuteCommand(string input)
	{
		this.AddMessageToHistory(input);
		this.commandHistory.Add(input);
		this.commandHistoryIndex = this.commandHistory.Count;
		string[] array = input.Split(' ', StringSplitOptions.None);
		string text = array[0];
		string[] array2 = new string[array.Length - 1];
		Array.Copy(array, 1, array2, 0, array2.Length);
		if (this.commands.ContainsKey(text))
		{
			if (!CommandConsole.cheatsEnabled && this.commands[text].cheat)
			{
				this.AddMessageToHistory("Unknown command: " + text);
				return;
			}
			if (this.commands[text].cheat && !CL_GameManager.gamemode.allowCheats)
			{
				this.AddMessageToHistory("Hey, cheats are blocked here. Stop it!");
				return;
			}
			this.commands[text].callback(array2);
		}
		else
		{
			this.AddMessageToHistory("Unknown command: " + text);
		}
		if (!CommandConsole.hasCheated && this.commands[text].cheat)
		{
			CommandConsole.hasCheated = true;
			CL_UIManager.instance.cheatTracker.SetActive(true);
			CommandConsole.Log("WARNING: Cheat activated. Saving of stats has been disabled for this session.", false);
		}
	}

	// Token: 0x0600060A RID: 1546 RVA: 0x00032236 File Offset: 0x00030436
	private void AddMessageToHistory(string message)
	{
		this.historyText.text = this.historyText.text + "\n" + message;
		if (base.gameObject.activeInHierarchy)
		{
			base.StartCoroutine(this.WaitForScrollToBottom());
		}
	}

	// Token: 0x0600060B RID: 1547 RVA: 0x00032273 File Offset: 0x00030473
	private IEnumerator WaitForScrollToBottom()
	{
		yield return new WaitForNextFrameUnit();
		this.historyScrollRect.verticalNormalizedPosition = -1f;
		yield break;
	}

	// Token: 0x0600060C RID: 1548 RVA: 0x00032284 File Offset: 0x00030484
	private void HelpCommand(string[] args)
	{
		string text = "---\nAvailable commands:\n";
		foreach (CommandConsole.Command command in this.commandList)
		{
			if (!command.cheat)
			{
				text = text + command.command + "\n";
			}
			else if (CommandConsole.cheatsEnabled && command.cheat && CL_GameManager.gamemode.allowCheats)
			{
				text = text + command.command + "\n";
			}
		}
		this.AddMessageToHistory(text);
	}

	// Token: 0x0600060D RID: 1549 RVA: 0x00032328 File Offset: 0x00030528
	private void EnableCheatsCommand(string[] args)
	{
		if (args.Length == 0)
		{
			CommandConsole.cheatsEnabled = !CommandConsole.cheatsEnabled;
		}
		else
		{
			bool flag;
			if (!bool.TryParse(args[0], out flag))
			{
				CommandConsole.Log("Unable to parse " + args[0] + " arg needs to be a boolean (true/false/0/1).", false);
				return;
			}
			CommandConsole.cheatsEnabled = Convert.ToBoolean(args[0]);
		}
		if (CommandConsole.cheatsEnabled)
		{
			this.AddMessageToHistory("Cheats Enabled");
			this.AddMessageToHistory("WARNING: Cheat activated. Saving of stats has been disabled for this session.");
			CommandConsole.hasCheated = true;
			CL_UIManager.instance.cheatTracker.SetActive(true);
			return;
		}
		this.AddMessageToHistory("Cheats Disabled");
	}

	// Token: 0x0600060E RID: 1550 RVA: 0x000323BC File Offset: 0x000305BC
	private void NavigateCommandHistory(int direction)
	{
		if (this.commandHistory.Count == 0)
		{
			return;
		}
		this.commandHistoryIndex = Mathf.Clamp(this.commandHistoryIndex + direction, 0, this.commandHistory.Count - 1);
		this.inputField.text = this.commandHistory[this.commandHistoryIndex];
		this.inputField.caretPosition = this.inputField.text.Length;
	}

	// Token: 0x0600060F RID: 1551 RVA: 0x00032430 File Offset: 0x00030630
	private void AutocompleteCommand()
	{
		string text = this.inputField.text;
		foreach (CommandConsole.Command command in this.commandList)
		{
			if (command.command.StartsWith(text, StringComparison.OrdinalIgnoreCase))
			{
				this.inputField.text = command.command;
				this.inputField.caretPosition = command.command.Length;
				break;
			}
		}
	}

	// Token: 0x06000610 RID: 1552 RVA: 0x000324C0 File Offset: 0x000306C0
	public List<string> GetCommandHistory()
	{
		return this.commandHistory;
	}

	// Token: 0x04000795 RID: 1941
	public GameObject consoleUI;

	// Token: 0x04000796 RID: 1942
	public TMP_Text historyText;

	// Token: 0x04000797 RID: 1943
	public TMP_InputField inputField;

	// Token: 0x04000798 RID: 1944
	public ScrollRect historyScrollRect;

	// Token: 0x04000799 RID: 1945
	public static CommandConsole instance;

	// Token: 0x0400079A RID: 1946
	private List<string> commandHistory = new List<string>();

	// Token: 0x0400079B RID: 1947
	private List<string> enteredCommands = new List<string>();

	// Token: 0x0400079C RID: 1948
	private int commandHistoryIndex = -1;

	// Token: 0x0400079D RID: 1949
	private Dictionary<string, CommandConsole.Command> commands = new Dictionary<string, CommandConsole.Command>();

	// Token: 0x0400079E RID: 1950
	private List<CommandConsole.Command> commandList = new List<CommandConsole.Command>();

	// Token: 0x0400079F RID: 1951
	public static bool cheatsEnabled;

	// Token: 0x040007A0 RID: 1952
	public static bool hasCheated;

	// Token: 0x040007A1 RID: 1953
	private bool isConsoleVisible;

	// Token: 0x040007A2 RID: 1954
	private bool buttonPressed;

	// Token: 0x0200027C RID: 636
	public class Command
	{
		// Token: 0x0400103D RID: 4157
		public string command;

		// Token: 0x0400103E RID: 4158
		public bool cheat;

		// Token: 0x0400103F RID: 4159
		public Action<string[]> callback;
	}
}
