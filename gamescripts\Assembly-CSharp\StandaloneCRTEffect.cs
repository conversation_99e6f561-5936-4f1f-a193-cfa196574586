﻿using System;
using UnityEngine;

// Token: 0x02000006 RID: 6
[AddComponentMenu("Image Effects/CRT/Ultimate CRT (standalone)")]
public class StandaloneCRTEffect : BaseCRTEffect
{
	// Token: 0x06000025 RID: 37 RVA: 0x00003EA4 File Offset: 0x000020A4
	private void OnPreCull()
	{
		base.InternalPreRender();
	}

	// Token: 0x06000026 RID: 38 RVA: 0x00003EAC File Offset: 0x000020AC
	protected override RenderTexture CreateCameraTexture(RenderTexture currentCameraTexture)
	{
		RenderTexture renderTexture = base.CreateCameraTexture(currentCameraTexture);
		if (renderTexture != null)
		{
			return renderTexture;
		}
		return new RenderTexture(Screen.width, Screen.height, 0);
	}

	// Token: 0x06000027 RID: 39 RVA: 0x00003EDC File Offset: 0x000020DC
	protected override void OnCameraPostRender(Texture texture)
	{
		base.ProcessEffect(texture, null);
	}
}
