﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200014F RID: 335
public class Footsteps : MonoBehaviour
{
	// Token: 0x06000987 RID: 2439 RVA: 0x00040DDF File Offset: 0x0003EFDF
	public void Footstep()
	{
		this.aud.clip = this.footsteps[Random.Range(0, this.footsteps.Count)];
		this.aud.Play();
	}

	// Token: 0x04000AC3 RID: 2755
	public CharacterController cCon;

	// Token: 0x04000AC4 RID: 2756
	public AudioSource aud;

	// Token: 0x04000AC5 RID: 2757
	public List<AudioClip> footsteps;

	// Token: 0x04000AC6 RID: 2758
	public List<AudioClip> jumps;

	// Token: 0x04000AC7 RID: 2759
	public float velocityCutoff = 0.5f;

	// Token: 0x04000AC8 RID: 2760
	public float footstepRate = 0.5f;

	// Token: 0x04000AC9 RID: 2761
	public float footstepRateMult = 1f;

	// Token: 0x04000ACA RID: 2762
	private float stepTime;

	// Token: 0x04000ACB RID: 2763
	private Vector3 lastPos;
}
