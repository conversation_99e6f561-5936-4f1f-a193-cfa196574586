﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200000E RID: 14
public class AnnouncementController : MonoBehaviour
{
	// Token: 0x0600004F RID: 79 RVA: 0x00004934 File Offset: 0x00002B34
	private void Awake()
	{
		this.announcementTimer = Random.Range(this.announcementTimeMin, this.announcementTimeMax);
		AnnouncementController.instance = this;
		this.eventClipDict = new Dictionary<string, AudioClip>();
		foreach (AudioClip audioClip in this.eventClips)
		{
			this.eventClipDict.Add(audioClip.name, audioClip);
		}
	}

	// Token: 0x06000050 RID: 80 RVA: 0x000049BC File Offset: 0x00002BBC
	private void Update()
	{
		if (!this.active)
		{
			return;
		}
		this.announcementTimer -= Time.deltaTime;
		if (this.announcementTimer <= 0f)
		{
			this.StartAnnouncement(null, null, false);
		}
	}

	// Token: 0x06000051 RID: 81 RVA: 0x000049F0 File Offset: 0x00002BF0
	public void StartAnnouncement(AudioClip announcementClip = null, AudioClip toneClip = null, bool force = false)
	{
		if (this.announcers.Count == 0)
		{
			return;
		}
		float num = 1f;
		if (WorldLoader.initialized && WorldLoader.instance.GetCurrentLevel() != null && WorldLoader.instance.GetCurrentLevel().level.subRegion != null)
		{
			num *= WorldLoader.instance.GetCurrentLevel().level.subRegion.announcementTimerMult;
		}
		this.announcementTimer = Random.Range(this.announcementTimeMin, this.announcementTimeMax) * num;
		base.StartCoroutine(this.PlayAnnouncement(announcementClip, toneClip, force));
	}

	// Token: 0x06000052 RID: 82 RVA: 0x00004A88 File Offset: 0x00002C88
	public void GetClipAndCategoryFromCategoryList(List<AnnouncementController.AnnouncementCategory> categories, out AnnouncementController.AnnouncementClip outClip, out AnnouncementController.AnnouncementCategory outCat)
	{
		List<AnnouncementController.AnnouncementClip> list = new List<AnnouncementController.AnnouncementClip>();
		Dictionary<AnnouncementController.AnnouncementClip, AnnouncementController.AnnouncementCategory> dictionary = new Dictionary<AnnouncementController.AnnouncementClip, AnnouncementController.AnnouncementCategory>();
		foreach (AnnouncementController.AnnouncementCategory announcementCategory in categories)
		{
			foreach (AnnouncementController.AnnouncementClip announcementClip in announcementCategory.announcements)
			{
				if (Random.value < announcementClip.chance)
				{
					list.Add(announcementClip);
					if (!dictionary.ContainsKey(announcementClip))
					{
						dictionary.Add(announcementClip, announcementCategory);
					}
				}
			}
		}
		AnnouncementController.AnnouncementClip announcementClip2 = list[Random.Range(0, list.Count)];
		outClip = announcementClip2;
		outCat = dictionary[announcementClip2];
	}

	// Token: 0x06000053 RID: 83 RVA: 0x00004B64 File Offset: 0x00002D64
	public IEnumerator PlayAnnouncement(AudioClip announcementClip = null, AudioClip toneClip = null, bool force = false)
	{
		AnnouncementController.<>c__DisplayClass16_0 CS$<>8__locals1 = new AnnouncementController.<>c__DisplayClass16_0();
		CS$<>8__locals1.<>4__this = this;
		if (CL_GameManager.gMan.localPlayer == null)
		{
			yield break;
		}
		AudioClip clip = null;
		AudioClip tone = null;
		AnnouncementController.AnnouncementClip announcementClip2 = null;
		if (announcementClip == null)
		{
			AnnouncementController.AnnouncementCategory announcementCategory = null;
			if (WorldLoader.initialized)
			{
				AnnouncementGroup announcementGroup = null;
				if (WorldLoader.instance.GetCurrentLevel().level.subRegion != null)
				{
					announcementGroup = WorldLoader.instance.GetCurrentLevel().level.subRegion.GetRandomAnnouncementGroup();
				}
				if (announcementGroup != null)
				{
					List<AnnouncementController.AnnouncementCategory> announcements = announcementGroup.announcements;
					this.GetClipAndCategoryFromCategoryList(announcements, out announcementClip2, out announcementCategory);
				}
				else
				{
					this.GetClipAndCategoryFromCategoryList(this.announcementCategories, out announcementClip2, out announcementCategory);
				}
			}
			else
			{
				this.GetClipAndCategoryFromCategoryList(this.announcementCategories, out announcementClip2, out announcementCategory);
			}
			clip = announcementClip2.clip;
			tone = announcementCategory.tone;
		}
		else
		{
			clip = announcementClip;
			tone = toneClip;
		}
		List<StringVariableExtractor.StringVariable> postAnnouncementEvents = new List<StringVariableExtractor.StringVariable>();
		CS$<>8__locals1.startFillingPostEvents = false;
		List<StringVariableExtractor.StringVariable>.Enumerator enumerator2;
		if (announcementClip2 != null && announcementClip2.onPlayEvents != null && announcementClip2.onPlayEvents.Count > 0)
		{
			foreach (string text in announcementClip2.onPlayEvents)
			{
				List<StringVariableExtractor.StringVariable> list = StringVariableExtractor.ExtractVariables(text);
				if (list != null)
				{
					foreach (StringVariableExtractor.StringVariable stringVariable in list)
					{
						if (CS$<>8__locals1.startFillingPostEvents)
						{
							postAnnouncementEvents.Add(stringVariable);
						}
						else
						{
							yield return CS$<>8__locals1.<PlayAnnouncement>g__EventInterpreter|0(stringVariable);
						}
					}
					enumerator2 = default(List<StringVariableExtractor.StringVariable>.Enumerator);
				}
			}
			List<string>.Enumerator enumerator = default(List<string>.Enumerator);
		}
		AnnouncementSource closestAnnouncer = this.FindClosestAnnouncer(CL_GameManager.gMan.localPlayer.transform.position);
		if (tone != null)
		{
			closestAnnouncer.PlayAnnouncement(tone, this.announcementVolume, false, force);
			yield return new WaitForSeconds(tone.length + 0.5f);
		}
		closestAnnouncer.PlayAnnouncement(clip, this.announcementVolume, true, force);
		foreach (StringVariableExtractor.StringVariable stringVariable2 in postAnnouncementEvents)
		{
			yield return CS$<>8__locals1.<PlayAnnouncement>g__EventInterpreter|0(stringVariable2);
		}
		enumerator2 = default(List<StringVariableExtractor.StringVariable>.Enumerator);
		yield break;
		yield break;
	}

	// Token: 0x06000054 RID: 84 RVA: 0x00004B88 File Offset: 0x00002D88
	public void AddAnnouncer(AnnouncementSource ann)
	{
		this.announcers.Add(ann);
	}

	// Token: 0x06000055 RID: 85 RVA: 0x00004B96 File Offset: 0x00002D96
	public void RemoveAnnouncer(AnnouncementSource ann)
	{
		if (this.announcers.Contains(ann))
		{
			this.announcers.Remove(ann);
		}
	}

	// Token: 0x06000056 RID: 86 RVA: 0x00004BB4 File Offset: 0x00002DB4
	public AnnouncementSource FindClosestAnnouncer(Vector3 pos)
	{
		float num = float.PositiveInfinity;
		AnnouncementSource announcementSource = null;
		foreach (AnnouncementSource announcementSource2 in this.announcers)
		{
			if (!(announcementSource2 == null) && announcementSource2.gameObject.activeInHierarchy)
			{
				float num2 = Vector3.Distance(announcementSource2.transform.position, pos);
				if (num2 < num)
				{
					num = num2;
					announcementSource = announcementSource2;
				}
			}
		}
		return announcementSource;
	}

	// Token: 0x06000057 RID: 87 RVA: 0x00004C3C File Offset: 0x00002E3C
	public void ResetAnnouncementTimer()
	{
		float num = 1f;
		if (WorldLoader.initialized && WorldLoader.instance.GetCurrentLevel() != null && WorldLoader.instance.GetCurrentLevel().level.subRegion != null)
		{
			num *= WorldLoader.instance.GetCurrentLevel().level.subRegion.announcementTimerMult;
		}
		this.announcementTimer = Random.Range(this.announcementTimeMin, this.announcementTimeMax) * num;
	}

	// Token: 0x0400005F RID: 95
	public static AnnouncementController instance;

	// Token: 0x04000060 RID: 96
	public bool active = true;

	// Token: 0x04000061 RID: 97
	public List<AnnouncementController.AnnouncementCategory> announcementCategories = new List<AnnouncementController.AnnouncementCategory>();

	// Token: 0x04000062 RID: 98
	public float announcementTimeMin = 60f;

	// Token: 0x04000063 RID: 99
	public float announcementTimeMax = 200f;

	// Token: 0x04000064 RID: 100
	private float announcementTimer;

	// Token: 0x04000065 RID: 101
	public float announcementVolume = 1f;

	// Token: 0x04000066 RID: 102
	public List<AnnouncementSource> announcers;

	// Token: 0x04000067 RID: 103
	public List<AudioClip> eventClips;

	// Token: 0x04000068 RID: 104
	private Dictionary<string, AudioClip> eventClipDict;

	// Token: 0x020001F0 RID: 496
	[Serializable]
	public class AnnouncementClip
	{
		// Token: 0x04000D6C RID: 3436
		public AudioClip clip;

		// Token: 0x04000D6D RID: 3437
		public string subtitle;

		// Token: 0x04000D6E RID: 3438
		public float chance = 1f;

		// Token: 0x04000D6F RID: 3439
		public List<string> onPlayEvents;
	}

	// Token: 0x020001F1 RID: 497
	[Serializable]
	public class AnnouncementCategory
	{
		// Token: 0x06000C75 RID: 3189 RVA: 0x0004E7E8 File Offset: 0x0004C9E8
		public void CreateAnnouncementListFromClips()
		{
			this.announcements = new List<AnnouncementController.AnnouncementClip>();
			foreach (AudioClip audioClip in this.clips)
			{
				AnnouncementController.AnnouncementClip announcementClip = new AnnouncementController.AnnouncementClip();
				announcementClip.clip = audioClip;
				announcementClip.subtitle = audioClip.name;
				announcementClip.chance = 1f;
				this.announcements.Add(announcementClip);
			}
		}

		// Token: 0x04000D70 RID: 3440
		public string name;

		// Token: 0x04000D71 RID: 3441
		public AudioClip tone;

		// Token: 0x04000D72 RID: 3442
		public List<AudioClip> clips;

		// Token: 0x04000D73 RID: 3443
		public List<AnnouncementController.AnnouncementClip> announcements;
	}
}
