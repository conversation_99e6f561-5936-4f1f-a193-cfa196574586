﻿using System;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000029 RID: 41
public class FX_ProgressBar : MonoBehaviour
{
	// Token: 0x06000183 RID: 387 RVA: 0x0000BB37 File Offset: 0x00009D37
	private void OnEnable()
	{
		FX_ProgressBar.SetBarValue = (Action<float>)Delegate.Combine(FX_ProgressBar.SetBarValue, new Action<float>(this.SetValue));
	}

	// Token: 0x06000184 RID: 388 RVA: 0x0000BB59 File Offset: 0x00009D59
	private void OnDisable()
	{
		FX_ProgressBar.SetBarValue = (Action<float>)Delegate.Remove(FX_ProgressBar.SetBarValue, new Action<float>(this.SetValue));
	}

	// Token: 0x06000185 RID: 389 RVA: 0x0000BB7C File Offset: 0x00009D7C
	private void Start()
	{
		this.spriteRenderer = base.GetComponent<SpriteRenderer>();
		this.meshRenderer = base.GetComponent<MeshRenderer>();
		this.imageRenderer = base.GetComponent<Image>();
		if (this.spriteRenderer)
		{
			this.mat = this.spriteRenderer.material;
			return;
		}
		if (this.meshRenderer)
		{
			this.mat = this.meshRenderer.material;
			return;
		}
		if (this.imageRenderer)
		{
			this.mat = this.imageRenderer.material;
		}
	}

	// Token: 0x06000186 RID: 390 RVA: 0x0000BC0C File Offset: 0x00009E0C
	private void Update()
	{
		if (this.mat)
		{
			this.mat.SetFloat("_Amount", FX_ProgressBar.amount);
		}
		Color color = this.imageRenderer.color;
		color.a = this.opacity;
		this.imageRenderer.color = color;
		if (this.opacity > 0f)
		{
			this.opacity -= Time.deltaTime;
		}
	}

	// Token: 0x06000187 RID: 391 RVA: 0x0000BC7F File Offset: 0x00009E7F
	public void SetValue(float a)
	{
		if (this.mat)
		{
			FX_ProgressBar.amount = a;
		}
		this.opacity = 1f;
	}

	// Token: 0x04000165 RID: 357
	private SpriteRenderer spriteRenderer;

	// Token: 0x04000166 RID: 358
	private MeshRenderer meshRenderer;

	// Token: 0x04000167 RID: 359
	private Image imageRenderer;

	// Token: 0x04000168 RID: 360
	private Material mat;

	// Token: 0x04000169 RID: 361
	public static float amount;

	// Token: 0x0400016A RID: 362
	private float opacity;

	// Token: 0x0400016B RID: 363
	public static Action<float> SetBarValue;
}
