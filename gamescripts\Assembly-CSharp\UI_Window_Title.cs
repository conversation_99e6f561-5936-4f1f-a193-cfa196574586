﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000109 RID: 265
public class UI_Window_Title : MonoBeh<PERSON>our, IPointerClickHandler, IEventSystemHandler, IDragHandler, IPointerEnterHandler, IPointerExitHandler, IBeginDragHandler, IEndDragHandler
{
	// Token: 0x0600080D RID: 2061 RVA: 0x0003B1BD File Offset: 0x000393BD
	private void Awake()
	{
		this.sprite = base.GetComponent<Image>();
	}

	// Token: 0x0600080E RID: 2062 RVA: 0x0003B1CC File Offset: 0x000393CC
	private void Update()
	{
		if (this.sprite)
		{
			this.sprite.color = Vector4.MoveTowards(this.sprite.color, this.target, Time.deltaTime * 10f);
		}
	}

	// Token: 0x0600080F RID: 2063 RVA: 0x0003B221 File Offset: 0x00039421
	public void OnPointerClick(PointerEventData eventData)
	{
		this.sprite.sprite = this.normalSprite;
		this.window.MoveToTop();
		this.window.OnClick();
	}

	// Token: 0x06000810 RID: 2064 RVA: 0x0003B24A File Offset: 0x0003944A
	public void OnDrag(PointerEventData eventData)
	{
		if (this.canDrag)
		{
			this.window.transform.position = OS_Manager.mousePosition + this.offset;
		}
	}

	// Token: 0x06000811 RID: 2065 RVA: 0x0003B280 File Offset: 0x00039480
	public void OnBeginDrag(PointerEventData eventData)
	{
		if (this.hideContentsOnDrag)
		{
			this.window.HideContents();
		}
		this.offset = this.window.transform.position - OS_Manager.mousePosition;
		this.sprite.sprite = this.dragSprite;
		OS_Manager.soundPlayer.PlaySound("os:drag-start");
	}

	// Token: 0x06000812 RID: 2066 RVA: 0x0003B2EC File Offset: 0x000394EC
	public void OnEndDrag(PointerEventData eventData)
	{
		if (this.hideContentsOnDrag)
		{
			this.window.ShowContents();
		}
		this.target = Color.white;
		this.sprite.sprite = this.normalSprite;
		this.window.MoveToTop();
		this.window.OnMove();
		OS_Manager.soundPlayer.PlaySound("os:drag-end");
	}

	// Token: 0x06000813 RID: 2067 RVA: 0x0003B34D File Offset: 0x0003954D
	public void OnPointerEnter(PointerEventData eventData)
	{
	}

	// Token: 0x06000814 RID: 2068 RVA: 0x0003B34F File Offset: 0x0003954F
	public void OnPointerExit(PointerEventData eventData)
	{
	}

	// Token: 0x06000815 RID: 2069 RVA: 0x0003B351 File Offset: 0x00039551
	public void SetTitle(string text)
	{
		if (this.titleText != null)
		{
			this.titleText.text = text;
		}
	}

	// Token: 0x04000976 RID: 2422
	public Image sprite;

	// Token: 0x04000977 RID: 2423
	private Color target = Color.white;

	// Token: 0x04000978 RID: 2424
	private Vector3 offset;

	// Token: 0x04000979 RID: 2425
	public UI_Window window;

	// Token: 0x0400097A RID: 2426
	public Sprite normalSprite;

	// Token: 0x0400097B RID: 2427
	public Sprite dragSprite;

	// Token: 0x0400097C RID: 2428
	public Sprite unfocusedSprite;

	// Token: 0x0400097D RID: 2429
	public Text titleText;

	// Token: 0x0400097E RID: 2430
	public bool canDrag = true;

	// Token: 0x0400097F RID: 2431
	public bool hideContentsOnDrag;
}
