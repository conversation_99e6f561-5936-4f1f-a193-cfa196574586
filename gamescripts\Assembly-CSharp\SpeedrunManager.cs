﻿using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

// Token: 0x020000C2 RID: 194
public class SpeedrunManager
{
	// Token: 0x06000656 RID: 1622 RVA: 0x00033AF3 File Offset: 0x00031CF3
	public static void Initialize()
	{
		SpeedrunManager.sessionEvents = new List<SpeedrunManager.SpeedrunEvent>();
	}

	// Token: 0x06000657 RID: 1623 RVA: 0x00033B00 File Offset: 0x00031D00
	public static void AddEvent(string s)
	{
		SpeedrunManager.SpeedrunEvent speedrunEvent = new SpeedrunManager.SpeedrunEvent();
		speedrunEvent.id = s;
		speedrunEvent.time = CL_GameManager.gMan.GetGameTime();
		Debug.Log(string.Format("Speedlog: Reached {0} at {1}", s, speedrunEvent.time));
		SpeedrunManager.sessionEvents.Add(speedrunEvent);
	}

	// Token: 0x06000658 RID: 1624 RVA: 0x00033B50 File Offset: 0x00031D50
	public static void SaveLog()
	{
		if (SpeedrunManager.sessionEvents == null || SpeedrunManager.sessionEvents.Count == 0)
		{
			return;
		}
		string text = "";
		string text2 = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
		text = text + "White Knuckle Speedrun Report - " + Application.version + "\n";
		text = string.Concat(new string[]
		{
			text,
			text2,
			" -- ",
			CL_GameManager.GetGamemodeName(true, false),
			"\n"
		});
		foreach (SpeedrunManager.SpeedrunEvent speedrunEvent in SpeedrunManager.sessionEvents)
		{
			text += string.Format("{0} - {1}\n", speedrunEvent.id, speedrunEvent.time);
		}
		string text3 = Path.Combine(Application.persistentDataPath, "Run Records");
		Directory.CreateDirectory(text3);
		File.WriteAllLines(Path.Combine(text3, string.Concat(new string[]
		{
			"runrecord-",
			CL_GameManager.GetGamemodeName(true, false),
			"-",
			text2,
			".txt"
		})), text.Split('\n', StringSplitOptions.None));
	}

	// Token: 0x040007DB RID: 2011
	public static List<SpeedrunManager.SpeedrunEvent> sessionEvents;

	// Token: 0x02000286 RID: 646
	public class SpeedrunEvent
	{
		// Token: 0x04001062 RID: 4194
		public string id;

		// Token: 0x04001063 RID: 4195
		public float time;
	}
}
