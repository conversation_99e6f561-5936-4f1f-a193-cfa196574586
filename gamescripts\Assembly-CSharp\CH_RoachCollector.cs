﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000015 RID: 21
[DefaultExecutionOrder(10)]
public class CH_RoachCollector : MonoBehaviour
{
	// Token: 0x0600007A RID: 122 RVA: 0x000054A0 File Offset: 0x000036A0
	private void Start()
	{
		if (this.useBankTarget)
		{
			base.StartCoroutine(this.WaitForText(string.Format("BANK {0} ROACHES", this.bankTarget), 0.5f));
			UI_ObjectiveViewer.CreateOrUpdateObjective("roachcollector", string.Format("BANK {0} ROACHES", this.bankTarget), string.Format("{0} / {1}", this.totalBanked, this.bankTarget), false);
		}
	}

	// Token: 0x0600007B RID: 123 RVA: 0x0000551C File Offset: 0x0000371C
	private void Update()
	{
		if (this.currentRoaches != CL_GameManager.roaches)
		{
			int num = CL_GameManager.roaches - this.currentRoaches;
			if (num > 0)
			{
				this.totalBanked += num;
				if (this.useBankTarget)
				{
					if (!this.hasFinished)
					{
						if (this.totalBanked >= this.bankTarget)
						{
							this.hasFinished = true;
							base.StopAllCoroutines();
							CL_UIManager.instance.ascentHeader.ShowText(this.successText);
							UI_ObjectiveViewer.CreateOrUpdateObjective("roachcollector", string.Format("BANK {0} ROACHES", this.bankTarget), string.Format("<color=green>{0} / {1}</color>", this.totalBanked, this.bankTarget), true);
							UI_ObjectiveViewer.PlayEarnSound();
							this.finishEvent.Invoke();
						}
						else
						{
							base.StopAllCoroutines();
							base.StartCoroutine(this.WaitToShowPrintout());
							UI_ObjectiveViewer.CreateOrUpdateObjective("roachcollector", string.Format("BANK {0} ROACHES", this.bankTarget), string.Format("{0} / {1}", this.totalBanked, this.bankTarget), true);
						}
					}
					else
					{
						UI_ObjectiveViewer.CreateOrUpdateObjective("roachcollector", string.Format("BANK {0} ROACHES", this.bankTarget), string.Format("<color=green>{0} / {1}</color>", this.totalBanked, this.bankTarget), true);
					}
				}
				if (this.bankAddsExtraScore && this.totalBanked >= this.bankTarget)
				{
					GamemodeModule_Challenge.AddBonusScore((float)num * this.roachAddedScoreMultiplier);
					UI_ObjectiveViewer.CreateOrUpdateObjective("scorebonus", "<color=red>SCORE BONUS", string.Format("{0}", Mathf.RoundToInt((float)(this.totalBanked - this.bankTarget) * this.roachAddedScoreMultiplier)), true);
				}
			}
			else
			{
				if (this.allowScoreReduction)
				{
					this.totalBanked += num;
				}
				if (this.bankAddsExtraScore && this.allowScoreReduction)
				{
					GamemodeModule_Challenge.AddBonusScore((float)num * this.roachAddedScoreMultiplier);
					UI_ObjectiveViewer.CreateOrUpdateObjective("scorebonus", "<color=red>SCORE BONUS", string.Format("{0}", Mathf.RoundToInt((float)(this.totalBanked - this.bankTarget) * this.roachAddedScoreMultiplier)), true);
				}
			}
			this.currentRoaches = CL_GameManager.roaches;
		}
	}

	// Token: 0x0600007C RID: 124 RVA: 0x00005768 File Offset: 0x00003968
	public IEnumerator WaitForText(string text, float time)
	{
		yield return new WaitForSeconds(time);
		CL_UIManager.instance.ascentHeader.ShowText(text);
		yield break;
	}

	// Token: 0x0600007D RID: 125 RVA: 0x0000577E File Offset: 0x0000397E
	public IEnumerator WaitToShowPrintout()
	{
		yield return new WaitForSeconds(4f);
		int num = this.bankTarget - this.totalBanked;
		num = Mathf.Max(num, 0);
		CL_UIManager.instance.ascentHeader.ShowText(string.Format("{0} roaches banked. {1} remain.", this.totalBanked, num));
		yield break;
	}

	// Token: 0x04000078 RID: 120
	public bool useBankTarget = true;

	// Token: 0x04000079 RID: 121
	private int currentRoaches;

	// Token: 0x0400007A RID: 122
	private int totalBanked;

	// Token: 0x0400007B RID: 123
	public int bankTarget = 40;

	// Token: 0x0400007C RID: 124
	public string successText;

	// Token: 0x0400007D RID: 125
	private bool hasFinished;

	// Token: 0x0400007E RID: 126
	public UnityEvent finishEvent;

	// Token: 0x0400007F RID: 127
	public bool bankAddsExtraScore = true;

	// Token: 0x04000080 RID: 128
	public bool allowScoreReduction;

	// Token: 0x04000081 RID: 129
	public float roachAddedScoreMultiplier = 100f;
}
