﻿using System;
using UnityEngine;

// Token: 0x02000028 RID: 40
public class FX_FadeFlareHit : MonoBehaviour
{
	// Token: 0x06000180 RID: 384 RVA: 0x0000BA89 File Offset: 0x00009C89
	private void Start()
	{
		this.lamp = base.GetComponent<CL_Lamp>();
		this.startIntensity = this.lamp.intensity;
	}

	// Token: 0x06000181 RID: 385 RVA: 0x0000BAA8 File Offset: 0x00009CA8
	private void Update()
	{
		this.curLifeTime += Time.deltaTime;
		this.lamp.intensity = this.startIntensity * this.intensityDropoff.Evaluate(Mathf.Lerp(1f, 0f, this.curLifeTime / this.lifeTime));
		if (this.curLifeTime >= this.lifeTime)
		{
			Object.Destroy(base.gameObject);
		}
	}

	// Token: 0x04000160 RID: 352
	private CL_Lamp lamp;

	// Token: 0x04000161 RID: 353
	public float lifeTime = 15f;

	// Token: 0x04000162 RID: 354
	private float curLifeTime;

	// Token: 0x04000163 RID: 355
	private float startIntensity = 1f;

	// Token: 0x04000164 RID: 356
	public AnimationCurve intensityDropoff;
}
