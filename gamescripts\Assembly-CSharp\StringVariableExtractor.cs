﻿using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

// Token: 0x0200007A RID: 122
public static class StringVariableExtractor
{
	// Token: 0x0600041D RID: 1053 RVA: 0x0002590C File Offset: 0x00023B0C
	public static List<StringVariableExtractor.StringVariable> ExtractVariables(string text)
	{
		List<StringVariableExtractor.StringVariable> list = new List<StringVariableExtractor.StringVariable>();
		foreach (object obj in new Regex("<(\\w+)=(.*?)>").Matches(text))
		{
			Match match = (Match)obj;
			list.Add(new StringVariableExtractor.StringVariable
			{
				name = match.Groups[1].Value,
				value = match.Groups[2].Value
			});
		}
		return list;
	}

	// Token: 0x02000250 RID: 592
	public class StringVariable
	{
		// Token: 0x04000F4E RID: 3918
		public string name;

		// Token: 0x04000F4F RID: 3919
		public string value;
	}
}
