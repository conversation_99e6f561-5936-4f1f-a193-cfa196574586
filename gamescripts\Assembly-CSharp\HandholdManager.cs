﻿using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;

// Token: 0x020000BC RID: 188
public class HandholdManager : MonoBehaviour
{
	// Token: 0x06000639 RID: 1593 RVA: 0x0003339B File Offset: 0x0003159B
	private void Start()
	{
		this.handholds = new List<CL_Handhold>();
		this.handholdDict = new Dictionary<string, HandholdManager.HandholdType>();
		this.LoadHandholds(base.gameObject);
	}

	// Token: 0x0600063A RID: 1594 RVA: 0x000333C0 File Offset: 0x000315C0
	public void LoadHandholdTypes()
	{
		foreach (HandholdManager.HandholdType handholdType in this.handholdTypes)
		{
			this.handholdDict.Add(handholdType.name, handholdType);
		}
	}

	// Token: 0x0600063B RID: 1595 RVA: 0x00033420 File Offset: 0x00031620
	public void LoadHandholds(GameObject g)
	{
		if (this.handholdDict.Count == 0)
		{
			this.LoadHandholdTypes();
		}
		List<Transform> list = this.TraverseHierarchy(g.transform);
		List<Transform> list2 = new List<Transform>();
		foreach (Transform transform in list)
		{
			string name = transform.name;
			if (name.Contains("handhold"))
			{
				string[] array = name.Split(':', StringSplitOptions.None);
				GameObject gameObject = transform.gameObject;
				HandholdManager.HandholdType handholdType = this.handholdTypes[0];
				if (array != null && array.Length > 1)
				{
					if (!this.handholdDict.ContainsKey(array[1].Split('.', StringSplitOptions.None)[0]))
					{
						continue;
					}
					handholdType = this.handholdDict[array[1].Split('.', StringSplitOptions.None)[0]];
				}
				GameObject prefab = handholdType.prefab;
				if (handholdType.spawnPrefab)
				{
					Object.Instantiate<GameObject>(handholdType.prefab, gameObject.transform.position, gameObject.transform.rotation, gameObject.transform).transform.localRotation *= Quaternion.Euler(90f, 0f, 0f);
				}
				else
				{
					gameObject.layer = prefab.layer;
					this.CopyComponents(prefab, gameObject);
					list2.Add(transform);
				}
			}
		}
	}

	// Token: 0x0600063C RID: 1596 RVA: 0x000335A0 File Offset: 0x000317A0
	private List<Transform> TraverseHierarchy(Transform root)
	{
		List<Transform> list = new List<Transform>();
		foreach (object obj in root)
		{
			Transform transform = (Transform)obj;
			list.Add(transform);
			list.AddRange(this.TraverseHierarchy(transform));
		}
		return list;
	}

	// Token: 0x0600063D RID: 1597 RVA: 0x00033608 File Offset: 0x00031808
	private void CopyComponents(GameObject source, GameObject target)
	{
		foreach (Component component in source.GetComponents<Component>())
		{
			if (!(component is Transform))
			{
				Component component2 = target.AddComponent(component.GetType());
				this.CopyFields(component, component2);
				this.CopyProperties(component, component2);
			}
		}
	}

	// Token: 0x0600063E RID: 1598 RVA: 0x00033654 File Offset: 0x00031854
	private void CopyFields(Component source, Component target)
	{
		foreach (FieldInfo fieldInfo in source.GetType().GetFields(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic))
		{
			fieldInfo.SetValue(target, fieldInfo.GetValue(source));
		}
	}

	// Token: 0x0600063F RID: 1599 RVA: 0x00033690 File Offset: 0x00031890
	private void CopyProperties(Component source, Component target)
	{
		foreach (PropertyInfo propertyInfo in source.GetType().GetProperties(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic))
		{
			if (propertyInfo.CanWrite && propertyInfo.CanRead && propertyInfo.GetIndexParameters().Length == 0)
			{
				propertyInfo.SetValue(target, propertyInfo.GetValue(source, null), null);
			}
		}
	}

	// Token: 0x06000640 RID: 1600 RVA: 0x000336E6 File Offset: 0x000318E6
	private void Update()
	{
	}

	// Token: 0x040007C0 RID: 1984
	public List<HandholdManager.HandholdType> handholdTypes;

	// Token: 0x040007C1 RID: 1985
	private Dictionary<string, HandholdManager.HandholdType> handholdDict = new Dictionary<string, HandholdManager.HandholdType>();

	// Token: 0x040007C2 RID: 1986
	public List<CL_Handhold> handholds;

	// Token: 0x02000283 RID: 643
	[Serializable]
	public class HandholdType
	{
		// Token: 0x04001058 RID: 4184
		public string name;

		// Token: 0x04001059 RID: 4185
		public GameObject prefab;

		// Token: 0x0400105A RID: 4186
		public bool spawnPrefab;
	}
}
