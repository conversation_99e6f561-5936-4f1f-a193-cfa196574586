﻿using System;
using UnityEngine;

// Token: 0x02000168 RID: 360
public class UT_FlagToggle : MonoBehaviour
{
	// Token: 0x06000A15 RID: 2581 RVA: 0x000439FD File Offset: 0x00041BFD
	private void OnEnable()
	{
		if (this.hideIfTrue)
		{
			base.gameObject.SetActive(!CL_GameManager.HasActiveFlag(this.flag));
			return;
		}
		base.gameObject.SetActive(CL_GameManager.HasActiveFlag(this.flag));
	}

	// Token: 0x04000B35 RID: 2869
	public string flag;

	// Token: 0x04000B36 RID: 2870
	public bool hideIfTrue;
}
