﻿using System;
using System.Collections.Generic;

// Token: 0x020000BD RID: 189
[Serializable]
public class LevelStat
{
	// Token: 0x06000642 RID: 1602 RVA: 0x000336FB File Offset: 0x000318FB
	public void AddTime(float t)
	{
		if (this.times == null)
		{
			this.times = new List<float>();
		}
		this.times.Add(t);
	}

	// Token: 0x040007C3 RID: 1987
	public string levelId;

	// Token: 0x040007C4 RID: 1988
	public float bestTime;

	// Token: 0x040007C5 RID: 1989
	public List<float> times;
}
