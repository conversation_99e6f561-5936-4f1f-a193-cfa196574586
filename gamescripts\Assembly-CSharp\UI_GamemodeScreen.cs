﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

// Token: 0x02000128 RID: 296
public class UI_GamemodeScreen : MonoBehaviour, GamemodeHolder
{
	// Token: 0x060008BC RID: 2236 RVA: 0x0003DDD4 File Offset: 0x0003BFD4
	private void Awake()
	{
		UI_GamemodeScreen.instance = this;
		this.group = base.GetComponent<CanvasGroup>();
		base.gameObject.SetActive(false);
		this.activePanels = new Dictionary<string, UI_GamemodeScreen_Panel>();
	}

	// Token: 0x060008BD RID: 2237 RVA: 0x0003DE00 File Offset: 0x0003C000
	public void Initialize(M_Gamemode mode)
	{
		if (this.currentPanel != null)
		{
			this.currentPanel.gameObject.SetActive(false);
		}
		if (!this.activePanels.ContainsKey(mode.gamemodePanel.id))
		{
			this.activePanels.Add(mode.gamemodePanel.id, Object.Instantiate<UI_GamemodeScreen_Panel>(mode.gamemodePanel, base.transform));
			this.activePanels[mode.gamemodePanel.id].Initialize();
		}
		this.baseGamemode = mode;
		this.currentPanel = this.activePanels[mode.gamemodePanel.id];
		this.activePanels[mode.gamemodePanel.id].gameObject.SetActive(true);
		this.OpenWindow();
		this.group.interactable = true;
		this.gamemodeSelectorGroup.interactable = false;
		this.currentPanel.firstSelect.Select();
		if (this.currentPanel.descriptionField != null)
		{
			this.currentPanel.descriptionField.text = this.baseGamemode.modeDescription;
		}
		CL_GameManager.gamemode = this.baseGamemode;
		if (mode.screenArt != null)
		{
			this.currentPanel.background.sprite = mode.screenArt;
		}
		UT_StatText[] stats = this.currentPanel.GetStats();
		for (int i = 0; i < stats.Length; i++)
		{
			stats[i].RefreshText(CL_GameManager.GetGamemodeName(true, false));
		}
		UI_GamemodeText[] componentsInChildren = base.GetComponentsInChildren<UI_GamemodeText>();
		for (int i = 0; i < componentsInChildren.Length; i++)
		{
			componentsInChildren[i].Refresh();
		}
		this.currentPanel.leaderboard.Refresh();
		bool flag = CL_SaveManager.SessionFileExists(this.baseGamemode.gamemodeName, SettingsManager.settings.g_hard);
		if (SettingsManager.settings.g_competitive)
		{
			flag = false;
		}
		foreach (GameObject gameObject in this.currentPanel.hasSaveObjects)
		{
			gameObject.SetActive(flag);
		}
		foreach (GameObject gameObject2 in this.currentPanel.noSaveObjects)
		{
			gameObject2.SetActive(!flag);
		}
		if (flag)
		{
			this.currentPanel.continueButtonText.text = "Continue: " + CL_SaveManager.GetSessionArea(this.baseGamemode.gamemodeName, SettingsManager.settings.g_hard);
		}
	}

	// Token: 0x060008BE RID: 2238 RVA: 0x0003E0A0 File Offset: 0x0003C2A0
	public void LoadGamemode()
	{
		CL_GameManager.gamemode = this.baseGamemode;
		SceneManager.LoadScene(this.baseGamemode.gamemodeScene);
		this.loadingText.SetActive(true);
	}

	// Token: 0x060008BF RID: 2239 RVA: 0x0003E0C9 File Offset: 0x0003C2C9
	public void LoadGamemodeSave()
	{
		CL_SaveManager.loadSessionSaveOnLoad = true;
		this.LoadGamemode();
	}

	// Token: 0x060008C0 RID: 2240 RVA: 0x0003E0D7 File Offset: 0x0003C2D7
	public void DeleteGamemodeSave()
	{
		CL_SaveManager.DeleteSessionSave(this.baseGamemode.gamemodeName, SettingsManager.settings.g_hard);
	}

	// Token: 0x060008C1 RID: 2241 RVA: 0x0003E0F4 File Offset: 0x0003C2F4
	public void CloseWindow()
	{
		if (this.currentPanel.lerpOpen != null)
		{
			this.currentPanel.lerpOpen.Hide();
		}
		else
		{
			base.gameObject.SetActive(false);
		}
		this.gamemodeSelectorGroup.interactable = true;
		this.group.interactable = false;
	}

	// Token: 0x060008C2 RID: 2242 RVA: 0x0003E14C File Offset: 0x0003C34C
	public void OpenWindow()
	{
		base.gameObject.SetActive(true);
		if (this.currentPanel.lerpOpen != null)
		{
			this.currentPanel.lerpOpen.Show();
		}
		this.currentPanel.openEvent.Invoke();
	}

	// Token: 0x060008C3 RID: 2243 RVA: 0x0003E198 File Offset: 0x0003C398
	public M_Gamemode GetGamemode()
	{
		return this.baseGamemode;
	}

	// Token: 0x04000A08 RID: 2568
	public static UI_GamemodeScreen instance;

	// Token: 0x04000A09 RID: 2569
	private M_Gamemode baseGamemode;

	// Token: 0x04000A0A RID: 2570
	public Dictionary<string, UI_GamemodeScreen_Panel> activePanels;

	// Token: 0x04000A0B RID: 2571
	public GameObject loadingText;

	// Token: 0x04000A0C RID: 2572
	public CanvasGroup gamemodeSelectorGroup;

	// Token: 0x04000A0D RID: 2573
	private UI_GamemodeScreen_Panel currentPanel;

	// Token: 0x04000A0E RID: 2574
	private CanvasGroup group;
}
