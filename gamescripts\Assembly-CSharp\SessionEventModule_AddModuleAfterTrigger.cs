﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200005D RID: 93
[Serializable]
public class SessionEventModule_AddModuleAfterTrigger : SessionEventModule
{
	// Token: 0x060003AC RID: 940 RVA: 0x00022AEA File Offset: 0x00020CEA
	public override void Initialize(SessionEvent s)
	{
		this.hasTriggered = false;
		base.Initialize(s);
	}

	// Token: 0x060003AD RID: 941 RVA: 0x00022AFC File Offset: 0x00020CFC
	public override void Update()
	{
		base.Update();
		if (this.hasTriggered)
		{
			return;
		}
		switch (this.triggerEvent)
		{
		case SessionEventModule_AddModuleAfterTrigger.TriggerEvent.afterRegion:
			if (this.sessionEvent.startRegion != CL_EventManager.currentRegion)
			{
				this.AddModules();
				return;
			}
			break;
		case SessionEventModule_AddModuleAfterTrigger.TriggerEvent.afterSubregion:
			if (this.sessionEvent.startSubregion == null)
			{
				if (this.sessionEvent.startSubregion != CL_EventManager.currentSubregion)
				{
					this.AddModules();
					return;
				}
			}
			else if (this.sessionEvent.startSubregion.subregionName != CL_EventManager.currentSubregion.subregionName)
			{
				this.AddModules();
				return;
			}
			break;
		case SessionEventModule_AddModuleAfterTrigger.TriggerEvent.afterLevel:
			if (this.sessionEvent.startLevel != CL_EventManager.currentLevel)
			{
				this.AddModules();
			}
			break;
		default:
			return;
		}
	}

	// Token: 0x060003AE RID: 942 RVA: 0x00022BC8 File Offset: 0x00020DC8
	private void AddModules()
	{
		this.hasTriggered = true;
		foreach (SessionEventModule sessionEventModule in this.modules)
		{
			this.sessionEvent.AddModule(sessionEventModule);
		}
	}

	// Token: 0x04000503 RID: 1283
	public SessionEventModule_AddModuleAfterTrigger.TriggerEvent triggerEvent;

	// Token: 0x04000504 RID: 1284
	[SerializeReference]
	public List<SessionEventModule> modules;

	// Token: 0x04000505 RID: 1285
	private bool hasTriggered;

	// Token: 0x02000247 RID: 583
	public enum TriggerEvent
	{
		// Token: 0x04000F29 RID: 3881
		afterRegion,
		// Token: 0x04000F2A RID: 3882
		afterSubregion,
		// Token: 0x04000F2B RID: 3883
		afterLevel
	}
}
