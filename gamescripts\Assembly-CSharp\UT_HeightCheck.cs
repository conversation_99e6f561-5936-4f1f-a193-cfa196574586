﻿using System;
using System.Collections;
using System.Runtime.CompilerServices;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000174 RID: 372
public class UT_HeightCheck : MonoBehaviour
{
	// Token: 0x06000A63 RID: 2659 RVA: 0x00044910 File Offset: 0x00042B10
	private void Start()
	{
		base.StartCoroutine(this.<Start>g__WaitToStart|6_0());
	}

	// Token: 0x06000A64 RID: 2660 RVA: 0x00044920 File Offset: 0x00042B20
	private void Update()
	{
		if (!this.triggered)
		{
			if (base.transform.position.y > this.targetHeight && this.triggerWhenAbove)
			{
				this.triggerEvent.Invoke();
				return;
			}
			if (!this.triggerWhenAbove)
			{
				this.triggerEvent.Invoke();
			}
		}
	}

	// Token: 0x06000A65 RID: 2661 RVA: 0x00044974 File Offset: 0x00042B74
	public void ResetTarget()
	{
		this.triggered = false;
		if (this.relative)
		{
			this.targetHeight = base.transform.position.y + this.height;
			return;
		}
		this.targetHeight = this.height;
	}

	// Token: 0x06000A67 RID: 2663 RVA: 0x000449BE File Offset: 0x00042BBE
	[CompilerGenerated]
	private IEnumerator <Start>g__WaitToStart|6_0()
	{
		this.triggered = true;
		yield return new WaitForSeconds(0.5f);
		this.ResetTarget();
		yield break;
	}

	// Token: 0x04000B65 RID: 2917
	public bool relative;

	// Token: 0x04000B66 RID: 2918
	public float height;

	// Token: 0x04000B67 RID: 2919
	private float targetHeight;

	// Token: 0x04000B68 RID: 2920
	public bool triggerWhenAbove = true;

	// Token: 0x04000B69 RID: 2921
	private bool triggered;

	// Token: 0x04000B6A RID: 2922
	public UnityEvent triggerEvent;
}
