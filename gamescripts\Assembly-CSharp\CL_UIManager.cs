﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;
using Febucci.UI.Core;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200001F RID: 31
public class CL_UIManager : MonoBehaviour
{
	// Token: 0x06000137 RID: 311 RVA: 0x0000A3BC File Offset: 0x000085BC
	private void Awake()
	{
		foreach (CL_UIManager.Vignette vignette in this.vignettes)
		{
			this.vignetteDict.Add(vignette.name, vignette);
			vignette.SetLerpTarget(0f);
		}
		CL_UIManager.instance = this;
		this.subtitles.text = "";
		this.subtitleBacking.color = Color.clear;
	}

	// Token: 0x06000138 RID: 312 RVA: 0x0000A44C File Offset: 0x0000864C
	private void OnEnable()
	{
	}

	// Token: 0x06000139 RID: 313 RVA: 0x0000A450 File Offset: 0x00008650
	private void Start()
	{
		this.timer.gameObject.SetActive(SettingsManager.settings.g_timer);
		if (CL_UIManager.debugState == 0)
		{
			DebugMenu.visible = false;
			this.debugMenu.gameObject.SetActive(false);
			CL_UIManager.debugState = 0;
			CL_UIManager.debug = false;
			return;
		}
		if (CL_UIManager.debugState == 2)
		{
			DebugMenu.visible = true;
			this.debugMenu.gameObject.SetActive(true);
			CL_UIManager.debug = true;
			return;
		}
		DebugMenu.visible = true;
		this.debugMenu.gameObject.SetActive(true);
		CL_UIManager.debug = false;
	}

	// Token: 0x0600013A RID: 314 RVA: 0x0000A4E8 File Offset: 0x000086E8
	private void Update()
	{
		if (this.inGame)
		{
			foreach (CL_UIManager.Vignette vignette in this.vignettes)
			{
				vignette.Update();
			}
			if (Input.GetKeyDown(KeyCode.F5))
			{
				CL_UIManager.debugState++;
				if (CL_UIManager.debugState > 2)
				{
					DebugMenu.visible = false;
					this.debugMenu.gameObject.SetActive(false);
					CL_UIManager.debugState = 0;
					CL_UIManager.debug = false;
				}
				else if (CL_UIManager.debugState == 2)
				{
					DebugMenu.visible = true;
					CL_UIManager.debug = true;
				}
				else
				{
					DebugMenu.visible = true;
					this.debugMenu.gameObject.SetActive(true);
				}
			}
			if (Input.GetKeyDown(KeyCode.F1))
			{
				this.timer.gameObject.SetActive(!this.timer.gameObject.activeSelf);
			}
			if (Input.GetKeyDown(KeyCode.F3))
			{
				CL_UIManager.ascentTrackerActive = !CL_UIManager.ascentTrackerActive;
			}
			if (CL_UIManager.ascentTrackerActive != this.ascentTracker.transform.parent.gameObject.activeSelf)
			{
				this.ascentTracker.transform.parent.gameObject.SetActive(CL_UIManager.ascentTrackerActive);
			}
			TimeSpan timeSpan = TimeSpan.FromSeconds((double)CL_GameManager.gMan.GetGameTime());
			if (timeSpan.TotalHours >= 1.0)
			{
				string text = timeSpan.ToString("hh\\:mm\\:ss\\:ff");
				this.timer.text = text;
				return;
			}
			string text2 = timeSpan.ToString("mm\\:ss\\:ff");
			this.timer.text = text2;
		}
	}

	// Token: 0x0600013B RID: 315 RVA: 0x0000A694 File Offset: 0x00008894
	public void SetDescription(string t)
	{
		this.descriptorText.text = t;
	}

	// Token: 0x0600013C RID: 316 RVA: 0x0000A6A2 File Offset: 0x000088A2
	public void Hurt()
	{
		this.SetVignette("hurt", 0.5f);
	}

	// Token: 0x0600013D RID: 317 RVA: 0x0000A6B4 File Offset: 0x000088B4
	public void SetVignetteTarget(string name, float value)
	{
		this.vignetteDict[name].SetLerpTarget(value);
	}

	// Token: 0x0600013E RID: 318 RVA: 0x0000A6C8 File Offset: 0x000088C8
	public void SetVignette(string name, float value)
	{
		this.vignetteDict[name].SetLerpAmount(value);
		this.vignetteDict[name].SetLerpTarget(value);
	}

	// Token: 0x0600013F RID: 319 RVA: 0x0000A6EE File Offset: 0x000088EE
	public void SetVignetteImage(string name, Sprite s)
	{
		this.vignetteDict[name].image.sprite = s;
	}

	// Token: 0x06000140 RID: 320 RVA: 0x0000A707 File Offset: 0x00008907
	public void FadeOut()
	{
		this.SetVignetteTarget("fade", 1f);
	}

	// Token: 0x06000141 RID: 321 RVA: 0x0000A719 File Offset: 0x00008919
	public void FadeIn()
	{
		this.SetVignetteTarget("fade", 0f);
	}

	// Token: 0x06000142 RID: 322 RVA: 0x0000A72B File Offset: 0x0000892B
	public void SetFadeAmount(float a)
	{
		this.SetVignette("fade", a);
	}

	// Token: 0x06000143 RID: 323 RVA: 0x0000A739 File Offset: 0x00008939
	public void SetFadeTarget(float a)
	{
		this.SetVignetteTarget("fade", a);
	}

	// Token: 0x06000144 RID: 324 RVA: 0x0000A747 File Offset: 0x00008947
	public void SetVignetteColor(string n, Color c)
	{
		if (this.vignetteDict.ContainsKey(n))
		{
			this.vignetteDict[n].SetColor(c);
		}
	}

	// Token: 0x06000145 RID: 325 RVA: 0x0000A76C File Offset: 0x0000896C
	public void FlashVignette(string n)
	{
		CL_UIManager.<>c__DisplayClass46_0 CS$<>8__locals1 = new CL_UIManager.<>c__DisplayClass46_0();
		CS$<>8__locals1.<>4__this = this;
		CS$<>8__locals1.n = n;
		if (this.vignetteDict[CS$<>8__locals1.n].currentCoroutine != null)
		{
			base.StopCoroutine(this.vignetteDict[CS$<>8__locals1.n].currentCoroutine);
		}
		this.vignetteDict[CS$<>8__locals1.n].currentCoroutine = CS$<>8__locals1.<FlashVignette>g__Flash|0(1f);
		base.StartCoroutine(this.vignetteDict[CS$<>8__locals1.n].currentCoroutine);
	}

	// Token: 0x06000146 RID: 326 RVA: 0x0000A800 File Offset: 0x00008A00
	public void QuickFlashVignette(string n)
	{
		CL_UIManager.<>c__DisplayClass47_0 CS$<>8__locals1 = new CL_UIManager.<>c__DisplayClass47_0();
		CS$<>8__locals1.<>4__this = this;
		CS$<>8__locals1.n = n;
		if (this.vignetteDict[CS$<>8__locals1.n].currentCoroutine != null)
		{
			base.StopCoroutine(this.vignetteDict[CS$<>8__locals1.n].currentCoroutine);
		}
		this.vignetteDict[CS$<>8__locals1.n].currentCoroutine = CS$<>8__locals1.<QuickFlashVignette>g__Flash|0();
		base.StartCoroutine(this.vignetteDict[CS$<>8__locals1.n].currentCoroutine);
	}

	// Token: 0x06000147 RID: 327 RVA: 0x0000A890 File Offset: 0x00008A90
	public void FlashVignetteImage(string n, Sprite s)
	{
		CL_UIManager.<>c__DisplayClass48_0 CS$<>8__locals1 = new CL_UIManager.<>c__DisplayClass48_0();
		CS$<>8__locals1.<>4__this = this;
		CS$<>8__locals1.n = n;
		base.StartCoroutine(CS$<>8__locals1.<FlashVignetteImage>g__Flash|0());
	}

	// Token: 0x06000148 RID: 328 RVA: 0x0000A8BE File Offset: 0x00008ABE
	public void SetCrosshairVisibility(bool b)
	{
		this.crosshair.gameObject.SetActive(b);
	}

	// Token: 0x06000149 RID: 329 RVA: 0x0000A8D1 File Offset: 0x00008AD1
	public static void SetLoading(bool b)
	{
		if (CL_UIManager.instance == null)
		{
			return;
		}
		CL_UIManager.instance.loadingPopup.SetActive(b);
	}

	// Token: 0x0600014A RID: 330 RVA: 0x0000A8F4 File Offset: 0x00008AF4
	internal void ResetVignettes()
	{
		foreach (CL_UIManager.Vignette vignette in this.vignettes)
		{
			vignette.SetLerpTarget(0f);
		}
	}

	// Token: 0x0600014B RID: 331 RVA: 0x0000A94C File Offset: 0x00008B4C
	public static void ShowSubtitle(string text)
	{
		CL_UIManager.<>c__DisplayClass52_0 CS$<>8__locals1 = new CL_UIManager.<>c__DisplayClass52_0();
		if (!SettingsManager.settings.showSubtitles || text == "")
		{
			return;
		}
		CS$<>8__locals1.subtitleStrings = new List<string>();
		CS$<>8__locals1.subtitleStrings.AddRange(text.Split("<br>", StringSplitOptions.None));
		CL_UIManager.instance.StopCoroutine("SubtitleDisplay");
		CL_UIManager.instance.StartCoroutine(CS$<>8__locals1.<ShowSubtitle>g__SubtitleDisplay|0());
	}

	// Token: 0x0600014D RID: 333 RVA: 0x0000A9D8 File Offset: 0x00008BD8
	[CompilerGenerated]
	internal static float <ShowSubtitle>g__GetDelay|52_1(ref string text)
	{
		float num = 0f;
		Match match = Regex.Match(text, "<delay\\s*=\\s*([+-]?\\d*(?:\\.\\d+)?|\\d+)>", RegexOptions.IgnoreCase);
		if (match.Success)
		{
			float num2;
			if (float.TryParse(match.Groups[1].Value, NumberStyles.Float, CultureInfo.InvariantCulture, out num2))
			{
				num = num2;
			}
			text = text.Remove(match.Index, match.Length);
		}
		return num;
	}

	// Token: 0x040000FF RID: 255
	public bool inGame = true;

	// Token: 0x04000100 RID: 256
	public Transform canvas;

	// Token: 0x04000101 RID: 257
	public Image crosshair;

	// Token: 0x04000102 RID: 258
	public GameObject loadingPopup;

	// Token: 0x04000103 RID: 259
	public GameObject standardInputModule;

	// Token: 0x04000104 RID: 260
	public DebugMenu debugMenu;

	// Token: 0x04000105 RID: 261
	public static bool debug;

	// Token: 0x04000106 RID: 262
	public static int debugState;

	// Token: 0x04000107 RID: 263
	public CanvasGroup reviveGroup;

	// Token: 0x04000108 RID: 264
	public GameObject vignetteParent;

	// Token: 0x04000109 RID: 265
	public List<CL_UIManager.Vignette> vignettes;

	// Token: 0x0400010A RID: 266
	private Dictionary<string, CL_UIManager.Vignette> vignetteDict = new Dictionary<string, CL_UIManager.Vignette>();

	// Token: 0x0400010B RID: 267
	public UT_TextScrawl header;

	// Token: 0x0400010C RID: 268
	public UT_TextScrawl ascentHeader;

	// Token: 0x0400010D RID: 269
	public UT_TextScrawl highscoreHeader;

	// Token: 0x0400010E RID: 270
	public UT_TextScrawl tipHeader;

	// Token: 0x0400010F RID: 271
	public TMP_Text descriptorText;

	// Token: 0x04000110 RID: 272
	public TMP_Text subtitles;

	// Token: 0x04000111 RID: 273
	public Image subtitleBacking;

	// Token: 0x04000112 RID: 274
	public UI_LerpOpen safeAreaText;

	// Token: 0x04000113 RID: 275
	public TMP_Text scoreTitle;

	// Token: 0x04000114 RID: 276
	public TMP_Text timer;

	// Token: 0x04000115 RID: 277
	public TMP_Text ascentTracker;

	// Token: 0x04000116 RID: 278
	public TMP_Text ascentRateTracker;

	// Token: 0x04000117 RID: 279
	public TMP_Text scoreTracker;

	// Token: 0x04000118 RID: 280
	public TMP_Text highScoreTracker;

	// Token: 0x04000119 RID: 281
	public GameObject cheatTracker;

	// Token: 0x0400011A RID: 282
	public TMP_Text achievementText;

	// Token: 0x0400011B RID: 283
	public TypewriterCore achievementTypewriter;

	// Token: 0x0400011C RID: 284
	public static CL_UIManager instance;

	// Token: 0x0400011D RID: 285
	public static bool ascentTrackerActive;

	// Token: 0x02000207 RID: 519
	[Serializable]
	public class Vignette
	{
		// Token: 0x06000CC6 RID: 3270 RVA: 0x0004F915 File Offset: 0x0004DB15
		public void SetColor(Color c)
		{
			this.color = c;
		}

		// Token: 0x06000CC7 RID: 3271 RVA: 0x0004F91E File Offset: 0x0004DB1E
		public Color GetColorTarget()
		{
			return this.color;
		}

		// Token: 0x06000CC8 RID: 3272 RVA: 0x0004F926 File Offset: 0x0004DB26
		internal void SetLerpAmount(float value)
		{
			this.lerpAmount = value;
			this.image.color = Color.Lerp(Color.clear, this.color, value);
		}

		// Token: 0x06000CC9 RID: 3273 RVA: 0x0004F94B File Offset: 0x0004DB4B
		public void SetLerpTarget(float t)
		{
			this.lerpTarget = t;
		}

		// Token: 0x06000CCA RID: 3274 RVA: 0x0004F954 File Offset: 0x0004DB54
		internal void Update()
		{
			this.SetLerpAmount(Mathf.Lerp(this.lerpAmount, this.lerpTarget, this.fadeSpeed * Time.unscaledDeltaTime));
		}

		// Token: 0x04000DCE RID: 3534
		public string name;

		// Token: 0x04000DCF RID: 3535
		public Image image;

		// Token: 0x04000DD0 RID: 3536
		public Color color;

		// Token: 0x04000DD1 RID: 3537
		private float lerpTarget;

		// Token: 0x04000DD2 RID: 3538
		private float lerpAmount;

		// Token: 0x04000DD3 RID: 3539
		public float fadeSpeed = 1f;

		// Token: 0x04000DD4 RID: 3540
		public IEnumerator currentCoroutine;
	}
}
