﻿using System;
using UnityEngine;

// Token: 0x02000040 RID: 64
public class ENT_Hitbox : MonoBehaviour, Damageable
{
	// Token: 0x0600026F RID: 623 RVA: 0x00016E1F File Offset: 0x0001501F
	public virtual bool Damage(float amount, string type)
	{
		string text = "Damaging! ";
		GameEntity gameEntity = this.entity;
		Debug.Log(text + ((gameEntity != null) ? gameEntity.ToString() : null));
		return this.entity.Damage(amount, type);
	}

	// Token: 0x06000270 RID: 624 RVA: 0x00016E4F File Offset: 0x0001504F
	public GameObject GetGameObject()
	{
		return this.entity.gameObject;
	}

	// Token: 0x06000271 RID: 625 RVA: 0x00016E5C File Offset: 0x0001505C
	public GameEntity GetEntity()
	{
		return this.entity;
	}

	// Token: 0x0400037C RID: 892
	public GameEntity entity;
}
