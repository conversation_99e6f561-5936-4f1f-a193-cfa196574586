﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200005E RID: 94
[Serializable]
public class SessionEventModule_AnnouncementPlayer : SessionEventModule
{
	// Token: 0x060003B0 RID: 944 RVA: 0x00022C30 File Offset: 0x00020E30
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		this.Activate();
	}

	// Token: 0x060003B1 RID: 945 RVA: 0x00022C3F File Offset: 0x00020E3F
	public override void Activate()
	{
		AnnouncementController.instance.StartAnnouncement(this.audioClips[Random.Range(0, this.audioClips.Count)], this.tone, false);
		base.Activate();
	}

	// Token: 0x04000506 RID: 1286
	public AudioClip tone;

	// Token: 0x04000507 RID: 1287
	public List<AudioClip> audioClips;
}
