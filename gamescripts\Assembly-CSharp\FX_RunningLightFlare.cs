﻿using System;
using UnityEngine;

// Token: 0x0200002A RID: 42
public class FX_RunningLightFlare : MonoBehaviour
{
	// Token: 0x06000189 RID: 393 RVA: 0x0000BCA7 File Offset: 0x00009EA7
	private void Start()
	{
		this.sprite = base.GetComponent<SpriteRenderer>();
		this.color = this.sprite.color;
	}

	// Token: 0x0600018A RID: 394 RVA: 0x0000BCC6 File Offset: 0x00009EC6
	private void OnEnable()
	{
		Camera.onPreRender = (Camera.CameraCallback)Delegate.Combine(Camera.onPreRender, new Camera.CameraCallback(this.OnPreRenderCallback));
		this.blinkTime = this.blinkOffset;
	}

	// Token: 0x0600018B RID: 395 RVA: 0x0000BCF4 File Offset: 0x00009EF4
	private void OnPreRenderCallback(Camera cam)
	{
		if (this.faceCamera)
		{
			base.transform.rotation = Quaternion.LookRotation(cam.transform.position - base.transform.position);
		}
	}

	// Token: 0x0600018C RID: 396 RVA: 0x0000BD29 File Offset: 0x00009F29
	private void OnDisable()
	{
		Camera.onPreRender = (Camera.CameraCallback)Delegate.Remove(Camera.onPreRender, new Camera.CameraCallback(this.OnPreRenderCallback));
	}

	// Token: 0x0600018D RID: 397 RVA: 0x0000BD4C File Offset: 0x00009F4C
	private void Update()
	{
		this.blinkTime -= Time.deltaTime;
		if (this.blinkTime <= 0f)
		{
			this.blinkState = !this.blinkState;
			this.blinkTime = (this.blinkState ? this.onTime : this.offTime);
		}
		this.sprite.color = Color.Lerp(this.sprite.color, this.blinkState ? this.color : Color.clear, Time.deltaTime * this.changeRate);
	}

	// Token: 0x0600018E RID: 398 RVA: 0x0000BDDF File Offset: 0x00009FDF
	public void SetColor(Color c)
	{
		this.color = c;
	}

	// Token: 0x0400016C RID: 364
	public float onTime = 1f;

	// Token: 0x0400016D RID: 365
	public float offTime = 1f;

	// Token: 0x0400016E RID: 366
	public float changeRate = 3f;

	// Token: 0x0400016F RID: 367
	public float blinkOffset;

	// Token: 0x04000170 RID: 368
	private float blinkTime;

	// Token: 0x04000171 RID: 369
	private bool blinkState;

	// Token: 0x04000172 RID: 370
	public bool faceCamera = true;

	// Token: 0x04000173 RID: 371
	private SpriteRenderer sprite;

	// Token: 0x04000174 RID: 372
	private Color color;
}
