﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

// Token: 0x0200010A RID: 266
public class OS_ButtonAudio : MonoBehaviour
{
	// Token: 0x06000817 RID: 2071 RVA: 0x0003B387 File Offset: 0x00039587
	private void Awake()
	{
		this.button = base.GetComponent<Button>();
		if (this.button == null)
		{
			return;
		}
		this.button.onClick.AddListener(new UnityAction(this.PressButton));
	}

	// Token: 0x06000818 RID: 2072 RVA: 0x0003B3C0 File Offset: 0x000395C0
	public void PressButton()
	{
		OS_Manager.soundPlayer.PlaySound(this.pressAudio);
	}

	// Token: 0x04000980 RID: 2432
	public string pressAudio = "os:app-button";

	// Token: 0x04000981 RID: 2433
	private Button button;
}
