﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000060 RID: 96
[Serializable]
public class SessionEventModule_AudioPlayer : SessionEventModule
{
	// Token: 0x060003B6 RID: 950 RVA: 0x00022D48 File Offset: 0x00020F48
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		this.Activate();
	}

	// Token: 0x060003B7 RID: 951 RVA: 0x00022D57 File Offset: 0x00020F57
	public override void Activate()
	{
		this.PlaySound();
		this.timer = this.repeatTime;
		base.Activate();
	}

	// Token: 0x060003B8 RID: 952 RVA: 0x00022D74 File Offset: 0x00020F74
	public override void Update()
	{
		base.Update();
		if (this.repeat)
		{
			this.timer -= Time.deltaTime;
			if (this.timer <= 0f)
			{
				this.timer = this.repeatTime;
				this.PlaySound();
			}
		}
	}

	// Token: 0x060003B9 RID: 953 RVA: 0x00022DC0 File Offset: 0x00020FC0
	public void PlaySound()
	{
		Transform transform = ENT_Player.playerObject.cam.transform;
		Vector3 vector = transform.position;
		if (this.randomDirection)
		{
			vector += Random.onUnitSphere * this.distanceFromPlayer;
		}
		else
		{
			vector += transform.TransformVector(this.relativePositionToPlayerCamera);
		}
		AudioManager.PlaySound(this.clips[Random.Range(0, this.clips.Count)], vector, this.volume, 1f, this.spatialBlend, false, 1f, null);
	}

	// Token: 0x0400050A RID: 1290
	public List<AudioClip> clips;

	// Token: 0x0400050B RID: 1291
	public float spatialBlend = 1f;

	// Token: 0x0400050C RID: 1292
	public float volume = 1f;

	// Token: 0x0400050D RID: 1293
	public bool randomDirection;

	// Token: 0x0400050E RID: 1294
	public float distanceFromPlayer = 1f;

	// Token: 0x0400050F RID: 1295
	public Vector3 relativePositionToPlayerCamera;

	// Token: 0x04000510 RID: 1296
	public bool repeat;

	// Token: 0x04000511 RID: 1297
	public float repeatTime;

	// Token: 0x04000512 RID: 1298
	private float timer;
}
