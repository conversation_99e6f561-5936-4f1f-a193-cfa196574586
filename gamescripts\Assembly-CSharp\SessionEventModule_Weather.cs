﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200006E RID: 110
[Serializable]
public class SessionEventModule_Weather : SessionEventModule
{
	// Token: 0x060003E5 RID: 997 RVA: 0x00023A3C File Offset: 0x00021C3C
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		this.player = CL_GameManager.gMan.localPlayer;
		this.weatherDebuff = new BuffContainer();
		this.weatherDebuff.id = this.weatherEffects.id;
		this.weatherDebuff.buffs = new List<BuffContainer.Buff>();
		this.weatherDebuff.buffs.AddRange(this.weatherEffects.buffs);
		this.weatherDebuff.loseOverTime = false;
		this.weatherDebuff.SetMultiplier(this.fadeCur);
		if (!this.player.curBuffs.HasBuffContainer(this.weatherEffects.id))
		{
			this.player.curBuffs.AddBuff(this.weatherDebuff);
		}
		if (this.useCloudEffect)
		{
			this.weatherObject = Object.Instantiate<GameObject>(this.weatherObjectAsset);
			this.particleEffect = this.weatherObject.GetComponentsInChildren<ParticleSystem>();
		}
		this.fadeTarget = 1f;
		this.fadeCur = 0f;
		this.particleRates = new Dictionary<ParticleSystem, float>();
		if (this.particleEffect != null)
		{
			foreach (ParticleSystem particleSystem in this.particleEffect)
			{
				particleSystem.Play();
				ParticleSystem.EmissionModule emission = particleSystem.emission;
				this.particleRates.Add(particleSystem, emission.rateOverTimeMultiplier);
				emission.rateOverTimeMultiplier = 0f;
			}
		}
	}

	// Token: 0x060003E6 RID: 998 RVA: 0x00023B94 File Offset: 0x00021D94
	public override void LateUpdate()
	{
		base.Update();
		if (this.useCloudEffect && this.weatherObject != null)
		{
			this.weatherObject.transform.position = Camera.main.transform.position;
		}
		this.fadeCur = Mathf.Lerp(this.fadeCur, this.fadeTarget, Time.deltaTime * 0.25f);
		this.weatherDebuff.SetMultiplier(this.fadeCur);
		this.DataLerp();
		if (this.useCloudEffect)
		{
			this.BlendClouds();
		}
	}

	// Token: 0x060003E7 RID: 999 RVA: 0x00023C23 File Offset: 0x00021E23
	public override void OnModuleDestroy()
	{
		base.OnModuleDestroy();
		if (this.useCloudEffect)
		{
			Object.Destroy(this.weatherObject);
		}
		this.player.curBuffs.RemoveBuffContainer(this.weatherEffects.id);
	}

	// Token: 0x060003E8 RID: 1000 RVA: 0x00023C5C File Offset: 0x00021E5C
	private void DataLerp()
	{
		if (!this.useFXZone)
		{
			return;
		}
		FXManager.FXZoneStore fxzoneStore = new FXManager.FXZoneStore();
		fxzoneStore.blend = this.fadeCur;
		fxzoneStore.priority = this.fxZonePriority;
		fxzoneStore.data = this.fxZone;
		FXManager.fxZones.Enqueue(fxzoneStore, this.fxZonePriority);
	}

	// Token: 0x060003E9 RID: 1001 RVA: 0x00023CB0 File Offset: 0x00021EB0
	private void BlendClouds()
	{
		foreach (ParticleSystem particleSystem in this.particleEffect)
		{
			if (!particleSystem.isPlaying)
			{
				particleSystem.Play();
			}
			particleSystem.emission.rateOverTimeMultiplier = this.fadeCur * this.particleRates[particleSystem];
		}
		foreach (Material material in this.cloudMaterials)
		{
			material.SetFloat("_AlphaMult", this.fadeCur);
			material.SetTextureOffset("_MainTex", new Vector2(0f, Camera.main.transform.position.y * this.cloudMaterialOffsetMult));
		}
	}

	// Token: 0x060003EA RID: 1002 RVA: 0x00023D84 File Offset: 0x00021F84
	public override void SendMessage(string m)
	{
		base.SendMessage(m);
		if (m == "fadeout")
		{
			this.fadeTarget = 0f;
			return;
		}
		if (m == "fadein")
		{
			this.fadeTarget = 1f;
		}
	}

	// Token: 0x0400053C RID: 1340
	public bool active = true;

	// Token: 0x0400053D RID: 1341
	private ENT_Player player;

	// Token: 0x0400053E RID: 1342
	private BuffContainer weatherDebuff;

	// Token: 0x0400053F RID: 1343
	public BuffContainer weatherEffects;

	// Token: 0x04000540 RID: 1344
	private ParticleSystem[] particleEffect;

	// Token: 0x04000541 RID: 1345
	public bool useCloudEffect;

	// Token: 0x04000542 RID: 1346
	public List<Material> cloudMaterials;

	// Token: 0x04000543 RID: 1347
	public float cloudMaterialOffsetMult = 1f;

	// Token: 0x04000544 RID: 1348
	public GameObject weatherObjectAsset;

	// Token: 0x04000545 RID: 1349
	private GameObject weatherObject;

	// Token: 0x04000546 RID: 1350
	public bool useFXZone;

	// Token: 0x04000547 RID: 1351
	public FXManager.FXData fxZone;

	// Token: 0x04000548 RID: 1352
	public byte fxZonePriority;

	// Token: 0x04000549 RID: 1353
	private float fadeTarget = 1f;

	// Token: 0x0400054A RID: 1354
	private float fadeCur;

	// Token: 0x0400054B RID: 1355
	private Dictionary<ParticleSystem, float> particleRates;
}
