﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using Drawing;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020000AA RID: 170
public class M_Level : MonoBehaviourGizmos
{
	// Token: 0x0600056C RID: 1388 RVA: 0x0002CE97 File Offset: 0x0002B097
	private void Awake()
	{
		this.currentBounds = this.CalculateBounds();
	}

	// Token: 0x0600056D RID: 1389 RVA: 0x0002CEA8 File Offset: 0x0002B0A8
	public void OnSpawn()
	{
		if (this.locators != null && this.locators.Count > 0)
		{
			foreach (UT_Locator ut_Locator in this.locators)
			{
				ut_Locator.Initialize();
			}
		}
	}

	// Token: 0x0600056E RID: 1390 RVA: 0x0002CF10 File Offset: 0x0002B110
	public void OnLevelActivate()
	{
		WorldLoader.instance.StartCoroutine(this.<OnLevelActivate>g__RunEnableStates|45_0());
	}

	// Token: 0x0600056F RID: 1391 RVA: 0x0002CF24 File Offset: 0x0002B124
	public void OnEnter()
	{
		if (this.hasEntered)
		{
			return;
		}
		this.hasEntered = true;
		this.enterEvent.Invoke();
		CL_EventManager.EnterLevel(this);
		if (this.subRegion != null)
		{
			StatManager.saveData.SaveFlag("hasvisited-" + this.subRegion.subregionName, true, "");
		}
	}

	// Token: 0x06000570 RID: 1392 RVA: 0x0002CF85 File Offset: 0x0002B185
	public void OnExit()
	{
		this.RemoveTimeTracker();
	}

	// Token: 0x06000571 RID: 1393 RVA: 0x0002CF8D File Offset: 0x0002B18D
	private void OnValidate()
	{
	}

	// Token: 0x06000572 RID: 1394 RVA: 0x0002CF8F File Offset: 0x0002B18F
	public void AddLocators()
	{
		this.locators = new List<UT_Locator>();
		this.locators.AddRange(base.GetComponentsInChildren<UT_Locator>());
	}

	// Token: 0x06000573 RID: 1395 RVA: 0x0002CFB0 File Offset: 0x0002B1B0
	public void SetFlipped(bool b)
	{
		this.flipped = b;
		foreach (Transform transform in this.positiveFlipList)
		{
			if (!(transform == null))
			{
				transform.gameObject.SetActive(!this.flipped);
			}
		}
		foreach (Transform transform2 in this.negativeFlipList)
		{
			if (!(transform2 == null))
			{
				transform2.gameObject.SetActive(this.flipped);
			}
		}
		foreach (BoxCollider boxCollider in base.GetComponentsInChildren<BoxCollider>())
		{
			if (boxCollider.transform.lossyScale.x < 0f || boxCollider.transform.lossyScale.y < 0f || boxCollider.transform.lossyScale.z < 0f)
			{
				boxCollider.size = Vector3.Scale(boxCollider.size, new Vector3(-1f, 1f, 1f));
			}
		}
		bool flag = this.flipped;
		Flippable[] componentsInChildren2 = base.GetComponentsInChildren<Flippable>();
		for (int i = 0; i < componentsInChildren2.Length; i++)
		{
			componentsInChildren2[i].OnFlip(this.flipped);
		}
	}

	// Token: 0x06000574 RID: 1396 RVA: 0x0002D138 File Offset: 0x0002B338
	private void FindFlips()
	{
		this.positiveFlipList = new List<Transform>();
		this.negativeFlipList = new List<Transform>();
		foreach (Transform transform in this.TraverseHierarchy(base.transform))
		{
			if (transform.name.Contains("flip"))
			{
				string[] array = transform.name.Split(":", StringSplitOptions.None);
				if (array[1] == "positive")
				{
					this.positiveFlipList.Add(transform);
				}
				else if (array[1] == "negative")
				{
					this.negativeFlipList.Add(transform);
				}
			}
		}
		this.SetFlipped(false);
	}

	// Token: 0x06000575 RID: 1397 RVA: 0x0002D204 File Offset: 0x0002B404
	private List<Transform> TraverseHierarchy(Transform root)
	{
		List<Transform> list = new List<Transform>();
		foreach (object obj in root)
		{
			Transform transform = (Transform)obj;
			list.Add(transform);
			list.AddRange(this.TraverseHierarchy(transform));
		}
		return list;
	}

	// Token: 0x06000576 RID: 1398 RVA: 0x0002D26C File Offset: 0x0002B46C
	public bool HasEntered()
	{
		return this.hasEntered;
	}

	// Token: 0x06000577 RID: 1399 RVA: 0x0002D274 File Offset: 0x0002B474
	public Bounds CalculateBounds()
	{
		if (this.customBounds)
		{
			if (this.boundsObject != null)
			{
				Transform parent = this.boundsObject.transform.parent;
				this.boundsObject.gameObject.SetActive(true);
				this.boundsObject.transform.parent = null;
				new Bounds(this.levelBounds.center + base.transform.position, this.levelBounds.size);
				Bounds bounds = this.boundsObject.bounds;
				bounds.Encapsulate(this.boundsObject.bounds);
				this.boundsObject.transform.parent = parent;
				this.boundsObject.gameObject.SetActive(false);
				return bounds;
			}
			return new Bounds(this.levelBounds.center + base.transform.position, this.levelBounds.size);
		}
		else
		{
			Collider[] componentsInChildren = base.GetComponentsInChildren<Collider>();
			if (componentsInChildren.Length == 0)
			{
				return new Bounds(base.transform.position, Vector3.zero);
			}
			Bounds bounds2 = componentsInChildren[0].bounds;
			foreach (Collider collider in componentsInChildren)
			{
				if (collider.gameObject.activeSelf && collider.enabled)
				{
					bounds2.Encapsulate(collider.bounds);
				}
			}
			return bounds2;
		}
	}

	// Token: 0x06000578 RID: 1400 RVA: 0x0002D3D8 File Offset: 0x0002B5D8
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			Draw.WireBox(this.GetBounds(), Color.green);
		}
		if (this.spawnPosition)
		{
			Draw.WireCapsule(this.spawnPosition.position - Vector3.up * 0.75f, this.spawnPosition.position + Vector3.up * 0.75f, 0.5f, Color.red);
		}
	}

	// Token: 0x06000579 RID: 1401 RVA: 0x0002D461 File Offset: 0x0002B661
	public Bounds GetBounds()
	{
		Bounds bounds = this.currentBounds;
		return this.currentBounds;
	}

	// Token: 0x0600057A RID: 1402 RVA: 0x0002D470 File Offset: 0x0002B670
	public void ResetBounds()
	{
		this.currentBounds = this.CalculateBounds();
	}

	// Token: 0x0600057B RID: 1403 RVA: 0x0002D480 File Offset: 0x0002B680
	private void Update()
	{
		if (this.timerRunning)
		{
			LevelStat levelStat = StatManager.instance.GetLevelStat(this.levelName);
			float num = CL_GameManager.gMan.GetGameTime() - this.levelStartTime;
			if (levelStat.bestTime == -1f)
			{
				UI_ObjectiveViewer.CreateOrUpdateObjective("timer", "<color=yellow>Level Timer:</color>", "<color=yellow>" + DarkMachineFunctions.SecondsToTimeString(num, "mm\\:ss\\:ff") + "</color>", false);
				return;
			}
			if (num < levelStat.bestTime)
			{
				UI_ObjectiveViewer.CreateOrUpdateObjective("timer", "<color=yellow>Level Timer:</color>", "<color=yellow>" + DarkMachineFunctions.SecondsToTimeString(num, "mm\\:ss\\:ff") + "</color> / " + DarkMachineFunctions.SecondsToTimeString(levelStat.bestTime, "mm\\:ss\\:ff"), false);
				return;
			}
			UI_ObjectiveViewer.CreateOrUpdateObjective("timer", "<color=yellow>Level Timer:</color>", "<color=red>" + DarkMachineFunctions.SecondsToTimeString(num, "mm\\:ss\\:ff") + "</color> / " + DarkMachineFunctions.SecondsToTimeString(levelStat.bestTime, "mm\\:ss\\:ff"), false);
		}
	}

	// Token: 0x0600057C RID: 1404 RVA: 0x0002D574 File Offset: 0x0002B774
	public void Initialize(int seed)
	{
		this.levelSeed = seed;
		this.currentBounds = this.CalculateBounds();
		this.meshColliders = base.GetComponentsInChildren<MeshCollider>();
		this.UpdateSeedables();
		Debug.Log("Initialized " + base.name + " :: " + this.currentBounds.size.ToString());
	}

	// Token: 0x0600057D RID: 1405 RVA: 0x0002D5DC File Offset: 0x0002B7DC
	private void UpdateSeedables()
	{
		Seedable[] componentsInChildren = base.transform.GetComponentsInChildren<Seedable>(true);
		int num = 0;
		Seedable[] array = componentsInChildren;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].SetSeed(this.levelSeed + num++);
		}
	}

	// Token: 0x0600057E RID: 1406 RVA: 0x0002D61C File Offset: 0x0002B81C
	public void CycleColliders()
	{
		if (base.gameObject == null)
		{
			return;
		}
		if (this.meshColliders == null || this.meshColliders.Length == 0)
		{
			return;
		}
		foreach (MeshCollider meshCollider in this.meshColliders)
		{
			if (meshCollider != null && meshCollider.enabled)
			{
				meshCollider.enabled = false;
				meshCollider.enabled = true;
			}
		}
		if (base.gameObject != null && base.gameObject.activeSelf)
		{
			base.StartCoroutine(this.<CycleColliders>g__Cycle|61_0());
		}
	}

	// Token: 0x0600057F RID: 1407 RVA: 0x0002D6AA File Offset: 0x0002B8AA
	public float GetHeight()
	{
		return this.GetLevelExit().position.y - this.GetLevelEntrance().position.y;
	}

	// Token: 0x06000580 RID: 1408 RVA: 0x0002D6CD File Offset: 0x0002B8CD
	public float GetLength()
	{
		return Vector3.Distance(this.GetLevelExit().position, this.GetLevelEntrance().position);
	}

	// Token: 0x06000581 RID: 1409 RVA: 0x0002D6EC File Offset: 0x0002B8EC
	public void SetLoadState(string s)
	{
		foreach (M_Level.LoadState loadState in this.loadStates)
		{
			if (s == loadState.name)
			{
				Debug.Log("Loading State into " + base.name);
				loadState.stateEvent.Invoke();
				loadState.state = true;
			}
		}
	}

	// Token: 0x06000582 RID: 1410 RVA: 0x0002D770 File Offset: 0x0002B970
	public void PlayLoadState(string s)
	{
		foreach (M_Level.LoadState loadState in this.loadStates)
		{
			if (s == loadState.name)
			{
				Debug.Log("Loading State into " + base.name);
				loadState.stateEvent.Invoke();
				if (loadState.resetPlayerPosition)
				{
					ENT_Player.playerObject.Teleport(this.spawnPosition.position, this.spawnPosition.rotation, false);
				}
				break;
			}
		}
	}

	// Token: 0x06000583 RID: 1411 RVA: 0x0002D818 File Offset: 0x0002BA18
	public void AddEnableLoadState(string s)
	{
		if (this.onEnableRunStates == null)
		{
			this.onEnableRunStates = new List<string>();
		}
		this.onEnableRunStates.Add(s);
	}

	// Token: 0x06000584 RID: 1412 RVA: 0x0002D839 File Offset: 0x0002BA39
	public float GetSpawnChance()
	{
		return this.spawnSettings.GetEffectiveSpawnChance();
	}

	// Token: 0x06000585 RID: 1413 RVA: 0x0002D846 File Offset: 0x0002BA46
	public bool CanSpawn()
	{
		return this.spawnSettings.GetEffectiveSpawnChance() != 0f;
	}

	// Token: 0x06000586 RID: 1414 RVA: 0x0002D85D File Offset: 0x0002BA5D
	public Transform GetParentRoot()
	{
		if (this.parentRoot == null)
		{
			return base.transform;
		}
		return this.parentRoot;
	}

	// Token: 0x06000587 RID: 1415 RVA: 0x0002D87C File Offset: 0x0002BA7C
	public void ClearGameobjectsInParentRoot()
	{
		if (this.parentRoot == null)
		{
			return;
		}
		for (int i = this.parentRoot.childCount - 1; i >= 0; i--)
		{
			Object.Destroy(this.parentRoot.GetChild(i).gameObject);
		}
	}

	// Token: 0x06000588 RID: 1416 RVA: 0x0002D8C6 File Offset: 0x0002BAC6
	public Transform GetLevelEntrance()
	{
		if (this.levelEntrance)
		{
			return this.levelEntrance;
		}
		return base.transform;
	}

	// Token: 0x06000589 RID: 1417 RVA: 0x0002D8E2 File Offset: 0x0002BAE2
	public Transform GetLevelExit()
	{
		if (this.levelExit)
		{
			return this.levelExit;
		}
		return base.transform;
	}

	// Token: 0x0600058A RID: 1418 RVA: 0x0002D8FE File Offset: 0x0002BAFE
	public void SetMassSpeedMult(float f)
	{
		this.massSpeedMult = f;
	}

	// Token: 0x0600058B RID: 1419 RVA: 0x0002D907 File Offset: 0x0002BB07
	public void SetAsCurrentLevel()
	{
		if (!WorldLoader.initialized || WorldLoader.instance == null)
		{
			return;
		}
		WorldLoader.instance.SetCurrentLevel(this);
	}

	// Token: 0x0600058C RID: 1420 RVA: 0x0002D929 File Offset: 0x0002BB29
	public int GetLevelSeed()
	{
		return this.levelSeed;
	}

	// Token: 0x0600058D RID: 1421 RVA: 0x0002D931 File Offset: 0x0002BB31
	public bool DoesPauseGeneration()
	{
		return this.lastLevel || this.pauseGeneration;
	}

	// Token: 0x0600058E RID: 1422 RVA: 0x0002D943 File Offset: 0x0002BB43
	public void ContinueGeneration()
	{
		this.lastLevel = false;
		this.pauseGeneration = false;
		WorldLoader.instance.ResumeGeneration();
	}

	// Token: 0x0600058F RID: 1423 RVA: 0x0002D960 File Offset: 0x0002BB60
	public void TimeTrackerStart()
	{
		this.levelStartTime = CL_GameManager.gMan.GetGameTime();
		this.timerRunning = true;
		UI_ObjectiveViewer.SetTitle("RACE: " + this.levelName);
		UI_ObjectiveViewer.CreateOrUpdateObjective("timer", "Level Timer", "00:00", true);
		if (this.devTime != 0f)
		{
			UI_ObjectiveViewer.CreateOrUpdateObjective("devtime", "<sprite=\"challenge_icons\" name=\"challenge_icons_plat\"> Dev Time", DarkMachineFunctions.SecondsToTimeString(this.devTime, "mm\\:ss\\:ff"), true);
			float num = Mathf.Floor(this.devTime * 1.35f);
			UI_ObjectiveViewer.CreateOrUpdateObjective("goldtime", "<sprite=\"challenge_icons\" name=\"challenge_icons_gold\"> Gold Time", DarkMachineFunctions.SecondsToTimeString(num, "mm\\:ss\\:ff"), true);
			float num2 = Mathf.Floor(this.devTime * 2f);
			UI_ObjectiveViewer.CreateOrUpdateObjective("silvertime", "<sprite=\"challenge_icons\" name=\"challenge_icons_silver\"> Silver Time", DarkMachineFunctions.SecondsToTimeString(num2, "mm\\:ss\\:ff"), true);
		}
	}

	// Token: 0x06000590 RID: 1424 RVA: 0x0002DA38 File Offset: 0x0002BC38
	public void TimeTrackerEnd()
	{
		if (!this.timerRunning)
		{
			return;
		}
		this.timerRunning = false;
		float num = CL_GameManager.gMan.GetGameTime() - this.levelStartTime;
		LevelStat levelStat = StatManager.instance.GetLevelStat(this.levelName);
		levelStat.AddTime(num);
		string text = DarkMachineFunctions.SecondsToTimeString(num, "mm\\:ss\\:ff");
		float num2 = this.devTime;
		string text2 = "";
		if (this.devTime != 0f && num <= this.devTime)
		{
			text2 = "<color=grey>Dev Time Beaten (" + DarkMachineFunctions.SecondsToTimeString(this.devTime, "mm\\:ss\\:ff") + ")";
		}
		if (levelStat.bestTime == -1f || num < levelStat.bestTime)
		{
			UI_ObjectiveViewer.CreateOrUpdateObjective("timer", "<color=yellow>New Best Time!</color>", "<color=yellow>" + text + "</color>", true);
			levelStat.bestTime = num;
			CL_UIManager.instance.tipHeader.ShowText("<color=yellow>New Best Time: " + text + "\n" + text2);
			AudioManager.PlayUISound("ui_challenge_major_objective_earned", 1f, 1f);
		}
		else
		{
			string text3 = DarkMachineFunctions.SecondsToTimeString(levelStat.bestTime, "mm\\:ss\\:ff");
			UI_ObjectiveViewer.CreateOrUpdateObjective("timer", "<color=yellow>Level Timer:</color>", string.Concat(new string[] { "<color=red>", text, " | Best: ", text3, "</color>" }), true);
			CL_UIManager.instance.tipHeader.ShowText("<color=red>New Time: " + text + " | Best: " + text3);
			AudioManager.PlayUISound("ui_challenge_failed", 1f, 1f);
		}
		if (this.devTime != 0f)
		{
			if (num <= this.devTime)
			{
				UI_ObjectiveViewer.CreateOrUpdateObjective("devtime", "<sprite=\"challenge_icons\" name=\"challenge_icons_plat\"> <color=green>Dev Time Beaten", DarkMachineFunctions.SecondsToTimeString(this.devTime, "mm\\:ss\\:ff"), true);
			}
			float num3 = Mathf.Floor(this.devTime * 1.35f);
			if (num <= num3)
			{
				UI_ObjectiveViewer.CreateOrUpdateObjective("goldtime", "<sprite=\"challenge_icons\" name=\"challenge_icons_gold\"> <color=green>Gold Time Beaten", DarkMachineFunctions.SecondsToTimeString(num3, "mm\\:ss\\:ff"), true);
			}
			float num4 = Mathf.Floor(this.devTime * 2f);
			if (num <= num4)
			{
				UI_ObjectiveViewer.CreateOrUpdateObjective("silvertime", "<sprite=\"challenge_icons\" name=\"challenge_icons_silver\"> <color=green>Silver Time Beaten", DarkMachineFunctions.SecondsToTimeString(num4, "mm\\:ss\\:ff"), true);
			}
		}
		StatManager.instance.SaveLevelStat(levelStat);
	}

	// Token: 0x06000591 RID: 1425 RVA: 0x0002DC73 File Offset: 0x0002BE73
	public void RemoveTimeTracker()
	{
		this.timerRunning = false;
	}

	// Token: 0x06000592 RID: 1426 RVA: 0x0002DC7C File Offset: 0x0002BE7C
	internal void AddPlacedObject(GameObject o)
	{
		if (this.placedObjects == null)
		{
			this.placedObjects = new List<GameObject>();
		}
		this.placedObjects.Add(o);
	}

	// Token: 0x06000593 RID: 1427 RVA: 0x0002DCA0 File Offset: 0x0002BEA0
	public void DestroyPlacedObjects()
	{
		if (this.placedObjects == null || this.placedObjects.Count == 0)
		{
			return;
		}
		for (int i = this.placedObjects.Count - 1; i >= 0; i--)
		{
			if (!(this.placedObjects[i] == null))
			{
				Object.Destroy(this.placedObjects[i]);
			}
		}
		this.placedObjects.Clear();
	}

	// Token: 0x06000594 RID: 1428 RVA: 0x0002DD0C File Offset: 0x0002BF0C
	public void AddSpawnAction(Action a)
	{
		if (this.spawnActionHash == null)
		{
			this.spawnActionHash = new HashSet<Action>();
		}
		if (!this.spawnActionHash.Contains(a))
		{
			this.spawnAction = (Action)Delegate.Combine(this.spawnAction, a);
			this.spawnActionHash.Add(a);
		}
	}

	// Token: 0x06000596 RID: 1430 RVA: 0x0002DDC6 File Offset: 0x0002BFC6
	[CompilerGenerated]
	private IEnumerator <OnLevelActivate>g__RunEnableStates|45_0()
	{
		yield return null;
		if (this.onEnableRunStates != null)
		{
			foreach (string text in this.onEnableRunStates)
			{
				this.PlayLoadState(text);
			}
			this.onEnableRunStates.Clear();
		}
		yield break;
	}

	// Token: 0x06000597 RID: 1431 RVA: 0x0002DDD5 File Offset: 0x0002BFD5
	[CompilerGenerated]
	private IEnumerator <CycleColliders>g__Cycle|61_0()
	{
		yield return null;
		if (base.gameObject == null)
		{
			yield break;
		}
		foreach (MeshCollider meshCollider in this.meshColliders)
		{
			if (meshCollider != null && meshCollider.enabled)
			{
				meshCollider.enabled = false;
				meshCollider.enabled = true;
			}
		}
		yield break;
	}

	// Token: 0x0400070C RID: 1804
	public string levelName;

	// Token: 0x0400070D RID: 1805
	public Transform levelExit;

	// Token: 0x0400070E RID: 1806
	public Transform levelEntrance;

	// Token: 0x0400070F RID: 1807
	[Space]
	public Transform spawnPosition;

	// Token: 0x04000710 RID: 1808
	[Space]
	public bool lastLevel;

	// Token: 0x04000711 RID: 1809
	internal bool pauseGeneration;

	// Token: 0x04000712 RID: 1810
	[Space]
	public bool showIntroText = true;

	// Token: 0x04000713 RID: 1811
	[TextArea]
	public string introText = "";

	// Token: 0x04000714 RID: 1812
	[Space]
	public UnityEvent enterEvent;

	// Token: 0x04000715 RID: 1813
	public string saveName = "";

	// Token: 0x04000716 RID: 1814
	private bool hasEntered;

	// Token: 0x04000717 RID: 1815
	public bool customBounds;

	// Token: 0x04000718 RID: 1816
	public Collider boundsObject;

	// Token: 0x04000719 RID: 1817
	public Bounds levelBounds;

	// Token: 0x0400071A RID: 1818
	private Bounds currentBounds;

	// Token: 0x0400071B RID: 1819
	private MeshCollider[] meshColliders;

	// Token: 0x0400071C RID: 1820
	public List<SessionEventList> sessionEventLists;

	// Token: 0x0400071D RID: 1821
	public bool setCustomRegionInfo;

	// Token: 0x0400071E RID: 1822
	public M_Region region;

	// Token: 0x0400071F RID: 1823
	public M_Subregion subRegion;

	// Token: 0x04000720 RID: 1824
	public bool canFlip = true;

	// Token: 0x04000721 RID: 1825
	public List<Transform> positiveFlipList = new List<Transform>();

	// Token: 0x04000722 RID: 1826
	public List<Transform> negativeFlipList = new List<Transform>();

	// Token: 0x04000723 RID: 1827
	private bool flipped;

	// Token: 0x04000724 RID: 1828
	public List<M_Level.LoadState> loadStates;

	// Token: 0x04000725 RID: 1829
	public SpawnTable.SpawnSettings spawnSettings;

	// Token: 0x04000726 RID: 1830
	public float massSpeedMult = 1f;

	// Token: 0x04000727 RID: 1831
	public List<TipList> tips;

	// Token: 0x04000728 RID: 1832
	public Transform parentRoot;

	// Token: 0x04000729 RID: 1833
	public bool loadAdjacent;

	// Token: 0x0400072A RID: 1834
	public bool showInLevelList = true;

	// Token: 0x0400072B RID: 1835
	public bool allowEvents = true;

	// Token: 0x0400072C RID: 1836
	public float devTime;

	// Token: 0x0400072D RID: 1837
	private int levelSeed;

	// Token: 0x0400072E RID: 1838
	private List<string> onEnableRunStates;

	// Token: 0x0400072F RID: 1839
	private float levelStartTime;

	// Token: 0x04000730 RID: 1840
	private bool timerRunning;

	// Token: 0x04000731 RID: 1841
	private List<GameObject> placedObjects;

	// Token: 0x04000732 RID: 1842
	private Action spawnAction;

	// Token: 0x04000733 RID: 1843
	private HashSet<Action> spawnActionHash;

	// Token: 0x04000734 RID: 1844
	[SerializeField]
	private List<UT_Locator> locators;

	// Token: 0x02000268 RID: 616
	[Serializable]
	public class LoadState
	{
		// Token: 0x04000FC1 RID: 4033
		public string name;

		// Token: 0x04000FC2 RID: 4034
		public bool state;

		// Token: 0x04000FC3 RID: 4035
		public UnityEvent stateEvent;

		// Token: 0x04000FC4 RID: 4036
		public bool resetPlayerPosition;
	}

	// Token: 0x02000269 RID: 617
	[Serializable]
	public class SaveData
	{
		// Token: 0x06000DC6 RID: 3526 RVA: 0x000542C8 File Offset: 0x000524C8
		public static void LoadDataIntoLevel(M_Level.SaveData data, M_Level targetLevel, bool setPlayerPosition = false, bool loadEntities = false)
		{
			Debug.Log("Loading Level: " + data.playerRespawnLevel.ToString());
			string text = "Loading Level: ";
			Vector3 vector = data.playerRespawnPositionRelativeToLevel;
			Debug.Log(text + vector.ToString());
			if (data.playerRespawnLevel && setPlayerPosition)
			{
				ENT_Player.playerObject.transform.position = targetLevel.transform.TransformPoint(data.playerRespawnPositionRelativeToLevel);
				Debug.Log("Respawn Position: " + targetLevel.transform.TransformPoint(data.playerRespawnPositionRelativeToLevel).ToString());
				string text2 = "Local Respawn Position: ";
				vector = data.playerRespawnPositionRelativeToLevel;
				Debug.Log(text2 + vector.ToString());
				string text3 = "Respawn Level: ";
				vector = data.playerRespawnPositionRelativeToLevel;
				Debug.Log(text3 + vector.ToString());
			}
			targetLevel.levelSeed = data.seed;
			targetLevel.UpdateSeedables();
			foreach (string text4 in data.activeStates)
			{
				targetLevel.AddEnableLoadState(text4);
			}
			if (loadEntities)
			{
				M_Level.SaveData.ClearGameEntitiesFromLevel(targetLevel);
				foreach (GameEntity.BaseEntitySaveData baseEntitySaveData in data.baseEntitySaves)
				{
					GameObject assetGameObject = CL_AssetManager.GetAssetGameObject(baseEntitySaveData.entityID, "");
					if (!(assetGameObject == null))
					{
						GameObject gameObject = Object.Instantiate<GameObject>(assetGameObject, targetLevel.transform.TransformPoint(baseEntitySaveData.position), baseEntitySaveData.rotation, targetLevel.transform);
						gameObject.transform.localRotation = baseEntitySaveData.rotation;
						GameEntity component = gameObject.GetComponent<GameEntity>();
						if (component != null)
						{
							component.LoadEntitySaveData(baseEntitySaveData);
						}
					}
				}
			}
			SaveableObject[] componentsInChildren = targetLevel.GetComponentsInChildren<SaveableObject>();
			if (data.saveInfos != null)
			{
				foreach (SaveableObject saveableObject in componentsInChildren)
				{
					if (saveableObject.CanSave(null))
					{
						foreach (SaveableInfo saveableInfo in data.saveInfos)
						{
							if (saveableObject.GetSaveID() != "" && saveableObject.GetSaveID() == saveableInfo.id)
							{
								saveableObject.SetSaveInfo(saveableInfo);
								break;
							}
						}
					}
				}
			}
		}

		// Token: 0x06000DC7 RID: 3527 RVA: 0x00054574 File Offset: 0x00052774
		public static M_Level.SaveData GetSave(M_Level targetLevel, string[] flags = null)
		{
			M_Level.SaveData saveData = new M_Level.SaveData();
			saveData.levelName = targetLevel.levelName;
			saveData.activeStates = new List<string>();
			saveData.seed = targetLevel.levelSeed;
			foreach (M_Level.LoadState loadState in targetLevel.loadStates)
			{
				if (loadState.state)
				{
					saveData.activeStates.Add(loadState.name);
				}
			}
			if (targetLevel.region != null)
			{
				if (targetLevel.region != null)
				{
					saveData.regionName = targetLevel.region.name;
				}
				if (targetLevel.subRegion != null)
				{
					saveData.subregionName = targetLevel.subRegion.name;
				}
			}
			if (ENT_Player.playerObject != null)
			{
				if (WorldLoader.initialized && WorldLoader.GetCurrentLevelFromBounds().level == targetLevel)
				{
					Debug.Log("Player is located in this level: " + ((targetLevel != null) ? targetLevel.ToString() : null));
					saveData.playerRespawnLevel = true;
					saveData.playerRespawnPositionRelativeToLevel = targetLevel.transform.InverseTransformPoint(ENT_Player.playerObject.transform.position);
				}
				else
				{
					saveData.playerRespawnPositionRelativeToLevel = ENT_Player.playerObject.transform.position;
				}
			}
			saveData.baseEntitySaves = new List<GameEntity.BaseEntitySaveData>();
			foreach (GameEntity gameEntity in targetLevel.GetComponentsInChildren<GameEntity>())
			{
				if (gameEntity.canSave)
				{
					saveData.baseEntitySaves.Add(gameEntity.GetEntitySaveData());
				}
			}
			saveData.saveInfos = new List<SaveableInfo>();
			foreach (SaveableObject saveableObject in targetLevel.GetComponentsInChildren<SaveableObject>())
			{
				if (saveableObject.CanSave(flags))
				{
					saveData.saveInfos.Add(saveableObject.GetSaveInfo());
				}
			}
			return saveData;
		}

		// Token: 0x06000DC8 RID: 3528 RVA: 0x00054760 File Offset: 0x00052960
		public static void ClearGameEntitiesFromLevel(M_Level targetLevel)
		{
			GameEntity[] componentsInChildren = targetLevel.GetComponentsInChildren<GameEntity>();
			for (int i = componentsInChildren.Length - 1; i >= 0; i--)
			{
				if (componentsInChildren[i].canSave)
				{
					componentsInChildren[i].gameObject.SetActive(false);
				}
			}
		}

		// Token: 0x04000FC5 RID: 4037
		public string levelName;

		// Token: 0x04000FC6 RID: 4038
		public List<string> activeStates;

		// Token: 0x04000FC7 RID: 4039
		public string regionName;

		// Token: 0x04000FC8 RID: 4040
		public string subregionName;

		// Token: 0x04000FC9 RID: 4041
		public bool playerRespawnLevel;

		// Token: 0x04000FCA RID: 4042
		public Vector3 playerRespawnPositionRelativeToLevel;

		// Token: 0x04000FCB RID: 4043
		public int seed;

		// Token: 0x04000FCC RID: 4044
		public List<GameEntity.BaseEntitySaveData> baseEntitySaves;

		// Token: 0x04000FCD RID: 4045
		public List<SaveableInfo> saveInfos;
	}
}
