﻿using System;
using UnityEngine;

// Token: 0x02000010 RID: 16
public class AnnouncementSource : MonoBehaviour
{
	// Token: 0x0600005A RID: 90 RVA: 0x00004CF6 File Offset: 0x00002EF6
	private void OnEnable()
	{
		if (AnnouncementController.instance != null)
		{
			AnnouncementController.instance.AddAnnouncer(this);
		}
	}

	// Token: 0x0600005B RID: 91 RVA: 0x00004D10 File Offset: 0x00002F10
	private void OnDisable()
	{
		if (AnnouncementController.instance != null)
		{
			AnnouncementController.instance.RemoveAnnouncer(this);
		}
	}

	// Token: 0x0600005C RID: 92 RVA: 0x00004D2A File Offset: 0x00002F2A
	private void Start()
	{
		this.aud = base.GetComponent<AudioSource>();
	}

	// Token: 0x0600005D RID: 93 RVA: 0x00004D38 File Offset: 0x00002F38
	public void PlayAnnouncement(AudioClip clip, float volume = 1f, bool showSubtitle = true, bool force = false)
	{
		if (this.blockAnnouncements && !force)
		{
			return;
		}
		if (this.aud.isPlaying && !force)
		{
			return;
		}
		this.aud.clip = clip;
		this.aud.Play();
		if (showSubtitle)
		{
			CL_UIManager.ShowSubtitle(CL_LocalizationManager.currentLocalization.GetAnnouncementLine(clip.name));
		}
	}

	// Token: 0x0600005E RID: 94 RVA: 0x00004D93 File Offset: 0x00002F93
	public void ForceAnnouncementIfNotPlaying(AudioClip clip)
	{
		if (this.aud.isPlaying)
		{
			return;
		}
		this.ForceAnnouncement(clip);
	}

	// Token: 0x0600005F RID: 95 RVA: 0x00004DAA File Offset: 0x00002FAA
	public void ForceAnnouncement(AudioClip clip)
	{
		this.aud.clip = clip;
		this.aud.Play();
		CL_UIManager.ShowSubtitle(CL_LocalizationManager.currentLocalization.GetAnnouncementLine(clip.name));
	}

	// Token: 0x06000060 RID: 96 RVA: 0x00004DD8 File Offset: 0x00002FD8
	public void StopAnnouncementSystem()
	{
		AnnouncementController.instance.active = false;
	}

	// Token: 0x06000061 RID: 97 RVA: 0x00004DE5 File Offset: 0x00002FE5
	public void ResumeAnnouncementSystem()
	{
		AnnouncementController.instance.active = true;
	}

	// Token: 0x0400006A RID: 106
	private AudioSource aud;

	// Token: 0x0400006B RID: 107
	public bool blockAnnouncements;
}
