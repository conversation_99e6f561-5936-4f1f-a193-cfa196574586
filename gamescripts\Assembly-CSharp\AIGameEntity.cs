﻿using System;
using System.Collections.Generic;
using DarkMachine.AI;
using UnityEngine;

// Token: 0x0200000B RID: 11
public class AIGameEntity : GameEntity
{
	// Token: 0x0600003E RID: 62 RVA: 0x00004311 File Offset: 0x00002511
	public virtual void Update()
	{
		this.tickTime -= Time.deltaTime;
		if (this.tickTime < 0f)
		{
			this.tickTime = this.tickRate;
			this.TickUpdate();
		}
	}

	// Token: 0x0600003F RID: 63 RVA: 0x00004344 File Offset: 0x00002544
	public virtual void TickUpdate()
	{
	}

	// Token: 0x06000040 RID: 64 RVA: 0x00004348 File Offset: 0x00002548
	public virtual void ChangeState(string state)
	{
		foreach (AIStateComponent aistateComponent in this.aiStates)
		{
			if (state == aistateComponent.name)
			{
				this.currentState.Exit(this, null);
				this.currentState = aistateComponent;
				aistateComponent.Enter(this, null);
				break;
			}
		}
	}

	// Token: 0x0400004A RID: 74
	public List<AIStateComponent> aiStates = new List<AIStateComponent>
	{
		new AIC_Teeth_Wander(),
		new AIC_Teeth_Chase()
	};

	// Token: 0x0400004B RID: 75
	public Transform target;

	// Token: 0x0400004C RID: 76
	public AIStateVariable[] stateVariables;

	// Token: 0x0400004D RID: 77
	public AISightComponent sight;

	// Token: 0x0400004E RID: 78
	public UT_AudioClipHandler clipHandler;

	// Token: 0x0400004F RID: 79
	internal AIStateComponent currentState;

	// Token: 0x04000050 RID: 80
	internal Vector3 targetPosition;

	// Token: 0x04000051 RID: 81
	public float tickRate = 0.1f;

	// Token: 0x04000052 RID: 82
	private float tickTime;

	// Token: 0x020001ED RID: 493
	public interface Grappler
	{
		// Token: 0x06000C6D RID: 3181
		void ReleaseGrapple();
	}
}
