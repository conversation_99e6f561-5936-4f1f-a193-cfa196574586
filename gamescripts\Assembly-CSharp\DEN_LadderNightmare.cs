﻿using System;
using UnityEngine;

// Token: 0x02000037 RID: 55
public class DEN_LadderNightmare : MonoBehaviour
{
	// Token: 0x0600020C RID: 524 RVA: 0x000111DA File Offset: 0x0000F3DA
	private void Start()
	{
		base.transform.parent = null;
		this.startPos = base.transform.position;
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		DEN_LadderNightmare.instance = this;
	}

	// Token: 0x0600020D RID: 525 RVA: 0x0001120C File Offset: 0x0000F40C
	private void Update()
	{
		if (CL_GameManager.GetGamemodeName(false, false) != "Ladder")
		{
			Object.Destroy(base.gameObject);
		}
		this.Breathing();
		float num = this.Peek(this.playerOffset);
		Vector3 vector = ENT_Player.playerObject.transform.position + Vector3.up * num;
		vector.x = Mathf.Lerp(vector.x, this.startPos.x, 0.2f);
		vector.z = Mathf.Lerp(vector.z, this.startPos.z, 0.2f);
		if (this.Attack())
		{
			return;
		}
		base.transform.position = Vector3.Lerp(base.transform.position, vector, Time.deltaTime * 2f);
		base.transform.rotation *= Quaternion.Euler(0f, Time.deltaTime * this.spinSpeed, 0f);
		if (this.canAttack && ENT_Player.playerObject.GetCurrentFallDistance() > 25f)
		{
			this.attacking = true;
			this.clipHandler.PlaySound("nightmare:attack");
		}
	}

	// Token: 0x0600020E RID: 526 RVA: 0x00011340 File Offset: 0x0000F540
	private bool Attack()
	{
		if (!this.attacking)
		{
			return false;
		}
		this.playerOffset = 0f;
		base.transform.position += (ENT_Player.playerObject.transform.position - base.transform.position).normalized * 20f * Time.deltaTime;
		base.transform.rotation *= Quaternion.Euler(0f, Time.deltaTime * this.spinSpeed * 3f, 0f);
		if (Vector3.Distance(base.transform.position, ENT_Player.playerObject.transform.position) < 3f)
		{
			ENT_Player.playerObject.Kill("laddernightmare");
		}
		CL_CameraControl.Shake(0.3f * Time.deltaTime);
		return true;
	}

	// Token: 0x0600020F RID: 527 RVA: 0x00011438 File Offset: 0x0000F638
	private float Peek(float curOffset)
	{
		if (!this.peeking)
		{
			if (this.hideTime > 0f)
			{
				this.hideTime -= Time.deltaTime;
				curOffset *= 1.25f;
			}
			else
			{
				this.peeking = true;
				this.peekTime = (float)Random.Range(2, 6);
			}
		}
		else if (this.peekTime > 0f)
		{
			this.peekTime -= Time.deltaTime;
		}
		else
		{
			this.peeking = false;
			this.hideTime = (float)Random.Range(5, 20);
			if (ENT_Player.playerObject.camRoot.CanSeeTarget(base.transform, 90f))
			{
				this.clipHandler.PlaySound("nightmare:hide");
			}
		}
		return curOffset;
	}

	// Token: 0x06000210 RID: 528 RVA: 0x000114F4 File Offset: 0x0000F6F4
	private void Breathing()
	{
		if (!this.breathing)
		{
			return;
		}
		this.breathTime -= Time.deltaTime;
		if (this.breathTime <= 0f)
		{
			this.breathTime = Random.Range(0f, this.breathTimer) * 2f + 5f;
			this.clipHandler.PlaySound("nightmare:breath", ENT_Player.playerObject.transform.position + Random.onUnitSphere * 6f);
		}
	}

	// Token: 0x06000211 RID: 529 RVA: 0x0001157E File Offset: 0x0000F77E
	public static void SetPlayerOffset(float f)
	{
		if (DEN_LadderNightmare.instance == null)
		{
			return;
		}
		DEN_LadderNightmare.instance.playerOffset = f;
	}

	// Token: 0x06000212 RID: 530 RVA: 0x00011599 File Offset: 0x0000F799
	public static void SetCanPeek(bool b)
	{
		if (DEN_LadderNightmare.instance == null)
		{
			return;
		}
		DEN_LadderNightmare.instance.canPeek = b;
	}

	// Token: 0x06000213 RID: 531 RVA: 0x000115B4 File Offset: 0x0000F7B4
	public static void SetBreathing(bool b)
	{
		if (DEN_LadderNightmare.instance == null)
		{
			return;
		}
		DEN_LadderNightmare.instance.breathing = b;
	}

	// Token: 0x06000214 RID: 532 RVA: 0x000115CF File Offset: 0x0000F7CF
	public static void SetCanAttack(bool b)
	{
		if (DEN_LadderNightmare.instance == null)
		{
			return;
		}
		DEN_LadderNightmare.instance.canAttack = b;
	}

	// Token: 0x04000285 RID: 645
	public float playerOffset = 15f;

	// Token: 0x04000286 RID: 646
	public float spinSpeed = 1f;

	// Token: 0x04000287 RID: 647
	private Vector3 startPos;

	// Token: 0x04000288 RID: 648
	public bool canAttack;

	// Token: 0x04000289 RID: 649
	private bool attacking;

	// Token: 0x0400028A RID: 650
	public bool canPeek;

	// Token: 0x0400028B RID: 651
	private bool peeking;

	// Token: 0x0400028C RID: 652
	private float peekTime;

	// Token: 0x0400028D RID: 653
	private float hideTime;

	// Token: 0x0400028E RID: 654
	public bool breathing;

	// Token: 0x0400028F RID: 655
	public float breathTimer = 10f;

	// Token: 0x04000290 RID: 656
	private float breathTime = 10f;

	// Token: 0x04000291 RID: 657
	private static DEN_LadderNightmare instance;

	// Token: 0x04000292 RID: 658
	private UT_AudioClipHandler clipHandler;
}
