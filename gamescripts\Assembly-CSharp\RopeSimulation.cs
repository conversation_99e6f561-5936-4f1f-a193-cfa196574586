﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000076 RID: 118
public class RopeSimulation
{
	// Token: 0x06000404 RID: 1028 RVA: 0x000252E8 File Offset: 0x000234E8
	public RopeSimulation(int segmentCount, float segmentLength, float ropeStiffness, Vector3 gravity)
	{
		this.segmentCount = segmentCount;
		this.segmentLength = segmentLength;
		this.ropeStiffness = ropeStiffness;
		this.gravity = gravity;
		this.ropeSegments = new List<RopeSegment>();
		for (int i = 0; i < segmentCount; i++)
		{
			this.ropeSegments.Add(new RopeSegment(Vector3.zero));
		}
	}

	// Token: 0x06000405 RID: 1029 RVA: 0x00025344 File Offset: 0x00023544
	public void SimulateRope(Vector3 startPosition)
	{
		for (int i = 1; i < this.ropeSegments.Count; i++)
		{
			RopeSegment ropeSegment = this.ropeSegments[i];
			Vector3 vector = ropeSegment.Velocity + this.gravity * Time.deltaTime;
			ropeSegment.Velocity = vector;
			ropeSegment.Position += vector * Time.deltaTime;
		}
		for (int j = 0; j < 50; j++)
		{
			this.ApplyConstraints(startPosition);
		}
	}

	// Token: 0x06000406 RID: 1030 RVA: 0x000253C8 File Offset: 0x000235C8
	private void ApplyConstraints(Vector3 startPosition)
	{
		this.ropeSegments[0].Position = startPosition;
		for (int i = 0; i < this.ropeSegments.Count - 1; i++)
		{
			RopeSegment ropeSegment = this.ropeSegments[i];
			RopeSegment ropeSegment2 = this.ropeSegments[i + 1];
			float magnitude = (ropeSegment.Position - ropeSegment2.Position).magnitude;
			float num = Mathf.Abs(magnitude - this.segmentLength);
			Vector3 vector = (ropeSegment.Position - ropeSegment2.Position).normalized * num;
			if (magnitude > this.segmentLength)
			{
				ropeSegment2.Position += vector * (1f - this.ropeStiffness);
				ropeSegment.Position -= vector * this.ropeStiffness;
			}
			else
			{
				ropeSegment2.Position -= vector * (1f - this.ropeStiffness);
				ropeSegment.Position += vector * this.ropeStiffness;
			}
			this.ropeSegments[i] = ropeSegment;
			this.ropeSegments[i + 1] = ropeSegment2;
			ropeSegment.TargetPosition = ropeSegment2.Position;
		}
	}

	// Token: 0x06000407 RID: 1031 RVA: 0x00025521 File Offset: 0x00023721
	public List<RopeSegment> GetRopeSegments()
	{
		return this.ropeSegments;
	}

	// Token: 0x0400055C RID: 1372
	private List<RopeSegment> ropeSegments;

	// Token: 0x0400055D RID: 1373
	private int segmentCount;

	// Token: 0x0400055E RID: 1374
	private float segmentLength;

	// Token: 0x0400055F RID: 1375
	private float ropeStiffness;

	// Token: 0x04000560 RID: 1376
	private Vector3 gravity;
}
