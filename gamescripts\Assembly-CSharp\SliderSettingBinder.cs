﻿using System;
using DarkMachine.UI;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000114 RID: 276
public class SliderSettingBinder : MonoBehaviour
{
	// Token: 0x0600084E RID: 2126 RVA: 0x0003C4CC File Offset: 0x0003A6CC
	private void Start()
	{
		this.slider = base.GetComponent<Slider>();
		this.submitSlider = base.GetComponent<SubmitSlider>();
		this.PullSetting();
		if (this.slider != null)
		{
			this.slider.onValueChanged.AddListener(delegate(float value)
			{
				this.UpdateOnValueChanged();
			});
			return;
		}
		this.submitSlider.onValueChanged.AddListener(delegate(float value)
		{
			this.UpdateOnValueChanged();
		});
	}

	// Token: 0x0600084F RID: 2127 RVA: 0x0003C540 File Offset: 0x0003A740
	private void PullSetting()
	{
		string setting = SettingsManager.GetSetting(this.settingName);
		if (this.slider != null)
		{
			Math.Round((double)this.slider.value, 2);
			if (setting == null || this.slider == null)
			{
				return;
			}
			this.slider.value = float.Parse(setting);
			return;
		}
		else
		{
			Math.Round((double)this.submitSlider.value, 2);
			if (setting == null || this.submitSlider == null)
			{
				return;
			}
			this.submitSlider.value = float.Parse(setting);
			return;
		}
	}

	// Token: 0x06000850 RID: 2128 RVA: 0x0003C5D4 File Offset: 0x0003A7D4
	public void UpdateOnValueChanged()
	{
		if (!this.updateOnValueChanged)
		{
			return;
		}
		this.UpdateSetting();
	}

	// Token: 0x06000851 RID: 2129 RVA: 0x0003C5E8 File Offset: 0x0003A7E8
	public void UpdateSetting()
	{
		float num;
		if (this.slider != null)
		{
			num = (float)Math.Round((double)this.slider.value, 2);
		}
		else
		{
			num = (float)Math.Round((double)this.submitSlider.value, 2);
		}
		if (Mathf.Abs(num - this.lastValue) > 0.01f)
		{
			this.lastValue = num;
			SettingsManager.SetSetting(new string[]
			{
				this.settingName,
				num.ToString()
			});
			SettingsManager.RefreshSettings(this.settingName);
		}
	}

	// Token: 0x040009B2 RID: 2482
	public string settingName;

	// Token: 0x040009B3 RID: 2483
	private Slider slider;

	// Token: 0x040009B4 RID: 2484
	private SubmitSlider submitSlider;

	// Token: 0x040009B5 RID: 2485
	private float lastValue;

	// Token: 0x040009B6 RID: 2486
	public bool updateOnValueChanged = true;
}
