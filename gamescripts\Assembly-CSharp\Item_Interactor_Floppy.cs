﻿using System;
using UnityEngine;

// Token: 0x0200009B RID: 155
[CreateAssetMenu(fileName = "New Insertable Item Interactor", menuName = "White Knuckle/Item/Interactor-Floppy")]
public class Item_Interactor_Floppy : Item_Interactor
{
	// Token: 0x0600051B RID: 1307 RVA: 0x0002B400 File Offset: 0x00029600
	public override void Interact(ENT_Player.Hand curhand, ref bool interacting, string fireButton, ref InteractHit hit, out Sprite interactSprite, Collider hitCollider, ObjectTagger tagger, Clickable clickable, Item item = null)
	{
		if (tagger.HasTag(this.interactionTag))
		{
			interactSprite = this.interactionSprite;
			OS_FloppyReader component = clickable.GetGameObject().GetComponent<OS_FloppyReader>();
			UT_InsertableItem ut_InsertableItem = null;
			if (component == null)
			{
				ut_InsertableItem = clickable.GetGameObject().GetComponent<UT_InsertableItem>();
			}
			if (component != null && component.CanInteract(curhand.GetPlayer(), curhand))
			{
				curhand.uiInteract.gameObject.SetActive(true);
			}
			else if (ut_InsertableItem != null && ut_InsertableItem.CanInteract(curhand.GetPlayer(), curhand))
			{
				curhand.uiInteract.gameObject.SetActive(true);
			}
			else
			{
				curhand.uiInteract.gameObject.SetActive(false);
			}
			if (InputManager.GetButton(fireButton).Down && !interacting)
			{
				if (component != null)
				{
					curhand.DropItem(component.transform.position);
					component.InsertFloppy(curhand.GetPlayer(), item);
					return;
				}
				if (ut_InsertableItem != null && ut_InsertableItem.CanInteract(curhand.GetPlayer(), curhand))
				{
					curhand.DestroyItem();
					ut_InsertableItem.Interact();
					return;
				}
			}
		}
		else
		{
			interactSprite = null;
			curhand.uiInteract.gameObject.SetActive(false);
		}
	}
}
