﻿using System;
using System.Threading.Tasks;
using Steamworks;
using Steamworks.Data;
using UnityEngine;

// Token: 0x020000C4 RID: 196
public class SteamManager : MonoBehaviour
{
	// Token: 0x06000670 RID: 1648 RVA: 0x00034774 File Offset: 0x00032974
	private void Awake()
	{
		if (SteamManager.instance != null && SteamManager.instance != this)
		{
			Object.Destroy(base.gameObject);
			return;
		}
		Object.DontDestroyOnLoad(base.gameObject);
		SteamManager.instance = this;
		SteamManager.initialized = true;
		try
		{
			SteamClient.Init(3195790U, true);
			SteamManager.connected = true;
		}
		catch (Exception ex)
		{
			SteamManager.connected = false;
			Debug.Log(ex.Message);
		}
	}

	// Token: 0x06000671 RID: 1649 RVA: 0x000347F4 File Offset: 0x000329F4
	private void Update()
	{
		SteamClient.RunCallbacks();
	}

	// Token: 0x06000672 RID: 1650 RVA: 0x000347FB File Offset: 0x000329FB
	private void OnDestroy()
	{
		if (SteamManager.instance == this)
		{
			SteamManager.Shutdown();
		}
	}

	// Token: 0x06000673 RID: 1651 RVA: 0x0003480F File Offset: 0x00032A0F
	public static void Shutdown()
	{
		if (SteamManager.hasShutdown)
		{
			return;
		}
		SteamManager.hasShutdown = true;
		SteamClient.RunCallbacks();
	}

	// Token: 0x06000674 RID: 1652 RVA: 0x00034824 File Offset: 0x00032A24
	public static Texture2D ConvertSteamIcon(Image image)
	{
		Texture2D texture2D = new Texture2D((int)image.Width, (int)image.Height, TextureFormat.ARGB32, false);
		texture2D.filterMode = FilterMode.Trilinear;
		int num = 0;
		while ((long)num < (long)((ulong)image.Width))
		{
			int num2 = 0;
			while ((long)num2 < (long)((ulong)image.Height))
			{
				global::Steamworks.Data.Color pixel = image.GetPixel(num, num2);
				texture2D.SetPixel(num, (int)(image.Height - (uint)num2), new global::UnityEngine.Color((float)pixel.r / 255f, (float)pixel.g / 255f, (float)pixel.b / 255f, (float)pixel.a / 255f));
				num2++;
			}
			num++;
		}
		texture2D.Apply();
		return texture2D;
	}

	// Token: 0x06000675 RID: 1653 RVA: 0x000348CC File Offset: 0x00032ACC
	public static async Task<Image?> GetAvatar(SteamId targetID)
	{
		Image? image;
		try
		{
			image = await SteamFriends.GetLargeAvatarAsync(targetID);
		}
		catch (Exception ex)
		{
			Debug.Log(ex);
			image = null;
		}
		return image;
	}

	// Token: 0x040007E8 RID: 2024
	public static bool initialized;

	// Token: 0x040007E9 RID: 2025
	public static SteamManager instance;

	// Token: 0x040007EA RID: 2026
	public static bool hasShutdown;

	// Token: 0x040007EB RID: 2027
	public static bool connected;
}
