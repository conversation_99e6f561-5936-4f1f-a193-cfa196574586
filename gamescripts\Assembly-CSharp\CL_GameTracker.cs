﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using UnityEngine;

// Token: 0x0200001B RID: 27
public class CL_GameTracker : MonoBehaviour
{
	// Token: 0x06000103 RID: 259 RVA: 0x0000900F File Offset: 0x0000720F
	private void Awake()
	{
		CL_GameTracker.InitializeSession();
		CL_GameTracker.instance = this;
		this.filePath = Application.persistentDataPath + "/gameData.dat";
	}

	// Token: 0x06000104 RID: 260 RVA: 0x00009031 File Offset: 0x00007231
	private static void InitializeSession()
	{
		if (CL_GameTracker.session != null)
		{
			return;
		}
		CL_GameTracker.session = new CL_GameTracker.Session();
		CL_GameTracker.session.Initialize();
	}

	// Token: 0x06000105 RID: 261 RVA: 0x00009050 File Offset: 0x00007250
	private void Update()
	{
		if (this.tickCounter <= 0f)
		{
			CL_GameTracker.session.AddNewTick();
			this.tickCounter = this.tickRate;
			CL_GameTracker.EventUpdate();
			if (CL_GameManager.gMan.localPlayer != null)
			{
				CL_GameTracker.session.maxHeight = CL_GameManager.gMan.localPlayer.transform.position.y;
			}
		}
		this.tickCounter -= Time.deltaTime;
	}

	// Token: 0x06000106 RID: 262 RVA: 0x000090D1 File Offset: 0x000072D1
	public static void AddEventToSession(CL_GameTracker.Session.Tick.Event e)
	{
		if (CL_GameTracker.session == null)
		{
			CL_GameTracker.InitializeSession();
		}
		CL_GameTracker.session.AddEventToCurrentTick(e);
	}

	// Token: 0x06000107 RID: 263 RVA: 0x000090EA File Offset: 0x000072EA
	public static CL_GameTracker.Session.Tick.Event CreateEvent(string name, string id, string objectType, CL_GameTracker.Session.Tick.Event.EventType type, string data)
	{
		return new CL_GameTracker.Session.Tick.Event
		{
			id = name + ":" + id,
			type = type,
			data = data,
			objectType = objectType
		};
	}

	// Token: 0x06000108 RID: 264 RVA: 0x00009119 File Offset: 0x00007319
	public static CL_GameTracker.Session.Tick.Event CreateEvent(string name, string id, string objectType, CL_GameTracker.Session.Tick.Event.EventType type, Vector3 data)
	{
		return CL_GameTracker.CreateEvent(name, id, objectType, type, CL_GameTracker.ConvertVector3ToString(data));
	}

	// Token: 0x06000109 RID: 265 RVA: 0x0000912C File Offset: 0x0000732C
	public static string ConvertVector3ToString(Vector3 vector)
	{
		return string.Concat(new string[]
		{
			vector.x.ToString(),
			",",
			vector.y.ToString(),
			",",
			vector.z.ToString()
		});
	}

	// Token: 0x0600010A RID: 266 RVA: 0x00009184 File Offset: 0x00007384
	public static object DecomposeData(CL_GameTracker.Session.Tick.Event e)
	{
		if (e.type != CL_GameTracker.Session.Tick.Event.EventType.move && e.type != CL_GameTracker.Session.Tick.Event.EventType.teleport && e.type != CL_GameTracker.Session.Tick.Event.EventType.spawn && e.type != CL_GameTracker.Session.Tick.Event.EventType.destroy && e.type != CL_GameTracker.Session.Tick.Event.EventType.kill)
		{
			return e.data;
		}
		string[] array = e.data.Split(",", StringSplitOptions.None);
		if (array.Length < 3)
		{
			CommandConsole.LogError("WARNING: Tried to decompose data of insufficient type. Vector is not assigned correctly.");
			return null;
		}
		return new Vector3(float.Parse(array[0]), float.Parse(array[1]), float.Parse(array[2]));
	}

	// Token: 0x0600010B RID: 267 RVA: 0x0000920C File Offset: 0x0000740C
	public static string ComposeData(CL_GameTracker.Session.Tick.Event.EventType type, object data)
	{
		if (type == CL_GameTracker.Session.Tick.Event.EventType.move || type == CL_GameTracker.Session.Tick.Event.EventType.teleport || type == CL_GameTracker.Session.Tick.Event.EventType.spawn || type == CL_GameTracker.Session.Tick.Event.EventType.destroy)
		{
			Vector3 vector = (Vector3)data;
			return string.Concat(new string[]
			{
				vector.x.ToString(),
				":",
				vector.y.ToString(),
				":",
				vector.z.ToString()
			});
		}
		return (string)data;
	}

	// Token: 0x0600010C RID: 268 RVA: 0x0000927F File Offset: 0x0000747F
	public static void SaveSession()
	{
		CL_GameTracker.instance.SerializeData(CL_GameTracker.session);
	}

	// Token: 0x0600010D RID: 269 RVA: 0x00009290 File Offset: 0x00007490
	public static CL_GameTracker.Session LoadData()
	{
		return CL_GameTracker.instance.DeserializeData();
	}

	// Token: 0x0600010E RID: 270 RVA: 0x0000929C File Offset: 0x0000749C
	private void SerializeData(CL_GameTracker.Session data)
	{
		BinaryFormatter binaryFormatter = new BinaryFormatter();
		using (FileStream fileStream = new FileStream(this.filePath, FileMode.Create))
		{
			binaryFormatter.Serialize(fileStream, data);
			Debug.Log("Data serialized to " + this.filePath);
		}
	}

	// Token: 0x0600010F RID: 271 RVA: 0x000092F8 File Offset: 0x000074F8
	private CL_GameTracker.Session DeserializeData()
	{
		if (File.Exists(this.filePath))
		{
			BinaryFormatter binaryFormatter = new BinaryFormatter();
			using (FileStream fileStream = new FileStream(this.filePath, FileMode.Open))
			{
				CL_GameTracker.Session session = binaryFormatter.Deserialize(fileStream) as CL_GameTracker.Session;
				Debug.Log("Data deserialized from " + this.filePath);
				return session;
			}
		}
		Debug.LogError("File not found at " + this.filePath);
		return null;
	}

	// Token: 0x040000D6 RID: 214
	public static CL_GameTracker.Session session;

	// Token: 0x040000D7 RID: 215
	public static Action EventUpdate;

	// Token: 0x040000D8 RID: 216
	private string filePath;

	// Token: 0x040000D9 RID: 217
	public CL_GameTracker.Session testSession;

	// Token: 0x040000DA RID: 218
	public static CL_GameTracker instance;

	// Token: 0x040000DB RID: 219
	private float tickCounter;

	// Token: 0x040000DC RID: 220
	private float tickRate = 0.5f;

	// Token: 0x02000201 RID: 513
	[Serializable]
	public class Session
	{
		// Token: 0x06000CBA RID: 3258 RVA: 0x0004F7CC File Offset: 0x0004D9CC
		public void Initialize()
		{
			this.startTime = Time.time;
			this.ticks = new List<CL_GameTracker.Session.Tick>();
			this.AddNewTick();
		}

		// Token: 0x06000CBB RID: 3259 RVA: 0x0004F7EA File Offset: 0x0004D9EA
		public float GetSessionTime()
		{
			return Time.time - this.startTime;
		}

		// Token: 0x06000CBC RID: 3260 RVA: 0x0004F7F8 File Offset: 0x0004D9F8
		public void AddNewTick()
		{
			CL_GameTracker.Session.Tick tick = new CL_GameTracker.Session.Tick();
			tick.timeStamp = this.GetSessionTime();
			tick.events = new List<CL_GameTracker.Session.Tick.Event>();
			this.ticks.Add(tick);
		}

		// Token: 0x06000CBD RID: 3261 RVA: 0x0004F830 File Offset: 0x0004DA30
		public void AddEventToCurrentTick(CL_GameTracker.Session.Tick.Event newEvent)
		{
			CL_GameTracker.Session.Tick lastTick = this.GetLastTick();
			for (int i = 0; i < lastTick.events.Count; i++)
			{
				CL_GameTracker.Session.Tick.Event @event = lastTick.events[i];
				if (this.CompareEvents(newEvent, @event))
				{
					lastTick.events[i] = newEvent;
					return;
				}
			}
			lastTick.events.Add(newEvent);
		}

		// Token: 0x06000CBE RID: 3262 RVA: 0x0004F88B File Offset: 0x0004DA8B
		public CL_GameTracker.Session.Tick GetLastTick()
		{
			return this.ticks[this.ticks.Count - 1];
		}

		// Token: 0x06000CBF RID: 3263 RVA: 0x0004F8A5 File Offset: 0x0004DAA5
		public bool CompareEvents(CL_GameTracker.Session.Tick.Event e1, CL_GameTracker.Session.Tick.Event e2)
		{
			return e1.id == e2.id && e1.type == e2.type;
		}

		// Token: 0x04000DB8 RID: 3512
		public List<CL_GameTracker.Session.Tick> ticks;

		// Token: 0x04000DB9 RID: 3513
		private float startTime;

		// Token: 0x04000DBA RID: 3514
		public float maxHeight;

		// Token: 0x020002FC RID: 764
		[Serializable]
		public class Tick
		{
			// Token: 0x040012BF RID: 4799
			public float timeStamp;

			// Token: 0x040012C0 RID: 4800
			public List<CL_GameTracker.Session.Tick.Event> events = new List<CL_GameTracker.Session.Tick.Event>();

			// Token: 0x0200031F RID: 799
			[Serializable]
			public class Event
			{
				// Token: 0x04001347 RID: 4935
				public string id;

				// Token: 0x04001348 RID: 4936
				public string objectType;

				// Token: 0x04001349 RID: 4937
				public CL_GameTracker.Session.Tick.Event.EventType type;

				// Token: 0x0400134A RID: 4938
				public string data;

				// Token: 0x02000321 RID: 801
				[Serializable]
				public enum EventType
				{
					// Token: 0x04001350 RID: 4944
					none,
					// Token: 0x04001351 RID: 4945
					move,
					// Token: 0x04001352 RID: 4946
					teleport,
					// Token: 0x04001353 RID: 4947
					destroy,
					// Token: 0x04001354 RID: 4948
					kill,
					// Token: 0x04001355 RID: 4949
					use,
					// Token: 0x04001356 RID: 4950
					spawn,
					// Token: 0x04001357 RID: 4951
					special
				}
			}
		}
	}
}
