﻿using System;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000153 RID: 339
public class Message : MonoBehaviour
{
	// Token: 0x060009A8 RID: 2472 RVA: 0x00042219 File Offset: 0x00040419
	private void Start()
	{
	}

	// Token: 0x060009A9 RID: 2473 RVA: 0x0004221B File Offset: 0x0004041B
	private void Update()
	{
	}

	// Token: 0x060009AA RID: 2474 RVA: 0x0004221D File Offset: 0x0004041D
	public void CloseMessage(string s)
	{
		if (this.closeFunction != null)
		{
			this.closeFunction();
		}
		this.exitFunction();
		Object.Destroy(base.gameObject);
		OS_Manager.soundPlayer.PlaySound("os:app-button");
	}

	// Token: 0x060009AB RID: 2475 RVA: 0x00042257 File Offset: 0x00040457
	public void OptionA(string s)
	{
		if (this.optionAFunction != null)
		{
			this.optionAFunction();
		}
		this.exitFunction();
		Object.Destroy(base.gameObject);
		OS_Manager.soundPlayer.PlaySound("os:app-button");
	}

	// Token: 0x060009AC RID: 2476 RVA: 0x00042291 File Offset: 0x00040491
	public virtual void Initialize(string message, string closeString)
	{
		this.textObject.text = message;
		this.closeText.text = closeString;
		this.firstSelect.Select();
	}

	// Token: 0x060009AD RID: 2477 RVA: 0x000422B6 File Offset: 0x000404B6
	public virtual void Initialize(string message, Action cF, string closeString)
	{
		this.textObject.text = message;
		this.closeFunction = cF;
		this.closeText.text = closeString;
		this.firstSelect.Select();
	}

	// Token: 0x060009AE RID: 2478 RVA: 0x000422E4 File Offset: 0x000404E4
	public virtual void Initialize(string message, Action cF, string closeString, Action aF, string aString)
	{
		this.textObject.text = message;
		this.closeFunction = cF;
		this.closeText.text = closeString;
		this.optionAFunction = aF;
		this.optionAText.text = aString;
		this.optionAText.transform.parent.gameObject.SetActive(true);
		this.firstSelect.Select();
	}

	// Token: 0x060009AF RID: 2479 RVA: 0x0004234C File Offset: 0x0004054C
	public virtual void Initialize(Message_Manager.Message_Packet packet)
	{
		this.textObject.text = packet.message;
		if (packet.closeFunction != null)
		{
			this.closeFunction = packet.closeFunction;
		}
		this.closeText.text = packet.closeText;
		if (packet.optionAFunction != null)
		{
			this.optionAFunction = packet.optionAFunction;
			this.optionAText.text = packet.aText;
			this.optionAText.transform.parent.gameObject.SetActive(true);
		}
		else
		{
			this.optionAText.transform.parent.gameObject.SetActive(false);
		}
		this.firstSelect.Select();
	}

	// Token: 0x04000AE4 RID: 2788
	public string messageText;

	// Token: 0x04000AE5 RID: 2789
	public Text textObject;

	// Token: 0x04000AE6 RID: 2790
	public Text closeText;

	// Token: 0x04000AE7 RID: 2791
	public Text optionAText;

	// Token: 0x04000AE8 RID: 2792
	internal Action closeFunction;

	// Token: 0x04000AE9 RID: 2793
	internal Action optionAFunction;

	// Token: 0x04000AEA RID: 2794
	public Action exitFunction;

	// Token: 0x04000AEB RID: 2795
	public Selectable firstSelect;
}
