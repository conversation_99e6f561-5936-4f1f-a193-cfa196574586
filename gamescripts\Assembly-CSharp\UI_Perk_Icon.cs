﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x02000133 RID: 307
public class UI_Perk_Icon : MonoBehaviour
{
	// Token: 0x060008FF RID: 2303 RVA: 0x0003EF64 File Offset: 0x0003D164
	public void SetPerk(Perk p)
	{
		this.perk = p;
		SpriteRenderer component = base.GetComponent<SpriteRenderer>();
		this.mat = component.material;
		component.sprite = this.perk.icon;
	}

	// Token: 0x06000900 RID: 2304 RVA: 0x0003EF9C File Offset: 0x0003D19C
	private void Update()
	{
		if (this.perk == null || !this.perk.IsActive())
		{
			Object.Destroy(base.gameObject);
			return;
		}
		this.mat.SetFloat("_Amount", this.perk.GetBarValue());
		if (this.perk.GetStackAmount() > 1)
		{
			this.text.text = this.perk.GetStackAmount().ToString() + "x";
			return;
		}
		this.text.text = "";
	}

	// Token: 0x04000A4A RID: 2634
	private Perk perk;

	// Token: 0x04000A4B RID: 2635
	private Material mat;

	// Token: 0x04000A4C RID: 2636
	public TMP_Text text;
}
