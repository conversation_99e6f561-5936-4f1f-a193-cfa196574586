﻿using System;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x020001B7 RID: 439
public class Projectile : MonoBehaviourGizmos
{
	// Token: 0x06000B70 RID: 2928 RVA: 0x00048F54 File Offset: 0x00047154
	private void Start()
	{
		this.startScale = base.transform.localScale;
		this.lastPos = base.transform.position;
		if (this.spawnSound != null)
		{
			AudioManager.PlaySound(this.spawnSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
		}
	}

	// Token: 0x06000B71 RID: 2929 RVA: 0x00048FC0 File Offset: 0x000471C0
	private void Update()
	{
		base.transform.localScale = Vector3.Lerp(this.startScale * 0.25f, this.startScale, this.timer * 8f);
		this.lifetime -= Time.deltaTime;
		this.timer += Time.deltaTime;
		if (this.lifetime <= 0f)
		{
			Object.Destroy(base.gameObject);
		}
		if (this.stopped)
		{
			return;
		}
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		this.velocity += Vector3.down * this.gravity * Time.deltaTime;
		Vector3 vector = -this.velocity.normalized * this.airResistance * this.velocity.sqrMagnitude;
		this.velocity += vector * Time.deltaTime;
		base.transform.position += this.velocity * Time.deltaTime;
		if (this.faceVelocity)
		{
			base.transform.rotation = Quaternion.LookRotation(this.velocity);
		}
		RaycastHit raycastHit;
		if (Physics.SphereCast(this.lastPos, this.hitRadius, (base.transform.position - this.lastPos).normalized, out raycastHit, Vector3.Distance(base.transform.position, this.lastPos), this.hitMask))
		{
			ObjectTagger objectTagger = raycastHit.collider.GetComponent<ObjectTagger>();
			if (objectTagger != null)
			{
				GameObject gameObject = raycastHit.collider.gameObject;
				if (objectTagger.HasTag("Hitbox"))
				{
					objectTagger = raycastHit.collider.GetComponent<ENT_Hitbox>().GetEntity().GetTagger();
					gameObject = objectTagger.gameObject;
				}
				if (objectTagger.HasTag("Entity") || objectTagger.HasTag("Prop"))
				{
					GameEntity component = gameObject.GetComponent<GameEntity>();
					if (objectTagger.HasTag("Prop"))
					{
						component.AddForceAtPosition(this.velocity * this.hitForceMultiplier, raycastHit.point);
						string text = "Hit Prop: ";
						Vector3 vector2 = this.velocity;
						Debug.Log(text + vector2.ToString());
					}
					else
					{
						component.AddForce(this.velocity * this.hitForceMultiplier);
					}
				}
				if (objectTagger.HasTag("Damageable"))
				{
					if (gameObject.GetComponent<Damageable>().Damage(this.damage, this.damageType) && this.pierceOnKill)
					{
						return;
					}
				}
				else if (objectTagger.HasTag("Button"))
				{
					CL_Button component2 = gameObject.GetComponent<CL_Button>();
					if (component2.projectilesCanPress)
					{
						component2.Interact();
					}
				}
			}
			if (Vector3.Angle(this.velocity, -raycastHit.normal) > this.bounceAngle)
			{
				this.velocity = Vector3.Reflect(this.velocity, -raycastHit.normal) * 0.8f * this.bounceDampen;
				if (this.reflectSound != null)
				{
					AudioSource.PlayClipAtPoint(this.reflectSound, raycastHit.point, 1f);
				}
				this.bounces++;
				if (this.maxBounces != 0 && this.maxBounces <= this.bounces && !this.useHitEffects && !this.destroyOnHit)
				{
					this.velocity = Vector3.zero;
					this.stopped = true;
				}
			}
			else if (raycastHit.collider != null)
			{
				bool flag = false;
				if (objectTagger != null)
				{
					foreach (Projectile.HitEffect hitEffect in this.customHitEffects)
					{
						if (objectTagger.HasTagInList(hitEffect.tags.ToArray()))
						{
							if (hitEffect.hitEffect != null)
							{
								GameObject gameObject2 = Object.Instantiate<GameObject>(hitEffect.hitEffect, raycastHit.point, base.transform.rotation);
								gameObject2.transform.parent = WorldLoader.GetCurrentLevelParentRoot();
								if (hitEffect.hitType == Projectile.HitType.parent)
								{
									gameObject2.transform.parent = raycastHit.collider.transform;
								}
								else if (hitEffect.hitType == Projectile.HitType.locationParent)
								{
									gameObject2.AddComponent<UT_SoftParent>().Initialize(raycastHit.transform);
								}
								if (WorldLoader.initialized)
								{
									WorldLoader.GetCurrentLevelFromBounds().level.AddPlacedObject(gameObject2);
								}
							}
							flag = true;
							break;
						}
					}
				}
				if (!flag)
				{
					GameObject gameObject3 = Object.Instantiate<GameObject>(this.hitEffect, raycastHit.point, base.transform.rotation);
					gameObject3.transform.parent = WorldLoader.GetCurrentLevelParentRoot();
					if (this.hitType == Projectile.HitType.parent)
					{
						gameObject3.transform.parent = raycastHit.collider.transform;
					}
					else if (this.hitType == Projectile.HitType.locationParent)
					{
						gameObject3.AddComponent<UT_SoftParent>().Initialize(raycastHit.transform);
					}
					if (WorldLoader.initialized)
					{
						WorldLoader.GetCurrentLevelFromBounds().level.AddPlacedObject(gameObject3);
					}
				}
				if (this.hitSound != null)
				{
					AudioManager.PlaySound(this.hitSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
				}
				Object.Destroy(base.gameObject);
			}
		}
		this.lastPos = base.transform.position;
	}

	// Token: 0x06000B72 RID: 2930 RVA: 0x00049568 File Offset: 0x00047768
	public void Initialize(Vector3 direction)
	{
		this.velocity = direction * this.speed;
	}

	// Token: 0x06000B73 RID: 2931 RVA: 0x0004957C File Offset: 0x0004777C
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			Draw.SphereOutline(base.transform.position, this.hitRadius, new Color(1f, 0.561f, 0f, 0.25f));
		}
	}

	// Token: 0x04000C61 RID: 3169
	public float damage = 1f;

	// Token: 0x04000C62 RID: 3170
	public string damageType;

	// Token: 0x04000C63 RID: 3171
	public float speed = 1f;

	// Token: 0x04000C64 RID: 3172
	public float lifetime = 10f;

	// Token: 0x04000C65 RID: 3173
	public bool pierceOnKill;

	// Token: 0x04000C66 RID: 3174
	private Vector3 velocity;

	// Token: 0x04000C67 RID: 3175
	public LayerMask hitMask;

	// Token: 0x04000C68 RID: 3176
	public float airResistance = 0.1f;

	// Token: 0x04000C69 RID: 3177
	public float hitRadius = 0.1f;

	// Token: 0x04000C6A RID: 3178
	public float hitForceMultiplier = 1f;

	// Token: 0x04000C6B RID: 3179
	public float gravity;

	// Token: 0x04000C6C RID: 3180
	public float bounceAngle = 30f;

	// Token: 0x04000C6D RID: 3181
	public float bounceDampen = 1f;

	// Token: 0x04000C6E RID: 3182
	public int maxBounces;

	// Token: 0x04000C6F RID: 3183
	public Vector3 lastPos;

	// Token: 0x04000C70 RID: 3184
	public bool faceVelocity = true;

	// Token: 0x04000C71 RID: 3185
	public bool useHitEffects = true;

	// Token: 0x04000C72 RID: 3186
	public bool destroyOnHit = true;

	// Token: 0x04000C73 RID: 3187
	public GameObject hitEffect;

	// Token: 0x04000C74 RID: 3188
	public Projectile.HitType hitType = Projectile.HitType.none;

	// Token: 0x04000C75 RID: 3189
	public List<Projectile.HitEffect> customHitEffects;

	// Token: 0x04000C76 RID: 3190
	public AudioClip spawnSound;

	// Token: 0x04000C77 RID: 3191
	public AudioClip reflectSound;

	// Token: 0x04000C78 RID: 3192
	public AudioClip hitSound;

	// Token: 0x04000C79 RID: 3193
	private bool stopped;

	// Token: 0x04000C7A RID: 3194
	private int bounces;

	// Token: 0x04000C7B RID: 3195
	private Vector3 fakeStartPos = Vector3.zero;

	// Token: 0x04000C7C RID: 3196
	private float timer;

	// Token: 0x04000C7D RID: 3197
	private Vector3 startScale;

	// Token: 0x020002E8 RID: 744
	[Serializable]
	public class HitEffect
	{
		// Token: 0x0400127C RID: 4732
		public string name;

		// Token: 0x0400127D RID: 4733
		public List<string> tags;

		// Token: 0x0400127E RID: 4734
		public GameObject hitEffect;

		// Token: 0x0400127F RID: 4735
		public Projectile.HitType hitType = Projectile.HitType.none;
	}

	// Token: 0x020002E9 RID: 745
	public enum HitType
	{
		// Token: 0x04001281 RID: 4737
		parent,
		// Token: 0x04001282 RID: 4738
		locationParent,
		// Token: 0x04001283 RID: 4739
		none
	}
}
