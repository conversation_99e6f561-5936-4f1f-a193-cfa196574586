﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020001A7 RID: 423
public class UT_CustomCommand : MonoBehaviour
{
	// Token: 0x06000B29 RID: 2857 RVA: 0x00048122 File Offset: 0x00046322
	private void OnEnable()
	{
		if (this.addCommandOnEnable)
		{
			this.AddCommand();
		}
	}

	// Token: 0x06000B2A RID: 2858 RVA: 0x00048132 File Offset: 0x00046332
	private void OnDisable()
	{
		this.RemoveCommand();
	}

	// Token: 0x06000B2B RID: 2859 RVA: 0x0004813A File Offset: 0x0004633A
	public void AddCommand()
	{
		CommandConsole.AddCommand(this.commandName, new Action<string[]>(this.CustomCommand), this.cheat);
	}

	// Token: 0x06000B2C RID: 2860 RVA: 0x00048159 File Offset: 0x00046359
	public void RemoveCommand()
	{
		CommandConsole.RemoveCommand(this.commandName);
	}

	// Token: 0x06000B2D RID: 2861 RVA: 0x00048166 File Offset: 0x00046366
	public void CustomCommand(string[] args)
	{
		this.commandEvent.Invoke();
	}

	// Token: 0x04000C2C RID: 3116
	public string commandName = "customcommand";

	// Token: 0x04000C2D RID: 3117
	public bool cheat = true;

	// Token: 0x04000C2E RID: 3118
	public bool addCommandOnEnable = true;

	// Token: 0x04000C2F RID: 3119
	public UnityEvent commandEvent;
}
