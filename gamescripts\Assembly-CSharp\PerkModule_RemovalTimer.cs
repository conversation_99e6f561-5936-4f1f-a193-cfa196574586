﻿using System;
using UnityEngine;

// Token: 0x0200004B RID: 75
public class PerkModule_RemovalTimer : PerkModule
{
	// Token: 0x0600032B RID: 811 RVA: 0x00020154 File Offset: 0x0001E354
	public override void Initialize(Perk p)
	{
		base.Initialize(p);
		this.startTime = this.removeTime;
	}

	// Token: 0x0600032C RID: 812 RVA: 0x0002016C File Offset: 0x0001E36C
	public override void Update()
	{
		base.Update();
		if (!CL_GameManager.gMan.isPaused)
		{
			this.removeTime -= Time.deltaTime;
			DebugMenu.UpdateDebugText("perkremoval:" + this.perk.id, "Removing Perk - " + this.perk.id + " :" + this.removeTime.ToString());
			this.perk.SetBarValue(this.removeTime / this.startTime);
			if (this.removeTime <= this.startTime / 2f && !this.halfWayTick)
			{
				this.halfWayTick = true;
				ENT_Player.playerObject.clipHandler.PlaySound("perk:timerhalf");
			}
			if (this.removeTime <= 0f && !this.hasRemoved)
			{
				this.hasRemoved = true;
				ENT_Player.playerObject.clipHandler.PlaySound("perk:loss");
				this.perk.RemoveFromPlayer();
				DebugMenu.DeleteDebugText("perkremoval:" + this.perk.id);
			}
		}
	}

	// Token: 0x0400045E RID: 1118
	private float startTime;

	// Token: 0x0400045F RID: 1119
	public float removeTime;

	// Token: 0x04000460 RID: 1120
	private bool halfWayTick;

	// Token: 0x04000461 RID: 1121
	private bool hasRemoved;
}
