﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using UnityEngine;

// Token: 0x02000064 RID: 100
[Serializable]
public class SessionEventModule_Cheatrooms_Jack : SessionEventModule
{
	// Token: 0x060003C4 RID: 964 RVA: 0x00023076 File Offset: 0x00021276
	public override void Initialize(SessionEvent s)
	{
		this.timer = 0f;
		this.currentAnnouncementSet = 0;
		this.announcementTimer = this.startTimer;
		base.Initialize(s);
		Debug.Log("Jack has entered the building.");
	}

	// Token: 0x060003C5 RID: 965 RVA: 0x000230A8 File Offset: 0x000212A8
	public override void Update()
	{
		this.timer += Time.deltaTime;
		base.Update();
		if (this.announcements.Count > this.currentAnnouncementSet + 1 && this.announcements[this.currentAnnouncementSet + 1].startTime < this.timer)
		{
			this.currentAnnouncementSet++;
		}
		this.announcementTimer -= Time.deltaTime;
		if (this.announcementTimer <= 0f)
		{
			this.announcements[this.currentAnnouncementSet].currentEvent++;
			if (this.announcements[this.currentAnnouncementSet].currentEvent >= this.announcements[this.currentAnnouncementSet].announcements.Count)
			{
				this.announcements[this.currentAnnouncementSet].currentEvent = 0;
			}
			SessionEventModule_Cheatrooms_Jack.Jack_AnnouncerEvent jack_AnnouncerEvent = this.announcements[this.currentAnnouncementSet].announcements[this.announcements[this.currentAnnouncementSet].currentEvent];
			AnnouncementController.instance.StartAnnouncement(jack_AnnouncerEvent.audioClips[Random.Range(0, jack_AnnouncerEvent.audioClips.Count)], null, true);
			this.announcementTimer = Random.Range(this.announcements[this.currentAnnouncementSet].announcementTimeMin, this.announcements[this.currentAnnouncementSet].announcementTimeMax);
			if (jack_AnnouncerEvent.eventCommand == "end")
			{
				WorldLoader.instance.StartCoroutine(this.<Update>g__WaitAndKillPlayer|9_0());
			}
		}
	}

	// Token: 0x060003C6 RID: 966 RVA: 0x0002324E File Offset: 0x0002144E
	public override void OnModuleDestroy()
	{
		base.OnModuleDestroy();
		Debug.Log("Jack has left the room");
	}

	// Token: 0x060003C8 RID: 968 RVA: 0x0002327E File Offset: 0x0002147E
	[CompilerGenerated]
	private IEnumerator <Update>g__WaitAndKillPlayer|9_0()
	{
		yield return new WaitForSeconds(15f);
		Object.Instantiate<GameObject>(this.chaserAsset, ENT_Player.playerObject.transform.position + Vector3.up * -40f, Quaternion.identity);
		CL_GameManager.SetGameFlag("jackkill", true, "", true);
		this.sessionEvent.StopEvent();
		yield break;
	}

	// Token: 0x0400051F RID: 1311
	public List<SessionEventModule_Cheatrooms_Jack.Jack_AnnouncementPeriod> announcements;

	// Token: 0x04000520 RID: 1312
	public float startTimer = 60f;

	// Token: 0x04000521 RID: 1313
	private int currentAnnouncementSet;

	// Token: 0x04000522 RID: 1314
	private float timer;

	// Token: 0x04000523 RID: 1315
	private float announcementTimer = 20f;

	// Token: 0x04000524 RID: 1316
	public GameObject chaserAsset;

	// Token: 0x02000249 RID: 585
	[Serializable]
	public class Jack_AnnouncerEvent
	{
		// Token: 0x04000F31 RID: 3889
		public string name;

		// Token: 0x04000F32 RID: 3890
		public AudioClip tone;

		// Token: 0x04000F33 RID: 3891
		public List<AudioClip> audioClips;

		// Token: 0x04000F34 RID: 3892
		internal bool hasBeenPlayed;

		// Token: 0x04000F35 RID: 3893
		public string eventCommand;
	}

	// Token: 0x0200024A RID: 586
	[Serializable]
	public class Jack_AnnouncementPeriod
	{
		// Token: 0x04000F36 RID: 3894
		public int currentEvent;

		// Token: 0x04000F37 RID: 3895
		public List<SessionEventModule_Cheatrooms_Jack.Jack_AnnouncerEvent> announcements;

		// Token: 0x04000F38 RID: 3896
		public float announcementTimeMin = 20f;

		// Token: 0x04000F39 RID: 3897
		public float announcementTimeMax = 20f;

		// Token: 0x04000F3A RID: 3898
		public float startTime;
	}
}
