﻿using System;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x0200001C RID: 28
[ExecuteInEditMode]
public class CL_Lamp : MonoBehaviourGizmos
{
	// Token: 0x06000111 RID: 273 RVA: 0x0000938F File Offset: 0x0000758F
	private void Awake()
	{
		this.intensityMultiplier = 1f;
	}

	// Token: 0x06000112 RID: 274 RVA: 0x0000939C File Offset: 0x0000759C
	private void Update()
	{
		this.fade = Mathf.Lerp(this.fade, this.fadeTarget, Time.deltaTime * 2f);
	}

	// Token: 0x06000113 RID: 275 RVA: 0x000093C0 File Offset: 0x000075C0
	private void OnRenderObject()
	{
		if (!base.gameObject.activeInHierarchy)
		{
			return;
		}
		switch (this.anim)
		{
		case CL_Lamp.Animations.Pulse:
			this.color = this.startColor * Mathf.Max(1f - (Mathf.Sin(Time.realtimeSinceStartup * this.animSpeed + this.animOffset) + 1f) * this.animAmplitude * 0.1f, 0f);
			this.curRange = this.range;
			this.flickerTime -= Time.deltaTime;
			break;
		case CL_Lamp.Animations.Flicker:
			if (this.flickerTime <= 0f)
			{
				this.flickerTime = 1f / this.animSpeed;
				this.curRange = this.range;
				this.flickerTarget = this.startColor * (1f - Random.Range(0f, this.animAmplitude * 0.5f));
			}
			this.color = Color.Lerp(this.color, this.flickerTarget, Time.deltaTime * 2f);
			this.flickerTime -= Time.deltaTime;
			break;
		case CL_Lamp.Animations.Lightning:
			this.color = Color.Lerp(this.color, Color.black, Time.deltaTime * this.animAmplitude);
			this.curRange = this.range;
			this.intensityMultiplier = Mathf.Lerp(this.intensityMultiplier, 0f, Time.deltaTime * this.animAmplitude);
			if (Random.value < this.animSpeed * Time.deltaTime / 100f && this.flickerTime <= 0f)
			{
				this.color = this.startColor;
				this.curRange = this.range;
				this.intensityMultiplier = 1f;
				this.flickerTime = 1f;
			}
			this.flickerTime -= Time.deltaTime;
			break;
		default:
			if (this.color != this.startColor || this.curRange != this.range)
			{
				this.color = this.startColor;
				this.curRange = this.range;
				return;
			}
			break;
		}
		if (FXManager.fxMan != null)
		{
			FXManager.fxMan.UpdateLamp(this);
		}
	}

	// Token: 0x06000114 RID: 276 RVA: 0x00009608 File Offset: 0x00007808
	private void OnEnable()
	{
		if (CL_Lamp.effectorLamps == null)
		{
			CL_Lamp.effectorLamps = new List<CL_Lamp>();
		}
		if (this.isEffector)
		{
			CL_Lamp.effectorLamps.Add(this);
		}
		if (FXManager.fxMan == null)
		{
			return;
		}
		FXManager.fxMan.AddLamp(this);
		this.SetIntensityMultiplier(1f);
	}

	// Token: 0x06000115 RID: 277 RVA: 0x00009660 File Offset: 0x00007860
	private void OnDisable()
	{
		if (CL_Lamp.effectorLamps == null)
		{
			CL_Lamp.effectorLamps = new List<CL_Lamp>();
		}
		if (this.isEffector && CL_Lamp.effectorLamps.Contains(this))
		{
			CL_Lamp.effectorLamps.Add(this);
		}
		if (FXManager.fxMan == null)
		{
			return;
		}
		FXManager.fxMan.DeleteLamp(this);
	}

	// Token: 0x06000116 RID: 278 RVA: 0x000096B7 File Offset: 0x000078B7
	private void OnDestroy()
	{
		if (FXManager.fxMan == null)
		{
			return;
		}
		FXManager.fxMan.DeleteLamp(this);
	}

	// Token: 0x06000117 RID: 279 RVA: 0x000096D4 File Offset: 0x000078D4
	public override void DrawGizmos()
	{
		Draw.SphereOutline(base.transform.position, 0.2f, Color.yellow);
		if (GizmoContext.InSelection(this))
		{
			Draw.SphereOutline(base.transform.position, this.range, this.startColor * new Color(1f, 1f, 1f, 0.1f));
		}
		else
		{
			Draw.SphereOutline(base.transform.position, this.range, this.startColor * new Color(1f, 1f, 1f, 0.005f));
		}
		if (this.angle > 0f)
		{
			float num = Mathf.Cos(this.angle * 0.00872665f);
			float num2 = Mathf.Sin(this.angle * 0.00872665f);
			Draw.Line(base.transform.position, base.transform.position + base.transform.forward * this.range, this.startColor);
			float num3 = 0.25f;
			Draw.DashedLine(base.transform.position, base.transform.position + (base.transform.forward * num + base.transform.up * num2).normalized * this.range, num3, num3, this.startColor);
			Draw.DashedLine(base.transform.position, base.transform.position + (base.transform.forward * num + -base.transform.up * num2).normalized * this.range, num3, num3, this.startColor);
			Draw.DashedLine(base.transform.position, base.transform.position + (base.transform.forward * num + -base.transform.right * num2).normalized * this.range, num3, num3, this.startColor);
			Draw.DashedLine(base.transform.position, base.transform.position + (base.transform.forward * num + base.transform.right * num2).normalized * this.range, num3, num3, this.startColor);
		}
	}

	// Token: 0x06000118 RID: 280 RVA: 0x000099B8 File Offset: 0x00007BB8
	public void SetAnimation(string a)
	{
		this.anim = (CL_Lamp.Animations)Enum.Parse(typeof(CL_Lamp.Animations), a);
	}

	// Token: 0x06000119 RID: 281 RVA: 0x000099D5 File Offset: 0x00007BD5
	public void SetColor(Color c)
	{
		this.color = c;
	}

	// Token: 0x0600011A RID: 282 RVA: 0x000099DE File Offset: 0x00007BDE
	public void SetRange(float r)
	{
		this.range = r;
	}

	// Token: 0x0600011B RID: 283 RVA: 0x000099E7 File Offset: 0x00007BE7
	public void SetAngle(float a)
	{
		this.angle = a;
	}

	// Token: 0x0600011C RID: 284 RVA: 0x000099F0 File Offset: 0x00007BF0
	public void FadeOut()
	{
		this.fadeTarget = 0f;
	}

	// Token: 0x0600011D RID: 285 RVA: 0x000099FD File Offset: 0x00007BFD
	public void FadeIn()
	{
		this.fadeTarget = 1f;
	}

	// Token: 0x0600011E RID: 286 RVA: 0x00009A0A File Offset: 0x00007C0A
	public void FadeTo(float f)
	{
		this.fadeTarget = f;
	}

	// Token: 0x0600011F RID: 287 RVA: 0x00009A13 File Offset: 0x00007C13
	public float GetFade()
	{
		return this.fade;
	}

	// Token: 0x06000120 RID: 288 RVA: 0x00009A1C File Offset: 0x00007C1C
	public static List<CL_Lamp> GetEffectorsOfPosition(Vector3 position)
	{
		List<CL_Lamp> list = new List<CL_Lamp>();
		if (CL_Lamp.effectorLamps == null || CL_Lamp.effectorLamps.Count <= 0)
		{
			return null;
		}
		foreach (CL_Lamp cl_Lamp in CL_Lamp.effectorLamps)
		{
			if (!(cl_Lamp == null))
			{
				Vector3 vector = position - cl_Lamp.transform.position;
				float num = Vector3.SqrMagnitude(vector);
				float num2 = cl_Lamp.range * cl_Lamp.range;
				if (num < num2)
				{
					if (cl_Lamp.angle > 0f)
					{
						if (Vector3.Angle(cl_Lamp.transform.forward, vector) * 2f < cl_Lamp.angle)
						{
							list.Add(cl_Lamp);
						}
					}
					else
					{
						list.Add(cl_Lamp);
					}
				}
			}
		}
		if (list.Count == 0)
		{
			return null;
		}
		return list;
	}

	// Token: 0x06000121 RID: 289 RVA: 0x00009B08 File Offset: 0x00007D08
	public static bool IsLitByEffector(Vector3 position)
	{
		return CL_Lamp.GetEffectorsOfPosition(position) != null;
	}

	// Token: 0x06000122 RID: 290 RVA: 0x00009B18 File Offset: 0x00007D18
	public static float GetHighestEffectorFearRating(Vector3 position)
	{
		List<CL_Lamp> effectorsOfPosition = CL_Lamp.GetEffectorsOfPosition(position);
		if (effectorsOfPosition == null)
		{
			return 0f;
		}
		float num = 0f;
		foreach (CL_Lamp cl_Lamp in effectorsOfPosition)
		{
			if (cl_Lamp.fearMultiplier != 0f && cl_Lamp.fearMultiplier >= num)
			{
				float num2 = Vector3.Distance(cl_Lamp.transform.position, position) / cl_Lamp.range;
				if (cl_Lamp.angle > 0f)
				{
					float num3 = 1f - Vector3.Angle(cl_Lamp.transform.forward, position - cl_Lamp.transform.position) * 2f / cl_Lamp.angle;
					num2 *= num3;
					float num4 = Mathf.Cos(cl_Lamp.angle * 0.00872665f);
					float num5 = Mathf.Sin(cl_Lamp.angle * 0.00872665f);
					Draw.Line(cl_Lamp.transform.position, cl_Lamp.transform.position + cl_Lamp.transform.forward * cl_Lamp.range, Color.red * cl_Lamp.fearMultiplier);
					Draw.SphereOutline(cl_Lamp.transform.position, 5f, Color.red * cl_Lamp.fearMultiplier);
					Draw.Label2D(cl_Lamp.transform.position, "Fear: " + (num2 * cl_Lamp.fearMultiplier).ToString() + " A: " + num3.ToString(), 14f);
					float num6 = 0.25f;
					Draw.DashedLine(cl_Lamp.transform.position, cl_Lamp.transform.position + (cl_Lamp.transform.forward * num4 + cl_Lamp.transform.up * num5).normalized * cl_Lamp.range, num6, num6, Color.red * cl_Lamp.fearMultiplier);
					Draw.DashedLine(cl_Lamp.transform.position, cl_Lamp.transform.position + (cl_Lamp.transform.forward * num4 + -cl_Lamp.transform.up * num5).normalized * cl_Lamp.range, num6, num6, Color.red * cl_Lamp.fearMultiplier);
					Draw.DashedLine(cl_Lamp.transform.position, cl_Lamp.transform.position + (cl_Lamp.transform.forward * num4 + -cl_Lamp.transform.right * num5).normalized * cl_Lamp.range, num6, num6, Color.red * cl_Lamp.fearMultiplier);
					Draw.DashedLine(cl_Lamp.transform.position, cl_Lamp.transform.position + (cl_Lamp.transform.forward * num4 + cl_Lamp.transform.right * num5).normalized * cl_Lamp.range, num6, num6, Color.red * cl_Lamp.fearMultiplier);
				}
				if (cl_Lamp.fearUsesIntensity)
				{
					num2 *= cl_Lamp.intensity;
				}
				if (num2 > num)
				{
					num = cl_Lamp.fearMultiplier;
				}
			}
		}
		return num;
	}

	// Token: 0x06000123 RID: 291 RVA: 0x00009F00 File Offset: 0x00008100
	public float GetCurrentIntensity()
	{
		return this.intensity * this.intensityMultiplier;
	}

	// Token: 0x06000124 RID: 292 RVA: 0x00009F0F File Offset: 0x0000810F
	internal void SetIntensityMultiplier(float v)
	{
		this.intensityMultiplier = v;
	}

	// Token: 0x06000125 RID: 293 RVA: 0x00009F18 File Offset: 0x00008118
	public void MoveToFrontOfQueue()
	{
		FXManager.fxMan.MoveLampToFrontOfQueue(this);
	}

	// Token: 0x040000DD RID: 221
	public static List<CL_Lamp> effectorLamps;

	// Token: 0x040000DE RID: 222
	public bool lampActive;

	// Token: 0x040000DF RID: 223
	[Range(0f, 240f)]
	public float range = 1f;

	// Token: 0x040000E0 RID: 224
	[Range(0f, 10f)]
	public int attentuation = 1;

	// Token: 0x040000E1 RID: 225
	[HideInInspector]
	public float curRange = 1f;

	// Token: 0x040000E2 RID: 226
	public Color startColor = Color.white;

	// Token: 0x040000E3 RID: 227
	[HideInInspector]
	public Color color;

	// Token: 0x040000E4 RID: 228
	[Range(0f, 360f)]
	public float angle;

	// Token: 0x040000E5 RID: 229
	public float intensity = 1f;

	// Token: 0x040000E6 RID: 230
	private float intensityMultiplier = 1f;

	// Token: 0x040000E7 RID: 231
	public float bypassLightmap;

	// Token: 0x040000E8 RID: 232
	public float emissiveMult = 1f;

	// Token: 0x040000E9 RID: 233
	public CL_Lamp.Animations anim;

	// Token: 0x040000EA RID: 234
	public float animSpeed = 0.2f;

	// Token: 0x040000EB RID: 235
	public float animAmplitude = 0.2f;

	// Token: 0x040000EC RID: 236
	public float animOffset;

	// Token: 0x040000ED RID: 237
	public float fadeTarget = 1f;

	// Token: 0x040000EE RID: 238
	private float fade = 1f;

	// Token: 0x040000EF RID: 239
	private float flickerTime;

	// Token: 0x040000F0 RID: 240
	private Color flickerTarget = Color.black;

	// Token: 0x040000F1 RID: 241
	public bool isEffector;

	// Token: 0x040000F2 RID: 242
	public float fearMultiplier = 1f;

	// Token: 0x040000F3 RID: 243
	public bool fearUsesIntensity = true;

	// Token: 0x02000202 RID: 514
	public enum Animations
	{
		// Token: 0x04000DBC RID: 3516
		None,
		// Token: 0x04000DBD RID: 3517
		Pulse,
		// Token: 0x04000DBE RID: 3518
		Flicker,
		// Token: 0x04000DBF RID: 3519
		Lightning
	}
}
