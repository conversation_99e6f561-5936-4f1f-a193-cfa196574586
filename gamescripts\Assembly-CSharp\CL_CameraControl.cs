﻿using System;
using UnityEngine;

// Token: 0x02000018 RID: 24
public class CL_CameraControl : MonoBehaviour
{
	// Token: 0x060000A8 RID: 168 RVA: 0x00006AAC File Offset: 0x00004CAC
	private void Start()
	{
		this.localRoot = base.transform.localPosition;
	}

	// Token: 0x060000A9 RID: 169 RVA: 0x00006AC0 File Offset: 0x00004CC0
	private void OnEnable()
	{
		CL_CameraControl.Shake = (Action<float>)Delegate.Combine(CL_CameraControl.Shake, new Action<float>(this.ShakeCamera));
		CL_CameraControl.ShakeAtPosition = (Action<Vector3, float, float>)Delegate.Combine(CL_CameraControl.ShakeAtPosition, new Action<Vector3, float, float>(this.ShakeCamera));
	}

	// Token: 0x060000AA RID: 170 RVA: 0x00006B10 File Offset: 0x00004D10
	private void OnDisable()
	{
		CL_CameraControl.Shake = (Action<float>)Delegate.Remove(CL_CameraControl.Shake, new Action<float>(this.ShakeCamera));
		CL_CameraControl.ShakeAtPosition = (Action<Vector3, float, float>)Delegate.Remove(CL_CameraControl.ShakeAtPosition, new Action<Vector3, float, float>(this.ShakeCamera));
	}

	// Token: 0x060000AB RID: 171 RVA: 0x00006B60 File Offset: 0x00004D60
	private void LateUpdate()
	{
		this.shakeAmount = Mathf.Lerp(this.shakeAmount, 0f, Time.deltaTime * 6f);
		this.shakeTime -= Time.deltaTime * 0.4f;
		if (this.shakeTime <= 0f && this.shakeAmount > 0.001f)
		{
			this.shakeTime = 0.01f;
			base.transform.GetChild(0).localPosition = Vector3.ClampMagnitude(Random.insideUnitSphere * this.shakeAmount, 0.5f);
			base.transform.GetChild(0).localRotation = Quaternion.Euler(Vector3.ClampMagnitude(Random.insideUnitSphere * this.shakeAmount * 30f, 20.5f));
		}
	}

	// Token: 0x060000AC RID: 172 RVA: 0x00006C30 File Offset: 0x00004E30
	public void UpdateLocalPosition(Vector3 newPos)
	{
		this.localRoot = newPos;
	}

	// Token: 0x060000AD RID: 173 RVA: 0x00006C39 File Offset: 0x00004E39
	public Vector3 GetLocalPosition()
	{
		return this.localRoot;
	}

	// Token: 0x060000AE RID: 174 RVA: 0x00006C41 File Offset: 0x00004E41
	public void ShakeCamera(float amount)
	{
		this.shakeAmount += amount;
	}

	// Token: 0x060000AF RID: 175 RVA: 0x00006C54 File Offset: 0x00004E54
	public void ShakeCamera(Vector3 position, float amount, float distance)
	{
		float num = 1f - Mathf.Clamp01(Vector3.Distance(position, base.transform.position) / distance);
		this.ShakeCamera(amount * num);
	}

	// Token: 0x060000B0 RID: 176 RVA: 0x00006C89 File Offset: 0x00004E89
	public static void CalculateShake(Vector3 position, float amount, float distance)
	{
		if (CL_CameraControl.ShakeAtPosition != null)
		{
			CL_CameraControl.ShakeAtPosition(position, amount, distance);
		}
	}

	// Token: 0x060000B1 RID: 177 RVA: 0x00006CA0 File Offset: 0x00004EA0
	public bool CanSeeTarget(Transform target, float maxAngle = 90f)
	{
		float num;
		if (this.camera != null)
		{
			num = Vector3.Angle(this.camera.transform.forward, (target.position - this.camera.transform.position).normalized);
		}
		else
		{
			num = Vector3.Angle(base.transform.forward, (target.position - base.transform.position).normalized);
		}
		return num < maxAngle;
	}

	// Token: 0x04000096 RID: 150
	private float shakeAmount;

	// Token: 0x04000097 RID: 151
	private Vector3 localRoot;

	// Token: 0x04000098 RID: 152
	private float shakeTime = 0.01f;

	// Token: 0x04000099 RID: 153
	public static Action<float> Shake;

	// Token: 0x0400009A RID: 154
	public static Action<Vector3, float, float> ShakeAtPosition;

	// Token: 0x0400009B RID: 155
	public Camera camera;
}
