﻿using System;
using UnityEngine;

// Token: 0x0200004C RID: 76
public class ViewSway : MonoBehaviour
{
	// Token: 0x0600032E RID: 814 RVA: 0x00020295 File Offset: 0x0001E495
	private void Start()
	{
		this.initialPosition = base.transform.localPosition;
	}

	// Token: 0x0600032F RID: 815 RVA: 0x000202A8 File Offset: 0x0001E4A8
	public void Initialize(ENT_Player.Hand h)
	{
		this.hand = h;
	}

	// Token: 0x06000330 RID: 816 RVA: 0x000202B4 File Offset: 0x0001E4B4
	private void Update()
	{
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		if (this.hand != null && this.hand.IsLocked())
		{
			return;
		}
		this.shakeAmount = Mathf.Lerp(this.shakeAmount, 0f, Time.deltaTime * 8f);
		this.rockAmount = Mathf.Lerp(this.shakeAmount, 0f, Time.deltaTime * 4f);
		float magnitude = ENT_Player.playerObject.cCon.velocity.magnitude;
		if (magnitude > 0.1f)
		{
			float num = Mathf.Sin(this.bobTimer * this.bobFrequency) * this.bobHorizontalAmplitude * magnitude;
			float num2 = Mathf.Abs(Mathf.Sin(this.bobTimer * this.bobFrequency * 2f + this.bobOffset)) * this.bobVerticalAmplitude * magnitude;
			Vector3 vector = new Vector3(this.initialPosition.x + num, this.initialPosition.y + num2, this.initialPosition.z);
			base.transform.localPosition = Vector3.Lerp(base.transform.localPosition, vector + this.targetOffset + this.offset, Time.deltaTime * this.bobSmoothing) + Vector3.ClampMagnitude(Random.insideUnitSphere * this.shakeAmount, 0.5f);
			this.bobTimer += Time.deltaTime;
		}
		else
		{
			this.bobTimer = Time.time;
			base.transform.localPosition = Vector3.Lerp(base.transform.localPosition, this.initialPosition + this.targetOffset + this.offset, Time.deltaTime * this.bobSmoothing) + Vector3.ClampMagnitude(Random.insideUnitSphere * this.shakeAmount, 0.5f);
		}
		base.transform.localRotation = Quaternion.Euler(Vector3.ClampMagnitude(Random.insideUnitSphere * this.shakeAmount * 30f, 20.5f)) * Quaternion.Euler(0f, 0f, Mathf.Sin(this.rockAmount + Time.time));
	}

	// Token: 0x06000331 RID: 817 RVA: 0x000204F7 File Offset: 0x0001E6F7
	public void Shake(float amount)
	{
		this.shakeAmount += amount;
	}

	// Token: 0x06000332 RID: 818 RVA: 0x00020507 File Offset: 0x0001E707
	public void Rock(float amount)
	{
		this.rockAmount += amount;
	}

	// Token: 0x06000333 RID: 819 RVA: 0x00020517 File Offset: 0x0001E717
	public float GetShake()
	{
		return this.shakeAmount;
	}

	// Token: 0x06000334 RID: 820 RVA: 0x0002051F File Offset: 0x0001E71F
	public void OffsetInitialPosition(Vector3 offset)
	{
		this.initialPosition += offset;
	}

	// Token: 0x04000462 RID: 1122
	public float bobFrequency = 2f;

	// Token: 0x04000463 RID: 1123
	public float bobHorizontalAmplitude = 0.005f;

	// Token: 0x04000464 RID: 1124
	public float bobVerticalAmplitude = 0.01f;

	// Token: 0x04000465 RID: 1125
	public float bobOffset;

	// Token: 0x04000466 RID: 1126
	public float bobSmoothing = 5f;

	// Token: 0x04000467 RID: 1127
	private float shakeAmount;

	// Token: 0x04000468 RID: 1128
	private float rockAmount;

	// Token: 0x04000469 RID: 1129
	private Vector3 initialPosition;

	// Token: 0x0400046A RID: 1130
	[HideInInspector]
	public Vector3 targetOffset = Vector3.down * 0.4f;

	// Token: 0x0400046B RID: 1131
	public Vector3 offset;

	// Token: 0x0400046C RID: 1132
	private float bobTimer;

	// Token: 0x0400046D RID: 1133
	private ENT_Player.Hand hand;
}
