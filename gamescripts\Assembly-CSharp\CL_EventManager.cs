﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000019 RID: 25
public class CL_EventManager : MonoBehaviour
{
	// Token: 0x060000B3 RID: 179 RVA: 0x00006D3F File Offset: 0x00004F3F
	private void Start()
	{
		CL_EventManager.currentEvents = new List<SessionEvent>();
		CL_EventManager.currentEventDict = new Dictionary<string, SessionEvent>();
		CL_EventManager.previousEventDict = new Dictionary<string, SessionEvent>();
		CL_EventManager.currentRegion = null;
		CL_EventManager.currentLevel = null;
		CL_EventManager.currentSubregion = null;
	}

	// Token: 0x060000B4 RID: 180 RVA: 0x00006D74 File Offset: 0x00004F74
	private void Update()
	{
		this.checkTime -= Time.deltaTime;
		if (this.checkTime <= 0f)
		{
			this.checkTime = 1f;
			CL_EventManager.CheckEventList(CL_EventManager.GetPossibleEvents(false, false, false, true));
		}
		for (int i = 0; i < CL_EventManager.currentEvents.Count; i++)
		{
			CL_EventManager.currentEvents[i].UpdateEvent();
		}
	}

	// Token: 0x060000B5 RID: 181 RVA: 0x00006DE0 File Offset: 0x00004FE0
	private void LateUpdate()
	{
		for (int i = 0; i < CL_EventManager.currentEvents.Count; i++)
		{
			CL_EventManager.currentEvents[i].LateUpdateEvent();
		}
	}

	// Token: 0x060000B6 RID: 182 RVA: 0x00006E14 File Offset: 0x00005014
	public static void EnterLevel(M_Level level)
	{
		CL_EventManager.currentLevel = level;
		bool flag = CL_EventManager.currentRegion != level.region;
		bool flag2 = CL_EventManager.currentSubregion != level.subRegion;
		CL_EventManager.currentRegion = level.region;
		CL_EventManager.currentSubregion = level.subRegion;
		CL_EventManager.CheckEventList(CL_EventManager.GetPossibleEvents(true, flag, flag2, false));
	}

	// Token: 0x060000B7 RID: 183 RVA: 0x00006E70 File Offset: 0x00005070
	public static void RemoveEvent(string id)
	{
		if (CL_EventManager.currentEventDict.ContainsKey(id))
		{
			CL_EventManager.currentEventDict[id].EndEvent();
			CL_EventManager.currentEvents.Remove(CL_EventManager.currentEventDict[id]);
			CL_EventManager.currentEventDict.Remove(id);
		}
	}

	// Token: 0x060000B8 RID: 184 RVA: 0x00006EBC File Offset: 0x000050BC
	public static List<SessionEvent> CheckEventList(List<SessionEvent> eventList)
	{
		if (eventList == null)
		{
			return null;
		}
		List<SessionEvent> list = new List<SessionEvent>();
		foreach (SessionEvent sessionEvent in eventList)
		{
			if (sessionEvent.spawnSettings.RandomCheck() && !CL_EventManager.currentEventDict.ContainsKey(sessionEvent.id) && (!sessionEvent.playOnce || !CL_EventManager.previousEventDict.ContainsKey(sessionEvent.id)))
			{
				list.Add(sessionEvent);
			}
		}
		foreach (SessionEvent sessionEvent2 in list)
		{
			if (!CL_EventManager.currentEventDict.ContainsKey(sessionEvent2.id))
			{
				SessionEvent sessionEvent3 = Object.Instantiate<SessionEvent>(sessionEvent2);
				CL_EventManager.currentEvents.Add(sessionEvent3);
				CL_EventManager.currentEventDict.Add(sessionEvent2.id, sessionEvent3);
				sessionEvent3.StartEvent(CL_EventManager.currentLevel, CL_EventManager.currentSubregion, CL_EventManager.currentRegion);
				if (!CL_EventManager.previousEventDict.ContainsKey(sessionEvent2.id))
				{
					CL_EventManager.previousEventDict.Add(sessionEvent2.id, sessionEvent3);
				}
			}
		}
		return list;
	}

	// Token: 0x060000B9 RID: 185 RVA: 0x00006FFC File Offset: 0x000051FC
	public static List<SessionEvent> GetPossibleEvents(bool enteredLevel = false, bool enteredRegion = false, bool enteredSubregion = false, bool update = false)
	{
		if (CL_EventManager.currentLevel != null && !CL_EventManager.currentLevel.allowEvents)
		{
			return null;
		}
		List<SessionEvent> list = new List<SessionEvent>();
		if (CL_EventManager.currentRegion != null)
		{
			foreach (SessionEventList sessionEventList in CL_EventManager.currentRegion.sessionEventLists)
			{
				foreach (SessionEvent sessionEvent in sessionEventList.events)
				{
					list.Add(sessionEvent);
				}
			}
		}
		if (CL_EventManager.currentSubregion != null)
		{
			foreach (SessionEventList sessionEventList2 in CL_EventManager.currentSubregion.sessionEventLists)
			{
				foreach (SessionEvent sessionEvent2 in sessionEventList2.events)
				{
					list.Add(sessionEvent2);
				}
			}
		}
		if (CL_EventManager.currentSubregion != null)
		{
			foreach (SessionEventList sessionEventList3 in CL_EventManager.currentSubregion.sessionEventLists)
			{
				foreach (SessionEvent sessionEvent3 in sessionEventList3.events)
				{
					list.Add(sessionEvent3);
				}
			}
		}
		List<SessionEvent> list2 = new List<SessionEvent>();
		foreach (SessionEvent sessionEvent4 in list)
		{
			if (!(sessionEvent4 == null) && (sessionEvent4.startCheck != SessionEvent.EventStart.startOfRegion || enteredRegion) && (sessionEvent4.startCheck != SessionEvent.EventStart.startOfSubregion || enteredSubregion) && (sessionEvent4.startCheck != SessionEvent.EventStart.startOfLevel || enteredLevel) && (sessionEvent4.startCheck != SessionEvent.EventStart.checkEverySecond || update))
			{
				list2.Add(sessionEvent4);
			}
		}
		return list2;
	}

	// Token: 0x060000BA RID: 186 RVA: 0x0000725C File Offset: 0x0000545C
	public static void StopAllEvents()
	{
		CL_EventManager.currentEvents.Clear();
		CL_EventManager.currentEventDict.Clear();
		CL_EventManager.previousEventDict.Clear();
	}

	// Token: 0x060000BB RID: 187 RVA: 0x0000727C File Offset: 0x0000547C
	public static void StartEvent(SessionEvent sessionEvent)
	{
		SessionEvent sessionEvent2 = Object.Instantiate<SessionEvent>(sessionEvent);
		CL_EventManager.currentEvents.Add(sessionEvent2);
		CL_EventManager.currentEventDict.Add(sessionEvent.id, sessionEvent2);
		sessionEvent2.StartEvent(CL_EventManager.currentLevel, CL_EventManager.currentSubregion, CL_EventManager.currentRegion);
		if (!CL_EventManager.previousEventDict.ContainsKey(sessionEvent.id))
		{
			CL_EventManager.previousEventDict.Add(sessionEvent.id, sessionEvent2);
		}
	}

	// Token: 0x060000BC RID: 188 RVA: 0x000072E4 File Offset: 0x000054E4
	public void StartEventCommand(string[] args)
	{
		if (args.Length != 0)
		{
			foreach (SessionEvent sessionEvent in CL_AssetManager.GetFullCombinedAssetDatabase().sessionEvents)
			{
				if (sessionEvent.name.ToLower().Contains(args[0].ToLower()))
				{
					SessionEvent sessionEvent2 = sessionEvent;
					CL_EventManager.StartEvent(sessionEvent2);
					CommandConsole.Log("Starting Event: " + sessionEvent2.name, false);
					return;
				}
			}
			CommandConsole.LogError("Event Not Found.");
			return;
		}
		CommandConsole.LogError("Error: Missing Argument. Provide Event Name.");
	}

	// Token: 0x060000BD RID: 189 RVA: 0x0000738C File Offset: 0x0000558C
	public void ListSessionEvents(string[] args)
	{
		foreach (SessionEvent sessionEvent in CL_AssetManager.GetFullCombinedAssetDatabase().sessionEvents)
		{
			if (sessionEvent.showInList)
			{
				CommandConsole.Log(string.Concat(new string[]
				{
					sessionEvent.name,
					"\n-- Spawn Type: ",
					sessionEvent.startCheck.ToString(),
					"\n-- Spawn Chance: ",
					sessionEvent.spawnSettings.GetEffectiveSpawnChance().ToString()
				}), false);
			}
		}
	}

	// Token: 0x0400009C RID: 156
	private static List<SessionEvent> currentEvents = new List<SessionEvent>();

	// Token: 0x0400009D RID: 157
	private static Dictionary<string, SessionEvent> currentEventDict = new Dictionary<string, SessionEvent>();

	// Token: 0x0400009E RID: 158
	private static Dictionary<string, SessionEvent> previousEventDict = new Dictionary<string, SessionEvent>();

	// Token: 0x0400009F RID: 159
	public static M_Level currentLevel;

	// Token: 0x040000A0 RID: 160
	public static M_Region currentRegion;

	// Token: 0x040000A1 RID: 161
	public static M_Subregion currentSubregion;

	// Token: 0x040000A2 RID: 162
	private float checkTime;
}
