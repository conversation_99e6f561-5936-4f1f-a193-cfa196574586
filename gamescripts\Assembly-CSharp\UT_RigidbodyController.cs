﻿using System;
using UnityEngine;

// Token: 0x020000DB RID: 219
public class UT_RigidbodyController : MonoBehaviour
{
	// Token: 0x060006DE RID: 1758 RVA: 0x0003628B File Offset: 0x0003448B
	private void Start()
	{
		this.rigid = base.GetComponent<Rigidbody>();
	}

	// Token: 0x060006DF RID: 1759 RVA: 0x00036299 File Offset: 0x00034499
	public void AddForce(Vector3 v)
	{
	}

	// Token: 0x060006E0 RID: 1760 RVA: 0x0003629B File Offset: 0x0003449B
	public void SpinX(float v)
	{
		this.rigid.AddTorque(new Vector3(v, 0f, 0f));
	}

	// Token: 0x060006E1 RID: 1761 RVA: 0x000362B8 File Offset: 0x000344B8
	public void SpinY(float v)
	{
		this.rigid.AddTorque(new Vector3(0f, v, 0f));
	}

	// Token: 0x060006E2 RID: 1762 RVA: 0x000362D5 File Offset: 0x000344D5
	public void SpinZ(float v)
	{
		this.rigid.AddTorque(new Vector3(0f, 0f, v));
	}

	// Token: 0x04000863 RID: 2147
	private Rigidbody rigid;
}
