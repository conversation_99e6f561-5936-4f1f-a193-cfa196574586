﻿using System;
using UnityEngine;

// Token: 0x020001A0 RID: 416
public class UT_UIModifier : MonoBehaviour
{
	// Token: 0x06000B14 RID: 2836 RVA: 0x00047E79 File Offset: 0x00046079
	private void Start()
	{
	}

	// Token: 0x06000B15 RID: 2837 RVA: 0x00047E7B File Offset: 0x0004607B
	private void Update()
	{
	}

	// Token: 0x06000B16 RID: 2838 RVA: 0x00047E7D File Offset: 0x0004607D
	public void SetStatusBarProgress(float f)
	{
		FX_ProgressBar.SetBarValue(f);
	}
}
