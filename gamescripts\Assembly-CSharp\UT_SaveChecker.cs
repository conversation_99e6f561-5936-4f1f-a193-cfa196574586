﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200018A RID: 394
public class UT_SaveChecker : MonoBehaviour
{
	// Token: 0x06000AC2 RID: 2754 RVA: 0x0004692C File Offset: 0x00044B2C
	private void Update()
	{
		if (this.checkTime <= 0f)
		{
			this.checkTime = 1f;
			this.Check();
		}
		this.checkTime -= Time.deltaTime;
	}

	// Token: 0x06000AC3 RID: 2755 RVA: 0x0004695E File Offset: 0x00044B5E
	public void Check()
	{
		if (CL_SaveManager.GetSaveStateByID(this.saveId, this.saveType) != null)
		{
			this.trueEvent.Invoke();
			return;
		}
		this.falseEvent.Invoke();
	}

	// Token: 0x04000BC3 RID: 3011
	public CL_SaveManager.SaveState.SaveType saveType;

	// Token: 0x04000BC4 RID: 3012
	public string saveId;

	// Token: 0x04000BC5 RID: 3013
	public UnityEvent trueEvent;

	// Token: 0x04000BC6 RID: 3014
	public UnityEvent falseEvent;

	// Token: 0x04000BC7 RID: 3015
	public bool CheckEverySecond;

	// Token: 0x04000BC8 RID: 3016
	private float checkTime;
}
