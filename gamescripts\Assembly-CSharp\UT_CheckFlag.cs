﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000160 RID: 352
public class UT_CheckFlag : MonoBehaviour
{
	// Token: 0x060009F5 RID: 2549 RVA: 0x0004324B File Offset: 0x0004144B
	private void Start()
	{
		this.checkFrame = Random.Range(0, 20);
		if (this.checkOnStart)
		{
			this.CheckFlag(false);
		}
	}

	// Token: 0x060009F6 RID: 2550 RVA: 0x0004326A File Offset: 0x0004146A
	private void OnEnable()
	{
		if (!this.active)
		{
			return;
		}
		if (this.checkOnEnable)
		{
			this.CheckFlag(false);
		}
	}

	// Token: 0x060009F7 RID: 2551 RVA: 0x00043284 File Offset: 0x00041484
	private void Update()
	{
		if (!this.active)
		{
			return;
		}
		if (this.checkOnTick)
		{
			this.checkFrame--;
			if (this.checkFrame <= 0)
			{
				this.CheckFlag(false);
				this.checkFrame = 10;
			}
		}
	}

	// Token: 0x060009F8 RID: 2552 RVA: 0x000432C0 File Offset: 0x000414C0
	public void CheckFlag(bool forced = false)
	{
		if (!this.active && !forced)
		{
			return;
		}
		if (this.runOnce && this.hasRun)
		{
			return;
		}
		this.hasRun = true;
		CL_GameManager.SessionFlag gameFlag = CL_GameManager.GetGameFlag(this.flagName);
		if (gameFlag == null)
		{
			if (this.outputIfFlagIsNull)
			{
				this.flagFalseEvent.Invoke();
			}
			return;
		}
		if (gameFlag.state)
		{
			this.flagTrueEvent.Invoke();
			return;
		}
		this.flagFalseEvent.Invoke();
	}

	// Token: 0x060009F9 RID: 2553 RVA: 0x00043334 File Offset: 0x00041534
	public void SetFlag(string dat)
	{
		string[] array = dat.Split(":", StringSplitOptions.None);
		CL_GameManager.SetGameFlag(array[0], bool.Parse(array[1]), "", false);
	}

	// Token: 0x060009FA RID: 2554 RVA: 0x00043364 File Offset: 0x00041564
	public void SetSaveFlag(string dat)
	{
		string[] array = dat.Split(":", StringSplitOptions.None);
		CL_GameManager.SetGameFlag(array[0], bool.Parse(array[1]), "", true);
	}

	// Token: 0x060009FB RID: 2555 RVA: 0x00043394 File Offset: 0x00041594
	public void CheckPlayerInventoryForTag(string tag)
	{
		if (ENT_Player.playerObject == null)
		{
			this.flagFalseEvent.Invoke();
		}
		if (Inventory.instance.GetItemByTag(tag) != null)
		{
			this.flagTrueEvent.Invoke();
			return;
		}
		this.flagFalseEvent.Invoke();
	}

	// Token: 0x04000B0F RID: 2831
	public string flagName;

	// Token: 0x04000B10 RID: 2832
	public bool active = true;

	// Token: 0x04000B11 RID: 2833
	public bool checkOnEnable;

	// Token: 0x04000B12 RID: 2834
	public bool checkOnStart;

	// Token: 0x04000B13 RID: 2835
	public bool checkOnTick;

	// Token: 0x04000B14 RID: 2836
	public bool outputIfFlagIsNull;

	// Token: 0x04000B15 RID: 2837
	public bool runOnce;

	// Token: 0x04000B16 RID: 2838
	private bool hasRun;

	// Token: 0x04000B17 RID: 2839
	private int checkFrame;

	// Token: 0x04000B18 RID: 2840
	public UnityEvent flagFalseEvent;

	// Token: 0x04000B19 RID: 2841
	public UnityEvent flagTrueEvent;
}
