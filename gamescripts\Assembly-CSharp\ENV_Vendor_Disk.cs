﻿using System;
using System.Collections;
using TMPro;
using UnityEngine;

// Token: 0x02000054 RID: 84
public class ENV_Vendor_Disk : MonoBehaviour
{
	// Token: 0x06000372 RID: 882 RVA: 0x00021D9A File Offset: 0x0001FF9A
	private void Start()
	{
		this.CheckRoaches();
	}

	// Token: 0x06000373 RID: 883 RVA: 0x00021DA2 File Offset: 0x0001FFA2
	private void Update()
	{
		if (CL_GameManager.roaches != this.lastRoaches)
		{
			this.CheckRoaches();
		}
	}

	// Token: 0x06000374 RID: 884 RVA: 0x00021DB8 File Offset: 0x0001FFB8
	public void CheckRoaches()
	{
		if (!this.allowPurchases)
		{
			return;
		}
		if (this.id != "")
		{
			CL_GameManager.SessionFlag gameFlag = CL_GameManager.GetGameFlag("boughtdisk-" + this.id + "-station");
			if (!this.hasBeenBought && gameFlag != null && gameFlag.state)
			{
				this.hasBeenBought = true;
				this.costText.text = "THANK YOU FOR YOUR PURCHASE";
				this.purchaseSprite.gameObject.SetActive(false);
			}
		}
		if (this.hasBeenBought)
		{
			this.purchaseButton.SetInteractable(false);
			return;
		}
		this.lastRoaches = CL_GameManager.roaches;
		this.costText.text = string.Format("Cost:{0} Bank:{1}", this.cost, CL_GameManager.roaches.ToString());
		if (this.cost > CL_GameManager.roaches)
		{
			this.purchaseButton.SetInteractable(false);
			return;
		}
		this.purchaseButton.SetInteractable(true);
	}

	// Token: 0x06000375 RID: 885 RVA: 0x00021EA8 File Offset: 0x000200A8
	public void Purchase()
	{
		this.purchaseButton.SetInteractable(false);
		if (this.hasBeenBought)
		{
			return;
		}
		if (this.cost <= CL_GameManager.roaches)
		{
			CL_GameManager.AddRoaches(-this.cost);
			this.hasBeenBought = true;
			this.costText.text = "THANK YOU FOR YOUR PURCHASE";
			this.purchaseSprite.gameObject.SetActive(false);
			if (!this.allowMultiplePurchase && this.id != "")
			{
				CL_GameManager.SetGameFlag("boughtdisk-" + this.id + "-station", true, "", false);
			}
			this.SpawnDisk();
		}
	}

	// Token: 0x06000376 RID: 886 RVA: 0x00021F4C File Offset: 0x0002014C
	public void SpawnDisk()
	{
		base.StartCoroutine(this.PurchaseSequence());
	}

	// Token: 0x06000377 RID: 887 RVA: 0x00021F5B File Offset: 0x0002015B
	private IEnumerator PurchaseSequence()
	{
		float ejectTime = 0f;
		Item_Object item = Object.Instantiate<Item_Object>(this.purchaseObject, this.diskSpawn.position, this.diskSpawn.rotation, base.transform.parent);
		item.GetComponent<Rigidbody>().isKinematic = true;
		AudioManager.PlaySound(this.floppyEject, base.transform.position, 1f, 1f, 1f, false, 1f, null);
		while (ejectTime < 1f)
		{
			ejectTime += Time.deltaTime;
			if (!item.gameObject.activeInHierarchy)
			{
				break;
			}
			item.transform.position = Vector3.Lerp(this.diskSpawn.position, this.diskSpawn.position + this.diskSpawn.forward * 0.5f, ejectTime);
			yield return null;
		}
		yield break;
	}

	// Token: 0x040004B4 RID: 1204
	public Item_Object purchaseObject;

	// Token: 0x040004B5 RID: 1205
	public bool allowPurchases = true;

	// Token: 0x040004B6 RID: 1206
	public int cost;

	// Token: 0x040004B7 RID: 1207
	public string id;

	// Token: 0x040004B8 RID: 1208
	public bool allowMultiplePurchase;

	// Token: 0x040004B9 RID: 1209
	public SpriteRenderer purchaseSprite;

	// Token: 0x040004BA RID: 1210
	public TMP_Text costText;

	// Token: 0x040004BB RID: 1211
	private int lastRoaches;

	// Token: 0x040004BC RID: 1212
	public CL_Button purchaseButton;

	// Token: 0x040004BD RID: 1213
	private bool hasBeenBought;

	// Token: 0x040004BE RID: 1214
	public Transform diskSpawn;

	// Token: 0x040004BF RID: 1215
	public AudioClip floppyEject;
}
