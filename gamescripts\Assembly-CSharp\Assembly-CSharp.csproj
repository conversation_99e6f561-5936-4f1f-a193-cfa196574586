﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{FDBC4CF3-1808-4B69-8D8A-647E4C18B085}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>Assembly-CSharp</RootNamespace>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ALINE">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\ALINE.dll</HintPath>
    </Reference>
    <Reference Include="Assembly-CSharp-firstpass">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Assembly-CSharp-firstpass.dll</HintPath>
    </Reference>
    <Reference Include="AstarPathfindingProject">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\AstarPathfindingProject.dll</HintPath>
    </Reference>
    <Reference Include="DarkMachineUI">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\DarkMachineUI.dll</HintPath>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\DOTween.dll</HintPath>
    </Reference>
    <Reference Include="Facepunch.Steamworks.Win64">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Facepunch.Steamworks.Win64.dll</HintPath>
    </Reference>
    <Reference Include="Febucci.TextAnimator.Runtime">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Febucci.TextAnimator.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Utilities">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Sirenix.Utilities.dll</HintPath>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Unity.InputSystem.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Unity.Mathematics.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Postprocessing.Runtime">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Unity.Postprocessing.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Unity.VisualScripting.Core.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Unity.VisualScripting.Flow.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.UI.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="WFowler1.BspImporter.Runtime">
      <HintPath>..\..\..\..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\WFowler1.BspImporter.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AchievementCollection.cs" />
    <Compile Include="Activatable.cs" />
    <Compile Include="AIC_Wander.cs" />
    <Compile Include="AIGameEntity.cs" />
    <Compile Include="AI_ControlNode.cs" />
    <Compile Include="AI_NodeGraphCreator.cs" />
    <Compile Include="AnnouncementController.cs" />
    <Compile Include="AnnouncementGroup.cs" />
    <Compile Include="AnnouncementSource.cs" />
    <Compile Include="App_DocumentReader.cs" />
    <Compile Include="App_PerkPage.cs" />
    <Compile Include="App_PerkPage_Card.cs" />
    <Compile Include="App_PictureViewer.cs" />
    <Compile Include="App_SavePage.cs" />
    <Compile Include="App_SavePage_DiskCard.cs" />
    <Compile Include="App_SavePage_Scan.cs" />
    <Compile Include="App_TrainingSectorControls.cs" />
    <Compile Include="App_TrainingSectorControls_Toggle.cs" />
    <Compile Include="AsciiGrid.cs" />
    <Compile Include="AudioManager.cs" />
    <Compile Include="BaseCRTEffect.cs" />
    <Compile Include="Blinkable.cs" />
    <Compile Include="BuddyModule.cs" />
    <Compile Include="BuddyModule_RadioEvent.cs" />
    <Compile Include="BuddyModule_SpeakToPlayer.cs" />
    <Compile Include="BuffContainer.cs" />
    <Compile Include="BugReporter.cs" />
    <Compile Include="ChallengeMode.cs" />
    <Compile Include="Changeable_Text.cs" />
    <Compile Include="CH_ChallengeCounter.cs" />
    <Compile Include="CH_RoachCollector.cs" />
    <Compile Include="Clickable.cs" />
    <Compile Include="CloseOnMouseExit.cs" />
    <Compile Include="CL_AchievementManager.cs" />
    <Compile Include="CL_AssetManager.cs" />
    <Compile Include="CL_Button.cs" />
    <Compile Include="CL_CameraControl.cs" />
    <Compile Include="CL_DebugView.cs" />
    <Compile Include="CL_EventManager.cs" />
    <Compile Include="CL_GameManager.cs" />
    <Compile Include="CL_GameTracker.cs" />
    <Compile Include="CL_Handhold.cs" />
    <Compile Include="CL_Handhold_Breakable.cs" />
    <Compile Include="CL_Handhold_Damage.cs" />
    <Compile Include="CL_Handhold_Icy.cs" />
    <Compile Include="CL_Handhold_Rope.cs" />
    <Compile Include="CL_HoldButton.cs" />
    <Compile Include="CL_Initializer.cs" />
    <Compile Include="CL_Lamp.cs" />
    <Compile Include="CL_LocalizationManager.cs" />
    <Compile Include="CL_ProgressionManager.cs" />
    <Compile Include="CL_Prop.cs" />
    <Compile Include="CL_SaveManager.cs" />
    <Compile Include="CL_ToggleButton.cs" />
    <Compile Include="CL_UIManager.cs" />
    <Compile Include="CommandConsole.cs" />
    <Compile Include="ContextMenu.cs" />
    <Compile Include="ContextMenu_Manager.cs" />
    <Compile Include="ConvexHull3D.cs" />
    <Compile Include="Crosshair.cs" />
    <Compile Include="CRTEffect.cs" />
    <Compile Include="CycleManager.cs" />
    <Compile Include="Damageable.cs" />
    <Compile Include="DarkMachineFunctions.cs" />
    <Compile Include="DarkMachine\AI\AIC_Teeth_Burrow.cs" />
    <Compile Include="DarkMachine\AI\AIC_Teeth_Chase.cs" />
    <Compile Include="DarkMachine\AI\AIC_Teeth_Retreat.cs" />
    <Compile Include="DarkMachine\AI\AIC_Teeth_Wait.cs" />
    <Compile Include="DarkMachine\AI\AIC_Teeth_Wander.cs" />
    <Compile Include="DarkMachine\AI\AISightComponent.cs" />
    <Compile Include="DarkMachine\AI\AIStateComponent.cs" />
    <Compile Include="DarkMachine\AI\AIStateVariable.cs" />
    <Compile Include="DarkMachine\AI\AI_Burrow.cs" />
    <Compile Include="DarkMachine\AI\AI_BurrowManager.cs" />
    <Compile Include="DarkMachine\AI\AI_NavigationManager.cs" />
    <Compile Include="DarkMachine\Collections\PriorityQueue.cs" />
    <Compile Include="DataClass.cs" />
    <Compile Include="DebugCameraController.cs" />
    <Compile Include="DebugMenu.cs" />
    <Compile Include="Denizen.cs" />
    <Compile Include="DEN_Apparition.cs" />
    <Compile Include="DEN_Barnacle.cs" />
    <Compile Include="DEN_Bloodbug.cs" />
    <Compile Include="DEN_Buddy.cs" />
    <Compile Include="DEN_Critter.cs" />
    <Compile Include="DEN_DeathFloor.cs" />
    <Compile Include="DEN_Drone.cs" />
    <Compile Include="DEN_EngravedDoor.cs" />
    <Compile Include="DEN_Face.cs" />
    <Compile Include="DEN_Gasbag.cs" />
    <Compile Include="DEN_GiantRoach.cs" />
    <Compile Include="DEN_LadderNightmare.cs" />
    <Compile Include="DEN_Roach.cs" />
    <Compile Include="DEN_SimpleChaseKiller.cs" />
    <Compile Include="DEN_SlugGrub.cs" />
    <Compile Include="DEN_Strider.cs" />
    <Compile Include="DEN_Teeth.cs" />
    <Compile Include="DEN_Teeth_Animator.cs" />
    <Compile Include="DEN_Turret.cs" />
    <Compile Include="DEN_VentThing.cs" />
    <Compile Include="DitzelGames\FastIK\FastIKFabric.cs" />
    <Compile Include="DitzelGames\FastIK\FastIKLook.cs" />
    <Compile Include="DropdownAttribute.cs" />
    <Compile Include="DropdownBinder.cs" />
    <Compile Include="EditorCools\ButtonAttribute.cs" />
    <Compile Include="ENT_Hitbox.cs" />
    <Compile Include="ENT_Player.cs" />
    <Compile Include="ENV_KillBubble.cs" />
    <Compile Include="ENV_LightningDischarge.cs" />
    <Compile Include="ENV_MassDampener.cs" />
    <Compile Include="ENV_Radio.cs" />
    <Compile Include="ENV_RadioStation.cs" />
    <Compile Include="ENV_Rope.cs" />
    <Compile Include="ENV_Rope_Node.cs" />
    <Compile Include="ENV_VendingMachine.cs" />
    <Compile Include="ENV_VendingMachine_Slot.cs" />
    <Compile Include="ENV_Vendor_Disk.cs" />
    <Compile Include="ENV_WarpFissure.cs" />
    <Compile Include="ETAS_Manager.cs" />
    <Compile Include="Event_Cold.cs" />
    <Compile Include="Event_Cold_Controller.cs" />
    <Compile Include="Event_HerDomain.cs" />
    <Compile Include="EX_TextHandler.cs" />
    <Compile Include="Flippable.cs" />
    <Compile Include="Footsteps.cs" />
    <Compile Include="FXManager.cs" />
    <Compile Include="FX_CameraShaderController.cs" />
    <Compile Include="FX_Cloud.cs" />
    <Compile Include="FX_FadeFlareHit.cs" />
    <Compile Include="FX_PlayerDust.cs" />
    <Compile Include="FX_ProgressBar.cs" />
    <Compile Include="FX_Quality.cs" />
    <Compile Include="FX_QualityMaterialSwapper.cs" />
    <Compile Include="FX_RunningLightFlare.cs" />
    <Compile Include="FX_ShaderVariableController.cs" />
    <Compile Include="FX_Zone.cs" />
    <Compile Include="GameEntity.cs" />
    <Compile Include="GamemodeHolder.cs" />
    <Compile Include="GamemodeModule.cs" />
    <Compile Include="GamemodeModule_Challenge.cs" />
    <Compile Include="GamemodeModule_Standard.cs" />
    <Compile Include="GamemodeModule_Training.cs" />
    <Compile Include="HandholdManager.cs" />
    <Compile Include="HandItem.cs" />
    <Compile Include="HandItem_BlinkEye.cs" />
    <Compile Include="HandItem_Buff.cs" />
    <Compile Include="HandItem_Flashlight.cs" />
    <Compile Include="HandItem_Food.cs" />
    <Compile Include="HandItem_Hammer.cs" />
    <Compile Include="HandItem_Note.cs" />
    <Compile Include="HandItem_Piton.cs" />
    <Compile Include="HandItem_Shoot.cs" />
    <Compile Include="Hand_SpriteController.cs" />
    <Compile Include="IK_Controller.cs" />
    <Compile Include="InputManager.cs" />
    <Compile Include="InteractHit.cs" />
    <Compile Include="Inventory.cs" />
    <Compile Include="Item.cs" />
    <Compile Include="Item_Data.cs" />
    <Compile Include="Item_Data_HandSprite.cs" />
    <Compile Include="Item_Data_Sprite.cs" />
    <Compile Include="Item_Data_Text.cs" />
    <Compile Include="Item_Data_Text_Random.cs" />
    <Compile Include="Item_Data_Text_Single.cs" />
    <Compile Include="Item_Interactor.cs" />
    <Compile Include="Item_Interactor_Floppy.cs" />
    <Compile Include="Item_Interactor_Insertable.cs" />
    <Compile Include="Item_Object.cs" />
    <Compile Include="Item_Object_Flashlight.cs" />
    <Compile Include="Leaderboard_Panel.cs" />
    <Compile Include="Leaderboard_Score.cs" />
    <Compile Include="LevelStat.cs" />
    <Compile Include="LocatorManager.cs" />
    <Compile Include="MarchingCubes.cs" />
    <Compile Include="MenuManager.cs" />
    <Compile Include="Message.cs" />
    <Compile Include="Message_Loading.cs" />
    <Compile Include="Message_Manager.cs" />
    <Compile Include="MinMaxSliderAttribute.cs" />
    <Compile Include="M_Gamemode.cs" />
    <Compile Include="M_GenerationBranch.cs" />
    <Compile Include="M_Level.cs" />
    <Compile Include="M_Region.cs" />
    <Compile Include="M_Subregion.cs" />
    <Compile Include="ObjectMaterial.cs" />
    <Compile Include="ObjectTagger.cs" />
    <Compile Include="OSPointerClickHandler.cs" />
    <Compile Include="OS_ButtonAudio.cs" />
    <Compile Include="OS_Computer_Interface.cs" />
    <Compile Include="OS_DiskData.cs" />
    <Compile Include="OS_Dropdown.cs" />
    <Compile Include="OS_EventSystem.cs" />
    <Compile Include="OS_File.cs" />
    <Compile Include="OS_Filesystem.cs" />
    <Compile Include="OS_FloppyReader.cs" />
    <Compile Include="OS_Folder.cs" />
    <Compile Include="OS_Manager.cs" />
    <Compile Include="OS_Pointer.cs" />
    <Compile Include="OS_PointerEvent.cs" />
    <Compile Include="OS_ResourceManager.cs" />
    <Compile Include="OS_SaveApp_Starfield.cs" />
    <Compile Include="OS_SoundEffects.cs" />
    <Compile Include="OS_SoundPlayer.cs" />
    <Compile Include="OS_Window.cs" />
    <Compile Include="PacketEncoder.cs" />
    <Compile Include="Perk.cs" />
    <Compile Include="PerkModule.cs" />
    <Compile Include="PerkModule_ItemAdder.cs" />
    <Compile Include="PerkModule_ItemRemover.cs" />
    <Compile Include="PerkModule_MassController.cs" />
    <Compile Include="PerkModule_ObjectSpawner.cs" />
    <Compile Include="PerkModule_RemovalTimer.cs" />
    <Compile Include="PostProcessGamma.cs" />
    <Compile Include="PostProcessGammaRenderer.cs" />
    <Compile Include="PostProcessOutline.cs" />
    <Compile Include="PostProcessOutlineRenderer.cs" />
    <Compile Include="PostProcessSharpen.cs" />
    <Compile Include="PostProcessSharpenRenderer.cs" />
    <Compile Include="ProgressionUnlock.cs" />
    <Compile Include="Projectile.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Pusheable.cs" />
    <Compile Include="RandomMeshPointSampler.cs" />
    <Compile Include="RaycastUtilities.cs" />
    <Compile Include="RebindSaveLoad.cs" />
    <Compile Include="RopeSegment.cs" />
    <Compile Include="RopeSimulation.cs" />
    <Compile Include="RunManager.cs" />
    <Compile Include="Sample_DataManager.cs" />
    <Compile Include="SaveableInfo.cs" />
    <Compile Include="SaveableObject.cs" />
    <Compile Include="Scanlines.cs" />
    <Compile Include="Screenshot.cs" />
    <Compile Include="Seedable.cs" />
    <Compile Include="SessionEvent.cs" />
    <Compile Include="SessionEventList.cs" />
    <Compile Include="SessionEventModule.cs" />
    <Compile Include="SessionEventModule_AddModuleAfterTrigger.cs" />
    <Compile Include="SessionEventModule_AnnouncementPlayer.cs" />
    <Compile Include="SessionEventModule_AnnouncementSequence.cs" />
    <Compile Include="SessionEventModule_AudioPlayer.cs" />
    <Compile Include="SessionEventModule_BasicControls.cs" />
    <Compile Include="SessionEventModule_CameraDirectionCheck.cs" />
    <Compile Include="SessionEventModule_Cheatrooms_Jack.cs" />
    <Compile Include="SessionEventModule_FlashVignette.cs" />
    <Compile Include="SessionEventModule_FXZone.cs" />
    <Compile Include="SessionEventModule_LocatorSpawner.cs" />
    <Compile Include="SessionEventModule_ModuleSequencer.cs" />
    <Compile Include="SessionEventModule_PrintToConsole.cs" />
    <Compile Include="SessionEventModule_SendMessageToModules.cs" />
    <Compile Include="SessionEventModule_SetGameFlag.cs" />
    <Compile Include="SessionEventModule_StartEvent.cs" />
    <Compile Include="SessionEventModule_StopEventAfterExit.cs" />
    <Compile Include="SessionEventModule_StopEventAfterTime.cs" />
    <Compile Include="SessionEventModule_StopThisEvent.cs" />
    <Compile Include="SessionEventModule_TextEvent.cs" />
    <Compile Include="SessionEventModule_Weather.cs" />
    <Compile Include="SettingsManager.cs" />
    <Compile Include="Settings_Resolution.cs" />
    <Compile Include="SliderSettingBinder.cs" />
    <Compile Include="SoftParent.cs" />
    <Compile Include="SpawnTable.cs" />
    <Compile Include="SpeedrunManager.cs" />
    <Compile Include="StandaloneCRTEffect.cs" />
    <Compile Include="StatManager.cs" />
    <Compile Include="SteamManager.cs" />
    <Compile Include="StringVariableExtractor.cs" />
    <Compile Include="System\ArrayExtensions\ArrayExtensions.cs" />
    <Compile Include="System\ArrayExtensions\ArrayTraverse.cs" />
    <Compile Include="System\ObjectExtensions.cs" />
    <Compile Include="System\ReferenceEqualityComparer.cs" />
    <Compile Include="TextSettingsBinder.cs" />
    <Compile Include="TheClamberer.cs" />
    <Compile Include="TipList.cs" />
    <Compile Include="ToggleSettingsBinder.cs" />
    <Compile Include="Tooltip.cs" />
    <Compile Include="Tooltip_Manager.cs" />
    <Compile Include="TransformEvent.cs" />
    <Compile Include="UI_AchievementText.cs" />
    <Compile Include="UI_AnimateOnSelect.cs" />
    <Compile Include="UI_AutoScrollToSelected.cs" />
    <Compile Include="UI_ButtonSettings.cs" />
    <Compile Include="UI_Camera.cs" />
    <Compile Include="UI_CanvasScaler.cs" />
    <Compile Include="UI_CapsuleButton.cs" />
    <Compile Include="UI_CapsuleContainer.cs" />
    <Compile Include="UI_CloseButtonOnBack.cs" />
    <Compile Include="UI_CopyColor.cs" />
    <Compile Include="UI_CopySelectedColor.cs" />
    <Compile Include="UI_CrosshairController.cs" />
    <Compile Include="UI_DisableInteractionOnGamepad.cs" />
    <Compile Include="UI_ElementScaler.cs" />
    <Compile Include="UI_Fade.cs" />
    <Compile Include="UI_FirstSelect.cs" />
    <Compile Include="UI_GamemodeScreen.cs" />
    <Compile Include="UI_GamemodeScreen_Panel.cs" />
    <Compile Include="UI_GamemodeSlideshowController.cs" />
    <Compile Include="UI_GamemodeText.cs" />
    <Compile Include="UI_Gamemode_Button.cs" />
    <Compile Include="UI_HeldItem.cs" />
    <Compile Include="UI_KeepOnTop.cs" />
    <Compile Include="UI_LerpOpen.cs" />
    <Compile Include="UI_MedalViewer.cs" />
    <Compile Include="UI_Menu.cs" />
    <Compile Include="UI_MenuButton.cs" />
    <Compile Include="UI_MenuScreen.cs" />
    <Compile Include="UI_ObjectiveViewer.cs" />
    <Compile Include="UI_OutlineOnSelect.cs" />
    <Compile Include="UI_Page.cs" />
    <Compile Include="UI_PageHolder.cs" />
    <Compile Include="UI_Perk_Icon.cs" />
    <Compile Include="UI_PlayPane.cs" />
    <Compile Include="UI_PressButtonOnInput.cs" />
    <Compile Include="UI_ProgressionLog.cs" />
    <Compile Include="UI_ProgressionPopup.cs" />
    <Compile Include="UI_ProgressionUnlockList.cs" />
    <Compile Include="UI_RebindOverlay.cs" />
    <Compile Include="UI_RoachBankAmount.cs" />
    <Compile Include="UI_SafeToggle.cs" />
    <Compile Include="UI_ScoreScreen.cs" />
    <Compile Include="UI_ScrollController.cs" />
    <Compile Include="UI_SelectEventTrigger.cs" />
    <Compile Include="UI_SessionTracker.cs" />
    <Compile Include="UI_SettingsMenu.cs" />
    <Compile Include="UI_SizeToFit.cs" />
    <Compile Include="UI_SliderController.cs" />
    <Compile Include="UI_TabFade.cs" />
    <Compile Include="UI_TabGroup.cs" />
    <Compile Include="UI_TextHints.cs" />
    <Compile Include="UI_UIScaleToSettings.cs" />
    <Compile Include="UI_VersionText.cs" />
    <Compile Include="UI_Window.cs" />
    <Compile Include="UI_Window_Title.cs" />
    <Compile Include="UnityEngine\EventSystems\CustomInputModule.cs" />
    <Compile Include="UnityEngine\InputSystem\Samples\RebindUI\GamepadIconsExample.cs" />
    <Compile Include="UnityEngine\InputSystem\Samples\RebindUI\RebindActionUI.cs" />
    <Compile Include="UnitySourceGeneratedAssemblyMonoScriptTypes_v1.cs" />
    <Compile Include="UT_AchievementSetter.cs" />
    <Compile Include="UT_ActivatableController.cs" />
    <Compile Include="UT_AddForceToPlayer.cs" />
    <Compile Include="UT_AnimatedTexture.cs" />
    <Compile Include="UT_AnnouncementPlayer.cs" />
    <Compile Include="UT_AudioClipHandler.cs" />
    <Compile Include="UT_AudioEffects.cs" />
    <Compile Include="UT_BranchGenerator.cs" />
    <Compile Include="UT_BuddyController.cs" />
    <Compile Include="UT_BuildTypeToggler.cs" />
    <Compile Include="UT_ButtonAudio.cs" />
    <Compile Include="UT_CameraTakeover.cs" />
    <Compile Include="UT_CheatTrigger.cs" />
    <Compile Include="UT_CheckFlag.cs" />
    <Compile Include="UT_ChildArray.cs" />
    <Compile Include="UT_CompetitiveModeEnable.cs" />
    <Compile Include="UT_Counter.cs" />
    <Compile Include="UT_CustomCommand.cs" />
    <Compile Include="UT_Damage.cs" />
    <Compile Include="UT_DamageTrigger.cs" />
    <Compile Include="UT_DebugController.cs" />
    <Compile Include="UT_DebugViewer_Text.cs" />
    <Compile Include="UT_DestroyAfterTime.cs" />
    <Compile Include="UT_DevModeOnly.cs" />
    <Compile Include="UT_Door.cs" />
    <Compile Include="UT_DrainingCounter.cs" />
    <Compile Include="UT_EventController.cs" />
    <Compile Include="UT_EventOnActive.cs" />
    <Compile Include="UT_EventOnEnableAndDisable.cs" />
    <Compile Include="UT_EventZone.cs" />
    <Compile Include="UT_Explosion.cs" />
    <Compile Include="UT_FaceCamera.cs" />
    <Compile Include="UT_FadeController.cs" />
    <Compile Include="UT_FlagToggle.cs" />
    <Compile Include="UT_FollowBase.cs" />
    <Compile Include="UT_FollowCam.cs" />
    <Compile Include="UT_FollowPlayer.cs" />
    <Compile Include="UT_FollowTarget.cs" />
    <Compile Include="UT_ForceZone.cs" />
    <Compile Include="UT_Framerate.cs" />
    <Compile Include="UT_GamemodeEventController.cs" />
    <Compile Include="UT_GameStateController.cs" />
    <Compile Include="UT_Gate_And.cs" />
    <Compile Include="UT_Gear.cs" />
    <Compile Include="UT_GooController.cs" />
    <Compile Include="UT_GooTrigger.cs" />
    <Compile Include="UT_GravityZone.cs" />
    <Compile Include="UT_HardModeEnable.cs" />
    <Compile Include="UT_Heater.cs" />
    <Compile Include="UT_HeightCheck.cs" />
    <Compile Include="UT_HideOnAwake.cs" />
    <Compile Include="UT_Highlight.cs" />
    <Compile Include="UT_HoverCursor.cs" />
    <Compile Include="UT_InitializeAssetDatabase.cs" />
    <Compile Include="UT_InsertableItem.cs" />
    <Compile Include="UT_InstantDeath.cs" />
    <Compile Include="UT_Intro.cs" />
    <Compile Include="UT_InventoryChecker.cs" />
    <Compile Include="UT_ItemDetector.cs" />
    <Compile Include="UT_LadderNightmare_Controller.cs" />
    <Compile Include="UT_LerpBetween.cs" />
    <Compile Include="UT_LightGroupController.cs" />
    <Compile Include="UT_LocationDataCollector.cs" />
    <Compile Include="UT_Locator.cs" />
    <Compile Include="UT_LookatPlayer.cs" />
    <Compile Include="UT_LookTrigger.cs" />
    <Compile Include="UT_LUTCreator.cs" />
    <Compile Include="UT_MeshDecalProjector.cs" />
    <Compile Include="UT_MeshFaceSpawner.cs" />
    <Compile Include="UT_Move.cs" />
    <Compile Include="UT_MovingGeometry.cs" />
    <Compile Include="UT_Navmesh.cs" />
    <Compile Include="UT_Null.cs" />
    <Compile Include="UT_ObjectiveController.cs" />
    <Compile Include="UT_ObjectSnapper.cs" />
    <Compile Include="UT_ObjectSpawner.cs" />
    <Compile Include="UT_OpenURL.cs" />
    <Compile Include="UT_PathMover.cs" />
    <Compile Include="UT_PerkGiver.cs" />
    <Compile Include="UT_PlayerTriggerRadius.cs" />
    <Compile Include="UT_PlayEvents.cs" />
    <Compile Include="UT_PlaySoundOnProximity.cs" />
    <Compile Include="UT_PlaySoundOnStart.cs" />
    <Compile Include="UT_Portal.cs" />
    <Compile Include="UT_RaceController.cs" />
    <Compile Include="UT_RandomAudio.cs" />
    <Compile Include="UT_RandomTimer.cs" />
    <Compile Include="UT_RandomTransform.cs" />
    <Compile Include="UT_RigidbodyController.cs" />
    <Compile Include="UT_RoachBankReader.cs" />
    <Compile Include="UT_RoachCounter.cs" />
    <Compile Include="UT_RoachTextCounter.cs" />
    <Compile Include="UT_RotateToFaceDirection.cs" />
    <Compile Include="UT_SaveChecker.cs" />
    <Compile Include="UT_SaveDataCounter.cs" />
    <Compile Include="UT_ScaleToFull.cs" />
    <Compile Include="UT_ScrawlEffect.cs" />
    <Compile Include="UT_ScreenShake.cs" />
    <Compile Include="UT_SecuritySystemController.cs" />
    <Compile Include="UT_SeededEnable.cs" />
    <Compile Include="UT_SendPlayerTransform.cs" />
    <Compile Include="UT_SettingEvent.cs" />
    <Compile Include="UT_ShatterProp.cs" />
    <Compile Include="UT_ShimmerHold.cs" />
    <Compile Include="UT_SimpleAnimatorController.cs" />
    <Compile Include="UT_SoftParent.cs" />
    <Compile Include="UT_SoftParentTarget.cs" />
    <Compile Include="UT_SoundtrackController.cs" />
    <Compile Include="UT_SoundtrackMixer.cs" />
    <Compile Include="UT_SpawnChance.cs" />
    <Compile Include="UT_SpawnEntity.cs" />
    <Compile Include="UT_SpawnEntityByID.cs" />
    <Compile Include="UT_SpawnTableSpawner.cs" />
    <Compile Include="UT_SpeedrunLogger.cs" />
    <Compile Include="UT_Spin.cs" />
    <Compile Include="UT_Spotlight.cs" />
    <Compile Include="UT_SpriteAnimator.cs" />
    <Compile Include="UT_SPT_Spawner.cs" />
    <Compile Include="UT_StatChecker.cs" />
    <Compile Include="UT_StatText.cs" />
    <Compile Include="UT_StopCheats.cs" />
    <Compile Include="UT_StretchTo.cs" />
    <Compile Include="UT_Sway.cs" />
    <Compile Include="UT_TeethController.cs" />
    <Compile Include="UT_Teleport.cs" />
    <Compile Include="UT_TextScrawl.cs" />
    <Compile Include="UT_Timer.cs" />
    <Compile Include="UT_Toggle.cs" />
    <Compile Include="UT_TrackObject.cs" />
    <Compile Include="UT_TriggerBase.cs" />
    <Compile Include="UT_TriggerMessageZone.cs" />
    <Compile Include="UT_TriggerSpawner.cs" />
    <Compile Include="UT_TriggerZone.cs" />
    <Compile Include="UT_UIModifier.cs" />
    <Compile Include="UT_Unparent.cs" />
    <Compile Include="UT_Wiggle.cs" />
    <Compile Include="UT_WorldLoaderController.cs" />
    <Compile Include="ValueControl.cs" />
    <Compile Include="VEH_Crane.cs" />
    <Compile Include="ViewSway.cs" />
    <Compile Include="VoiceSettingsBinder.cs" />
    <Compile Include="WKAssetDatabase.cs" />
    <Compile Include="WorldGenerator.cs" />
    <Compile Include="WorldLoader.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>