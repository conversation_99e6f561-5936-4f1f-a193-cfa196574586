﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020000AD RID: 173
[CreateAssetMenu(fileName = "New Sub-Region", menuName = "White Knuckle/Sub-Region")]
public class M_Subregion : ScriptableObject
{
	// Token: 0x0600059F RID: 1439 RVA: 0x0002E5A9 File Offset: 0x0002C7A9
	public void EnterSubregion()
	{
	}

	// Token: 0x060005A0 RID: 1440 RVA: 0x0002E5AB File Offset: 0x0002C7AB
	public void ExitSubregion()
	{
	}

	// Token: 0x060005A1 RID: 1441 RVA: 0x0002E5AD File Offset: 0x0002C7AD
	public void Update()
	{
	}

	// Token: 0x060005A2 RID: 1442 RVA: 0x0002E5B0 File Offset: 0x0002C7B0
	public AnnouncementGroup GetRandomAnnouncementGroup()
	{
		if (this.announcementGroups == null || this.announcementGroups.Count == 0)
		{
			return null;
		}
		float num = 0f;
		foreach (AnnouncementGroup announcementGroup in this.announcementGroups)
		{
			num += (float)announcementGroup.announcements.Count;
		}
		float num2 = Random.Range(0f, num);
		float num3 = 0f;
		foreach (AnnouncementGroup announcementGroup2 in this.announcementGroups)
		{
			num3 += (float)announcementGroup2.announcements.Count;
			if (num2 <= num3)
			{
				return announcementGroup2;
			}
		}
		return null;
	}

	// Token: 0x060005A3 RID: 1443 RVA: 0x0002E698 File Offset: 0x0002C898
	public bool CanSpawn()
	{
		if (this.flagWhitelist.Count > 0)
		{
			using (List<string>.Enumerator enumerator = this.flagWhitelist.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					if (!CL_GameManager.HasActiveFlag(enumerator.Current))
					{
						return false;
					}
				}
			}
		}
		if (this.flagBlacklist.Count > 0)
		{
			using (List<string>.Enumerator enumerator = this.flagBlacklist.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					if (CL_GameManager.HasActiveFlag(enumerator.Current))
					{
						return false;
					}
				}
			}
		}
		return true;
	}

	// Token: 0x04000747 RID: 1863
	public string subregionName;

	// Token: 0x04000748 RID: 1864
	public bool showIntroText = true;

	// Token: 0x04000749 RID: 1865
	public string introText;

	// Token: 0x0400074A RID: 1866
	public M_Subregion.SubregionOrder subregionOrder;

	// Token: 0x0400074B RID: 1867
	public List<M_Level> levels;

	// Token: 0x0400074C RID: 1868
	public bool useLevelCount;

	// Token: 0x0400074D RID: 1869
	public int subregionMinLength = 2;

	// Token: 0x0400074E RID: 1870
	public int subregionMaxLength = 5;

	// Token: 0x0400074F RID: 1871
	public float subregionHeight = 250f;

	// Token: 0x04000750 RID: 1872
	public List<SessionEventList> sessionEventLists;

	// Token: 0x04000751 RID: 1873
	[Range(0f, 1f)]
	public float difficulty;

	// Token: 0x04000752 RID: 1874
	[Range(0f, 2f)]
	public float chance = 1f;

	// Token: 0x04000753 RID: 1875
	[Header("Generation Flags")]
	[Tooltip("World flags required before this will generate.")]
	public List<string> flagWhitelist;

	// Token: 0x04000754 RID: 1876
	[Tooltip("World flags that will prevent this from generating.")]
	public List<string> flagBlacklist;

	// Token: 0x04000755 RID: 1877
	public float massSpeedMult = 1f;

	// Token: 0x04000756 RID: 1878
	public List<AnnouncementGroup> announcementGroups;

	// Token: 0x04000757 RID: 1879
	public List<TipList> tips;

	// Token: 0x04000758 RID: 1880
	public float announcementTimerMult = 1f;

	// Token: 0x0200026F RID: 623
	public enum SubregionOrder
	{
		// Token: 0x04000FDF RID: 4063
		standard,
		// Token: 0x04000FE0 RID: 4064
		single,
		// Token: 0x04000FE1 RID: 4065
		playlist
	}
}
