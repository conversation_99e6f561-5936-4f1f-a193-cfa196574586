﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020000B0 RID: 176
[CreateAssetMenu(fileName = "AssetDatabase", menuName = "White Knuckle/Asset Database")]
public class WKAssetDatabase : ScriptableObject
{
	// Token: 0x04000777 RID: 1911
	public string id = "untitleddatabase";

	// Token: 0x04000778 RID: 1912
	[Tooltip("All Prefabs in the project that have an M_Level on their root.")]
	public List<GameObject> itemPrefabs = new List<GameObject>();

	// Token: 0x04000779 RID: 1913
	public List<GameObject> entityPrefabs = new List<GameObject>();

	// Token: 0x0400077A RID: 1914
	public List<GameObject> denizenPrefabs = new List<GameObject>();

	// Token: 0x0400077B RID: 1915
	public List<GameObject> levelPrefabs = new List<GameObject>();

	// Token: 0x0400077C RID: 1916
	public List<M_Region> regionAssets = new List<M_Region>();

	// Token: 0x0400077D RID: 1917
	public List<M_Subregion> subRegionAssets = new List<M_Subregion>();

	// Token: 0x0400077E RID: 1918
	public List<M_Gamemode> gamemodeAssets = new List<M_Gamemode>();

	// Token: 0x0400077F RID: 1919
	public List<Perk> perkAssets = new List<Perk>();

	// Token: 0x04000780 RID: 1920
	public List<OS_DiskData> diskData = new List<OS_DiskData>();

	// Token: 0x04000781 RID: 1921
	public List<SessionEvent> sessionEvents = new List<SessionEvent>();

	// Token: 0x04000782 RID: 1922
	public List<Sprite> spriteAssets = new List<Sprite>();

	// Token: 0x04000783 RID: 1923
	public GameObject nullItemObject;
}
