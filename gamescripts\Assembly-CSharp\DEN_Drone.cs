﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000033 RID: 51
public class DEN_Drone : Denizen
{
	// Token: 0x060001E8 RID: 488 RVA: 0x0000F5C0 File Offset: 0x0000D7C0
	public override void Start()
	{
		base.Start();
		this.droneTarget = new DEN_Drone.Target();
		this.rigid = base.GetComponent<Rigidbody>();
		this.targetPosition = this.FindWanderSpot();
		this.rigid.AddForce(base.transform.forward);
		this.noiseOffsetX = Random.Range(0f, 100f);
		this.noiseOffsetY = Random.Range(0f, 100f);
		this.noiseOffsetZ = Random.Range(0f, 100f);
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		this.restTimer = (float)Random.Range(5, 15);
		this.flyAudioTarget = this.flyAudio.volume;
		if (this.alwaysTargetPlayer)
		{
			this.droneTarget.transform = CL_GameManager.gMan.localPlayer.transform;
			this.droneTarget.tagger = CL_GameManager.gMan.localPlayer.GetTagger();
			this.droneTarget.entity = CL_GameManager.gMan.localPlayer;
			this.droneTarget.isVisible = true;
		}
	}

	// Token: 0x060001E9 RID: 489 RVA: 0x0000F6D4 File Offset: 0x0000D8D4
	public override void FixedUpdate()
	{
		base.FixedUpdate();
		if (this.alwaysTargetPlayer && this.droneTarget.transform == null)
		{
			this.droneTarget.transform = CL_GameManager.gMan.localPlayer.transform;
			this.droneTarget.tagger = CL_GameManager.gMan.localPlayer.GetTagger();
			this.droneTarget.entity = CL_GameManager.gMan.localPlayer;
			this.droneTarget.isVisible = true;
			this.droneTarget.lastSightedTimer = 1000f;
		}
		if (!this.dead)
		{
			this.AI();
			this.Movement();
			return;
		}
		if (this.flyAudio.volume > 0.1f && this.flyAudio.isPlaying)
		{
			this.flyAudio.volume = Mathf.Lerp(this.flyAudio.volume, 0f, Time.fixedDeltaTime * 5f);
			return;
		}
		if (this.flyAudio.isPlaying)
		{
			this.flyAudio.Stop();
		}
	}

	// Token: 0x060001EA RID: 490 RVA: 0x0000F7DF File Offset: 0x0000D9DF
	private new void Update()
	{
		if (CL_UIManager.debug)
		{
			CL_DebugView.draw.Cross(this.targetPosition, 0.5f, Color.green);
		}
	}

	// Token: 0x060001EB RID: 491 RVA: 0x0000F808 File Offset: 0x0000DA08
	private void AI()
	{
		float num = 0f;
		if (this.chargeCooldownTime > 0f)
		{
			this.chargeCooldownTime -= Time.fixedDeltaTime;
		}
		if (this.droneTarget.transform == null)
		{
			this.aiState = DEN_Drone.AIStates.wandering;
		}
		else
		{
			num = Vector3.Distance(base.transform.position, this.droneTarget.transform.position);
			if (!this.droneTarget.isVisible)
			{
				this.droneTarget.lastSightedTimer -= Time.fixedDeltaTime;
			}
			else
			{
				this.droneTarget.lastSightedTimer = this.loseTargetTimer;
			}
			if (this.droneTarget.lastSightedTimer <= 0f)
			{
				this.droneTarget.Clear();
				this.aiState = DEN_Drone.AIStates.wandering;
			}
		}
		switch (this.aiState)
		{
		case DEN_Drone.AIStates.wandering:
			this.moveState = DEN_Drone.MovementState.flying;
			if (Vector3.Distance(base.transform.position, this.targetPosition) < 1f)
			{
				if (this.hoverTime <= 0f)
				{
					this.targetPosition = this.FindWanderSpot();
					this.hoverTime = Random.Range(this.hoverTimeMin, this.hoverTimeMax);
				}
				else
				{
					this.hoverTime -= Time.deltaTime;
				}
			}
			if (this.alwaysTargetPlayer)
			{
				this.aiState = DEN_Drone.AIStates.hunting;
			}
			break;
		case DEN_Drone.AIStates.hunting:
			this.moveState = DEN_Drone.MovementState.flying;
			if (this.droneTarget.isVisible)
			{
				this.targetPosition = this.droneTarget.transform.position + Vector3.up * 0.5f - (this.droneTarget.transform.position + Vector3.up * 0.5f - base.transform.position).normalized * this.hoverDistance;
				if (num < this.chargeDistance && this.chargeCooldownTime <= 0f && this.willCharge)
				{
					this.aiState = DEN_Drone.AIStates.charging;
					this.clipHandler.PlaySound("drone:chargewindup");
				}
			}
			else
			{
				this.targetPosition = this.droneTarget.lastSighted;
			}
			break;
		case DEN_Drone.AIStates.charging:
			this.targetPosition = this.droneTarget.transform.position + Vector3.up * 0.5f - (this.droneTarget.transform.position + Vector3.up * 0.5f - base.transform.position).normalized * 1f;
			this.chargeTime += Time.fixedDeltaTime;
			if (this.chargeTime > this.chargeWindup)
			{
				this.moveState = DEN_Drone.MovementState.charging;
				this.rigid.AddForce((this.droneTarget.transform.position - base.transform.position).normalized * 100f);
				if (!this.hasCharged)
				{
					this.hasCharged = true;
					this.clipHandler.PlaySound("drone:charge");
				}
				if (this.chargeTime > this.chargeWindup + 1f)
				{
					this.moveState = DEN_Drone.MovementState.flying;
					this.aiState = DEN_Drone.AIStates.hunting;
					this.chargeCooldownTime = this.chargeCooldown;
					this.chargeTime = 0f;
					this.hasCharged = false;
				}
			}
			break;
		}
		if (base.IsTickFrame())
		{
			if (WorldLoader.initialized)
			{
				base.transform.parent = WorldLoader.GetClosestLevelToPosition(base.transform.position).level.GetParentRoot();
			}
			if (this.lookForTarget)
			{
				if (this.alwaysTargetPlayer)
				{
					this.droneTarget.isVisible = true;
					this.droneTarget.lastSighted = this.droneTarget.transform.position;
					return;
				}
				RaycastHit raycastHit;
				if (Physics.Raycast(base.transform.position, -Vector3.up, out raycastHit, 3f, this.flyMask))
				{
					this.rigid.AddForce(Vector3.up * 50f);
				}
				if (this.droneTarget.transform == null)
				{
					this.FindTarget();
					return;
				}
				if (this.sight.CanSeeTarget(base.transform.position, this.droneTarget.transform, this.sight.sightDistance, null) && !CL_GameManager.noTarget)
				{
					this.droneTarget.isVisible = true;
					this.droneTarget.lastSighted = this.droneTarget.transform.position;
					return;
				}
				this.droneTarget.isVisible = false;
				return;
			}
			else
			{
				this.droneTarget.isVisible = false;
			}
		}
	}

	// Token: 0x060001EC RID: 492 RVA: 0x0000FCE0 File Offset: 0x0000DEE0
	private void Movement()
	{
		DEN_Drone.MovementState movementState = this.moveState;
		if (movementState != DEN_Drone.MovementState.flying)
		{
			if (movementState == DEN_Drone.MovementState.charging)
			{
				this.Charge();
			}
		}
		else
		{
			this.Fly();
		}
		this.rigid.AddForce(this.targetMoveVector);
		this.ApplyBankingRotation();
	}

	// Token: 0x060001ED RID: 493 RVA: 0x0000FD24 File Offset: 0x0000DF24
	private void Fly()
	{
		this.rigid.useGravity = true;
		this.rigid.isKinematic = false;
		this.flyAudio.volume = Mathf.Lerp(this.flyAudio.volume, this.flyAudioTarget, Time.fixedDeltaTime * 3f);
		if (this.moveState != DEN_Drone.MovementState.charging)
		{
			this.moveState = DEN_Drone.MovementState.flying;
		}
		DEN_Drone.AIStates aistates = this.aiState;
		Vector3 vector = new Vector3(Mathf.PerlinNoise(Time.time * this.noiseFrequency + this.noiseOffsetX, 0f) - 0.5f, Mathf.PerlinNoise(Time.time * this.noiseFrequency + this.noiseOffsetY, 0f) - 0.5f, Mathf.PerlinNoise(Time.time * this.noiseFrequency + this.noiseOffsetZ, 0f) - 0.5f);
		vector *= this.noiseIntensity;
		Vector3 vector2 = this.targetPosition - base.transform.position;
		Vector3 vector3 = vector2;
		this.positionErrorSum = Vector3.ClampMagnitude(this.positionErrorSum, 1f);
		this.positionErrorSum += vector2 * Time.fixedDeltaTime;
		if (CL_UIManager.debug)
		{
			CL_DebugView.draw.Arrow(base.transform.position, base.transform.position + this.positionErrorSum, Color.green);
			CL_DebugView.draw.Arrow(base.transform.position, base.transform.position + this.rigid.velocity, Color.blue);
			CL_DebugView.draw.Arrow(base.transform.position, base.transform.position + vector2, Color.red);
		}
		Vector3 vector4 = this.positionErrorSum;
		Vector3 vector5 = (vector2 - this.lastPositionError) / Time.fixedDeltaTime;
		Vector3 vector6 = this.pGain * vector3 + this.iGain * vector4 + this.dGain * vector5;
		vector6 = Vector3.ClampMagnitude(vector6 * this.flySpeed, this.flySpeedClamp);
		this.rigid.AddForce(vector6 + vector);
		if (!this.droneTarget.isVisible)
		{
			this.lookVector = Vector3.Scale((this.rigid.velocity.normalized + vector2.normalized).normalized, new Vector3(1f, 0.2f, 1f));
		}
		else if (this.aiState == DEN_Drone.AIStates.charging)
		{
			this.lookVector = this.droneTarget.transform.position - base.transform.position;
		}
		else
		{
			this.lookVector = Vector3.Lerp(this.droneTarget.transform.position, this.targetPosition, 0.5f) - base.transform.position;
		}
		this.lastPositionError = vector2;
	}

	// Token: 0x060001EE RID: 494 RVA: 0x00010050 File Offset: 0x0000E250
	private void Charge()
	{
		this.Fly();
	}

	// Token: 0x060001EF RID: 495 RVA: 0x00010058 File Offset: 0x0000E258
	private void ApplyBankingRotation()
	{
		if (this.lookVector.magnitude < 0.01f)
		{
			this.lookVector = this.targetPosition - base.transform.position;
		}
		Vector3 normalized = this.lookVector.normalized;
		normalized.y *= 0.3f;
		float num = Mathf.Clamp(Vector3.SignedAngle(base.transform.forward, normalized, Vector3.up), -this.maxBankAngle, this.maxBankAngle);
		Quaternion quaternion = Quaternion.Euler(0f, 0f, -num);
		Quaternion quaternion2 = Quaternion.LookRotation(normalized, Vector3.up) * quaternion;
		base.transform.rotation = Quaternion.Slerp(base.transform.rotation, quaternion2, Time.deltaTime * this.rotationSpeed);
	}

	// Token: 0x060001F0 RID: 496 RVA: 0x00010124 File Offset: 0x0000E324
	private Vector3 FindWanderSpot()
	{
		Vector3 vector = Random.onUnitSphere * Random.Range(1f, 10f);
		RaycastHit raycastHit;
		if (Physics.Raycast(base.transform.position, vector, out raycastHit, vector.magnitude, this.flyMask))
		{
			Vector3 vector2 = raycastHit.point + raycastHit.normal * 2f;
			if (Physics.Linecast(base.transform.position, vector2, this.flyMask))
			{
				vector2 = base.transform.position + vector.normalized * raycastHit.distance * 0.2f;
			}
			return vector2;
		}
		return base.transform.position + vector;
	}

	// Token: 0x060001F1 RID: 497 RVA: 0x000101F0 File Offset: 0x0000E3F0
	private void FindTarget()
	{
		if (CL_GameManager.noTarget)
		{
			return;
		}
		foreach (Collider collider in Physics.OverlapSphere(base.transform.position, this.sight.sightDistance, this.sight.sightMask))
		{
			ObjectTagger objectTagger;
			if (ObjectTagger.TagCheck(collider.gameObject, this.targetTags, out objectTagger) && this.sight.CanSeeTarget(base.transform.position, collider.transform, this.sight.sightDistance, null))
			{
				this.droneTarget.transform = collider.transform;
				this.droneTarget.tagger = objectTagger;
				this.droneTarget.entity = collider.GetComponent<GameEntity>();
				this.droneTarget.isVisible = true;
				this.aiState = DEN_Drone.AIStates.hunting;
				return;
			}
		}
	}

	// Token: 0x060001F2 RID: 498 RVA: 0x000102CA File Offset: 0x0000E4CA
	private void OnCollisionStay(Collision other)
	{
		this.collision = other;
	}

	// Token: 0x060001F3 RID: 499 RVA: 0x000102D4 File Offset: 0x0000E4D4
	private void OnCollisionEnter(Collision other)
	{
		if (this.moveState == DEN_Drone.MovementState.charging)
		{
			ObjectTagger component = other.gameObject.GetComponent<ObjectTagger>();
			if (component != null && component.HasTag("Entity"))
			{
				GameEntity component2 = other.gameObject.GetComponent<GameEntity>();
				float num = 1f;
				float num2 = 1f;
				if (CL_GameManager.IsHardmode())
				{
					num = 1.7f;
					num2 = 2f;
				}
				component2.AddForce((Vector3.Scale((other.transform.position - base.transform.position).normalized, new Vector3(1f, 0f, 1f)) + Vector3.up * 0.5f).normalized * this.chargeHitForce * num);
				component2.Damage(num2, this.objectType);
				if (component.HasTag("Player"))
				{
					component2.GetComponent<ENT_Player>().DropHang(0.1f);
				}
			}
			this.moveState = DEN_Drone.MovementState.flying;
			this.aiState = DEN_Drone.AIStates.hunting;
			if (CL_GameManager.IsHardmode())
			{
				this.chargeCooldownTime = this.chargeCooldown * 0.75f;
			}
			else
			{
				this.chargeCooldownTime = this.chargeCooldown;
			}
			this.chargeTime = 0f;
			this.hasCharged = false;
		}
		else if (this.moveState == DEN_Drone.MovementState.flying && this.aiState == DEN_Drone.AIStates.wandering && this.rigid != null && this.collision != null)
		{
			this.rigid.AddForce(this.collision.impulse.normalized * 5f);
			this.targetPosition = this.collision.contacts[0].point + this.collision.contacts[0].normal;
		}
		if (this.dead)
		{
			this.grounded = true;
		}
	}

	// Token: 0x060001F4 RID: 500 RVA: 0x000104BE File Offset: 0x0000E6BE
	private void OnCollisionExit(Collision other)
	{
		if (this.dead)
		{
			this.grounded = false;
		}
	}

	// Token: 0x060001F5 RID: 501 RVA: 0x000104CF File Offset: 0x0000E6CF
	public override void AddForce(Vector3 v)
	{
		this.rigid.AddForce(v * 10f);
		base.AddForce(v);
	}

	// Token: 0x060001F6 RID: 502 RVA: 0x000104EE File Offset: 0x0000E6EE
	public override bool Damage(float amount, string type)
	{
		if (!this.dead)
		{
			this.clipHandler.PlaySound("drone:hurt");
		}
		return base.Damage(amount, type);
	}

	// Token: 0x060001F7 RID: 503 RVA: 0x00010510 File Offset: 0x0000E710
	public override void Kill(string type = "")
	{
		if (this.dead)
		{
			return;
		}
		this.deathEvent.Invoke();
		this.rigid.freezeRotation = false;
		this.rigid.AddTorque(Random.onUnitSphere * 10f);
		this.clipHandler.PlaySound("drone:die");
		base.gameObject.layer = LayerMask.NameToLayer("CreaturePassthrough");
		this.dead = true;
		if (this.destroyObject)
		{
			Object.Instantiate<GameObject>(this.destroyObject, base.transform.position, base.transform.rotation, base.transform.parent);
		}
		this.health = 0f;
		CL_GameTracker.AddEventToSession(CL_GameTracker.CreateEvent(base.name, base.gameObject.GetInstanceID().ToString(), this.objectType, CL_GameTracker.Session.Tick.Event.EventType.kill, base.transform.position));
	}

	// Token: 0x060001F8 RID: 504 RVA: 0x000105FD File Offset: 0x0000E7FD
	public DEN_Drone.Target GetTarget()
	{
		return this.droneTarget;
	}

	// Token: 0x04000210 RID: 528
	[Header("Drone")]
	public LayerMask flyMask;

	// Token: 0x04000211 RID: 529
	public float flySpeed = 1f;

	// Token: 0x04000212 RID: 530
	public float flySpeedClamp = 1f;

	// Token: 0x04000213 RID: 531
	public float crawlSpeed = 1f;

	// Token: 0x04000214 RID: 532
	public float hoverDistance = 1f;

	// Token: 0x04000215 RID: 533
	public float rotationSpeed = 2f;

	// Token: 0x04000216 RID: 534
	public float maxBankAngle = 45f;

	// Token: 0x04000217 RID: 535
	public float hoverTimeMin = 1f;

	// Token: 0x04000218 RID: 536
	public float hoverTimeMax = 2f;

	// Token: 0x04000219 RID: 537
	private float hoverTime;

	// Token: 0x0400021A RID: 538
	public float chargeHitForce = 2f;

	// Token: 0x0400021B RID: 539
	[Header("AI")]
	public DEN_Drone.AIStates aiState = DEN_Drone.AIStates.wandering;

	// Token: 0x0400021C RID: 540
	public DEN_Drone.MovementState moveState;

	// Token: 0x0400021D RID: 541
	public float loseTargetTimer = 5f;

	// Token: 0x0400021E RID: 542
	public bool willCharge = true;

	// Token: 0x0400021F RID: 543
	public float chargeDistance;

	// Token: 0x04000220 RID: 544
	private float chargeTime;

	// Token: 0x04000221 RID: 545
	public float chargeWindup = 3f;

	// Token: 0x04000222 RID: 546
	public float chargeCooldown = 10f;

	// Token: 0x04000223 RID: 547
	private float chargeCooldownTime;

	// Token: 0x04000224 RID: 548
	private float restTimer;

	// Token: 0x04000225 RID: 549
	private bool foundRestSpot;

	// Token: 0x04000226 RID: 550
	private bool hasCharged;

	// Token: 0x04000227 RID: 551
	public AudioSource flyAudio;

	// Token: 0x04000228 RID: 552
	private float flyAudioTarget;

	// Token: 0x04000229 RID: 553
	private Collision collision;

	// Token: 0x0400022A RID: 554
	public bool alwaysTargetPlayer;

	// Token: 0x0400022B RID: 555
	private DEN_Drone.Target droneTarget;

	// Token: 0x0400022C RID: 556
	private Vector3 targetMoveVector;

	// Token: 0x0400022D RID: 557
	public string[] targetTags;

	// Token: 0x0400022E RID: 558
	public bool lookForTarget = true;

	// Token: 0x0400022F RID: 559
	private Vector3 lookVector;

	// Token: 0x04000230 RID: 560
	private RaycastHit searchRaycast;

	// Token: 0x04000231 RID: 561
	private bool grounded;

	// Token: 0x04000232 RID: 562
	private Vector3 wallNormal;

	// Token: 0x04000233 RID: 563
	private Vector3 facingDir;

	// Token: 0x04000234 RID: 564
	[Header("Physics")]
	public float pGain = 1f;

	// Token: 0x04000235 RID: 565
	public float iGain = 0.1f;

	// Token: 0x04000236 RID: 566
	public float dGain = 0.5f;

	// Token: 0x04000237 RID: 567
	private Rigidbody rigid;

	// Token: 0x04000238 RID: 568
	private Vector3 positionErrorSum = Vector3.zero;

	// Token: 0x04000239 RID: 569
	private Vector3 lastPositionError = Vector3.zero;

	// Token: 0x0400023A RID: 570
	public float noiseIntensity = 0.5f;

	// Token: 0x0400023B RID: 571
	public float noiseFrequency = 1f;

	// Token: 0x0400023C RID: 572
	private float noiseOffsetX;

	// Token: 0x0400023D RID: 573
	private float noiseOffsetY;

	// Token: 0x0400023E RID: 574
	private float noiseOffsetZ;

	// Token: 0x0400023F RID: 575
	private Vector3 landDir;

	// Token: 0x04000240 RID: 576
	public UnityEvent deathEvent;

	// Token: 0x0200021B RID: 539
	public enum AIStates
	{
		// Token: 0x04000E35 RID: 3637
		searching,
		// Token: 0x04000E36 RID: 3638
		wandering,
		// Token: 0x04000E37 RID: 3639
		hunting,
		// Token: 0x04000E38 RID: 3640
		charging
	}

	// Token: 0x0200021C RID: 540
	public enum MovementState
	{
		// Token: 0x04000E3A RID: 3642
		flying,
		// Token: 0x04000E3B RID: 3643
		charging
	}

	// Token: 0x0200021D RID: 541
	public class Target
	{
		// Token: 0x06000D0E RID: 3342 RVA: 0x00050D12 File Offset: 0x0004EF12
		public void Clear()
		{
			this.transform = null;
			this.tagger = null;
			this.entity = null;
			this.isVisible = false;
		}

		// Token: 0x04000E3C RID: 3644
		public Transform transform;

		// Token: 0x04000E3D RID: 3645
		public ObjectTagger tagger;

		// Token: 0x04000E3E RID: 3646
		public GameEntity entity;

		// Token: 0x04000E3F RID: 3647
		public Vector3 lastSighted;

		// Token: 0x04000E40 RID: 3648
		public bool isVisible;

		// Token: 0x04000E41 RID: 3649
		public float lastSightedTimer;
	}
}
