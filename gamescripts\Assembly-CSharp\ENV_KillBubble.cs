﻿using System;
using System.Collections;
using UnityEngine;

// Token: 0x02000055 RID: 85
public class ENV_KillBubble : MonoBehaviour
{
	// Token: 0x06000379 RID: 889 RVA: 0x00021F79 File Offset: 0x00020179
	private void Start()
	{
		this.scale = this.bubbleTransform.localScale.x;
		base.transform.parent = null;
	}

	// Token: 0x0600037A RID: 890 RVA: 0x00021FA0 File Offset: 0x000201A0
	private void Update()
	{
		if (this.effectTransform)
		{
			this.effectTransform.position = base.transform.position + (ENT_Player.playerObject.transform.position - base.transform.position).normalized * this.scale;
		}
		this.bubbleTransform.localScale = Vector3.one * this.scale;
		if (Vector3.Distance(base.transform.position, ENT_Player.playerObject.transform.position) < this.scale)
		{
			ENT_Player.playerObject.Kill("");
			Debug.Log("KillPlayer");
		}
	}

	// Token: 0x0600037B RID: 891 RVA: 0x00022062 File Offset: 0x00020262
	public void GrowTo(float f)
	{
		base.StopAllCoroutines();
		base.StartCoroutine(this.ScaleTo(f));
	}

	// Token: 0x0600037C RID: 892 RVA: 0x00022078 File Offset: 0x00020278
	private IEnumerator ScaleTo(float f)
	{
		float startScale = this.scale;
		float scaleTime = Mathf.Abs(f - startScale);
		float curScaleTime = scaleTime;
		while (curScaleTime > 0f)
		{
			curScaleTime -= Time.deltaTime * this.growSpeed;
			this.scale = Mathf.Lerp(f, startScale, curScaleTime / scaleTime);
			yield return null;
		}
		yield break;
	}

	// Token: 0x040004C0 RID: 1216
	public bool active;

	// Token: 0x040004C1 RID: 1217
	public float growSpeed = 1f;

	// Token: 0x040004C2 RID: 1218
	public float sizeChecker = 1f;

	// Token: 0x040004C3 RID: 1219
	private float scale = 1f;

	// Token: 0x040004C4 RID: 1220
	public Transform bubbleTransform;

	// Token: 0x040004C5 RID: 1221
	public Transform effectTransform;
}
