﻿using System;
using System.Collections.Generic;
using Drawing;
using EditorCools;
using UnityEngine;

// Token: 0x0200003A RID: 58
[RequireComponent(typeof(Animator))]
[RequireComponent(typeof(UT_AudioClipHandler))]
public class DEN_Strider : Denizen
{
	// Token: 0x06000225 RID: 549 RVA: 0x00012290 File Offset: 0x00010490
	public override void Start()
	{
		this.callTime = Random.Range(this.callFrequency / 2f, this.callFrequency * 2f);
		this.animator = base.GetComponent<Animator>();
		base.Start();
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		this.RefreshNodes();
	}

	// Token: 0x06000226 RID: 550 RVA: 0x000122E4 File Offset: 0x000104E4
	public override void FixedUpdate()
	{
		if (this.walking)
		{
			if (this.curSpeed <= this.speed)
			{
				this.curSpeed = Mathf.Clamp(this.curSpeed + Time.fixedDeltaTime * this.acceleration, 0f, this.speed);
			}
			this.animator.SetBool("walking", this.walking);
			base.transform.position += base.transform.forward * this.curSpeed * Time.deltaTime;
			Quaternion quaternion = Quaternion.LookRotation((this.nodes[this.currentNode].position - base.transform.position).normalized + Vector3.up * Mathf.Sin(Time.time * 1f) * 0.15f);
			base.transform.rotation = Quaternion.Lerp(base.transform.rotation, quaternion, Time.deltaTime * this.turnSpeed);
			if (Vector3.Distance(base.transform.position, this.nodes[this.currentNode].position) < 3f)
			{
				this.currentNode++;
				if (this.currentNode >= this.nodes.Count)
				{
					this.currentNode = 0;
				}
			}
		}
		else if (this.curSpeed >= 0f)
		{
			this.curSpeed = Mathf.Clamp(this.curSpeed - Time.fixedDeltaTime * this.acceleration, 0f, this.speed);
		}
		if (this.stateTime <= 0f)
		{
			this.stateTime = (float)Random.Range(10, 20);
			this.walking = !this.walking;
			this.animator.SetBool("walking", this.walking);
		}
		this.animator.SetFloat("walkspeed", this.curSpeed / this.speed);
		this.stateTime -= Time.fixedDeltaTime;
		this.callTime -= Time.fixedDeltaTime;
		if (this.callTime <= 0f)
		{
			this.clipHandler.PlaySound("strider:call");
			this.callTime = Random.Range(this.callFrequency, this.callFrequency * 2f);
		}
		base.FixedUpdate();
	}

	// Token: 0x06000227 RID: 551 RVA: 0x00012554 File Offset: 0x00010754
	[Button(null, null, 0f)]
	public void RefreshNodes()
	{
		this.nodes = new List<Transform>();
		for (int i = 0; i < this.nodeParent.childCount; i++)
		{
			this.nodes.Add(this.nodeParent.GetChild(i));
		}
	}

	// Token: 0x06000228 RID: 552 RVA: 0x0001259C File Offset: 0x0001079C
	public override void DrawGizmos()
	{
		if (this.nodes == null)
		{
			return;
		}
		for (int i = 0; i < this.nodes.Count; i++)
		{
			if (!(this.nodes[i] == null))
			{
				int num = i + 1;
				if (i + 1 >= this.nodes.Count)
				{
					num = 0;
				}
				Draw.Arrow(this.nodes[i].position, this.nodes[num].position, Vector3.up, 0.5f, Color.red);
			}
		}
	}

	// Token: 0x06000229 RID: 553 RVA: 0x0001263C File Offset: 0x0001083C
	public void Footstep(int i)
	{
		this.clipHandler.PlaySound("strider:footstep", this.feet[i].position);
		this.splashEffect.transform.position = this.feet[i].position;
		this.splashEffect.Play();
		this.ShakeScreen();
	}

	// Token: 0x0600022A RID: 554 RVA: 0x00012694 File Offset: 0x00010894
	public void ShakeScreen()
	{
		foreach (Collider collider in Physics.OverlapSphere(base.transform.position, this.shakeRadius, this.playerMask))
		{
			collider.BroadcastMessage("ShakeCamera", this.shakeIntensity * (1f - Vector3.Distance(base.transform.position, collider.transform.position) / this.shakeRadius));
		}
	}

	// Token: 0x040002B3 RID: 691
	private Animator animator;

	// Token: 0x040002B4 RID: 692
	public float speed = 1f;

	// Token: 0x040002B5 RID: 693
	public float acceleration = 1f;

	// Token: 0x040002B6 RID: 694
	public float turnSpeed = 1f;

	// Token: 0x040002B7 RID: 695
	private float curSpeed;

	// Token: 0x040002B8 RID: 696
	private bool walking;

	// Token: 0x040002B9 RID: 697
	private List<Transform> nodes;

	// Token: 0x040002BA RID: 698
	private int currentNode;

	// Token: 0x040002BB RID: 699
	private int direction = 1;

	// Token: 0x040002BC RID: 700
	public Transform nodeParent;

	// Token: 0x040002BD RID: 701
	private float stateTime = 5f;

	// Token: 0x040002BE RID: 702
	public Transform[] feet;

	// Token: 0x040002BF RID: 703
	public LayerMask playerMask;

	// Token: 0x040002C0 RID: 704
	public float shakeRadius = 10f;

	// Token: 0x040002C1 RID: 705
	public float shakeIntensity = 0.2f;

	// Token: 0x040002C2 RID: 706
	public float callFrequency = 10f;

	// Token: 0x040002C3 RID: 707
	private float callTime = 10f;

	// Token: 0x040002C4 RID: 708
	public ParticleSystem splashEffect;
}
