﻿using System;
using System.Collections;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x0200002C RID: 44
[ExecuteInEditMode]
public class FX_Zone : MonoBehaviourGizmos
{
	// Token: 0x06000193 RID: 403 RVA: 0x0000BE55 File Offset: 0x0000A055
	private void Start()
	{
		this.tickTime = Random.value;
		this.lerpData = new FXManager.FXData();
		if (this.resetBlendOnStart && Application.isPlaying)
		{
			this.blend = this.resetBlend;
		}
	}

	// Token: 0x06000194 RID: 404 RVA: 0x0000BE88 File Offset: 0x0000A088
	private void OnEnable()
	{
		FXManager.OnPreRenderFX = (Action<Camera>)Delegate.Combine(FXManager.OnPreRenderFX, new Action<Camera>(this.RenderFX));
	}

	// Token: 0x06000195 RID: 405 RVA: 0x0000BEAA File Offset: 0x0000A0AA
	private void OnDisable()
	{
		FXManager.OnPreRenderFX = (Action<Camera>)Delegate.Remove(FXManager.OnPreRenderFX, new Action<Camera>(this.RenderFX));
	}

	// Token: 0x06000196 RID: 406 RVA: 0x0000BECC File Offset: 0x0000A0CC
	[ExecuteAlways]
	private void LateUpdate()
	{
		if (this.tickTime <= 0f)
		{
			this.tickTime = this.tickRate;
			this.tick = true;
			return;
		}
		this.tick = false;
	}

	// Token: 0x06000197 RID: 407 RVA: 0x0000BEF8 File Offset: 0x0000A0F8
	private void RenderFX(Camera camera)
	{
		if (!this.inRange.ContainsKey(camera))
		{
			this.inRange.Add(camera, false);
		}
		float num = Vector3.Distance(camera.transform.position, base.transform.position);
		if (num < this.maxRange)
		{
			this.inRange[camera] = true;
			this.DataLerp(num);
			return;
		}
		if (this.inRange[camera])
		{
			this.inRange[camera] = false;
			this.DataLerp(num);
		}
	}

	// Token: 0x06000198 RID: 408 RVA: 0x0000BF84 File Offset: 0x0000A184
	private void DataLerp(float distance)
	{
		this.lerpAmount = (1f - Mathf.Clamp((distance - this.minRange) / (this.maxRange - this.minRange), 0f, 1f)) * this.blend;
		if (!base.enabled)
		{
			return;
		}
		FXManager.FXZoneStore fxzoneStore = new FXManager.FXZoneStore();
		fxzoneStore.blend = this.lerpAmount;
		fxzoneStore.priority = this.priority;
		fxzoneStore.data = this.fxData;
		FXManager.fxZones.Enqueue(fxzoneStore, this.priority);
	}

	// Token: 0x06000199 RID: 409 RVA: 0x0000C010 File Offset: 0x0000A210
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			Draw.SphereOutline(base.transform.position, this.maxRange, this.fxData.fog * new Color(1f, 1f, 1f, 0.5f));
			Draw.SphereOutline(base.transform.position, this.maxRange + 0.5f, new Color(1f, 1f, 1f, 0.02f));
			Draw.SphereOutline(base.transform.position, this.minRange, this.fxData.fog * 2f * new Color(1f, 1f, 1f, 1f));
		}
		else
		{
			Draw.SphereOutline(base.transform.position, this.maxRange, this.fxData.fog * new Color(1f, 1f, 1f, 0.005f));
			Draw.SphereOutline(base.transform.position, this.minRange, this.fxData.fog * new Color(1f, 1f, 1f, 0.01f));
		}
		Draw.Label2D(base.transform.position, "Lerp: " + this.lerpAmount.ToString(), 14f);
	}

	// Token: 0x0600019A RID: 410 RVA: 0x0000C1B0 File Offset: 0x0000A3B0
	public void FadeTo(float fadeTarget)
	{
		base.StartCoroutine(this.FadeOverTime(fadeTarget));
	}

	// Token: 0x0600019B RID: 411 RVA: 0x0000C1C0 File Offset: 0x0000A3C0
	private IEnumerator FadeOverTime(float fadeTarget)
	{
		float fade = 0f;
		float startBlend = this.blend;
		while (fade < 1f)
		{
			fade += Time.deltaTime * 0.5f;
			this.blend = Mathf.Lerp(startBlend, fadeTarget, fade);
			yield return null;
		}
		yield break;
	}

	// Token: 0x04000176 RID: 374
	public float maxRange;

	// Token: 0x04000177 RID: 375
	public float minRange;

	// Token: 0x04000178 RID: 376
	[Range(0f, 1f)]
	public float blend = 1f;

	// Token: 0x04000179 RID: 377
	public byte priority = 1;

	// Token: 0x0400017A RID: 378
	public bool resetBlendOnStart;

	// Token: 0x0400017B RID: 379
	public float resetBlend = 1f;

	// Token: 0x0400017C RID: 380
	private float tickRate = 0.1f;

	// Token: 0x0400017D RID: 381
	private float tickTime;

	// Token: 0x0400017E RID: 382
	public FXManager.FXData fxData = new FXManager.FXData();

	// Token: 0x0400017F RID: 383
	private FXManager.FXData lerpData = new FXManager.FXData();

	// Token: 0x04000180 RID: 384
	private float lerpAmount;

	// Token: 0x04000181 RID: 385
	private bool tick;

	// Token: 0x04000182 RID: 386
	private Dictionary<Camera, bool> inRange = new Dictionary<Camera, bool>();
}
