﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000093 RID: 147
[Serializable]
public class Item
{
	// Token: 0x060004E5 RID: 1253 RVA: 0x0002A3A8 File Offset: 0x000285A8
	public virtual void InitializeItemData(Item_Object selfObject)
	{
		if (this.initialized)
		{
			return;
		}
		this.itemGUID = Guid.NewGuid().ToString();
		this.initialized = true;
		this.dropObject = selfObject;
		try
		{
			this.itemAsset = CL_AssetManager.GetAssetGameObject(this.prefabName, "").GetComponent<Item_Object>();
		}
		catch
		{
			Debug.LogError("Cannot find asset " + this.prefabName + " in Asset Manager! Make sure an asset with that name exists in there, or that this prefab has an asset ID assigned!");
		}
	}

	// Token: 0x060004E6 RID: 1254 RVA: 0x0002A430 File Offset: 0x00028630
	public virtual void Execute(Inventory inv)
	{
		if (this.dropObject != null)
		{
			this.dropObject.Execute(inv);
		}
	}

	// Token: 0x060004E7 RID: 1255 RVA: 0x0002A44C File Offset: 0x0002864C
	private void OnDestroy()
	{
	}

	// Token: 0x060004E8 RID: 1256 RVA: 0x0002A450 File Offset: 0x00028650
	public void InitializeInHand(Inventory inv, ENT_Player.Hand h)
	{
		string[] array = new string[6];
		array[0] = "Initializing In Hand: ";
		array[1] = this.prefabName;
		array[2] = " :: ";
		int num = 3;
		HandItem handItem = this.handItemAsset;
		array[num] = ((handItem != null) ? handItem.ToString() : null);
		array[4] = " :: ";
		array[5] = this.itemName;
		Debug.Log(string.Concat(array));
		if (this.handItemAsset == null)
		{
			this.CopyNonDataFromItemAsset();
			string[] array2 = new string[6];
			array2[0] = "Checking For Asset: ";
			array2[1] = this.prefabName;
			array2[2] = " :: ";
			int num2 = 3;
			HandItem handItem2 = this.handItemAsset;
			array2[num2] = ((handItem2 != null) ? handItem2.ToString() : null);
			array2[4] = " :: ";
			array2[5] = this.itemName;
			Debug.Log(string.Concat(array2));
			if (this.handItemAsset == null)
			{
				Debug.Log("Missing Asset: " + this.prefabName + " :: " + this.itemName);
			}
		}
		this.pHand = h;
		this.inventory = inv;
		this.handItem = Object.Instantiate<HandItem>(this.handItemAsset, h.inventoryHand.handInventoryRoot.position + h.inventoryHand.handInventoryRoot.up * -0.4f, inv.playerInventoryRoot.rotation, inv.playerInventoryRoot);
		Vector3 localScale = this.handItem.transform.localScale;
		localScale.x = ((h.id == 0) ? (-localScale.x) : localScale.x);
		this.handItem.transform.localScale = localScale;
		this.handItem.Initialize(this, h);
		if (this.dropObject == null)
		{
			this.CreateDropObject();
		}
		this.dropObject.gameObject.SetActive(false);
		if (this.useFlags && this.flagOnPickup != "")
		{
			CL_GameManager.SetGameFlag(this.flagOnPickup, true, "", false);
		}
	}

	// Token: 0x060004E9 RID: 1257 RVA: 0x0002A63E File Offset: 0x0002883E
	public HandItem GetHandItem()
	{
		return this.handItem;
	}

	// Token: 0x060004EA RID: 1258 RVA: 0x0002A646 File Offset: 0x00028846
	public virtual string GetFullItemName()
	{
		return this.itemName;
	}

	// Token: 0x060004EB RID: 1259 RVA: 0x0002A650 File Offset: 0x00028850
	public void Flash(Color c)
	{
		this.itemImage.color = c;
		UI_TabFade component = this.itemImage.gameObject.GetComponent<UI_TabFade>();
		if (component != null)
		{
			component.delayTime = 0.5f;
		}
	}

	// Token: 0x060004EC RID: 1260 RVA: 0x0002A690 File Offset: 0x00028890
	public virtual void ActivateItem(Inventory.ItemHand hand)
	{
		this.handItem.gameObject.SetActive(true);
		this.handItem.GetComponent<ViewSway>().targetOffset = Vector3.up * 0.3f;
		this.handItem.GetComponent<ViewSway>().bobOffset = (float)(hand.hand.id * 5);
		this.handItem.active = true;
	}

	// Token: 0x060004ED RID: 1261 RVA: 0x0002A6F7 File Offset: 0x000288F7
	public virtual bool CanUse()
	{
		return true;
	}

	// Token: 0x060004EE RID: 1262 RVA: 0x0002A6FA File Offset: 0x000288FA
	public virtual void SetUsed(bool b)
	{
		this.used = b;
	}

	// Token: 0x060004EF RID: 1263 RVA: 0x0002A703 File Offset: 0x00028903
	public virtual void Use()
	{
		this.handItem.Use();
	}

	// Token: 0x060004F0 RID: 1264 RVA: 0x0002A710 File Offset: 0x00028910
	public virtual void StopUse()
	{
		this.handItem.StopUse();
	}

	// Token: 0x060004F1 RID: 1265 RVA: 0x0002A720 File Offset: 0x00028920
	public void Drop(Vector3 pos, Vector3 dir)
	{
		if (this.itemAsset == null)
		{
			try
			{
				this.itemAsset = CL_AssetManager.GetAssetGameObject(this.prefabName, "").GetComponent<Item_Object>();
			}
			catch
			{
				Debug.LogError("Cannot find asset " + this.prefabName + " in Asset Manager! Make sure an asset with that name exists in there, or that this prefab has an asset ID assigned!");
				return;
			}
		}
		if (this.dropObject != null)
		{
			this.dropObject.gameObject.SetActive(true);
			this.dropObject.transform.position = pos;
		}
		else
		{
			this.CreateDropObject().transform.position = pos;
			this.dropObject.gameObject.SetActive(true);
			this.dropObject.itemData = this;
		}
		this.dropObject.itemData = this;
		Rigidbody component = this.dropObject.GetComponent<Rigidbody>();
		if (component != null)
		{
			component.velocity = Vector3.zero;
		}
		CL_Prop component2 = this.dropObject.GetComponent<CL_Prop>();
		if (component2 != null)
		{
			component2.Drop();
		}
		if (this.handItem != null)
		{
			Object.Destroy(this.handItem.gameObject);
		}
	}

	// Token: 0x060004F2 RID: 1266 RVA: 0x0002A850 File Offset: 0x00028A50
	public void SetItemAsset(Item_Object asset)
	{
		this.itemAsset = asset;
		this.prefabName = asset.name;
		this.ClearDropObject();
		this.CreateDropObject();
	}

	// Token: 0x060004F3 RID: 1267 RVA: 0x0002A872 File Offset: 0x00028A72
	public void ClearDropObject()
	{
		if (this.dropObject != null)
		{
			Object.Destroy(this.dropObject.gameObject);
			this.dropObject = null;
		}
	}

	// Token: 0x060004F4 RID: 1268 RVA: 0x0002A899 File Offset: 0x00028A99
	public void SetDropObject(Item_Object ob)
	{
		this.dropObject = ob;
		this.dropVel = this.dropObject.itemData.dropVel;
	}

	// Token: 0x060004F5 RID: 1269 RVA: 0x0002A8B8 File Offset: 0x00028AB8
	public Item_Object CreateDropObject()
	{
		string[] array = new string[5];
		array[0] = this.itemName;
		array[1] = " :: ";
		int num = 2;
		Item_Object item_Object = this.itemAsset;
		array[num] = ((item_Object != null) ? item_Object.ToString() : null);
		array[3] = " :: ";
		int num2 = 4;
		HandItem handItem = this.handItemAsset;
		array[num2] = ((handItem != null) ? handItem.ToString() : null);
		Debug.Log(string.Concat(array));
		if (this.itemAsset == null)
		{
			GameObject assetGameObject = CL_AssetManager.GetAssetGameObject(this.prefabName, "");
			if (assetGameObject != null)
			{
				this.itemAsset = assetGameObject.GetComponent<Item_Object>();
				if (this.itemAsset == null)
				{
					Debug.LogError("Error! Could not find asset from database for " + this.prefabName + " : Missing Item Component");
					Debug.LogError("Creating temp drop object from database to substitute.");
					this.itemAsset = CL_AssetManager.GetNullItemObject().GetComponent<Item_Object>();
				}
			}
			else
			{
				Debug.LogError("Error! Could not find asset gameobject from database for " + this.prefabName);
				Debug.LogError("Creating temp drop object from database to substitute.");
				this.itemAsset = CL_AssetManager.GetNullItemObject().GetComponent<Item_Object>();
			}
		}
		Item_Object item_Object2 = Object.Instantiate<Item_Object>(this.itemAsset, Vector3.zero, Quaternion.Euler(Random.insideUnitSphere * 360f));
		item_Object2.gameObject.SetActive(false);
		item_Object2.itemData = this;
		this.dropObject = item_Object2;
		return item_Object2;
	}

	// Token: 0x060004F6 RID: 1270 RVA: 0x0002AA02 File Offset: 0x00028C02
	public Item_Object GetDropObject()
	{
		if (this.dropObject == null)
		{
			this.CreateDropObject();
		}
		return this.dropObject;
	}

	// Token: 0x060004F7 RID: 1271 RVA: 0x0002AA1F File Offset: 0x00028C1F
	public void Interact()
	{
	}

	// Token: 0x060004F8 RID: 1272 RVA: 0x0002AA24 File Offset: 0x00028C24
	public void DestroyItem()
	{
		if (this.inventory == null)
		{
			this.inventory = ENT_Player.GetInventory();
		}
		if (this.inventory != null)
		{
			this.inventory.bagItems.Remove(this);
		}
		this.ClearDropObject();
		if (this.handItem != null)
		{
			this.inventory.ClearItemFromHand(this);
		}
	}

	// Token: 0x060004F9 RID: 1273 RVA: 0x0002AA8C File Offset: 0x00028C8C
	public Item GetClone()
	{
		Item item = new Item();
		this.Save();
		item.itemName = this.itemName;
		item.prefabName = this.prefabName;
		item.inventoryScale = this.inventoryScale;
		item.used = this.used;
		item.dropVel = this.dropVel;
		item.pocketable = this.pocketable;
		item.upDirection = this.upDirection;
		item.interactType = this.interactType;
		item.interactor = this.interactor;
		item.data = this.data.Copy<List<string>>();
		item.dataModules = this.dataModules.Copy<List<Item_Data>>();
		item.dataModuleSaves = this.dataModuleSaves.Copy<List<Item.DataModuleSave>>();
		item.normalSprite = this.normalSprite;
		item.itemAsset = this.itemAsset;
		item.handItemAsset = this.handItemAsset;
		item.pickupSounds = this.pickupSounds;
		item.offset = this.offset;
		item.bagPosition = this.bagPosition;
		item.bagRotation = this.bagRotation;
		item.inBag = false;
		item.initialized = false;
		item.itemTag = this.itemTag;
		return item;
	}

	// Token: 0x060004FA RID: 1274 RVA: 0x0002ABB1 File Offset: 0x00028DB1
	internal bool InInventory()
	{
		return this.inventory != null;
	}

	// Token: 0x060004FB RID: 1275 RVA: 0x0002ABC0 File Offset: 0x00028DC0
	public List<T> GetAllDataModulesByType<T>() where T : Item_Data
	{
		if (this.dataModules == null || this.dataModules.Count == 0)
		{
			return null;
		}
		List<T> list = new List<T>();
		Debug.Log("Got here at least.");
		foreach (Item_Data item_Data in this.dataModules)
		{
			string text = "Checking Type: ";
			Type type = item_Data.GetType();
			Debug.Log(text + ((type != null) ? type.ToString() : null));
			Debug.Log("Type Name: " + typeof(T).Name);
			Debug.Log("Is Assigneable?: " + typeof(T).IsAssignableFrom(item_Data.GetType()).ToString());
			if (typeof(T).IsAssignableFrom(item_Data.GetType()))
			{
				string text2 = "Getting Type of Data Module: ";
				Type type2 = item_Data.GetType();
				Debug.Log(text2 + ((type2 != null) ? type2.ToString() : null));
				list.Add(item_Data as T);
			}
		}
		return list;
	}

	// Token: 0x060004FC RID: 1276 RVA: 0x0002ACF0 File Offset: 0x00028EF0
	public string GetFirstDataStringByType(string type, bool returnEverythingAfterType = false)
	{
		if (this.data == null || this.data.Count == 0)
		{
			return null;
		}
		foreach (string text in this.data)
		{
			if (text.Contains(type + ":"))
			{
				if (returnEverythingAfterType)
				{
					return text.Replace(type + ":", "");
				}
				return text.Split(':', StringSplitOptions.None)[1];
			}
		}
		return null;
	}

	// Token: 0x060004FD RID: 1277 RVA: 0x0002AD94 File Offset: 0x00028F94
	public void SetFirstDataStringsofType(string type, string value)
	{
		if (this.data == null || this.data.Count == 0)
		{
			this.data = new List<string>();
		}
		for (int i = 0; i < this.data.Count; i++)
		{
			string text = this.data[i];
			if (text.Contains(type + ":"))
			{
				text.Split(':', StringSplitOptions.None)[1] = value;
				this.data[i] = type + ":" + value;
				return;
			}
		}
		this.data.Add(type + ":" + value);
	}

	// Token: 0x060004FE RID: 1278 RVA: 0x0002AE33 File Offset: 0x00029033
	public bool IsUnlocked()
	{
		if (this.unlockType == Item.UnlockType.progression)
		{
			return CL_ProgressionManager.HasProgressionUnlock(this.progressionUnlockID);
		}
		return this.unlockType != Item.UnlockType.stat || StatManager.GetTotalStatisticInt(this.statUnlockName) >= this.statUnlockAmount;
	}

	// Token: 0x060004FF RID: 1279 RVA: 0x0002AE70 File Offset: 0x00029070
	public void SendUnlockMessage()
	{
		CL_ProgressionManager.ShowUnlockPopup(this.unlockSprite, "Vendor Restock", this.unlockTextDesc);
	}

	// Token: 0x06000500 RID: 1280 RVA: 0x0002AE88 File Offset: 0x00029088
	public void CopyNonDataFromItemAsset()
	{
		Debug.Log("Attempting To Copy Data From: " + this.prefabName);
		GameObject assetGameObject = CL_AssetManager.GetAssetGameObject(this.prefabName, "");
		string text = "Copying Data From: ";
		string text2 = this.prefabName;
		string text3 = " :: ";
		GameObject gameObject = assetGameObject;
		Debug.Log(text + text2 + text3 + ((gameObject != null) ? gameObject.ToString() : null));
		if (assetGameObject != null)
		{
			Item_Object component = assetGameObject.GetComponent<Item_Object>();
			this.handItemAsset = component.itemData.handItemAsset;
			Debug.Log(this.handItemAsset);
			this.itemAsset = component.itemData.itemAsset;
			this.pickupSounds = component.itemData.pickupSounds;
			this.interactor = component.itemData.interactor;
			this.unlockSprite = component.itemData.unlockSprite;
		}
	}

	// Token: 0x06000501 RID: 1281 RVA: 0x0002AF54 File Offset: 0x00029154
	public void OnPickup()
	{
		if (!this.hasBeenPickedUp)
		{
			if (this.pickupStat != "")
			{
				StatManager.sessionStats.UpdateStatistic(this.pickupStat, 1, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Add, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
			}
			StatManager.sessionStats.UpdateStatistic("pickup-item-" + this.itemName, 1, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Add, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
			if (this.itemTag != null)
			{
				StatManager.sessionStats.UpdateStatistic("pickup-tag-" + this.itemTag, 1, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Add, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
			}
			this.hasBeenPickedUp = true;
		}
	}

	// Token: 0x06000502 RID: 1282 RVA: 0x0002AFF0 File Offset: 0x000291F0
	internal void UpdateProgressionStat(int i = 1)
	{
		if (CL_GameManager.GetBaseGamemode().HasTag("blockvendorunlocks"))
		{
			return;
		}
		if (this.unlockType == Item.UnlockType.progression)
		{
			ProgressionUnlock progressionUnlock = CL_ProgressionManager.GetProgressionUnlock(this.progressionUnlockID);
			if (progressionUnlock != null)
			{
				StatManager.sessionStats.UpdateStatistic(progressionUnlock.statName, i, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Add, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
			}
		}
	}

	// Token: 0x06000503 RID: 1283 RVA: 0x0002B048 File Offset: 0x00029248
	public void Save()
	{
		if (this.dataModules != null && this.dataModules.Count > 0)
		{
			this.dataModuleSaves = new List<Item.DataModuleSave>();
			foreach (Item_Data item_Data in this.dataModules)
			{
				Item.DataModuleSave dataModuleSave = new Item.DataModuleSave();
				dataModuleSave.type = item_Data.GetType().Name;
				dataModuleSave.moduleData = JsonUtility.ToJson(item_Data);
				dataModuleSave.saveData = item_Data.GetSaveData();
				this.dataModuleSaves.Add(dataModuleSave);
				Debug.Log("saving this info: " + JsonUtility.ToJson(item_Data));
			}
		}
	}

	// Token: 0x06000504 RID: 1284 RVA: 0x0002B10C File Offset: 0x0002930C
	public void Load()
	{
		if (this.dataModuleSaves != null && this.dataModuleSaves.Count > 0)
		{
			this.dataModules = new List<Item_Data>();
			foreach (Item.DataModuleSave dataModuleSave in this.dataModuleSaves)
			{
				object obj = JsonUtility.FromJson(dataModuleSave.moduleData, Type.GetType(dataModuleSave.type));
				this.dataModules.Add((Item_Data)obj);
				this.dataModules[this.dataModules.Count - 1].LoadSaveData(dataModuleSave.saveData);
			}
		}
	}

	// Token: 0x04000675 RID: 1653
	public string itemName;

	// Token: 0x04000676 RID: 1654
	public string itemTag;

	// Token: 0x04000677 RID: 1655
	[Space]
	public string prefabName;

	// Token: 0x04000678 RID: 1656
	public float inventoryScale = 1f;

	// Token: 0x04000679 RID: 1657
	[Tooltip("This is rarely set manually, determines the data state of the object")]
	public bool used;

	// Token: 0x0400067A RID: 1658
	[Tooltip("How hard is this item thrown from the player when dropped?")]
	public float dropVel;

	// Token: 0x0400067B RID: 1659
	[Tooltip("Does this item fit in pockets?")]
	public bool pocketable = true;

	// Token: 0x0400067C RID: 1660
	[Tooltip("The direction that this object points in the inventory.")]
	public Vector3 upDirection = Vector3.up;

	// Token: 0x0400067D RID: 1661
	public Item.InteractType interactType;

	// Token: 0x0400067E RID: 1662
	public Item_Interactor interactor;

	// Token: 0x0400067F RID: 1663
	[TextArea(4, 15)]
	public List<string> data;

	// Token: 0x04000680 RID: 1664
	[SerializeReference]
	public List<Item_Data> dataModules;

	// Token: 0x04000681 RID: 1665
	[HideInInspector]
	public List<Item.DataModuleSave> dataModuleSaves;

	// Token: 0x04000682 RID: 1666
	[HideInInspector]
	public Vector3 bagPosition;

	// Token: 0x04000683 RID: 1667
	[HideInInspector]
	public Quaternion bagRotation;

	// Token: 0x04000684 RID: 1668
	[HideInInspector]
	public Inventory inventory;

	// Token: 0x04000685 RID: 1669
	public Sprite normalSprite;

	// Token: 0x04000686 RID: 1670
	public Item_Object itemAsset;

	// Token: 0x04000687 RID: 1671
	public HandItem handItemAsset;

	// Token: 0x04000688 RID: 1672
	public List<AudioClip> pickupSounds;

	// Token: 0x04000689 RID: 1673
	public bool useFlags;

	// Token: 0x0400068A RID: 1674
	public string flagOnPickup;

	// Token: 0x0400068B RID: 1675
	public string flagOnDrop;

	// Token: 0x0400068C RID: 1676
	public string pickupStat = "";

	// Token: 0x0400068D RID: 1677
	public Item.UnlockType unlockType;

	// Token: 0x0400068E RID: 1678
	public string progressionUnlockID;

	// Token: 0x0400068F RID: 1679
	public string statUnlockName;

	// Token: 0x04000690 RID: 1680
	public int statUnlockAmount;

	// Token: 0x04000691 RID: 1681
	public Sprite unlockSprite;

	// Token: 0x04000692 RID: 1682
	public string unlockTextDesc;

	// Token: 0x04000693 RID: 1683
	private Item_Object dropObject;

	// Token: 0x04000694 RID: 1684
	private Vector2 offset;

	// Token: 0x04000695 RID: 1685
	private HandItem handItem;

	// Token: 0x04000696 RID: 1686
	private Image itemImage;

	// Token: 0x04000697 RID: 1687
	private ENT_Player.Hand pHand;

	// Token: 0x04000698 RID: 1688
	[HideInInspector]
	public bool inBag;

	// Token: 0x04000699 RID: 1689
	private bool initialized;

	// Token: 0x0400069A RID: 1690
	private bool hasBeenPickedUp;

	// Token: 0x0400069B RID: 1691
	public string itemGUID;

	// Token: 0x02000256 RID: 598
	public enum InteractType
	{
		// Token: 0x04000F6A RID: 3946
		none,
		// Token: 0x04000F6B RID: 3947
		roach,
		// Token: 0x04000F6C RID: 3948
		usable
	}

	// Token: 0x02000257 RID: 599
	[Serializable]
	public class DataModuleSave
	{
		// Token: 0x04000F6D RID: 3949
		public string type;

		// Token: 0x04000F6E RID: 3950
		public string moduleData;

		// Token: 0x04000F6F RID: 3951
		public string saveData;
	}

	// Token: 0x02000258 RID: 600
	public enum UnlockType
	{
		// Token: 0x04000F71 RID: 3953
		none,
		// Token: 0x04000F72 RID: 3954
		progression,
		// Token: 0x04000F73 RID: 3955
		stat
	}
}
