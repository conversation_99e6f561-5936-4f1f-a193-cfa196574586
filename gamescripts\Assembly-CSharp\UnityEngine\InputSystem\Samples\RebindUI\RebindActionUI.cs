﻿using System;
using System.Collections.Generic;
using UnityEngine.Events;
using UnityEngine.UI;

namespace UnityEngine.InputSystem.Samples.RebindUI
{
	// Token: 0x020001DE RID: 478
	public class RebindActionUI : MonoBehaviour
	{
		// Token: 0x17000017 RID: 23
		// (get) Token: 0x06000C2A RID: 3114 RVA: 0x0004D0E8 File Offset: 0x0004B2E8
		// (set) Token: 0x06000C2B RID: 3115 RVA: 0x0004D0F0 File Offset: 0x0004B2F0
		public InputActionReference actionReference
		{
			get
			{
				return this.m_Action;
			}
			set
			{
				this.m_Action = value;
				this.UpdateActionLabel();
				this.UpdateBindingDisplay();
			}
		}

		// Token: 0x17000018 RID: 24
		// (get) Token: 0x06000C2C RID: 3116 RVA: 0x0004D105 File Offset: 0x0004B305
		// (set) Token: 0x06000C2D RID: 3117 RVA: 0x0004D10D File Offset: 0x0004B30D
		public string bindingId
		{
			get
			{
				return this.m_BindingId;
			}
			set
			{
				this.m_BindingId = value;
				this.UpdateBindingDisplay();
			}
		}

		// Token: 0x17000019 RID: 25
		// (get) Token: 0x06000C2E RID: 3118 RVA: 0x0004D11C File Offset: 0x0004B31C
		// (set) Token: 0x06000C2F RID: 3119 RVA: 0x0004D124 File Offset: 0x0004B324
		public InputBinding.DisplayStringOptions displayStringOptions
		{
			get
			{
				return this.m_DisplayStringOptions;
			}
			set
			{
				this.m_DisplayStringOptions = value;
				this.UpdateBindingDisplay();
			}
		}

		// Token: 0x1700001A RID: 26
		// (get) Token: 0x06000C30 RID: 3120 RVA: 0x0004D133 File Offset: 0x0004B333
		// (set) Token: 0x06000C31 RID: 3121 RVA: 0x0004D13B File Offset: 0x0004B33B
		public Text actionLabel
		{
			get
			{
				return this.m_ActionLabel;
			}
			set
			{
				this.m_ActionLabel = value;
				this.UpdateActionLabel();
			}
		}

		// Token: 0x1700001B RID: 27
		// (get) Token: 0x06000C32 RID: 3122 RVA: 0x0004D14A File Offset: 0x0004B34A
		// (set) Token: 0x06000C33 RID: 3123 RVA: 0x0004D152 File Offset: 0x0004B352
		public Text bindingText
		{
			get
			{
				return this.m_BindingText;
			}
			set
			{
				this.m_BindingText = value;
				this.UpdateBindingDisplay();
			}
		}

		// Token: 0x1700001C RID: 28
		// (get) Token: 0x06000C34 RID: 3124 RVA: 0x0004D161 File Offset: 0x0004B361
		// (set) Token: 0x06000C35 RID: 3125 RVA: 0x0004D169 File Offset: 0x0004B369
		public Text rebindPrompt
		{
			get
			{
				return this.m_RebindText;
			}
			set
			{
				this.m_RebindText = value;
			}
		}

		// Token: 0x1700001D RID: 29
		// (get) Token: 0x06000C36 RID: 3126 RVA: 0x0004D172 File Offset: 0x0004B372
		// (set) Token: 0x06000C37 RID: 3127 RVA: 0x0004D17A File Offset: 0x0004B37A
		public GameObject rebindOverlay
		{
			get
			{
				return this.m_RebindOverlay;
			}
			set
			{
				this.m_RebindOverlay = value;
			}
		}

		// Token: 0x1700001E RID: 30
		// (get) Token: 0x06000C38 RID: 3128 RVA: 0x0004D183 File Offset: 0x0004B383
		public RebindActionUI.UpdateBindingUIEvent updateBindingUIEvent
		{
			get
			{
				if (this.m_UpdateBindingUIEvent == null)
				{
					this.m_UpdateBindingUIEvent = new RebindActionUI.UpdateBindingUIEvent();
				}
				return this.m_UpdateBindingUIEvent;
			}
		}

		// Token: 0x1700001F RID: 31
		// (get) Token: 0x06000C39 RID: 3129 RVA: 0x0004D19E File Offset: 0x0004B39E
		public RebindActionUI.InteractiveRebindEvent startRebindEvent
		{
			get
			{
				if (this.m_RebindStartEvent == null)
				{
					this.m_RebindStartEvent = new RebindActionUI.InteractiveRebindEvent();
				}
				return this.m_RebindStartEvent;
			}
		}

		// Token: 0x17000020 RID: 32
		// (get) Token: 0x06000C3A RID: 3130 RVA: 0x0004D1B9 File Offset: 0x0004B3B9
		public RebindActionUI.InteractiveRebindEvent stopRebindEvent
		{
			get
			{
				if (this.m_RebindStopEvent == null)
				{
					this.m_RebindStopEvent = new RebindActionUI.InteractiveRebindEvent();
				}
				return this.m_RebindStopEvent;
			}
		}

		// Token: 0x17000021 RID: 33
		// (get) Token: 0x06000C3B RID: 3131 RVA: 0x0004D1D4 File Offset: 0x0004B3D4
		public InputActionRebindingExtensions.RebindingOperation ongoingRebind
		{
			get
			{
				return this.m_RebindOperation;
			}
		}

		// Token: 0x06000C3C RID: 3132 RVA: 0x0004D1DC File Offset: 0x0004B3DC
		public bool ResolveActionAndBinding(out InputAction action, out int bindingIndex)
		{
			bindingIndex = -1;
			InputActionReference action2 = this.m_Action;
			action = ((action2 != null) ? action2.action : null);
			if (action == null)
			{
				return false;
			}
			if (string.IsNullOrEmpty(this.m_BindingId))
			{
				return false;
			}
			Guid bindingId = new Guid(this.m_BindingId);
			bindingIndex = action.bindings.IndexOf((InputBinding x) => x.id == bindingId);
			if (bindingIndex == -1)
			{
				Debug.LogError(string.Format("Cannot find binding with ID '{0}' on '{1}'", bindingId, action), this);
				return false;
			}
			return true;
		}

		// Token: 0x06000C3D RID: 3133 RVA: 0x0004D270 File Offset: 0x0004B470
		public void UpdateBindingDisplay()
		{
			string text = string.Empty;
			string text2 = null;
			string text3 = null;
			string text4 = "";
			InputActionReference action = this.m_Action;
			InputAction inputAction = ((action != null) ? action.action : null);
			if (inputAction != null)
			{
				int num = inputAction.bindings.IndexOf((InputBinding x) => x.id.ToString() == this.m_BindingId);
				if (num != -1)
				{
					text = inputAction.GetBindingDisplayString(num, out text2, out text3, this.displayStringOptions);
					text4 = inputAction.bindings[num].effectivePath;
				}
			}
			if (this.m_BindingText != null)
			{
				this.m_BindingText.text = text;
			}
			RebindActionUI.UpdateBindingUIEvent updateBindingUIEvent = this.m_UpdateBindingUIEvent;
			if (updateBindingUIEvent != null)
			{
				updateBindingUIEvent.Invoke(this, text, text2, text3);
			}
			this.bindingString = text4 + text;
		}

		// Token: 0x06000C3E RID: 3134 RVA: 0x0004D334 File Offset: 0x0004B534
		public void ResetToDefault()
		{
			InputAction inputAction;
			int num;
			if (!this.ResolveActionAndBinding(out inputAction, out num))
			{
				return;
			}
			if (inputAction.bindings[num].isComposite)
			{
				for (int i = num + 1; i < inputAction.bindings.Count; i++)
				{
					if (!inputAction.bindings[i].isPartOfComposite)
					{
						break;
					}
					inputAction.RemoveBindingOverride(i);
				}
			}
			else
			{
				inputAction.RemoveBindingOverride(num);
			}
			this.UpdateBindingDisplay();
			InputManager.RefreshActionStringDict();
		}

		// Token: 0x06000C3F RID: 3135 RVA: 0x0004D3BC File Offset: 0x0004B5BC
		public void StartInteractiveRebind()
		{
			InputAction inputAction;
			int num;
			if (!this.ResolveActionAndBinding(out inputAction, out num))
			{
				return;
			}
			if (inputAction.bindings[num].isComposite)
			{
				int num2 = num + 1;
				if (num2 < inputAction.bindings.Count && inputAction.bindings[num2].isPartOfComposite)
				{
					this.PerformInteractiveRebind(inputAction, num2, true);
					return;
				}
			}
			else
			{
				this.PerformInteractiveRebind(inputAction, num, false);
			}
		}

		// Token: 0x06000C40 RID: 3136 RVA: 0x0004D434 File Offset: 0x0004B634
		private void PerformInteractiveRebind(InputAction action, int bindingIndex, bool allCompositeParts = false)
		{
			InputActionRebindingExtensions.RebindingOperation rebindOperation = this.m_RebindOperation;
			if (rebindOperation != null)
			{
				rebindOperation.Cancel();
			}
			action.Disable();
			this.m_RebindOperation = action.PerformInteractiveRebinding(bindingIndex).OnCancel(delegate(InputActionRebindingExtensions.RebindingOperation operation)
			{
				RebindActionUI.InteractiveRebindEvent rebindStopEvent = this.m_RebindStopEvent;
				if (rebindStopEvent != null)
				{
					rebindStopEvent.Invoke(this, operation);
				}
				GameObject rebindOverlay2 = this.m_RebindOverlay;
				if (rebindOverlay2 != null)
				{
					rebindOverlay2.SetActive(false);
				}
				this.UpdateBindingDisplay();
				base.<PerformInteractiveRebind>g__CleanUp|0();
			}).OnComplete(delegate(InputActionRebindingExtensions.RebindingOperation operation)
			{
				GameObject rebindOverlay3 = this.m_RebindOverlay;
				if (rebindOverlay3 != null)
				{
					rebindOverlay3.SetActive(false);
				}
				RebindActionUI.InteractiveRebindEvent rebindStopEvent2 = this.m_RebindStopEvent;
				if (rebindStopEvent2 != null)
				{
					rebindStopEvent2.Invoke(this, operation);
				}
				this.UpdateBindingDisplay();
				base.<PerformInteractiveRebind>g__CleanUp|0();
				InputManager.RefreshActionStringDict();
				if (allCompositeParts)
				{
					int num = bindingIndex + 1;
					if (num < action.bindings.Count && action.bindings[num].isPartOfComposite)
					{
						this.PerformInteractiveRebind(action, num, true);
					}
				}
			})
				.OnPotentialMatch(delegate(InputActionRebindingExtensions.RebindingOperation operation)
				{
					Debug.Log("Checking match: " + operation.selectedControl.path);
					string text3 = "Checking match: ";
					InputControl selectedControl = operation.selectedControl;
					Debug.Log(text3 + ((selectedControl != null) ? selectedControl.ToString() : null));
					if (operation.selectedControl.path == "/Keyboard/escape")
					{
						Debug.Log("Cancelling Rebind. Got this far.");
						operation.Cancel();
						return;
					}
					if (operation.selectedControl.path == "/Gamepad/select")
					{
						Debug.Log("Cancelling Rebind. Got this far.");
						operation.Cancel();
						return;
					}
					if (operation.selectedControl.parent.name == "Gamepad")
					{
						operation.Cancel();
						return;
					}
				});
			string text = null;
			if (action.bindings[bindingIndex].isPartOfComposite)
			{
				text = "Binding '" + action.bindings[bindingIndex].name + "'. ";
			}
			GameObject rebindOverlay = this.m_RebindOverlay;
			if (rebindOverlay != null)
			{
				rebindOverlay.SetActive(true);
			}
			if (this.m_RebindText != null)
			{
				string text2 = ((!string.IsNullOrEmpty(this.m_RebindOperation.expectedControlType)) ? (text + "Waiting for " + this.m_RebindOperation.expectedControlType + " input...") : (text + "Waiting for input..."));
				this.m_RebindText.text = text2;
			}
			if (this.m_RebindOverlay == null && this.m_RebindText == null && this.m_RebindStartEvent == null && this.m_BindingText != null)
			{
				this.m_BindingText.text = "<Waiting...>";
			}
			RebindActionUI.InteractiveRebindEvent rebindStartEvent = this.m_RebindStartEvent;
			if (rebindStartEvent != null)
			{
				rebindStartEvent.Invoke(this, this.m_RebindOperation);
			}
			this.m_RebindOperation.Start();
			action.Enable();
		}

		// Token: 0x06000C41 RID: 3137 RVA: 0x0004D60C File Offset: 0x0004B80C
		protected void OnEnable()
		{
			if (RebindActionUI.s_RebindActionUIs == null)
			{
				RebindActionUI.s_RebindActionUIs = new List<RebindActionUI>();
			}
			RebindActionUI.s_RebindActionUIs.Add(this);
			if (RebindActionUI.s_RebindActionUIs.Count == 1)
			{
				InputSystem.onActionChange += RebindActionUI.OnActionChange;
			}
			this.UpdateBindingDisplay();
			RebindActionUI.CheckOverlapping();
		}

		// Token: 0x06000C42 RID: 3138 RVA: 0x0004D660 File Offset: 0x0004B860
		protected void OnDisable()
		{
			InputActionRebindingExtensions.RebindingOperation rebindOperation = this.m_RebindOperation;
			if (rebindOperation != null)
			{
				rebindOperation.Dispose();
			}
			this.m_RebindOperation = null;
			RebindActionUI.s_RebindActionUIs.Remove(this);
			if (RebindActionUI.s_RebindActionUIs.Count == 0)
			{
				RebindActionUI.s_RebindActionUIs = null;
				InputSystem.onActionChange -= RebindActionUI.OnActionChange;
			}
		}

		// Token: 0x06000C43 RID: 3139 RVA: 0x0004D6B4 File Offset: 0x0004B8B4
		private static void CheckOverlapping()
		{
			for (int i = 0; i < RebindActionUI.s_RebindActionUIs.Count; i++)
			{
				RebindActionUI rebindActionUI = RebindActionUI.s_RebindActionUIs[i];
				rebindActionUI.bindingText.color = Color.white;
				for (int j = 0; j < RebindActionUI.s_RebindActionUIs.Count; j++)
				{
					if (i != j && RebindActionUI.s_RebindActionUIs[j].GetCurrentBindingString() == rebindActionUI.GetCurrentBindingString() && RebindActionUI.s_RebindActionUIs[j].GetCurrentBindingString() != "")
					{
						RebindActionUI.s_RebindActionUIs[j].bindingText.color = Color.red;
						rebindActionUI.bindingText.color = Color.red;
						break;
					}
				}
			}
		}

		// Token: 0x06000C44 RID: 3140 RVA: 0x0004D778 File Offset: 0x0004B978
		private static void OnActionChange(object obj, InputActionChange change)
		{
			if (change != InputActionChange.BoundControlsChanged)
			{
				return;
			}
			InputAction inputAction = obj as InputAction;
			InputActionMap inputActionMap = ((inputAction != null) ? inputAction.actionMap : null) ?? (obj as InputActionMap);
			InputActionAsset inputActionAsset = ((inputActionMap != null) ? inputActionMap.asset : null) ?? (obj as InputActionAsset);
			for (int i = 0; i < RebindActionUI.s_RebindActionUIs.Count; i++)
			{
				RebindActionUI rebindActionUI = RebindActionUI.s_RebindActionUIs[i];
				InputActionReference actionReference = rebindActionUI.actionReference;
				InputAction inputAction2 = ((actionReference != null) ? actionReference.action : null);
				if (inputAction2 != null)
				{
					if (inputAction2 != inputAction && inputAction2.actionMap != inputActionMap)
					{
						InputActionMap actionMap = inputAction2.actionMap;
						if (!(((actionMap != null) ? actionMap.asset : null) == inputActionAsset))
						{
							goto IL_0095;
						}
					}
					rebindActionUI.UpdateBindingDisplay();
				}
				IL_0095:;
			}
			RebindActionUI.CheckOverlapping();
		}

		// Token: 0x06000C45 RID: 3141 RVA: 0x0004D830 File Offset: 0x0004BA30
		private void UpdateActionLabel()
		{
			InputActionReference action = this.m_Action;
			InputAction inputAction = ((action != null) ? action.action : null);
			string text = ((inputAction != null) ? inputAction.name : string.Empty);
			if (this.m_ActionLabel != null)
			{
				this.m_ActionLabel.text = text;
			}
		}

		// Token: 0x06000C46 RID: 3142 RVA: 0x0004D87B File Offset: 0x0004BA7B
		public string GetCurrentBindingString()
		{
			return this.bindingString;
		}

		// Token: 0x04000D06 RID: 3334
		private string bindingString;

		// Token: 0x04000D07 RID: 3335
		private string bindCheckString;

		// Token: 0x04000D08 RID: 3336
		[Tooltip("Reference to action that is to be rebound from the UI.")]
		[SerializeField]
		private InputActionReference m_Action;

		// Token: 0x04000D09 RID: 3337
		[SerializeField]
		private string m_BindingId;

		// Token: 0x04000D0A RID: 3338
		[SerializeField]
		private InputBinding.DisplayStringOptions m_DisplayStringOptions;

		// Token: 0x04000D0B RID: 3339
		[Tooltip("Text label that will receive the name of the action. Optional. Set to None to have the rebind UI not show a label for the action.")]
		[SerializeField]
		private Text m_ActionLabel;

		// Token: 0x04000D0C RID: 3340
		[Tooltip("Text label that will receive the current, formatted binding string.")]
		[SerializeField]
		private Text m_BindingText;

		// Token: 0x04000D0D RID: 3341
		[Tooltip("Optional UI that will be shown while a rebind is in progress.")]
		[SerializeField]
		private GameObject m_RebindOverlay;

		// Token: 0x04000D0E RID: 3342
		[Tooltip("Optional text label that will be updated with prompt for user input.")]
		[SerializeField]
		private Text m_RebindText;

		// Token: 0x04000D0F RID: 3343
		[Tooltip("Event that is triggered when the way the binding is display should be updated. This allows displaying bindings in custom ways, e.g. using images instead of text.")]
		[SerializeField]
		private RebindActionUI.UpdateBindingUIEvent m_UpdateBindingUIEvent;

		// Token: 0x04000D10 RID: 3344
		[Tooltip("Event that is triggered when an interactive rebind is being initiated. This can be used, for example, to implement custom UI behavior while a rebind is in progress. It can also be used to further customize the rebind.")]
		[SerializeField]
		private RebindActionUI.InteractiveRebindEvent m_RebindStartEvent;

		// Token: 0x04000D11 RID: 3345
		[Tooltip("Event that is triggered when an interactive rebind is complete or has been aborted.")]
		[SerializeField]
		private RebindActionUI.InteractiveRebindEvent m_RebindStopEvent;

		// Token: 0x04000D12 RID: 3346
		private InputActionRebindingExtensions.RebindingOperation m_RebindOperation;

		// Token: 0x04000D13 RID: 3347
		private static List<RebindActionUI> s_RebindActionUIs;

		// Token: 0x020002F1 RID: 753
		[Serializable]
		public class UpdateBindingUIEvent : UnityEvent<RebindActionUI, string, string, string>
		{
		}

		// Token: 0x020002F2 RID: 754
		[Serializable]
		public class InteractiveRebindEvent : UnityEvent<RebindActionUI, InputActionRebindingExtensions.RebindingOperation>
		{
		}
	}
}
