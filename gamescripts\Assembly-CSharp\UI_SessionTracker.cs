﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200013C RID: 316
public class UI_SessionTracker : MonoBehaviour
{
	// Token: 0x0600092C RID: 2348 RVA: 0x0003F934 File Offset: 0x0003DB34
	private void Start()
	{
		this.eventIconDict = new Dictionary<string, UI_SessionTracker.EventIcons>();
		foreach (UI_SessionTracker.EventIcons eventIcons in this.eventIcons)
		{
			this.eventIconDict.Add(eventIcons.name, eventIcons);
		}
		this.trackedObjectsDict = new Dictionary<string, UI_SessionTracker.ObjectTracker>();
		this.trackedObjects = new List<UI_SessionTracker.ObjectTracker>();
	}

	// Token: 0x0600092D RID: 2349 RVA: 0x0003F9B4 File Offset: 0x0003DBB4
	private void Update()
	{
		if (Input.GetKeyDown(KeyCode.Quote))
		{
			this.StartShowcase();
		}
		if (this.loadedSession != null)
		{
			this.UpdateShowcase();
		}
	}

	// Token: 0x0600092E RID: 2350 RVA: 0x0003F9D4 File Offset: 0x0003DBD4
	private void UpdateShowcase()
	{
		this.readTime += Time.unscaledDeltaTime * this.playbackSpeed;
		if (this.sessionTickQueue.Count == 0)
		{
			return;
		}
		CL_GameTracker.Session.Tick tick = this.sessionTickQueue.Peek();
		if (tick.timeStamp < this.readTime)
		{
			this.ShowcaseTick(tick);
			this.sessionTickQueue.Dequeue();
		}
		foreach (UI_SessionTracker.ObjectTracker objectTracker in this.trackedObjects)
		{
			objectTracker.Update();
		}
	}

	// Token: 0x0600092F RID: 2351 RVA: 0x0003FA78 File Offset: 0x0003DC78
	private void ShowcaseTick(CL_GameTracker.Session.Tick tick)
	{
		if (tick.events.Count > 0)
		{
			for (int i = 0; i < tick.events.Count; i++)
			{
				this.ShowcaseEvent(tick.events[i]);
			}
		}
	}

	// Token: 0x06000930 RID: 2352 RVA: 0x0003FABC File Offset: 0x0003DCBC
	private void ShowcaseEvent(CL_GameTracker.Session.Tick.Event curEvent)
	{
		string[] array = curEvent.id.Split(":", StringSplitOptions.None);
		if (curEvent.type == CL_GameTracker.Session.Tick.Event.EventType.spawn)
		{
			UI_SessionTracker.ObjectTracker objectTracker = new UI_SessionTracker.ObjectTracker();
			objectTracker.id = array[1];
			objectTracker.name = array[0];
			objectTracker.objectType = curEvent.objectType;
			if (this.eventIconDict.ContainsKey(curEvent.objectType))
			{
				objectTracker.curIcon = this.eventIconDict[curEvent.objectType];
			}
			else
			{
				objectTracker.curIcon = this.eventIcons[0];
			}
			Vector3 vector = this.ConvertPositionVector((Vector3)CL_GameTracker.DecomposeData(curEvent));
			objectTracker.trackerGameObject = Object.Instantiate<GameObject>(this.trackerObject, base.transform);
			objectTracker.trackerGameObject.name = string.Concat(new string[] { objectTracker.objectType, " ", objectTracker.name, " ", objectTracker.id });
			objectTracker.trackerGameObject.transform.localPosition = vector;
			objectTracker.SetTargetPosition(vector);
			objectTracker.Initialize();
			this.trackedObjectsDict.Add(objectTracker.id, objectTracker);
			this.trackedObjects.Add(objectTracker);
			return;
		}
		UI_SessionTracker.ObjectTracker objectTracker2 = this.trackedObjectsDict[array[1]];
		if (curEvent.type == CL_GameTracker.Session.Tick.Event.EventType.destroy || curEvent.type == CL_GameTracker.Session.Tick.Event.EventType.kill)
		{
			Vector3 vector2 = this.ConvertPositionVector((Vector3)CL_GameTracker.DecomposeData(curEvent));
			objectTracker2.SetIcon(objectTracker2.curIcon.destroyIcon);
			objectTracker2.SetTargetPosition(vector2);
			return;
		}
		if (curEvent.type == CL_GameTracker.Session.Tick.Event.EventType.move)
		{
			Vector3 vector3 = this.ConvertPositionVector((Vector3)CL_GameTracker.DecomposeData(curEvent));
			objectTracker2.SetTargetPosition(vector3);
		}
	}

	// Token: 0x06000931 RID: 2353 RVA: 0x0003FC64 File Offset: 0x0003DE64
	private Vector3 ConvertPositionVector(Vector3 input)
	{
		float num = Mathf.Abs(this.startPosition.localPosition.y - this.endPosition.localPosition.y);
		Vector3 vector = input / this.loadedSession.maxHeight * num + this.startPosition.localPosition;
		vector.x = (vector.z + vector.x) * 0.7f;
		vector.z = 0f;
		return vector;
	}

	// Token: 0x06000932 RID: 2354 RVA: 0x0003FCE7 File Offset: 0x0003DEE7
	public void StartShowcase()
	{
		this.readTime = 0f;
		this.loadedSession = CL_GameTracker.LoadData();
		this.sessionTickQueue = new Queue<CL_GameTracker.Session.Tick>(this.loadedSession.ticks);
	}

	// Token: 0x04000A71 RID: 2673
	public GameObject trackerObject;

	// Token: 0x04000A72 RID: 2674
	public Transform startPosition;

	// Token: 0x04000A73 RID: 2675
	public Transform endPosition;

	// Token: 0x04000A74 RID: 2676
	public List<UI_SessionTracker.EventIcons> eventIcons;

	// Token: 0x04000A75 RID: 2677
	public Dictionary<string, UI_SessionTracker.EventIcons> eventIconDict;

	// Token: 0x04000A76 RID: 2678
	public List<UI_SessionTracker.ObjectTracker> trackedObjects;

	// Token: 0x04000A77 RID: 2679
	public Dictionary<string, UI_SessionTracker.ObjectTracker> trackedObjectsDict;

	// Token: 0x04000A78 RID: 2680
	private CL_GameTracker.Session loadedSession;

	// Token: 0x04000A79 RID: 2681
	private float readTime;

	// Token: 0x04000A7A RID: 2682
	public float playbackSpeed = 1f;

	// Token: 0x04000A7B RID: 2683
	private Queue<CL_GameTracker.Session.Tick> sessionTickQueue;

	// Token: 0x020002C1 RID: 705
	[Serializable]
	public class EventIcons
	{
		// Token: 0x040011B4 RID: 4532
		public string name;

		// Token: 0x040011B5 RID: 4533
		public Sprite icon;

		// Token: 0x040011B6 RID: 4534
		public Sprite destroyIcon;
	}

	// Token: 0x020002C2 RID: 706
	public class SpecialObjectType
	{
		// Token: 0x040011B7 RID: 4535
		public string objectType;

		// Token: 0x040011B8 RID: 4536
		public GameObject specialGameObject;
	}

	// Token: 0x020002C3 RID: 707
	public class ObjectTracker
	{
		// Token: 0x06000F09 RID: 3849 RVA: 0x00059EDC File Offset: 0x000580DC
		public void Initialize()
		{
			this.spriteRenderer = this.trackerGameObject.GetComponent<Image>();
			this.spriteRenderer.sprite = this.curIcon.icon;
			if (this.curIcon.icon == null)
			{
				this.spriteRenderer.enabled = false;
			}
		}

		// Token: 0x06000F0A RID: 3850 RVA: 0x00059F2F File Offset: 0x0005812F
		public void Update()
		{
			this.trackerGameObject.transform.localPosition = Vector3.Lerp(this.trackerGameObject.transform.localPosition, this.targetPosition, Time.unscaledDeltaTime * 3f);
		}

		// Token: 0x06000F0B RID: 3851 RVA: 0x00059F67 File Offset: 0x00058167
		public void SetTargetPosition(Vector3 pos)
		{
			this.targetPosition = pos;
		}

		// Token: 0x06000F0C RID: 3852 RVA: 0x00059F70 File Offset: 0x00058170
		public void SetIcon(Sprite sprite)
		{
			this.spriteRenderer.sprite = sprite;
			if (this.spriteRenderer.sprite == null)
			{
				this.spriteRenderer.enabled = false;
				return;
			}
			this.spriteRenderer.enabled = true;
		}

		// Token: 0x040011B9 RID: 4537
		public string name;

		// Token: 0x040011BA RID: 4538
		public string id;

		// Token: 0x040011BB RID: 4539
		public string objectType;

		// Token: 0x040011BC RID: 4540
		public UI_SessionTracker.EventIcons curIcon;

		// Token: 0x040011BD RID: 4541
		public GameObject trackerGameObject;

		// Token: 0x040011BE RID: 4542
		private Vector3 targetPosition;

		// Token: 0x040011BF RID: 4543
		private Image spriteRenderer;
	}
}
