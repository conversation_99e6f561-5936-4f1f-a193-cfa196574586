﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000184 RID: 388
public class UT_PlayEvents : MonoBehaviour
{
	// Token: 0x06000AAC RID: 2732 RVA: 0x00046443 File Offset: 0x00044643
	private void Awake()
	{
		if (this.playOnStart)
		{
			this.PlayEvent();
		}
	}

	// Token: 0x06000AAD RID: 2733 RVA: 0x00046453 File Offset: 0x00044653
	private void OnEnable()
	{
		if (this.playOnStart)
		{
			this.PlayEvent();
		}
	}

	// Token: 0x06000AAE RID: 2734 RVA: 0x00046463 File Offset: 0x00044663
	public void PlayEvent()
	{
		this.playEvent.Invoke();
	}

	// Token: 0x04000BAC RID: 2988
	public UnityEvent playEvent;

	// Token: 0x04000BAD RID: 2989
	public bool playOnStart;
}
