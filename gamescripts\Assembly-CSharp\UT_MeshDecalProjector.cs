﻿using System;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x02000180 RID: 384
[ExecuteInEditMode]
public class UT_MeshDecalProjector : MonoBehaviourGizmos
{
	// Token: 0x06000A92 RID: 2706 RVA: 0x000452A0 File Offset: 0x000434A0
	private void Start()
	{
		RaycastHit raycastHit;
		if (this.projectOnStart && Application.isPlaying && Physics.Raycast(base.transform.position, base.transform.forward, out raycastHit, 100f))
		{
			Vector3 point = raycastHit.point;
			Vector3 normal = raycastHit.normal;
			GameObject gameObject = raycastHit.collider.gameObject;
			Debug.Log(raycastHit.collider.name);
			this.ProjectDecal(point, normal, gameObject);
		}
	}

	// Token: 0x06000A93 RID: 2707 RVA: 0x00045318 File Offset: 0x00043518
	public void ProjectDecal(Vector3 position, Vector3 normal, GameObject targetObject)
	{
		for (int i = 0; i < base.transform.childCount; i++)
		{
			Object.DestroyImmediate(base.transform.GetChild(i).gameObject);
		}
		this.decal = new GameObject("MeshDecal");
		this.decal.transform.parent = base.transform;
		this.decal.transform.position = position;
		this.decal.transform.rotation = Quaternion.LookRotation(-normal);
		this.decal.transform.localScale = Vector3.one * this.decalSize;
		MeshFilter meshFilter = this.decal.AddComponent<MeshFilter>();
		this.decal.AddComponent<MeshRenderer>().material = this.decalMaterial;
		Mesh mesh = this.GenerateDecalMesh(this.decal.transform, targetObject);
		if (mesh != null)
		{
			meshFilter.mesh = mesh;
			return;
		}
	}

	// Token: 0x06000A94 RID: 2708 RVA: 0x0004540C File Offset: 0x0004360C
	private Mesh GenerateDecalMesh(Transform decalTransform, GameObject targetObject)
	{
		Vector3 vector = new Vector3(this.decalSize * 0.5f, this.decalSize * 0.5f, this.decalDepth * 0.5f);
		Bounds bounds = new Bounds(decalTransform.position, vector * 2f);
		Renderer[] array = new Renderer[] { targetObject.GetComponent<Renderer>() };
		List<Vector3> list = new List<Vector3>();
		List<Vector3> list2 = new List<Vector3>();
		List<Vector2> list3 = new List<Vector2>();
		List<int> list4 = new List<int>();
		foreach (Renderer renderer in array)
		{
			if (renderer.bounds.Intersects(bounds) && (this.affectedLayers.value & (1 << renderer.gameObject.layer)) != 0)
			{
				MeshFilter component = renderer.GetComponent<MeshFilter>();
				if (!(component == null))
				{
					Mesh sharedMesh = component.sharedMesh;
					if (!(sharedMesh == null))
					{
						Matrix4x4 matrix4x = decalTransform.worldToLocalMatrix * component.transform.localToWorldMatrix;
						Vector3[] vertices = sharedMesh.vertices;
						Vector3[] normals = sharedMesh.normals;
						int[] triangles = sharedMesh.triangles;
						for (int j = 0; j < triangles.Length; j += 3)
						{
							Vector3[] array3 = new Vector3[3];
							Vector3[] array4 = new Vector3[3];
							for (int k = 0; k < 3; k++)
							{
								int num = triangles[j + k];
								Vector3 vector2 = vertices[num];
								Vector3 vector3 = normals[num];
								vector2 = matrix4x.MultiplyPoint(vector2 + vector3 * this.offset);
								vector3 = matrix4x.MultiplyVector(vector3);
								array3[k] = vector2;
								array4[k] = vector3;
							}
							if (this.ClipTriangleToBounds(array3, vector))
							{
								int count = list.Count;
								list.AddRange(array3);
								list2.AddRange(array4);
								for (int l = 0; l < 3; l++)
								{
									Vector2 vector4 = new Vector2(array3[l].x / this.decalSize + 0.5f, array3[l].y / this.decalSize + 0.5f);
									list3.Add(vector4);
								}
								list4.Add(count);
								list4.Add(count + 1);
								list4.Add(count + 2);
							}
						}
					}
				}
			}
		}
		if (list.Count == 0)
		{
			return null;
		}
		return new Mesh
		{
			vertices = list.ToArray(),
			normals = list2.ToArray(),
			uv = list3.ToArray(),
			triangles = list4.ToArray()
		};
	}

	// Token: 0x06000A95 RID: 2709 RVA: 0x000456B4 File Offset: 0x000438B4
	private bool ClipTriangleToBounds(Vector3[] triangle, Vector3 halfSize)
	{
		foreach (Vector3 vector in triangle)
		{
			if (Mathf.Abs(vector.x) <= halfSize.x && Mathf.Abs(vector.y) <= halfSize.y && Mathf.Abs(vector.z) <= halfSize.z)
			{
				return true;
			}
		}
		return false;
	}

	// Token: 0x06000A96 RID: 2710 RVA: 0x00045718 File Offset: 0x00043918
	public override void DrawGizmos()
	{
		if (this.projectOnStart && GizmoContext.InSelection(this))
		{
			Draw.DashedLine(base.transform.position, base.transform.position + base.transform.forward, 0.1f, 0.1f, Color.yellow);
			Draw.WireBox(base.transform.position, this.decalSize, Color.yellow);
		}
	}

	// Token: 0x04000B91 RID: 2961
	public Material decalMaterial;

	// Token: 0x04000B92 RID: 2962
	public float decalSize = 1f;

	// Token: 0x04000B93 RID: 2963
	public float decalDepth = 0.1f;

	// Token: 0x04000B94 RID: 2964
	public LayerMask affectedLayers;

	// Token: 0x04000B95 RID: 2965
	public float offset = 0.1f;

	// Token: 0x04000B96 RID: 2966
	public bool projectOnStart;

	// Token: 0x04000B97 RID: 2967
	private GameObject decal;
}
