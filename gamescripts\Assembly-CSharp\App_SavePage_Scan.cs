﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000ED RID: 237
public class App_SavePage_Scan : MonoBehaviour
{
	// Token: 0x06000756 RID: 1878 RVA: 0x000385DD File Offset: 0x000367DD
	private void Start()
	{
		this.crosshairTarget = Vector3.Scale(Random.insideUnitSphere, new Vector3(250f, 150f, 0f));
	}

	// Token: 0x06000757 RID: 1879 RVA: 0x00038604 File Offset: 0x00036804
	private void Update()
	{
		this.imageTime += Time.unscaledDeltaTime * this.imageSpeed;
		this.foregroundTime += Time.unscaledDeltaTime * this.imageSpeed;
		if (this.imageTime >= 1f)
		{
			this.image.sprite = this.sprites[Random.Range(0, this.sprites.Count)];
			this.imageTime = 0f;
			if (this.imageAudio != null && this.imageAudio.Count > 0)
			{
				AudioManager.PlayUISound(this.imageAudio[Random.Range(0, this.imageAudio.Count)], 1f, 1f);
			}
			this.crosshairTarget = Vector3.Scale(Random.insideUnitSphere, new Vector3(250f, 150f, 0f));
		}
		if (this.foregroundTime >= 0.5f)
		{
			this.foreground.sprite = this.sprites[Random.Range(0, this.sprites.Count)];
			this.foregroundTime = 0f;
			if (this.imageAudio != null && this.imageAudio.Count > 0)
			{
				AudioManager.PlayUISound(this.imageAudio[Random.Range(0, this.imageAudio.Count)], 0.5f, 1.5f);
			}
			this.crosshairRotation = (float)Random.Range(-15, 15);
		}
		this.foreground.transform.localScale = Vector3.Lerp(Vector3.one, Vector3.one * 1.2f, this.foregroundTime);
		this.image.transform.localScale = Vector3.Lerp(Vector3.one, Vector3.one * 1.3f, this.imageTime);
		if (this.crosshair != null)
		{
			this.crosshair.localPosition = Vector3.Lerp(this.crosshair.localPosition, this.crosshairTarget, Time.unscaledDeltaTime * 4f);
			this.crosshair.rotation = Quaternion.Lerp(this.crosshair.rotation, Quaternion.Euler(0f, 0f, this.crosshairRotation), Time.unscaledDeltaTime * 5f);
		}
	}

	// Token: 0x040008D1 RID: 2257
	public Transform crosshair;

	// Token: 0x040008D2 RID: 2258
	public Image image;

	// Token: 0x040008D3 RID: 2259
	public Image foreground;

	// Token: 0x040008D4 RID: 2260
	public List<Sprite> sprites;

	// Token: 0x040008D5 RID: 2261
	public List<Sprite> foregroundSprites;

	// Token: 0x040008D6 RID: 2262
	private float imageTime = 1f;

	// Token: 0x040008D7 RID: 2263
	private float foregroundTime = 0.3f;

	// Token: 0x040008D8 RID: 2264
	public float imageSpeed = 1f;

	// Token: 0x040008D9 RID: 2265
	public List<AudioClip> imageAudio;

	// Token: 0x040008DA RID: 2266
	private Vector3 crosshairTarget;

	// Token: 0x040008DB RID: 2267
	private float crosshairRotation;
}
