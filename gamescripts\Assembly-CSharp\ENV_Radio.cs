﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000050 RID: 80
public class ENV_Radio : MonoBehaviour
{
	// Token: 0x06000353 RID: 851 RVA: 0x00021382 File Offset: 0x0001F582
	private void Start()
	{
		if (this.playOnStart)
		{
			this.Play();
		}
	}

	// Token: 0x06000354 RID: 852 RVA: 0x00021394 File Offset: 0x0001F594
	public void Play()
	{
		if (this.useRadioStation)
		{
			if (this.currentTrack == null || this.randomTrackEachPlay)
			{
				this.currentTrack = this.radioStation.GetRandomTrack();
			}
			this.aud.clip = this.currentTrack.trackClip;
		}
		else
		{
			this.aud.clip = this.radioClip;
		}
		this.aud.Play();
		this.toggleOnEvent.Invoke();
	}

	// Token: 0x06000355 RID: 853 RVA: 0x00021409 File Offset: 0x0001F609
	public void Stop()
	{
		this.aud.Stop();
		this.toggleOffEvent.Invoke();
	}

	// Token: 0x06000356 RID: 854 RVA: 0x00021421 File Offset: 0x0001F621
	public void Toggle()
	{
		this.toggleEvent.Invoke();
		if (this.aud.isPlaying)
		{
			this.Stop();
			return;
		}
		this.Play();
	}

	// Token: 0x04000491 RID: 1169
	public bool playOnStart = true;

	// Token: 0x04000492 RID: 1170
	public bool useRadioStation;

	// Token: 0x04000493 RID: 1171
	public AudioClip radioClip;

	// Token: 0x04000494 RID: 1172
	public ENV_RadioStation radioStation;

	// Token: 0x04000495 RID: 1173
	public bool randomTrackEachPlay;

	// Token: 0x04000496 RID: 1174
	public UnityEvent toggleOnEvent;

	// Token: 0x04000497 RID: 1175
	public UnityEvent toggleOffEvent;

	// Token: 0x04000498 RID: 1176
	public UnityEvent toggleEvent;

	// Token: 0x04000499 RID: 1177
	public AudioSource aud;

	// Token: 0x0400049A RID: 1178
	private ENV_RadioStation.RadioTrack currentTrack;
}
