﻿using System;
using System.Collections;
using System.Runtime.CompilerServices;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000121 RID: 289
public class UI_CloseButtonOnBack : MonoBehaviour
{
	// Token: 0x0600089B RID: 2203 RVA: 0x0003D829 File Offset: 0x0003BA29
	private void Start()
	{
		this.button = base.GetComponent<Button>();
	}

	// Token: 0x0600089C RID: 2204 RVA: 0x0003D837 File Offset: 0x0003BA37
	private void OnEnable()
	{
		base.StartCoroutine(this.<OnEnable>g__FrameWait|3_0());
	}

	// Token: 0x0600089D RID: 2205 RVA: 0x0003D846 File Offset: 0x0003BA46
	private void OnDisable()
	{
		this.active = false;
	}

	// Token: 0x0600089E RID: 2206 RVA: 0x0003D84F File Offset: 0x0003BA4F
	private void Update()
	{
		if (!this.active)
		{
			return;
		}
		if (this.button.IsInteractable() && InputManager.GetButton("UIBackButton").Down)
		{
			this.button.onClick.Invoke();
		}
	}

	// Token: 0x060008A0 RID: 2208 RVA: 0x0003D890 File Offset: 0x0003BA90
	[CompilerGenerated]
	private IEnumerator <OnEnable>g__FrameWait|3_0()
	{
		yield return new WaitForSecondsRealtime(0.1f);
		this.active = true;
		yield break;
	}

	// Token: 0x040009EB RID: 2539
	private Button button;

	// Token: 0x040009EC RID: 2540
	private bool active;
}
