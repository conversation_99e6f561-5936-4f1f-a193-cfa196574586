﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200001E RID: 30
public class CL_ProgressionManager : MonoBehaviour
{
	// Token: 0x06000129 RID: 297 RVA: 0x0000A000 File Offset: 0x00008200
	private void Awake()
	{
		this.Initialize();
	}

	// Token: 0x0600012A RID: 298 RVA: 0x0000A008 File Offset: 0x00008208
	internal void Initialize()
	{
		CL_ProgressionManager.sessionUnlocks = new List<ProgressionUnlock>();
		CL_ProgressionManager.sessionPopups = new List<CL_ProgressionManager.PopupInfo>();
		if (this.initialized)
		{
			return;
		}
		if (CL_ProgressionManager.instance != null)
		{
			Object.Destroy(base.gameObject);
			return;
		}
		CL_ProgressionManager.instance = this;
		CL_ProgressionManager.progressionDict = new Dictionary<string, ProgressionUnlock>();
		foreach (ProgressionUnlock progressionUnlock in this.unlockList)
		{
			progressionUnlock.TryUpgrade();
			if (SettingsManager.settings.unlockAll)
			{
				progressionUnlock.state = true;
			}
			else
			{
				progressionUnlock.state = false;
			}
			CL_ProgressionManager.progressionDict.Add(progressionUnlock.name, progressionUnlock);
		}
		CL_ProgressionManager.RefreshExperience();
		this.initialized = true;
	}

	// Token: 0x0600012B RID: 299 RVA: 0x0000A0DC File Offset: 0x000082DC
	private void OnDestroy()
	{
		if (CL_ProgressionManager.instance == this)
		{
			CL_ProgressionManager.instance = null;
		}
	}

	// Token: 0x0600012C RID: 300 RVA: 0x0000A0F1 File Offset: 0x000082F1
	public static bool HasProgressionUnlock(string unlockName)
	{
		return CL_ProgressionManager.progressionDict != null && CL_ProgressionManager.progressionDict.ContainsKey(unlockName) && CL_ProgressionManager.progressionDict[unlockName].state;
	}

	// Token: 0x0600012D RID: 301 RVA: 0x0000A11C File Offset: 0x0000831C
	public static void UpdateUnlocks(bool showPopups = false)
	{
		foreach (ProgressionUnlock progressionUnlock in CL_ProgressionManager.instance.unlockList)
		{
			if (!progressionUnlock.state && progressionUnlock.CheckUnlock())
			{
				if (showPopups)
				{
					CL_ProgressionManager.ShowUnlockPopup(progressionUnlock);
				}
			}
			else if (!progressionUnlock.state && progressionUnlock.showProgression && progressionUnlock.GetLastProgress() < progressionUnlock.GetProgress() && showPopups)
			{
				CL_ProgressionManager.ShowUnlockProgress(progressionUnlock.unlockIcon, progressionUnlock.unlockTitle, "Progress: " + progressionUnlock.GetProgressString() + " " + progressionUnlock.progressionString);
			}
		}
	}

	// Token: 0x0600012E RID: 302 RVA: 0x0000A1D4 File Offset: 0x000083D4
	public static void AddExperience(int i)
	{
		CL_ProgressionManager.playerExperience += i;
		StatManager.sessionStats.SetStatistic("progression-experience", CL_ProgressionManager.playerExperience, StatManager.Statistic.DataType.Int);
		if (DebugMenu.initialized)
		{
			DebugMenu.UpdateDebugText("player-experience", "<color=yellow>Player Experience: " + CL_ProgressionManager.playerExperience.ToString());
		}
	}

	// Token: 0x0600012F RID: 303 RVA: 0x0000A234 File Offset: 0x00008434
	public static void RefreshExperience()
	{
		CL_ProgressionManager.playerExperience = StatManager.GetStatisticInt(StatManager.saveData.gameStats, "progression-experience");
		CL_ProgressionManager.UpdateUnlocks(false);
		if (DebugMenu.initialized)
		{
			DebugMenu.UpdateDebugText("player-experience", "<color=yellow>Player Experience: " + CL_ProgressionManager.playerExperience.ToString());
		}
	}

	// Token: 0x06000130 RID: 304 RVA: 0x0000A28A File Offset: 0x0000848A
	public static void ShowUnlockPopup(ProgressionUnlock unlock)
	{
		CL_ProgressionManager.ShowUnlockPopup(unlock.unlockIcon, unlock.unlockTitle, unlock.unlockDescription);
	}

	// Token: 0x06000131 RID: 305 RVA: 0x0000A2A4 File Offset: 0x000084A4
	public static void ShowUnlockPopup(Sprite icon, string title, string desc)
	{
		if (CL_ProgressionManager.instance == null || CL_ProgressionManager.instance.popupAsset == null)
		{
			return;
		}
		CL_ProgressionManager.PopupInfo popupInfo = new CL_ProgressionManager.PopupInfo
		{
			icon = icon,
			title = title,
			desc = desc
		};
		CL_ProgressionManager.sessionPopups.Add(popupInfo);
		CL_ProgressionManager.instance.popupAsset.Popup(icon, title, desc);
	}

	// Token: 0x06000132 RID: 306 RVA: 0x0000A30C File Offset: 0x0000850C
	public static void ShowUnlockProgress(Sprite icon, string title, string desc)
	{
		if (CL_ProgressionManager.instance == null || CL_ProgressionManager.instance.popupAsset == null)
		{
			return;
		}
		CL_ProgressionManager.PopupInfo popupInfo = new CL_ProgressionManager.PopupInfo();
		popupInfo.icon = icon;
		popupInfo.title = title;
		popupInfo.desc = desc;
		CL_ProgressionManager.instance.popupAsset.Popup(icon, title, desc);
	}

	// Token: 0x06000133 RID: 307 RVA: 0x0000A366 File Offset: 0x00008566
	public static List<ProgressionUnlock> GetUnlockList()
	{
		return CL_ProgressionManager.instance.unlockList;
	}

	// Token: 0x06000134 RID: 308 RVA: 0x0000A372 File Offset: 0x00008572
	internal static ProgressionUnlock GetProgressionUnlock(string n)
	{
		if (CL_ProgressionManager.progressionDict == null)
		{
			return null;
		}
		if (CL_ProgressionManager.progressionDict.ContainsKey(n))
		{
			return CL_ProgressionManager.progressionDict[n];
		}
		return null;
	}

	// Token: 0x040000F6 RID: 246
	public List<ProgressionUnlock> unlockList;

	// Token: 0x040000F7 RID: 247
	public static CL_ProgressionManager instance;

	// Token: 0x040000F8 RID: 248
	private static Dictionary<string, ProgressionUnlock> progressionDict;

	// Token: 0x040000F9 RID: 249
	public static int playerExperience = 0;

	// Token: 0x040000FA RID: 250
	private bool initialized;

	// Token: 0x040000FB RID: 251
	public UI_ProgressionPopup popupAsset;

	// Token: 0x040000FC RID: 252
	public UI_ProgressionUnlockList deathList;

	// Token: 0x040000FD RID: 253
	private static List<ProgressionUnlock> sessionUnlocks = new List<ProgressionUnlock>();

	// Token: 0x040000FE RID: 254
	public static List<CL_ProgressionManager.PopupInfo> sessionPopups = new List<CL_ProgressionManager.PopupInfo>();

	// Token: 0x02000205 RID: 517
	[Serializable]
	public class ProgressionUnlockOld
	{
		// Token: 0x04000DC3 RID: 3523
		public string name;

		// Token: 0x04000DC4 RID: 3524
		public CL_ProgressionManager.ProgressionUnlockOld.RequirementType requirement;

		// Token: 0x04000DC5 RID: 3525
		public int unlockExperience;

		// Token: 0x04000DC6 RID: 3526
		public string unlockAchievement;

		// Token: 0x04000DC7 RID: 3527
		public bool state;

		// Token: 0x04000DC8 RID: 3528
		[Header("Unlock Details")]
		public Sprite unlockIcon;

		// Token: 0x04000DC9 RID: 3529
		public string unlockTitle;

		// Token: 0x04000DCA RID: 3530
		[TextArea]
		public string unlockDescription;

		// Token: 0x020002FD RID: 765
		public enum RequirementType
		{
			// Token: 0x040012C2 RID: 4802
			experience,
			// Token: 0x040012C3 RID: 4803
			achievement
		}
	}

	// Token: 0x02000206 RID: 518
	public class PopupInfo
	{
		// Token: 0x04000DCB RID: 3531
		public Sprite icon;

		// Token: 0x04000DCC RID: 3532
		public string title;

		// Token: 0x04000DCD RID: 3533
		public string desc;
	}
}
