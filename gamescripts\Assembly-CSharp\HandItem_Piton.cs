﻿using System;
using UnityEngine;

// Token: 0x02000090 RID: 144
public class HandItem_Piton : HandItem
{
	// Token: 0x060004CF RID: 1231 RVA: 0x00029B58 File Offset: 0x00027D58
	private void OnEnable()
	{
		this.used = false;
	}

	// Token: 0x060004D0 RID: 1232 RVA: 0x00029B61 File Offset: 0x00027D61
	public override void Use()
	{
		base.Use();
		if (this.used)
		{
			return;
		}
		this.anim.SetTrigger("Use");
		CL_CameraControl.Shake(0.015f);
	}

	// Token: 0x060004D1 RID: 1233 RVA: 0x00029B94 File Offset: 0x00027D94
	public void PitonHit()
	{
		if (this.PitonRaycast())
		{
			HandItem_Piton.<>c__DisplayClass10_0 CS$<>8__locals1 = new HandItem_Piton.<>c__DisplayClass10_0();
			Debug.DrawLine(Camera.main.transform.position, this.hit.point, Color.red);
			this.used = true;
			this.hand.lockHand = true;
			GameObject gameObject = Object.Instantiate<GameObject>(this.pitonWorldObject, this.hit.collider.transform.TransformPoint(this.hitLocal), Quaternion.LookRotation(this.hit.normal) * Quaternion.Euler(0f, 0f, (float)Random.Range(-30, 30)));
			gameObject.transform.parent = WorldLoader.GetCurrentLevelParentRoot();
			gameObject.AddComponent<UT_SoftParent>().Initialize(this.hit.collider.transform);
			if (WorldLoader.initialized)
			{
				WorldLoader.instance.GetCurrentLevel().level.AddPlacedObject(gameObject);
			}
			this.UpdateStats();
			CS$<>8__locals1.handhold = gameObject.GetComponent<CL_Handhold>();
			if (CS$<>8__locals1.handhold != null && !CS$<>8__locals1.handhold.secure)
			{
				base.StartCoroutine(CS$<>8__locals1.<PitonHit>g__SecurePiton|0());
			}
			this.hand.lockHand = false;
			CL_CameraControl.Shake(0.04f);
			return;
		}
		this.anim.SetTrigger("Fail");
	}

	// Token: 0x060004D2 RID: 1234 RVA: 0x00029CEC File Offset: 0x00027EEC
	private bool PitonRaycast()
	{
		if (Physics.Raycast(Camera.main.transform.position, Camera.main.transform.forward, out this.hit, this.placeDistance, this.hitMask))
		{
			ObjectTagger component = this.hit.collider.GetComponent<ObjectTagger>();
			this.hitLocal = this.hit.collider.transform.InverseTransformPoint(this.hit.point + this.hit.normal * this.placeOffset);
			if (component != null)
			{
				if (this.placementTags != null && this.placementTags.Length != 0)
				{
					foreach (string text in this.placementTags)
					{
						if (!component.HasTag(text))
						{
							return false;
						}
					}
				}
				if (this.blockTags != null && this.blockTags.Length != 0)
				{
					foreach (string text2 in this.blockTags)
					{
						if (component.HasTag(text2))
						{
							return false;
						}
					}
				}
			}
			else if (this.placementTags != null && this.placementTags.Length != 0)
			{
				return false;
			}
			return true;
		}
		return false;
	}

	// Token: 0x060004D3 RID: 1235 RVA: 0x00029E17 File Offset: 0x00028017
	public override bool ShowAimCircle()
	{
		return this.useAimCircle && this.PitonRaycast();
	}

	// Token: 0x060004D4 RID: 1236 RVA: 0x00029E29 File Offset: 0x00028029
	public override RaycastHit GetAimCircleHit()
	{
		return this.hit;
	}

	// Token: 0x060004D5 RID: 1237 RVA: 0x00029E31 File Offset: 0x00028031
	public override bool CanDrop()
	{
		return !this.used;
	}

	// Token: 0x0400065A RID: 1626
	public LayerMask hitMask;

	// Token: 0x0400065B RID: 1627
	public GameObject pitonWorldObject;

	// Token: 0x0400065C RID: 1628
	private RaycastHit hit;

	// Token: 0x0400065D RID: 1629
	public float placeDistance = 2.5f;

	// Token: 0x0400065E RID: 1630
	public string[] placementTags;

	// Token: 0x0400065F RID: 1631
	public string[] blockTags;

	// Token: 0x04000660 RID: 1632
	public float placeOffset = 0.3f;

	// Token: 0x04000661 RID: 1633
	private Vector3 hitLocal;
}
