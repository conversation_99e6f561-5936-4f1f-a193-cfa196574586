﻿using System;
using UnityEngine;

// Token: 0x02000077 RID: 119
public class RopeSegment
{
	// Token: 0x06000408 RID: 1032 RVA: 0x00025529 File Offset: 0x00023729
	public RopeSegment(Vector3 initialPosition)
	{
		this.Position = initialPosition;
		this.TargetPosition = initialPosition;
		this.Velocity = Vector3.zero;
	}

	// Token: 0x04000561 RID: 1377
	public Vector3 Position;

	// Token: 0x04000562 RID: 1378
	public Vector3 TargetPosition;

	// Token: 0x04000563 RID: 1379
	public Vector3 Velocity;
}
