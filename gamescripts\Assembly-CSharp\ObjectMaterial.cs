﻿using System;
using UnityEngine;

// Token: 0x02000156 RID: 342
public class ObjectMaterial : MonoBehaviour
{
	// Token: 0x060009BA RID: 2490 RVA: 0x00042634 File Offset: 0x00040834
	public ObjectMaterial.MaterialType GetMaterialType()
	{
		return this.type;
	}

	// Token: 0x04000AF3 RID: 2803
	public ObjectMaterial.MaterialType type;

	// Token: 0x020002D5 RID: 725
	public enum MaterialType
	{
		// Token: 0x04001224 RID: 4644
		standard,
		// Token: 0x04001225 RID: 4645
		metal,
		// Token: 0x04001226 RID: 4646
		vent,
		// Token: 0x04001227 RID: 4647
		carpet,
		// Token: 0x04001228 RID: 4648
		water,
		// Token: 0x04001229 RID: 4649
		sludge,
		// Token: 0x0400122A RID: 4650
		acid,
		// Token: 0x0400122B RID: 4651
		wood,
		// Token: 0x0400122C RID: 4652
		flesh,
		// Token: 0x0400122D RID: 4653
		dirt,
		// Token: 0x0400122E RID: 4654
		ice
	}
}
