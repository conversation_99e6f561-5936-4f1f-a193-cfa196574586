﻿using System;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x02000159 RID: 345
public class UT_TriggerBase : MonoBehaviourGizmos
{
	// Token: 0x060009D0 RID: 2512 RVA: 0x00042B1C File Offset: 0x00040D1C
	internal virtual void Start()
	{
		this.tickFrame = Random.Range(0, 10);
		this.triggerCollider = base.GetComponent<Collider>();
	}

	// Token: 0x060009D1 RID: 2513 RVA: 0x00042B38 File Offset: 0x00040D38
	internal virtual void TriggerTick()
	{
		this.tickFrame--;
		if (this.tickFrame <= 0)
		{
			this.tickFrame = 5;
			this.TickBoundsCheck();
		}
	}

	// Token: 0x060009D2 RID: 2514 RVA: 0x00042B60 File Offset: 0x00040D60
	internal virtual void TickBoundsCheck()
	{
		if (this.triggerCollider == null)
		{
			return;
		}
		if (this.triggerObjects.Count > 0)
		{
			if (this.stillInsideCheckCounter >= this.triggerObjects.Count)
			{
				this.stillInsideCheckCounter = 0;
			}
			Bounds bounds = this.triggerCollider.bounds;
			bounds.Expand(2f);
			if (!bounds.Contains(this.triggerObjects[this.stillInsideCheckCounter].transform.position))
			{
				this.RemoveIndexFromTrigger(this.stillInsideCheckCounter);
			}
		}
	}

	// Token: 0x060009D3 RID: 2515 RVA: 0x00042BEC File Offset: 0x00040DEC
	internal virtual void RemoveIndexFromTrigger(int i)
	{
		this.triggerObjects.RemoveAt(i);
	}

	// Token: 0x060009D4 RID: 2516 RVA: 0x00042BFA File Offset: 0x00040DFA
	internal virtual void FixedUpdate()
	{
		this.TickBoundsCheck();
	}

	// Token: 0x04000AF4 RID: 2804
	public string[] triggerTags = new string[] { "Player" };

	// Token: 0x04000AF5 RID: 2805
	internal List<GameObject> triggerObjects = new List<GameObject>();

	// Token: 0x04000AF6 RID: 2806
	private int stillInsideCheckCounter;

	// Token: 0x04000AF7 RID: 2807
	private int tickFrame = 5;

	// Token: 0x04000AF8 RID: 2808
	private Collider triggerCollider;
}
