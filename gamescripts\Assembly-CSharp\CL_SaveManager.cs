﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Runtime.CompilerServices;
using System.Text;
using UnityEngine;

// Token: 0x020000B4 RID: 180
public class CL_SaveManager : MonoBehaviour
{
	// Token: 0x060005E2 RID: 1506 RVA: 0x00031162 File Offset: 0x0002F362
	private void Start()
	{
		CL_SaveManager.instance = this;
		CL_SaveManager.saveStates = new List<CL_SaveManager.SaveState>();
		if (CL_SaveManager.loadSessionSaveOnLoad)
		{
			CL_SaveManager.loadSessionSaveOnLoad = false;
			CL_SaveManager.isSessionSave = true;
			base.StartCoroutine(this.<Start>g__WaitUntilLoad|6_0());
		}
	}

	// Token: 0x060005E3 RID: 1507 RVA: 0x00031194 File Offset: 0x0002F394
	private void Update()
	{
	}

	// Token: 0x060005E4 RID: 1508 RVA: 0x00031198 File Offset: 0x0002F398
	public static CL_SaveManager.SaveState GetMostRecentSaveStateWithFlag(string flag, bool contains = false)
	{
		foreach (CL_SaveManager.SaveState saveState in CL_SaveManager.saveStates)
		{
			Debug.Log(saveState.flag);
		}
		for (int i = CL_SaveManager.saveStates.Count - 1; i >= 0; i--)
		{
			CL_SaveManager.SaveState saveState2 = CL_SaveManager.saveStates[i];
			Debug.Log(saveState2.flag);
			if (contains)
			{
				if (saveState2.flag.Contains(flag))
				{
					Debug.Log("Found save state with session: " + flag + " : " + saveState2.type.ToString());
					return saveState2;
				}
			}
			else if (saveState2.flag == flag)
			{
				Debug.Log("Found save state with session: " + flag + " : " + saveState2.type.ToString());
				return saveState2;
			}
		}
		return null;
	}

	// Token: 0x060005E5 RID: 1509 RVA: 0x00031290 File Offset: 0x0002F490
	public static CL_SaveManager.SaveState GetMostRecentSaveStateByType(CL_SaveManager.SaveState.SaveType type)
	{
		for (int i = CL_SaveManager.saveStates.Count - 1; i >= 0; i--)
		{
			CL_SaveManager.SaveState saveState = CL_SaveManager.saveStates[i];
			if (saveState.type == type)
			{
				return saveState;
			}
		}
		return null;
	}

	// Token: 0x060005E6 RID: 1510 RVA: 0x000312CC File Offset: 0x0002F4CC
	public static CL_SaveManager.SaveState GetSaveStateByID(string id, CL_SaveManager.SaveState.SaveType type)
	{
		for (int i = CL_SaveManager.saveStates.Count - 1; i >= 0; i--)
		{
			CL_SaveManager.SaveState saveState = CL_SaveManager.saveStates[i];
			if (saveState.id == id)
			{
				return saveState;
			}
		}
		return null;
	}

	// Token: 0x060005E7 RID: 1511 RVA: 0x00031310 File Offset: 0x0002F510
	public static List<CL_SaveManager.SaveState> GetAllSaveStatesByType(CL_SaveManager.SaveState.SaveType type)
	{
		List<CL_SaveManager.SaveState> list = new List<CL_SaveManager.SaveState>();
		for (int i = CL_SaveManager.saveStates.Count - 1; i >= 0; i--)
		{
			if (CL_SaveManager.saveStates[i].type == type)
			{
				list.Add(CL_SaveManager.saveStates[i]);
			}
		}
		return list;
	}

	// Token: 0x060005E8 RID: 1512 RVA: 0x00031360 File Offset: 0x0002F560
	public static int GetNumberOfDiskLives()
	{
		int num = 0;
		for (int i = CL_SaveManager.saveStates.Count - 1; i >= 0; i--)
		{
			if (CL_SaveManager.saveStates[i].type == CL_SaveManager.SaveState.SaveType.disk)
			{
				num += CL_SaveManager.saveStates[i].amount;
			}
		}
		return num;
	}

	// Token: 0x060005E9 RID: 1513 RVA: 0x000313B0 File Offset: 0x0002F5B0
	public static void CreateSaveState(string id, string flag, CL_SaveManager.SaveState.SaveType type)
	{
		CL_SaveManager.SaveState saveState = new CL_SaveManager.SaveState();
		saveState.id = id;
		saveState.flag = flag;
		saveState.type = type;
		saveState.amount = 1;
		saveState.gamemode = CL_GameManager.gamemode.name;
		saveState.Save(new List<string> { "savetype:" + type.ToString() }.ToArray());
		CL_SaveManager.saveStates.Add(saveState);
	}

	// Token: 0x060005EA RID: 1514 RVA: 0x0003142C File Offset: 0x0002F62C
	public static CL_SaveManager.SaveState CreateOrUpdateSaveState(string id, string flag, CL_SaveManager.SaveState.SaveType type, int amountAdjust = 1, bool saveToSaveStates = true)
	{
		CL_SaveManager.SaveState saveState = (saveToSaveStates ? CL_SaveManager.GetSaveStateByID(id, type) : null);
		Debug.Log("CREATING SAVE STATE: " + flag + " ID: " + id);
		List<string> list = new List<string>();
		list.Add("savetype:" + type.ToString());
		if (saveState == null)
		{
			saveState = new CL_SaveManager.SaveState();
			saveState.id = id;
			saveState.flag = flag;
			saveState.type = type;
			saveState.amount = Mathf.Max(amountAdjust, 0);
			saveState.Save(list.ToArray());
			saveState.clearItems = false;
			saveState.gamemode = CL_GameManager.gamemode.name;
			if (type == CL_SaveManager.SaveState.SaveType.reset)
			{
				saveState.forceRoomReset = true;
			}
			if (saveToSaveStates)
			{
				CL_SaveManager.saveStates.Add(saveState);
			}
		}
		else
		{
			saveState.Save(list.ToArray());
			saveState.amount += amountAdjust;
			saveState.amount = Mathf.Max(saveState.amount, 0);
		}
		return saveState;
	}

	// Token: 0x060005EB RID: 1515 RVA: 0x00031518 File Offset: 0x0002F718
	public static void LoadSave(CL_SaveManager.SaveState save, CL_SaveManager.SessionSave sessionSave = null)
	{
		if (save != null)
		{
			CL_SaveManager.instance.StartCoroutine(save.LoadSave(sessionSave));
		}
	}

	// Token: 0x060005EC RID: 1516 RVA: 0x0003152F File Offset: 0x0002F72F
	public void StartDiskDeathSequence()
	{
		base.StartCoroutine(this.DeathSequence());
	}

	// Token: 0x060005ED RID: 1517 RVA: 0x0003153E File Offset: 0x0002F73E
	public IEnumerator DeathSequence()
	{
		Time.timeScale = 0.05f;
		AudioManager.instance.gameMixer.audioMixer.SetFloat("pitch", 0.1f);
		yield return new WaitForSecondsRealtime(1f);
		AudioManager.instance.gameMixer.audioMixer.SetFloat("pitch", 1f);
		yield break;
	}

	// Token: 0x060005EE RID: 1518 RVA: 0x00031548 File Offset: 0x0002F748
	public void LoadSession()
	{
		string text = "";
		if (SettingsManager.settings.g_hard)
		{
			text += "-hardmode";
		}
		string text2 = CL_GameManager.GetGamemodeName(false, false).ToLower() + text;
		CL_SaveManager.SessionSave sessionSave = CL_SaveManager.LoadSessionFromFile(text2);
		CL_GameManager.gMan.sessionFlags = sessionSave.sessionFlags;
		CL_GameManager.roaches = sessionSave.roaches;
		CommandConsole.hasCheated = sessionSave.hasCheated;
		Debug.Log(sessionSave.gamemode);
		if (text2 != null || text2 != "")
		{
			M_Gamemode gamemodeAsset = CL_AssetManager.GetGamemodeAsset(sessionSave.gamemode, "");
			CL_GameManager.gMan.SetGamemode(gamemodeAsset);
		}
		CL_GameManager.SetLoading(true);
		CL_SaveManager.saveStates = sessionSave.saveStates;
		StatManager.sessionStats = sessionSave.sessionStats;
		CL_GameManager.gMan.SetGameTime(sessionSave.gameTime);
		for (int i = CL_SaveManager.saveStates.Count - 1; i >= 0; i--)
		{
			CL_SaveManager.SaveState saveState = CL_SaveManager.saveStates[i];
			Debug.Log(string.Concat(new string[]
			{
				"Save State Found: ",
				saveState.id,
				" ",
				saveState.flag,
				" ",
				saveState.type.ToString(),
				" Amount: ",
				saveState.amount.ToString()
			}));
		}
		CL_SaveManager.SaveState saveState2 = null;
		int num = CL_SaveManager.saveStates.Count - 1;
		if (num >= 0)
		{
			CL_SaveManager.SaveState saveState3 = CL_SaveManager.saveStates[num];
			Debug.Log(string.Concat(new string[]
			{
				"Save State: ",
				saveState3.id,
				" ",
				saveState3.flag,
				" ",
				saveState3.type.ToString()
			}));
			if (saveState3.type == CL_SaveManager.SaveState.SaveType.standard && saveState3.flag.Contains("session"))
			{
				saveState2 = saveState3;
				Debug.Log("Found Save: " + saveState2.flag + " Clear: " + saveState2.clearItems.ToString());
			}
			else
			{
				saveState2 = saveState3;
			}
		}
		if (saveState2 == null)
		{
			if (CL_SaveManager.saveStates.Count > 0)
			{
				saveState2 = CL_SaveManager.saveStates[CL_SaveManager.saveStates.Count - 1];
			}
			else
			{
				saveState2 = sessionSave.playerSave;
				Debug.LogError("Uh oh! Found no normal save data, this should not happen! Resetting to the playersave.");
			}
		}
		else if (saveState2.type == CL_SaveManager.SaveState.SaveType.standard)
		{
			this.RemoveOrLowerSaveAmount(ref CL_SaveManager.saveStates, saveState2);
		}
		Debug.Log(string.Concat(new string[]
		{
			"Loading State: ",
			saveState2.id,
			" ",
			saveState2.flag,
			" ",
			saveState2.type.ToString()
		}));
		CL_SaveManager.LoadSave(saveState2, sessionSave);
		this.lastSessionSave = sessionSave;
	}

	// Token: 0x060005EF RID: 1519 RVA: 0x00031820 File Offset: 0x0002FA20
	public CL_SaveManager.SessionSave SaveSession(bool eraseStats = false, bool saveToFile = true, bool clearItems = false, string nameAppend = "", bool resetRoom = false)
	{
		CL_SaveManager.SessionSave sessionSave = new CL_SaveManager.SessionSave();
		sessionSave.gamemode = CL_GameManager.GetGamemodeName(false, true);
		sessionSave.sessionStats = StatManager.sessionStats;
		sessionSave.worldSeed = WorldLoader.instance.seed;
		sessionSave.sessionFlags = CL_GameManager.gMan.sessionFlags;
		sessionSave.gameTime = CL_GameManager.gMan.GetGameTime();
		sessionSave.playerAscent = CL_GameManager.gMan.GetPlayerAscent();
		sessionSave.hasCheated = CommandConsole.hasCheated;
		sessionSave.roaches = CL_GameManager.roaches;
		sessionSave.clearItems = clearItems;
		if (WorldLoader.instance != null && WorldLoader.instance.GetCurrentLevel().level != null)
		{
			sessionSave.saveName = WorldLoader.instance.GetCurrentLevel().level.saveName;
		}
		else
		{
			sessionSave.saveName = "Unknown";
		}
		string text = "session";
		if (nameAppend != "")
		{
			text = text + "-" + nameAppend;
		}
		CL_SaveManager.SaveState mostRecentSaveStateWithFlag = CL_SaveManager.GetMostRecentSaveStateWithFlag(text, false);
		if (mostRecentSaveStateWithFlag != null)
		{
			CL_SaveManager.saveStates.Remove(mostRecentSaveStateWithFlag);
		}
		CL_SaveManager.SaveState saveState = CL_SaveManager.CreateOrUpdateSaveState(CL_GameManager.gamemode.gamemodeName + "-" + text, text, CL_SaveManager.SaveState.SaveType.standard, 0, true);
		if (clearItems)
		{
			saveState.clearItems = true;
		}
		saveState.forceRoomReset = resetRoom;
		sessionSave.saveStates = CL_SaveManager.saveStates;
		sessionSave.playerSave = CL_SaveManager.CreateOrUpdateSaveState(CL_GameManager.gamemode.gamemodeName + "-" + text, text, CL_SaveManager.SaveState.SaveType.standard, 0, false);
		string text2 = "";
		if (SettingsManager.settings.g_hard)
		{
			text2 += "-hardmode";
		}
		string text3 = CL_GameManager.GetGamemodeName(false, true).ToLower() + text2;
		if (saveToFile)
		{
			CL_SaveManager.SaveSessionToFile(sessionSave, text3);
		}
		if (eraseStats)
		{
			StatManager.sessionStats = new StatManager.GameStats();
		}
		this.lastSessionSave = sessionSave;
		return sessionSave;
	}

	// Token: 0x060005F0 RID: 1520 RVA: 0x000319E4 File Offset: 0x0002FBE4
	public static void SaveSessionToFile(CL_SaveManager.SessionSave data, string fileName)
	{
		string text = JsonUtility.ToJson(data, true);
		Directory.CreateDirectory(Path.Combine(Application.persistentDataPath, "Sessions/"));
		using (FileStream fileStream = new FileStream(Path.Combine(Application.persistentDataPath, "Sessions/" + fileName + "-save.session"), FileMode.Create, FileAccess.Write))
		{
			using (GZipStream gzipStream = new GZipStream(fileStream, CompressionLevel.Optimal))
			{
				using (StreamWriter streamWriter = new StreamWriter(gzipStream, Encoding.UTF8))
				{
					streamWriter.Write(text);
				}
			}
		}
	}

	// Token: 0x060005F1 RID: 1521 RVA: 0x00031A94 File Offset: 0x0002FC94
	public static CL_SaveManager.SessionSave LoadSessionFromFile(string fileName)
	{
		string text = Path.Combine(Application.persistentDataPath, "Sessions/" + fileName + "-save.session");
		Debug.Log("Attempting to decompress!");
		if (!File.Exists(text))
		{
			return null;
		}
		CL_SaveManager.SessionSave sessionSave;
		using (FileStream fileStream = new FileStream(text, FileMode.Open, FileAccess.Read))
		{
			using (GZipStream gzipStream = new GZipStream(fileStream, CompressionMode.Decompress))
			{
				using (StreamReader streamReader = new StreamReader(gzipStream, Encoding.UTF8))
				{
					sessionSave = JsonUtility.FromJson<CL_SaveManager.SessionSave>(streamReader.ReadToEnd());
				}
			}
		}
		return sessionSave;
	}

	// Token: 0x060005F2 RID: 1522 RVA: 0x00031B44 File Offset: 0x0002FD44
	internal static void DeleteSessionSave(string gamemodeName, bool hardmode)
	{
		string text = "";
		if (hardmode)
		{
			text += "-hardmode";
		}
		string text2 = gamemodeName.ToLower() + text;
		string text3 = Path.Combine(Application.persistentDataPath, "Sessions/" + text2 + "-save.session");
		if (File.Exists(text3))
		{
			File.Delete(text3);
		}
	}

	// Token: 0x060005F3 RID: 1523 RVA: 0x00031B9C File Offset: 0x0002FD9C
	public void RemoveOrLowerSaveAmount(ref List<CL_SaveManager.SaveState> saveList, CL_SaveManager.SaveState s)
	{
		if (s.amount > 1)
		{
			s.amount--;
			return;
		}
		saveList.Remove(s);
	}

	// Token: 0x060005F4 RID: 1524 RVA: 0x00031BC0 File Offset: 0x0002FDC0
	public void SaveLastSessionToFile(bool removeLastDisk = true)
	{
		if (this.lastSessionSave != null)
		{
			string text = "";
			if (SettingsManager.settings.g_hard)
			{
				text += "-hardmode";
			}
			string text2 = CL_GameManager.GetGamemodeName(false, true).ToLower() + text;
			for (int i = this.lastSessionSave.saveStates.Count - 1; i >= 0; i--)
			{
				if (this.lastSessionSave.saveStates[i].type == CL_SaveManager.SaveState.SaveType.standard)
				{
					this.lastSessionSave.saveStates.RemoveAt(i);
				}
			}
			this.lastSessionSave.playerSave = CL_SaveManager.CreateOrUpdateSaveState(CL_GameManager.gamemode.gamemodeName + "-session", "playerSave", CL_SaveManager.SaveState.SaveType.standard, 0, false);
			CL_SaveManager.SaveSessionToFile(this.lastSessionSave, text2);
		}
	}

	// Token: 0x060005F5 RID: 1525 RVA: 0x00031C8C File Offset: 0x0002FE8C
	public static bool SessionFileExists(string gamemode, bool hardmode)
	{
		string text = "";
		if (hardmode)
		{
			text += "-hardmode";
		}
		string text2 = gamemode.ToLower() + text;
		return File.Exists(Path.Combine(Application.persistentDataPath, "Sessions/" + text2 + "-save.session"));
	}

	// Token: 0x060005F6 RID: 1526 RVA: 0x00031CE0 File Offset: 0x0002FEE0
	public static string GetSessionArea(string gamemode, bool hardmode)
	{
		string text = "";
		if (hardmode)
		{
			text += "-hardmode";
		}
		string text2 = gamemode.ToLower() + text;
		Debug.Log("Getting Session Area");
		CL_SaveManager.SessionSave sessionSave = CL_SaveManager.LoadSessionFromFile(text2);
		if (sessionSave != null)
		{
			return sessionSave.saveName;
		}
		return "Null";
	}

	// Token: 0x060005F9 RID: 1529 RVA: 0x00031D47 File Offset: 0x0002FF47
	[CompilerGenerated]
	private IEnumerator <Start>g__WaitUntilLoad|6_0()
	{
		yield return null;
		this.LoadSession();
		CL_SaveManager.DeleteSessionSave(CL_GameManager.GetGamemodeName(false, false), SettingsManager.settings.g_hard);
		yield break;
	}

	// Token: 0x0400078E RID: 1934
	public static bool loadSessionSaveOnLoad = false;

	// Token: 0x0400078F RID: 1935
	public static bool isSessionSave;

	// Token: 0x04000790 RID: 1936
	public static CL_SaveManager instance;

	// Token: 0x04000791 RID: 1937
	private CL_SaveManager.SessionSave lastSessionSave;

	// Token: 0x04000792 RID: 1938
	public static List<CL_SaveManager.SaveState> saveStates = new List<CL_SaveManager.SaveState>();

	// Token: 0x02000278 RID: 632
	[Serializable]
	public class SaveState
	{
		// Token: 0x06000DF3 RID: 3571 RVA: 0x0005585F File Offset: 0x00053A5F
		public IEnumerator LoadSave(CL_SaveManager.SessionSave sessionSave = null)
		{
			if (this.type == CL_SaveManager.SaveState.SaveType.disk || this.type == CL_SaveManager.SaveState.SaveType.reset || this.clearItems)
			{
				this.playerData.inventory = new Item[1];
				this.playerData.inventory[0] = CL_AssetManager.GetAssetGameObject("Item_Hammer", "").GetComponent<Item_Object>().itemData.GetClone();
				this.playerData.inventory[0].bagPosition = new Vector3(0f, 0f, 1f);
				this.playerData.inventory[0].bagRotation = Quaternion.LookRotation(-ENT_Player.GetPlayer().transform.forward + Vector3.up * 0.5f) * Quaternion.LookRotation(this.playerData.inventory[0].upDirection);
				this.playerData.handInventory = new Item[2];
			}
			if (sessionSave != null)
			{
				this.playerData.perks = sessionSave.playerSave.playerData.perks;
				this.playerData.buffs = sessionSave.playerSave.playerData.buffs;
			}
			if (this.type == CL_SaveManager.SaveState.SaveType.disk)
			{
				if (sessionSave != null)
				{
					this.playerData.LoadDataIntoPlayer(ENT_Player.playerObject, true, true, true, "disk");
				}
				else
				{
					this.playerData.LoadDataIntoPlayer(ENT_Player.playerObject, true, false, false, "disk");
				}
			}
			else
			{
				this.playerData.LoadDataIntoPlayer(ENT_Player.playerObject, true, true, true, "other");
			}
			if (this.gamemode != null || this.gamemode != "")
			{
				M_Gamemode gamemodeAsset = CL_AssetManager.GetGamemodeAsset(this.gamemode, "");
				if (gamemodeAsset != null)
				{
					CL_GameManager.gMan.SetGamemode(gamemodeAsset);
				}
			}
			ENT_Player.playerObject.UnLock();
			List<M_Level> list = new List<M_Level>();
			if (this.levelData != null)
			{
				foreach (M_Level.SaveData saveData in this.levelData)
				{
					list.Add(CL_AssetManager.GetAssetGameObject(saveData.levelName, "").GetComponent<M_Level>());
				}
			}
			if (WorldLoader.initialized)
			{
				if (DEN_DeathFloor.instance != null)
				{
					DEN_DeathFloor.instance.transform.position = Vector3.up * -50f;
				}
				WorldLoader.GenerationParameters generationParameters = new WorldLoader.GenerationParameters
				{
					generationType = WorldLoader.GenerationParameters.GenerationType.generateUntilFound,
					targetLevel = list[0],
					levelData = this.levelData,
					preloadLevels = list,
					lastRegion = CL_AssetManager.GetRegionAsset(this.levelData[0].regionName, ""),
					startRegion = CL_AssetManager.GetRegionAsset(this.levelData[0].regionName, ""),
					setPlayerPositionToTargetLevel = true
				};
				if (this.type == CL_SaveManager.SaveState.SaveType.reset)
				{
					generationParameters.setPlayerPositionToTargetLevel = false;
				}
				if (this.type == CL_SaveManager.SaveState.SaveType.disk)
				{
					WorldLoader.IncrementSeed(1);
				}
				else if (!this.forceRoomReset)
				{
					generationParameters.loadSavedEntities = true;
				}
				WorldLoader.instance.RestartGenerationFromLevelList(generationParameters);
				if (this.type != CL_SaveManager.SaveState.SaveType.disk)
				{
					CL_UIManager.instance.FadeOut();
				}
				while (!WorldLoader.isLoaded)
				{
					yield return null;
				}
				if (this.type != CL_SaveManager.SaveState.SaveType.disk)
				{
					CL_UIManager.instance.FadeIn();
				}
				if (this.type == CL_SaveManager.SaveState.SaveType.reset)
				{
					ENT_Player.playerObject.transform.position = WorldLoader.instance.GetCurrentLevel().level.transform.TransformPoint(this.playerData.positionRelativeToLevel);
					ENT_Player.playerObject.transform.rotation = this.playerData.playerRotation * WorldLoader.instance.GetCurrentLevel().level.transform.rotation;
					ENT_Player.playerObject.camTransform.localRotation = this.playerData.playerCameraRotation;
				}
			}
			else
			{
				ENT_Player.playerObject.transform.position = this.playerData.positionRelativeToLevel;
				ENT_Player.playerObject.transform.rotation = this.playerData.playerRotation;
				ENT_Player.playerObject.camTransform.localRotation = this.playerData.playerCameraRotation;
			}
			CL_SaveManager.instance.StartCoroutine(this.LoadDelay());
			if (DEN_Teeth.instance != null)
			{
				DEN_Teeth.instance.Despawn();
			}
			if (this.type == CL_SaveManager.SaveState.SaveType.disk)
			{
				this.amount--;
				if (this.amount <= 0)
				{
					CL_SaveManager.saveStates.Remove(this);
				}
			}
			yield return new WaitForSeconds(3f);
			if (sessionSave != null)
			{
				if (this.type != CL_SaveManager.SaveState.SaveType.reset && this.type != CL_SaveManager.SaveState.SaveType.resetwithitems)
				{
					int numberOfDiskLives = CL_SaveManager.GetNumberOfDiskLives();
					if (numberOfDiskLives > 0)
					{
						if (numberOfDiskLives == 1)
						{
							CL_UIManager.instance.highscoreHeader.ShowText(string.Format("<size=28>Resumed Save. {0} Life Remaining.", numberOfDiskLives));
						}
						else
						{
							CL_UIManager.instance.highscoreHeader.ShowText(string.Format("<size=28>Resumed Save. {0} Lives Remaining.", numberOfDiskLives));
						}
					}
					else
					{
						CL_UIManager.instance.highscoreHeader.ShowText("<size=28>No Lives Remaining.\nGood Luck.");
					}
				}
			}
			else if (this.type != CL_SaveManager.SaveState.SaveType.reset && this.type != CL_SaveManager.SaveState.SaveType.resetwithitems)
			{
				int numberOfDiskLives2 = CL_SaveManager.GetNumberOfDiskLives();
				if (numberOfDiskLives2 > 0)
				{
					if (numberOfDiskLives2 == 1)
					{
						CL_UIManager.instance.highscoreHeader.ShowText(string.Format("<size=28>Life Lost. {0} Life Remaining.", numberOfDiskLives2));
					}
					else
					{
						CL_UIManager.instance.highscoreHeader.ShowText(string.Format("<size=28>Life Lost. {0} Lives Remaining.", numberOfDiskLives2));
					}
				}
				else
				{
					CL_UIManager.instance.highscoreHeader.ShowText("<size=28>No Lives Remaining.\nGood Luck.");
				}
			}
			yield break;
		}

		// Token: 0x06000DF4 RID: 3572 RVA: 0x00055875 File Offset: 0x00053A75
		private IEnumerator LoadDelay()
		{
			if (DEN_DeathFloor.instance != null)
			{
				DEN_DeathFloor.instance.transform.position = Vector3.up * -50f;
			}
			yield return new WaitForSeconds(0.5f);
			if (DEN_DeathFloor.instance != null)
			{
				DEN_DeathFloor.instance.transform.position = Vector3.up * -50f;
				DEN_DeathFloor.instance.LoadDataFromSave(this.gooData);
			}
			yield break;
		}

		// Token: 0x06000DF5 RID: 3573 RVA: 0x00055884 File Offset: 0x00053A84
		public void UpdatePlayerData(ENT_Player player)
		{
			this.playerData = new ENT_Player.SaveData();
			this.playerData.SaveDataIntoClass(player);
		}

		// Token: 0x06000DF6 RID: 3574 RVA: 0x000558A0 File Offset: 0x00053AA0
		public void Save(string[] flags = null)
		{
			this.UpdatePlayerData(ENT_Player.playerObject);
			if (WorldLoader.initialized)
			{
				M_Level.SaveData save = M_Level.SaveData.GetSave(WorldLoader.GetCurrentLevelFromBounds().level, flags);
				save.playerRespawnLevel = true;
				if (this.type == CL_SaveManager.SaveState.SaveType.disk)
				{
					save.activeStates.Add("onDiskReload");
				}
				save.activeStates.Add("onLoad");
				this.levelData = new List<M_Level.SaveData> { save };
			}
			if (DEN_DeathFloor.instance != null)
			{
				this.gooData = DEN_DeathFloor.instance.GetSaveData();
			}
		}

		// Token: 0x0400101E RID: 4126
		public string id;

		// Token: 0x0400101F RID: 4127
		public string flag;

		// Token: 0x04001020 RID: 4128
		public string gamemode;

		// Token: 0x04001021 RID: 4129
		public ENT_Player.SaveData playerData;

		// Token: 0x04001022 RID: 4130
		public List<M_Level.SaveData> levelData;

		// Token: 0x04001023 RID: 4131
		public DEN_DeathFloor.SaveData gooData;

		// Token: 0x04001024 RID: 4132
		public CL_SaveManager.SaveState.SaveType type;

		// Token: 0x04001025 RID: 4133
		public int amount;

		// Token: 0x04001026 RID: 4134
		public List<string> soldOutTerminals;

		// Token: 0x04001027 RID: 4135
		public bool clearItems;

		// Token: 0x04001028 RID: 4136
		public bool forceRoomReset;

		// Token: 0x0200030B RID: 779
		public enum SaveType
		{
			// Token: 0x040012F8 RID: 4856
			standard,
			// Token: 0x040012F9 RID: 4857
			disk,
			// Token: 0x040012FA RID: 4858
			scissors,
			// Token: 0x040012FB RID: 4859
			reset,
			// Token: 0x040012FC RID: 4860
			resetwithitems,
			// Token: 0x040012FD RID: 4861
			session
		}
	}

	// Token: 0x02000279 RID: 633
	public class SessionSave
	{
		// Token: 0x04001029 RID: 4137
		public int version = 1;

		// Token: 0x0400102A RID: 4138
		public string gamemode;

		// Token: 0x0400102B RID: 4139
		public int worldSeed;

		// Token: 0x0400102C RID: 4140
		public StatManager.GameStats sessionStats;

		// Token: 0x0400102D RID: 4141
		public CL_SaveManager.SaveState checkpointState;

		// Token: 0x0400102E RID: 4142
		public List<CL_SaveManager.SaveState> saveStates = new List<CL_SaveManager.SaveState>();

		// Token: 0x0400102F RID: 4143
		public CL_SaveManager.SaveState playerSave;

		// Token: 0x04001030 RID: 4144
		public List<CL_GameManager.SessionFlag> sessionFlags;

		// Token: 0x04001031 RID: 4145
		public float playerAscent;

		// Token: 0x04001032 RID: 4146
		public float gameTime;

		// Token: 0x04001033 RID: 4147
		public bool hasCheated;

		// Token: 0x04001034 RID: 4148
		public int roaches;

		// Token: 0x04001035 RID: 4149
		public bool clearItems;

		// Token: 0x04001036 RID: 4150
		public string loadInText;

		// Token: 0x04001037 RID: 4151
		public string saveName;
	}
}
