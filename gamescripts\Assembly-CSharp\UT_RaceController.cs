﻿using System;
using UnityEngine;

// Token: 0x020001B1 RID: 433
public class UT_RaceController : MonoBehaviour
{
	// Token: 0x06000B5A RID: 2906 RVA: 0x0004893C File Offset: 0x00046B3C
	private void Start()
	{
		this.parentLevel = base.GetComponentInParent<M_Level>();
		this.levelSaveData = M_Level.SaveData.GetSave(this.parentLevel, null);
	}

	// Token: 0x06000B5B RID: 2907 RVA: 0x0004895C File Offset: 0x00046B5C
	public void RaceTrackerStart()
	{
		if (this.parentLevel == null)
		{
			return;
		}
		this.parentLevel.TimeTrackerStart();
		AudioManager.PlayUISound("tick1", 1f, 1f);
		Inventory.instance.ClearInventoryExceptTag("hammer");
		ENT_Player.playerObject.AddGripStrength(10f, false);
		if (this.levelSaveData != null)
		{
			M_Level.SaveData.LoadDataIntoLevel(this.levelSaveData, this.parentLevel, false, true);
			this.parentLevel.DestroyPlacedObjects();
		}
	}

	// Token: 0x06000B5C RID: 2908 RVA: 0x000489DC File Offset: 0x00046BDC
	public void RaceTrackerEnd()
	{
		if (this.parentLevel == null)
		{
			return;
		}
		this.parentLevel.TimeTrackerEnd();
		Inventory.instance.ClearInventoryExceptTag("hammer");
		if (this.levelSaveData != null)
		{
			M_Level.SaveData.LoadDataIntoLevel(this.levelSaveData, this.parentLevel, false, true);
			this.parentLevel.DestroyPlacedObjects();
		}
	}

	// Token: 0x04000C49 RID: 3145
	private M_Level parentLevel;

	// Token: 0x04000C4A RID: 3146
	private M_Level.SaveData levelSaveData;
}
