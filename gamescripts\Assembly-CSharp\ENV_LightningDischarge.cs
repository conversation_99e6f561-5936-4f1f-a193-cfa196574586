﻿using System;
using System.Collections;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x02000056 RID: 86
public class ENV_LightningDischarge : MonoBehaviourGizmos
{
	// Token: 0x0600037E RID: 894 RVA: 0x000220B7 File Offset: 0x000202B7
	private void Start()
	{
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		this.strikeWaitTime = Random.Range(this.strikeWaitMin, this.strikeWaitMax);
		this.DischargeEnd();
	}

	// Token: 0x0600037F RID: 895 RVA: 0x000220E4 File Offset: 0x000202E4
	private void Update()
	{
		if (this.strikeWaitTime <= 0f)
		{
			this.strikeWaitTime = Random.Range(this.strikeWaitMin, this.strikeWaitMax);
			base.StartCoroutine(this.Discharge());
		}
		this.strikeWaitTime -= Time.deltaTime;
	}

	// Token: 0x06000380 RID: 896 RVA: 0x00022134 File Offset: 0x00020334
	private IEnumerator Discharge()
	{
		float strikeTime = this.strikeLength;
		float num = 1f;
		float damageTime = 0f;
		this.dischargeRoot.transform.localRotation = Quaternion.identity;
		this.lightningParticle.Play();
		yield return new WaitForSeconds(this.strikeBuildupLength);
		foreach (Renderer renderer in this.lightningRenderers)
		{
			renderer.gameObject.SetActive(true);
		}
		foreach (CL_Lamp cl_Lamp in this.lamps)
		{
			cl_Lamp.gameObject.SetActive(true);
		}
		this.loopAudio.Play();
		this.clipHandler.PlaySound("discharge:strike");
		CL_CameraControl.ShakeAtPosition(base.transform.position, this.strikeCamShake, this.strikeCamShakeDistance);
		Quaternion targetRotation = Quaternion.Euler(Random.insideUnitSphere * this.strikeDrift);
		while (strikeTime >= 0f)
		{
			num = this.strikeCurve.Evaluate(strikeTime / this.strikeLength);
			this.loopAudio.volume = num * this.loopAudioVolume;
			this.dischargeRoot.transform.localRotation = Quaternion.Lerp(Quaternion.identity, targetRotation, 1f - strikeTime / this.strikeLength);
			foreach (Renderer renderer2 in this.lightningRenderers)
			{
				renderer2.material.SetFloat("_AlphaMult", num);
			}
			foreach (CL_Lamp cl_Lamp2 in this.lamps)
			{
				cl_Lamp2.SetIntensityMultiplier(num);
			}
			CL_CameraControl.ShakeAtPosition(base.transform.position, this.strikeCamShake * Time.deltaTime, this.strikeCamShakeDistance);
			strikeTime -= Time.deltaTime;
			if (damageTime <= 0f)
			{
				this.DamageCheck();
				damageTime = 0.1f;
			}
			damageTime -= Time.deltaTime;
			yield return null;
		}
		this.DischargeEnd();
		yield break;
	}

	// Token: 0x06000381 RID: 897 RVA: 0x00022144 File Offset: 0x00020344
	private void DamageCheck()
	{
		foreach (Collider collider in Physics.OverlapCapsule(this.dischargeRoot.transform.position, this.endTransform.position, this.damageRadius, this.damageMask))
		{
			if (ObjectTagger.TagCheck(collider.gameObject, "Damageable"))
			{
				Damageable component = collider.GetComponent<Damageable>();
				if (component != null)
				{
					component.Damage(this.hitDamage, "lightning");
				}
			}
		}
	}

	// Token: 0x06000382 RID: 898 RVA: 0x000221C4 File Offset: 0x000203C4
	private void DischargeEnd()
	{
		this.lightningParticle.Stop();
		this.loopAudio.Stop();
		foreach (CL_Lamp cl_Lamp in this.lamps)
		{
			cl_Lamp.SetIntensityMultiplier(0f);
			cl_Lamp.gameObject.SetActive(false);
		}
		this.dischargeRoot.transform.localRotation = Quaternion.identity;
		foreach (Renderer renderer in this.lightningRenderers)
		{
			renderer.gameObject.SetActive(false);
		}
	}

	// Token: 0x06000383 RID: 899 RVA: 0x00022298 File Offset: 0x00020498
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			Draw.WireCapsule(this.dischargeRoot.transform.position, this.endTransform.position, this.damageRadius, new Color(1f, 1f, 1f, 0.1f));
		}
	}

	// Token: 0x040004C6 RID: 1222
	public GameObject dischargeRoot;

	// Token: 0x040004C7 RID: 1223
	public Transform endTransform;

	// Token: 0x040004C8 RID: 1224
	public float strikeLength = 1f;

	// Token: 0x040004C9 RID: 1225
	public float strikeBuildupLength = 1f;

	// Token: 0x040004CA RID: 1226
	public float strikeWaitMin = 5f;

	// Token: 0x040004CB RID: 1227
	public float strikeWaitMax = 10f;

	// Token: 0x040004CC RID: 1228
	public float strikeDrift = 5f;

	// Token: 0x040004CD RID: 1229
	public float strikeCamShake = 3f;

	// Token: 0x040004CE RID: 1230
	public float strikeCamShakeDistance = 20f;

	// Token: 0x040004CF RID: 1231
	public List<CL_Lamp> lamps;

	// Token: 0x040004D0 RID: 1232
	public List<Renderer> lightningRenderers;

	// Token: 0x040004D1 RID: 1233
	public ParticleSystem lightningParticle;

	// Token: 0x040004D2 RID: 1234
	public ParticleSystem chargeParticle;

	// Token: 0x040004D3 RID: 1235
	private float strikeWaitTime;

	// Token: 0x040004D4 RID: 1236
	public AnimationCurve strikeCurve;

	// Token: 0x040004D5 RID: 1237
	public AudioSource loopAudio;

	// Token: 0x040004D6 RID: 1238
	public float loopAudioVolume = 1f;

	// Token: 0x040004D7 RID: 1239
	private UT_AudioClipHandler clipHandler;

	// Token: 0x040004D8 RID: 1240
	public float hitDamage = 1f;

	// Token: 0x040004D9 RID: 1241
	public LayerMask damageMask;

	// Token: 0x040004DA RID: 1242
	public float damageRadius;
}
