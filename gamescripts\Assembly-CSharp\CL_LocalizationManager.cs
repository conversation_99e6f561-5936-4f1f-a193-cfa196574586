﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using UnityEngine;

// Token: 0x0200001D RID: 29
public class CL_LocalizationManager : MonoBehaviour
{
	// Token: 0x06000127 RID: 295 RVA: 0x00009FCD File Offset: 0x000081CD
	private void Awake()
	{
		CL_LocalizationManager.currentLocalization = new CL_LocalizationManager.Localization();
		CL_LocalizationManager.currentLocalization = JsonConvert.DeserializeObject<CL_LocalizationManager.Localization>(this.languageFile.text);
		Debug.Log(CL_LocalizationManager.currentLocalization);
	}

	// Token: 0x040000F4 RID: 244
	public static CL_LocalizationManager.Localization currentLocalization;

	// Token: 0x040000F5 RID: 245
	public TextAsset languageFile;

	// Token: 0x02000203 RID: 515
	[Serializable]
	public class LocalizationLine
	{
		// Token: 0x04000DC0 RID: 3520
		public string id;

		// Token: 0x04000DC1 RID: 3521
		public string text;
	}

	// Token: 0x02000204 RID: 516
	[Serializable]
	public class Localization
	{
		// Token: 0x06000CC2 RID: 3266 RVA: 0x0004F8DB File Offset: 0x0004DADB
		public string GetAnnouncementLine(string key)
		{
			if (!this.announcements.ContainsKey(key))
			{
				return "";
			}
			return this.announcements[key];
		}

		// Token: 0x04000DC2 RID: 3522
		public Dictionary<string, string> announcements;
	}
}
