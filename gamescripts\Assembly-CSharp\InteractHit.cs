﻿using System;
using UnityEngine;

// Token: 0x02000073 RID: 115
public class InteractHit
{
	// Token: 0x060003FC RID: 1020 RVA: 0x00024A1C File Offset: 0x00022C1C
	public void Initialize(RaycastHit hit)
	{
		this.point = hit.point;
		this.distance = hit.distance;
		this.collider = hit.collider;
		this.normal = hit.normal;
		this.transform = hit.transform;
		this.rigidbody = hit.rigidbody;
	}

	// Token: 0x04000554 RID: 1364
	public Vector3 point;

	// Token: 0x04000555 RID: 1365
	public float distance;

	// Token: 0x04000556 RID: 1366
	public Collider collider;

	// Token: 0x04000557 RID: 1367
	public Vector3 normal;

	// Token: 0x04000558 RID: 1368
	public Transform transform;

	// Token: 0x04000559 RID: 1369
	public Rigidbody rigidbody;
}
