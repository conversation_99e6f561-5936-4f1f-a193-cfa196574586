﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000117 RID: 279
public class VoiceSettingsBinder : MonoBehaviour
{
	// Token: 0x06000862 RID: 2146 RVA: 0x0003C88B File Offset: 0x0003AA8B
	private void Start()
	{
		this.PullSetting();
		this.voiceToggle.onClick.AddListener(delegate
		{
			this.UpdateSetting();
		});
	}

	// Token: 0x06000863 RID: 2147 RVA: 0x0003C8B0 File Offset: 0x0003AAB0
	private void PullSetting()
	{
		int num = int.Parse(SettingsManager.GetSetting(this.settingName));
		this.curValue = num;
		this.ToggleDisplay(num);
	}

	// Token: 0x06000864 RID: 2148 RVA: 0x0003C8DC File Offset: 0x0003AADC
	private void ToggleDisplay(int value)
	{
		this.text.text = "Voice " + (value + 1).ToString();
	}

	// Token: 0x06000865 RID: 2149 RVA: 0x0003C90C File Offset: 0x0003AB0C
	public void UpdateSetting()
	{
		if (this.curValue == 1)
		{
			this.curValue = 0;
		}
		else
		{
			this.curValue = 1;
		}
		this.ToggleDisplay(this.curValue);
		AudioManager.PlayUISound(this.voiceExamples[this.curValue], 0.7f, 1f);
		SettingsManager.SetSetting(new string[]
		{
			this.settingName,
			this.curValue.ToString()
		});
		SettingsManager.RefreshSettings(this.settingName);
	}

	// Token: 0x040009BF RID: 2495
	public string settingName;

	// Token: 0x040009C0 RID: 2496
	public Button voiceToggle;

	// Token: 0x040009C1 RID: 2497
	public TMP_Text text;

	// Token: 0x040009C2 RID: 2498
	private int curValue;

	// Token: 0x040009C3 RID: 2499
	public AudioClip[] voiceExamples;
}
