﻿using System;
using UnityEngine;

// Token: 0x02000165 RID: 357
public class UT_DrainingCounter : UT_Counter
{
	// Token: 0x06000A0D RID: 2573 RVA: 0x00043799 File Offset: 0x00041999
	private void Update()
	{
		this.count = Mathf.RoundToInt(this.curCount);
		if (this.curCount > 0f)
		{
			this.curCount -= Time.deltaTime * this.drainSpeed;
		}
	}

	// Token: 0x06000A0E RID: 2574 RVA: 0x000437D4 File Offset: 0x000419D4
	public override void AddCount(int i)
	{
		base.AddCount(i);
		if (this.count >= this.maxCount)
		{
			if (this.lockAtMax)
			{
				this.locked = true;
			}
			if (this.resetAtMax)
			{
				this.curCount = 0f;
				this.Reset();
			}
		}
		this.curCount = (float)this.count;
	}

	// Token: 0x04000B29 RID: 2857
	private float curCount;

	// Token: 0x04000B2A RID: 2858
	public float drainSpeed = 1f;

	// Token: 0x04000B2B RID: 2859
	public bool lockAtMax = true;

	// Token: 0x04000B2C RID: 2860
	private bool locked;

	// Token: 0x04000B2D RID: 2861
	public bool resetAtMax;
}
