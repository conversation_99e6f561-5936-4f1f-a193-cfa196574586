﻿using System;
using UnityEngine;

// Token: 0x02000039 RID: 57
[RequireComponent(typeof(UT_AudioClipHandler))]
public class DEN_SlugGrub : Denizen
{
	// Token: 0x06000221 RID: 545 RVA: 0x00011FC0 File Offset: 0x000101C0
	public override void Start()
	{
		base.Start();
		this.itemObject = base.GetComponent<Item_Object>();
		if (Random.value > 0.5f)
		{
			base.transform.localScale = Vector3.Scale(base.transform.localScale, new Vector3(-1f, 1f, 1f));
		}
		this.barkTime = Random.Range(this.barkRate * 0.5f, this.barkRate * 2f);
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		this.rigid = base.GetComponent<Rigidbody>();
	}

	// Token: 0x06000222 RID: 546 RVA: 0x00012058 File Offset: 0x00010258
	private void OnEnable()
	{
		if (this.itemObject != null && this.itemObject.itemData.inBag)
		{
			this.wakeTime = Random.Range(0f, 2.5f);
			this.animator.SetBool("awake", false);
		}
	}

	// Token: 0x06000223 RID: 547 RVA: 0x000120AC File Offset: 0x000102AC
	public override void Update()
	{
		base.Update();
		Collider[] array = Physics.OverlapSphere(base.transform.position, this.sightDistance, this.sight.sightMask);
		if (array.Length != 0)
		{
			for (int i = 0; i < array.Length; i++)
			{
				ObjectTagger component = array[i].GetComponent<ObjectTagger>();
				if (component != null && component.HasTagInList(this.fearTags))
				{
					if (CL_UIManager.debug)
					{
						CL_DebugView.draw.SphereOutline(base.transform.position, 0.45f, Color.yellow);
					}
					if (this.fearTime <= 0f)
					{
						this.wakeTime = Random.Range(0f, 2.5f);
					}
					this.fearTime = 1f;
				}
			}
		}
		if (this.fearTime > 0f)
		{
			this.barkTime -= Time.deltaTime;
			this.fearTime -= Time.deltaTime;
			if (this.wakeTime <= 0f)
			{
				this.animator.SetBool("awake", true);
			}
			else
			{
				this.wakeTime -= Time.deltaTime;
			}
		}
		else
		{
			this.animator.SetBool("awake", false);
		}
		if (this.itemObject.itemData.inBag)
		{
			if (this.wakeTime <= 0f)
			{
				this.animator.SetBool("awake", true);
			}
			else
			{
				this.wakeTime -= Time.deltaTime;
			}
		}
		if (!this.rigid.isKinematic)
		{
			base.transform.rotation = Quaternion.Euler(0f, base.transform.rotation.eulerAngles.y, 0f);
		}
	}

	// Token: 0x040002AA RID: 682
	public string[] fearTags;

	// Token: 0x040002AB RID: 683
	public float sightDistance = 2f;

	// Token: 0x040002AC RID: 684
	public float barkRate = 1f;

	// Token: 0x040002AD RID: 685
	private float barkTime;

	// Token: 0x040002AE RID: 686
	private float fearTime;

	// Token: 0x040002AF RID: 687
	private float wakeTime;

	// Token: 0x040002B0 RID: 688
	public Animator animator;

	// Token: 0x040002B1 RID: 689
	private Rigidbody rigid;

	// Token: 0x040002B2 RID: 690
	private Item_Object itemObject;
}
