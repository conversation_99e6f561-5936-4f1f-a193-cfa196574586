﻿using System;
using UnityEngine;

// Token: 0x020000CA RID: 202
public class UT_DestroyAfterTime : MonoBehaviour
{
	// Token: 0x06000699 RID: 1689 RVA: 0x00035264 File Offset: 0x00033464
	private void Start()
	{
		this.time = this.destroyTime;
	}

	// Token: 0x0600069A RID: 1690 RVA: 0x00035274 File Offset: 0x00033474
	private void Update()
	{
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		this.time -= Time.deltaTime;
		if (this.time <= 0f)
		{
			if (this.deathObject)
			{
				Object.Instantiate<GameObject>(this.deathObject, base.transform.position, base.transform.rotation, base.transform.parent);
			}
			if (!this.disableAfterTime)
			{
				Object.Destroy(base.gameObject);
				return;
			}
			base.gameObject.SetActive(false);
		}
	}

	// Token: 0x0600069B RID: 1691 RVA: 0x00035307 File Offset: 0x00033507
	public void ResetTimer()
	{
		this.time = this.destroyTime;
	}

	// Token: 0x04000812 RID: 2066
	public float destroyTime = 10f;

	// Token: 0x04000813 RID: 2067
	public GameObject deathObject;

	// Token: 0x04000814 RID: 2068
	public bool disableAfterTime;

	// Token: 0x04000815 RID: 2069
	private float time;
}
