﻿using System;
using System.Collections;
using System.Runtime.CompilerServices;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.SceneManagement;
using UnityEngine.Video;

// Token: 0x02000178 RID: 376
public class UT_Intro : MonoBehaviour
{
	// Token: 0x06000A6F RID: 2671 RVA: 0x00044A70 File Offset: 0x00042C70
	private void Start()
	{
		this.video = base.GetComponent<VideoPlayer>();
		this.videoTime = (float)this.video.length + 1f;
		base.StartCoroutine(this.<Start>g__StartProcess|8_0());
	}

	// Token: 0x06000A70 RID: 2672 RVA: 0x00044AA4 File Offset: 0x00042CA4
	private void Update()
	{
		if (this.hasSkipped)
		{
			return;
		}
		if (Input.GetKeyDown(KeyCode.Space) || Input.GetButtonDown("Jump") || Input.anyKeyDown)
		{
			this.EndIntro();
		}
		else if (this.videoTime <= 0f)
		{
			this.EndIntro();
		}
		this.videoTime -= Time.deltaTime;
	}

	// Token: 0x06000A71 RID: 2673 RVA: 0x00044B04 File Offset: 0x00042D04
	private void EndIntro()
	{
		this.video.Stop();
		this.hasSkipped = true;
		this.loadPercentageText.transform.parent.gameObject.SetActive(true);
		this.loadPercentageText.text = "Loading: 0%";
		base.StartCoroutine(this.<EndIntro>g__WaitOneFrame|10_0());
	}

	// Token: 0x06000A73 RID: 2675 RVA: 0x00044B63 File Offset: 0x00042D63
	[CompilerGenerated]
	private IEnumerator <Start>g__StartProcess|8_0()
	{
		this.loadPercentageText.transform.parent.gameObject.SetActive(false);
		yield return null;
		this.loadPercentageText.transform.parent.gameObject.SetActive(true);
		yield return null;
		this.loadPercentageText.transform.parent.gameObject.SetActive(false);
		this.loadMenuOperation = SceneManager.LoadSceneAsync(this.loadScene);
		this.loadMenuOperation.allowSceneActivation = false;
		yield break;
	}

	// Token: 0x06000A74 RID: 2676 RVA: 0x00044B72 File Offset: 0x00042D72
	[CompilerGenerated]
	private IEnumerator <EndIntro>g__WaitOneFrame|10_0()
	{
		yield return new WaitForSeconds(0.1f);
		this.onEnd.Invoke();
		yield return new WaitForSeconds(0.1f);
		while (!this.loadMenuOperation.isDone)
		{
			this.loadPercentageText.text = string.Format("Loading: {0}%", Mathf.RoundToInt(this.loadMenuOperation.progress * 100f));
			yield return null;
			yield return null;
			if ((double)this.loadMenuOperation.progress >= 0.89)
			{
				this.loadMenuOperation.allowSceneActivation = true;
			}
		}
		this.loadPercentageText.text = string.Format("Loading: {0}%", Mathf.RoundToInt(this.loadMenuOperation.progress * 100f));
		this.loadMenuOperation.allowSceneActivation = true;
		yield break;
	}

	// Token: 0x04000B6F RID: 2927
	public string loadScene;

	// Token: 0x04000B70 RID: 2928
	private VideoPlayer video;

	// Token: 0x04000B71 RID: 2929
	private float videoTime;

	// Token: 0x04000B72 RID: 2930
	private bool hasSkipped;

	// Token: 0x04000B73 RID: 2931
	public UnityEvent onSkip;

	// Token: 0x04000B74 RID: 2932
	public UnityEvent onEnd;

	// Token: 0x04000B75 RID: 2933
	private AsyncOperation loadMenuOperation;

	// Token: 0x04000B76 RID: 2934
	public TMP_Text loadPercentageText;
}
