﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000091 RID: 145
public class HandItem_Shoot : HandItem
{
	// Token: 0x060004D7 RID: 1239 RVA: 0x00029E5C File Offset: 0x0002805C
	public override void Initialize(Item i, ENT_Player.Hand h)
	{
		base.Initialize(i, h);
		if (this.useAmmo)
		{
			int.TryParse(this.item.GetFirstDataStringByType("ammo", false), out this.ammo);
			if (this.loadedAnimationState)
			{
				this.anim.SetBool("Loaded", this.ammo > 0);
			}
		}
	}

	// Token: 0x060004D8 RID: 1240 RVA: 0x00029EB7 File Offset: 0x000280B7
	private void OnEnable()
	{
		this.ResetUsedState();
	}

	// Token: 0x060004D9 RID: 1241 RVA: 0x00029EC0 File Offset: 0x000280C0
	public override void Use()
	{
		base.Use();
		if (this.used)
		{
			return;
		}
		if (this.useAmmo)
		{
			int.TryParse(this.item.GetFirstDataStringByType("ammo", false), out this.ammo);
			if (this.loadedAnimationState)
			{
				this.anim.SetBool("Loaded", this.ammo > 0);
			}
			if (this.ammo == 0)
			{
				Item itemByTag = Inventory.instance.GetItemByTag(this.ammoTag);
				if (itemByTag != null)
				{
					itemByTag.DestroyItem();
					this.StartReload();
					return;
				}
				if (this.clipHandler != null)
				{
					this.clipHandler.PlaySound("item:noAmmo");
				}
				return;
			}
		}
		this.used = true;
		this.anim.SetTrigger("Use");
		if (this.useAmmo)
		{
			this.ammo--;
			this.item.SetFirstDataStringsofType("ammo", this.ammo.ToString());
			if (this.loadedAnimationState && this.ammo == 0)
			{
				this.anim.SetBool("Loaded", false);
			}
		}
		if (this.clipHandler != null)
		{
			this.clipHandler.PlaySound("item:use");
		}
		CL_CameraControl.Shake(this.screenShake);
		this.useEvent.Invoke();
	}

	// Token: 0x060004DA RID: 1242 RVA: 0x0002A010 File Offset: 0x00028210
	public void Shoot()
	{
		RaycastHit raycastHit;
		Physics.Raycast(Camera.main.transform.position, Camera.main.transform.forward, out raycastHit, float.PositiveInfinity, this.aimMask);
		Vector3 vector = Vector3.Lerp(base.transform.position, Camera.main.transform.position, 0.5f) + -base.transform.forward * 0.25f;
		GameObject gameObject = Object.Instantiate<GameObject>(this.projectile, vector, Quaternion.LookRotation(base.transform.forward));
		if (Physics.Raycast(Camera.main.transform.position, Camera.main.transform.forward, out raycastHit, float.PositiveInfinity, this.aimMask))
		{
			gameObject.GetComponent<Projectile>().Initialize((raycastHit.point - gameObject.transform.position).normalized * this.shootSpeed);
		}
		else
		{
			gameObject.GetComponent<Projectile>().Initialize(Camera.main.transform.forward * this.shootSpeed);
		}
		this.UpdateStats();
		CL_CameraControl.Shake(0.1f);
		if (this.recoilJumpStat != "" && this.hand.GetPlayer().cCon.velocity.y > 1f)
		{
			StatManager.sessionStats.UpdateStatistic(this.recoilJumpStat, 1, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Add, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
		}
		if (this.hand.GetPlayer().cCon.isGrounded)
		{
			this.hand.GetPlayer().AddForce(-Camera.main.transform.forward * this.recoil * 0.2f);
			return;
		}
		this.hand.GetPlayer().AddForce(-Camera.main.transform.forward * this.recoil * 1f, this.maxRecoilMagnitude);
		if (!this.canUseWhileHanging)
		{
			this.hand.GetPlayer().DropHang(0.3f);
		}
	}

	// Token: 0x060004DB RID: 1243 RVA: 0x0002A256 File Offset: 0x00028456
	public void ResetUsedState()
	{
		this.used = false;
	}

	// Token: 0x060004DC RID: 1244 RVA: 0x0002A25F File Offset: 0x0002845F
	public void PitonHit()
	{
		this.Shoot();
	}

	// Token: 0x060004DD RID: 1245 RVA: 0x0002A267 File Offset: 0x00028467
	public virtual void StartReload()
	{
		this.used = true;
		this.anim.SetTrigger("Reload");
		if (this.clipHandler != null)
		{
			this.clipHandler.PlaySound("item:reload");
		}
	}

	// Token: 0x060004DE RID: 1246 RVA: 0x0002A2A0 File Offset: 0x000284A0
	public virtual void FinishReload()
	{
		this.used = false;
		this.ammo++;
		this.item.SetFirstDataStringsofType("ammo", this.ammo.ToString() ?? "");
		if (this.loadedAnimationState)
		{
			this.anim.SetBool("Loaded", true);
		}
		this.reloadEvent.Invoke();
	}

	// Token: 0x060004DF RID: 1247 RVA: 0x0002A30A File Offset: 0x0002850A
	public override bool CanDrop()
	{
		return !this.used;
	}

	// Token: 0x04000662 RID: 1634
	public LayerMask aimMask;

	// Token: 0x04000663 RID: 1635
	public GameObject projectile;

	// Token: 0x04000664 RID: 1636
	private RaycastHit hit;

	// Token: 0x04000665 RID: 1637
	public float shootSpeed = 50f;

	// Token: 0x04000666 RID: 1638
	public float recoil = 1f;

	// Token: 0x04000667 RID: 1639
	public float maxRecoilMagnitude = 0.5f;

	// Token: 0x04000668 RID: 1640
	public float screenShake = 0.015f;

	// Token: 0x04000669 RID: 1641
	public bool useAmmo;

	// Token: 0x0400066A RID: 1642
	public string ammoTag;

	// Token: 0x0400066B RID: 1643
	public bool canReload = true;

	// Token: 0x0400066C RID: 1644
	public int maxAmmo = 5;

	// Token: 0x0400066D RID: 1645
	public bool loadedAnimationState;

	// Token: 0x0400066E RID: 1646
	private int ammo = 5;

	// Token: 0x0400066F RID: 1647
	public UnityEvent useEvent;

	// Token: 0x04000670 RID: 1648
	public UnityEvent reloadEvent;

	// Token: 0x04000671 RID: 1649
	public UT_AudioClipHandler clipHandler;

	// Token: 0x04000672 RID: 1650
	public string recoilJumpStat;
}
