﻿using System;
using Drawing;
using UnityEngine;

// Token: 0x0200010B RID: 267
public class OS_Computer_Interface : MonoBehaviour, SaveableObject, Seedable
{
	// Token: 0x0600081A RID: 2074 RVA: 0x0003B3E8 File Offset: 0x000395E8
	private void Start()
	{
		this.os = Object.Instantiate<OS_Manager>(this.managerPrefab, null);
		this.os.cameraControl = this.screenTakeover;
		this.systemData.leverPulled = false;
		this.os.worldInterface = this;
		Debug.Log(this.os.worldInterface);
		this.os.gameObject.SetActive(false);
		this.aud = base.GetComponent<AudioSource>();
		this.volume = this.aud.volume;
		this.eventController = base.GetComponent<UT_EventController>();
		if (this.computerID != "")
		{
			CL_GameManager.SessionFlag gameFlag = CL_GameManager.GetGameFlag("recieved-" + this.computerID + "-perk");
			if (gameFlag != null && gameFlag.state)
			{
				this.eventController.PlayEvent("injector-plunge");
				return;
			}
		}
	}

	// Token: 0x0600081B RID: 2075 RVA: 0x0003B4C3 File Offset: 0x000396C3
	private void OnDisable()
	{
		if (this.os != null)
		{
			this.os.gameObject.SetActive(false);
		}
	}

	// Token: 0x0600081C RID: 2076 RVA: 0x0003B4E4 File Offset: 0x000396E4
	private void OnEnable()
	{
		this.os != null;
	}

	// Token: 0x0600081D RID: 2077 RVA: 0x0003B4F3 File Offset: 0x000396F3
	private void OnDestroy()
	{
		if (this.os != null)
		{
			Object.Destroy(this.os.gameObject);
		}
	}

	// Token: 0x0600081E RID: 2078 RVA: 0x0003B514 File Offset: 0x00039714
	public void ActivateComputer()
	{
		if (CL_GameManager.IsLoading() || CL_GameManager.gMan.IsReviving())
		{
			return;
		}
		if (OS_Manager.activeComputer != null && OS_Manager.activeComputer.IsInUse())
		{
			return;
		}
		ENT_Player.playerObject.StashItemsInHand();
		this.os.gameObject.SetActive(true);
		this.os.Activate();
		this.aud.Play();
		this.pitchTarget = 1f;
	}

	// Token: 0x0600081F RID: 2079 RVA: 0x0003B58B File Offset: 0x0003978B
	public void DeactivateComputer()
	{
		this.pitchTarget = 0.8f;
		this.os.Deactivate();
	}

	// Token: 0x06000820 RID: 2080 RVA: 0x0003B5A4 File Offset: 0x000397A4
	private void Update()
	{
		this.aud.pitch = Mathf.Lerp(this.aud.pitch, this.pitchTarget, Time.deltaTime);
		this.aud.volume = Mathf.Lerp(this.aud.volume, this.pitchTarget * this.volume, Time.deltaTime);
		if (CL_UIManager.debug)
		{
			CL_DebugView.draw.Label2D(base.transform.position, "Computer Seed: " + this.seed.ToString(), 8f, LabelAlignment.Center, Color.white);
		}
	}

	// Token: 0x06000821 RID: 2081 RVA: 0x0003B649 File Offset: 0x00039849
	public void SetLeverState(bool b)
	{
		this.systemData.leverPulled = b;
	}

	// Token: 0x06000822 RID: 2082 RVA: 0x0003B657 File Offset: 0x00039857
	public OS_Manager GetComputer()
	{
		return this.os;
	}

	// Token: 0x06000823 RID: 2083 RVA: 0x0003B65F File Offset: 0x0003985F
	public UT_EventController GetEventController()
	{
		return this.eventController;
	}

	// Token: 0x06000824 RID: 2084 RVA: 0x0003B667 File Offset: 0x00039867
	public SaveableInfo GetSaveInfo()
	{
		new SaveableInfo();
		throw new NotImplementedException();
	}

	// Token: 0x06000825 RID: 2085 RVA: 0x0003B674 File Offset: 0x00039874
	public void SetSaveInfo(SaveableInfo info)
	{
		throw new NotImplementedException();
	}

	// Token: 0x06000826 RID: 2086 RVA: 0x0003B67B File Offset: 0x0003987B
	public void SetSeed(int s)
	{
		this.seed = s;
	}

	// Token: 0x06000827 RID: 2087 RVA: 0x0003B684 File Offset: 0x00039884
	public int GetSeed()
	{
		return this.seed;
	}

	// Token: 0x04000982 RID: 2434
	public OS_Manager managerPrefab;

	// Token: 0x04000983 RID: 2435
	private OS_Manager os;

	// Token: 0x04000984 RID: 2436
	public OS_DiskData desktopData;

	// Token: 0x04000985 RID: 2437
	public UT_CameraTakeover screenTakeover;

	// Token: 0x04000986 RID: 2438
	public Transform[] handPositions;

	// Token: 0x04000987 RID: 2439
	public OS_Computer_Interface.SystemData systemData = new OS_Computer_Interface.SystemData();

	// Token: 0x04000988 RID: 2440
	public Transform playerPosition;

	// Token: 0x04000989 RID: 2441
	public CL_ToggleButton lever;

	// Token: 0x0400098A RID: 2442
	public string computerID;

	// Token: 0x0400098B RID: 2443
	private AudioSource aud;

	// Token: 0x0400098C RID: 2444
	private float pitchTarget;

	// Token: 0x0400098D RID: 2445
	private float volume;

	// Token: 0x0400098E RID: 2446
	private UT_EventController eventController;

	// Token: 0x0400098F RID: 2447
	private int seed = -1;

	// Token: 0x020002AA RID: 682
	public class SystemData
	{
		// Token: 0x0400110A RID: 4362
		public bool leverPulled;

		// Token: 0x0400110B RID: 4363
		public bool floppyInserted;
	}
}
