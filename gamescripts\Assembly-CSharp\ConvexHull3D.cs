﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000071 RID: 113
public static class ConvexHull3D
{
	// Token: 0x060003F3 RID: 1011 RVA: 0x00023E88 File Offset: 0x00022088
	public static Mesh GenerateConvexHullMesh(List<Vector3> points)
	{
		if (points == null || points.Count < 4)
		{
			return null;
		}
		HashSet<Vector3> hashSet = new HashSet<Vector3>(points);
		Vector3[] array = new Vector3[hashSet.Count];
		hashSet.CopyTo(array);
		return ConvexHull3D.FacesToMesh(ConvexHull3D.QuickHull(array));
	}

	// Token: 0x060003F4 RID: 1012 RVA: 0x00023EC8 File Offset: 0x000220C8
	private static List<ConvexHull3D.Face> QuickHull(Vector3[] points)
	{
		int num = 0;
		int num2 = 0;
		for (int i = 1; i < points.Length; i++)
		{
			if (points[i].x < points[num].x)
			{
				num = i;
			}
			if (points[i].x > points[num2].x)
			{
				num2 = i;
			}
		}
		int num3 = num;
		int num4 = num2;
		float num5 = -1f;
		int num6 = -1;
		for (int j = 0; j < points.Length; j++)
		{
			if (j != num3 && j != num4)
			{
				float magnitude = Vector3.Cross(points[num4] - points[num3], points[j] - points[num3]).magnitude;
				if (magnitude > num5)
				{
					num5 = magnitude;
					num6 = j;
				}
			}
		}
		float num7 = -1f;
		int num8 = -1;
		Vector3 vector = Vector3.Cross(points[num4] - points[num3], points[num6] - points[num3]);
		for (int k = 0; k < points.Length; k++)
		{
			if (k != num3 && k != num4 && k != num6)
			{
				float num9 = Mathf.Abs(Vector3.Dot(points[k] - points[num3], vector));
				if (num9 > num7)
				{
					num7 = num9;
					num8 = k;
				}
			}
		}
		List<ConvexHull3D.Face> list = new List<ConvexHull3D.Face>();
		if (Vector3.Dot(vector, points[num8] - points[num3]) < 0f)
		{
			int num10 = num4;
			num4 = num6;
			num6 = num10;
			vector = Vector3.Cross(points[num4] - points[num3], points[num6] - points[num3]);
		}
		ConvexHull3D.Face face = new ConvexHull3D.Face(num3, num4, num6, points);
		ConvexHull3D.Face face2 = new ConvexHull3D.Face(num3, num6, num8, points);
		ConvexHull3D.Face face3 = new ConvexHull3D.Face(num3, num8, num4, points);
		ConvexHull3D.Face face4 = new ConvexHull3D.Face(num4, num8, num6, points);
		list.Add(face);
		list.Add(face2);
		list.Add(face3);
		list.Add(face4);
		for (int l = 0; l < points.Length; l++)
		{
			if (l != num3 && l != num4 && l != num6 && l != num8)
			{
				int num11 = -1;
				float num12 = 0f;
				foreach (ConvexHull3D.Face face5 in list)
				{
					float num13 = Vector3.Dot(face5.normal, points[l] - points[face5.vertIndices[0]]);
					if (num13 > 0f && num13 > num12)
					{
						num12 = num13;
						num11 = list.IndexOf(face5);
					}
				}
				if (num11 >= 0)
				{
					list[num11].outside.Add(l);
				}
			}
		}
		bool flag = false;
		while (!flag)
		{
			flag = true;
			for (int m = 0; m < list.Count; m++)
			{
				ConvexHull3D.Face face6 = list[m];
				if (face6.outside.Count > 0)
				{
					int furthestPoint = ConvexHull3D.GetFurthestPoint(face6, points);
					ConvexHull3D.UpdateHullWithPoint(ref list, face6, furthestPoint, points);
					flag = false;
					break;
				}
			}
		}
		return list;
	}

	// Token: 0x060003F5 RID: 1013 RVA: 0x00024210 File Offset: 0x00022410
	private static int GetFurthestPoint(ConvexHull3D.Face face, Vector3[] points)
	{
		int num = face.outside[0];
		float num2 = Vector3.Dot(face.normal, points[num] - points[face.vertIndices[0]]);
		foreach (int num3 in face.outside)
		{
			float num4 = Vector3.Dot(face.normal, points[num3] - points[face.vertIndices[0]]);
			if (num4 > num2)
			{
				num2 = num4;
				num = num3;
			}
		}
		return num;
	}

	// Token: 0x060003F6 RID: 1014 RVA: 0x000242C4 File Offset: 0x000224C4
	private static void UpdateHullWithPoint(ref List<ConvexHull3D.Face> faces, ConvexHull3D.Face face, int pointIndex, Vector3[] points)
	{
		List<ConvexHull3D.Face> list = new List<ConvexHull3D.Face>();
		foreach (ConvexHull3D.Face face2 in faces)
		{
			if (face2.PointAboveFace(points[pointIndex], points[face2.vertIndices[0]]))
			{
				list.Add(face2);
			}
		}
		HashSet<ValueTuple<int, int>> hashSet = new HashSet<ValueTuple<int, int>>();
		foreach (ConvexHull3D.Face face3 in list)
		{
			for (int i = 0; i < 3; i++)
			{
				int num = face3.vertIndices[i];
				int num2 = face3.vertIndices[(i + 1) % 3];
				ValueTuple<int, int> valueTuple = new ValueTuple<int, int>(Mathf.Min(num, num2), Mathf.Max(num, num2));
				hashSet.Add(valueTuple);
			}
		}
		List<ValueTuple<int, int>> list2 = new List<ValueTuple<int, int>>();
		Dictionary<ValueTuple<int, int>, int> dictionary = new Dictionary<ValueTuple<int, int>, int>();
		foreach (ValueTuple<int, int> valueTuple2 in hashSet)
		{
			if (!dictionary.ContainsKey(valueTuple2))
			{
				dictionary[valueTuple2] = 0;
			}
			Dictionary<ValueTuple<int, int>, int> dictionary2 = dictionary;
			ValueTuple<int, int> valueTuple3 = valueTuple2;
			int num3 = dictionary2[valueTuple3];
			dictionary2[valueTuple3] = num3 + 1;
		}
		foreach (KeyValuePair<ValueTuple<int, int>, int> keyValuePair in dictionary)
		{
			if (keyValuePair.Value > 1)
			{
				list2.Add(keyValuePair.Key);
			}
		}
		foreach (ValueTuple<int, int> valueTuple4 in list2)
		{
			hashSet.Remove(valueTuple4);
		}
		foreach (ConvexHull3D.Face face4 in list)
		{
			faces.Remove(face4);
		}
		List<ConvexHull3D.Face> list3 = new List<ConvexHull3D.Face>();
		foreach (ValueTuple<int, int> valueTuple5 in hashSet)
		{
			ConvexHull3D.Face face5 = new ConvexHull3D.Face(valueTuple5.Item1, valueTuple5.Item2, pointIndex, points);
			list3.Add(face5);
		}
		faces.AddRange(list3);
		List<int> list4 = new List<int>();
		foreach (ConvexHull3D.Face face6 in list)
		{
			list4.AddRange(face6.outside);
		}
		HashSet<int> hashSet2 = new HashSet<int>(list4);
		hashSet2.Remove(pointIndex);
		foreach (int num4 in hashSet2)
		{
			float num5 = 0f;
			ConvexHull3D.Face face7 = null;
			foreach (ConvexHull3D.Face face8 in list3)
			{
				float num6 = Vector3.Dot(face8.normal, points[num4] - points[face8.vertIndices[0]]);
				if (num6 > 0f && num6 > num5)
				{
					num5 = num6;
					face7 = face8;
				}
			}
			if (face7 != null)
			{
				face7.outside.Add(num4);
			}
		}
	}

	// Token: 0x060003F7 RID: 1015 RVA: 0x000246A4 File Offset: 0x000228A4
	private static Mesh FacesToMesh(List<ConvexHull3D.Face> faces)
	{
		HashSet<int> hashSet = new HashSet<int>();
		foreach (ConvexHull3D.Face face in faces)
		{
			hashSet.Add(face.vertIndices[0]);
			hashSet.Add(face.vertIndices[1]);
			hashSet.Add(face.vertIndices[2]);
		}
		return ConvexHull3D.BuildMeshFromFaces(faces, ConvexHull3D.lastUsedPoints);
	}

	// Token: 0x060003F8 RID: 1016 RVA: 0x0002472C File Offset: 0x0002292C
	private static Mesh BuildMeshFromFaces(List<ConvexHull3D.Face> faces, Vector3[] points)
	{
		HashSet<int> hashSet = new HashSet<int>();
		foreach (ConvexHull3D.Face face in faces)
		{
			hashSet.Add(face.vertIndices[0]);
			hashSet.Add(face.vertIndices[1]);
			hashSet.Add(face.vertIndices[2]);
		}
		Dictionary<int, int> dictionary = new Dictionary<int, int>();
		List<Vector3> list = new List<Vector3>();
		foreach (int num in hashSet)
		{
			dictionary[num] = list.Count;
			list.Add(points[num]);
		}
		List<int> list2 = new List<int>();
		foreach (ConvexHull3D.Face face2 in faces)
		{
			list2.Add(dictionary[face2.vertIndices[0]]);
			list2.Add(dictionary[face2.vertIndices[1]]);
			list2.Add(dictionary[face2.vertIndices[2]]);
		}
		Mesh mesh = new Mesh();
		mesh.vertices = list.ToArray();
		mesh.triangles = list2.ToArray();
		mesh.RecalculateNormals();
		mesh.RecalculateBounds();
		return mesh;
	}

	// Token: 0x060003F9 RID: 1017 RVA: 0x000248B4 File Offset: 0x00022AB4
	private static Mesh FacesToMesh(List<ConvexHull3D.Face> faces, Vector3[] points)
	{
		List<Vector3> list = new List<Vector3>();
		Dictionary<int, int> dictionary = new Dictionary<int, int>();
		foreach (ConvexHull3D.Face face in faces)
		{
			foreach (int num in face.vertIndices)
			{
				if (!dictionary.ContainsKey(num))
				{
					dictionary[num] = list.Count;
					list.Add(points[num]);
				}
			}
		}
		List<int> list2 = new List<int>();
		foreach (ConvexHull3D.Face face2 in faces)
		{
			list2.Add(dictionary[face2.vertIndices[0]]);
			list2.Add(dictionary[face2.vertIndices[1]]);
			list2.Add(dictionary[face2.vertIndices[2]]);
		}
		Mesh mesh = new Mesh();
		mesh.vertices = list.ToArray();
		mesh.triangles = list2.ToArray();
		mesh.RecalculateNormals();
		mesh.RecalculateBounds();
		return mesh;
	}

	// Token: 0x04000552 RID: 1362
	private static Vector3[] lastUsedPoints;

	// Token: 0x0200024F RID: 591
	private class Face
	{
		// Token: 0x06000D9D RID: 3485 RVA: 0x00053368 File Offset: 0x00051568
		public Face(int v0, int v1, int v2, Vector3[] points)
		{
			this.vertIndices = new int[] { v0, v1, v2 };
			this.normal = Vector3.Normalize(Vector3.Cross(points[v1] - points[v0], points[v2] - points[v0]));
			this.outside = new List<int>();
		}

		// Token: 0x06000D9E RID: 3486 RVA: 0x000533D8 File Offset: 0x000515D8
		public bool PointAboveFace(Vector3 p, Vector3 refPoint)
		{
			return Vector3.Dot(p - refPoint, this.normal) > 0f;
		}

		// Token: 0x04000F4B RID: 3915
		public Vector3 normal;

		// Token: 0x04000F4C RID: 3916
		public int[] vertIndices;

		// Token: 0x04000F4D RID: 3917
		public List<int> outside;
	}
}
