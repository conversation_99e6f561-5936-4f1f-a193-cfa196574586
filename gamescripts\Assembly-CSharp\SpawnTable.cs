﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000118 RID: 280
[CreateAssetMenu(fileName = "New Spawn Table", menuName = "White Knuckle/Spawning/Spawn Table")]
public class SpawnTable : ScriptableObject
{
	// Token: 0x06000868 RID: 2152 RVA: 0x0003C998 File Offset: 0x0003AB98
	public SpawnTable.SpawnTableAsset GetRandomSpawnObject()
	{
		if (this.spawnList == null || this.spawnList.Count == 0)
		{
			Debug.LogWarning("Spawn table is empty!");
			return null;
		}
		float num = 0f;
		foreach (SpawnTable.SpawnTableAsset spawnTableAsset in this.spawnList)
		{
			num += spawnTableAsset.GetEffectiveSpawnChance();
		}
		float num2 = Random.Range(0f, num);
		float num3 = 0f;
		foreach (SpawnTable.SpawnTableAsset spawnTableAsset2 in this.spawnList)
		{
			num3 += spawnTableAsset2.GetEffectiveSpawnChance();
			if (num2 <= num3)
			{
				return spawnTableAsset2;
			}
		}
		return null;
	}

	// Token: 0x040009C4 RID: 2500
	public List<SpawnTable.SpawnTableAsset> spawnList;

	// Token: 0x020002B1 RID: 689
	[Serializable]
	public class SpawnSettings
	{
		// Token: 0x06000EBB RID: 3771 RVA: 0x00058EA8 File Offset: 0x000570A8
		public float GetEffectiveSpawnChance()
		{
			float num = this.spawnChance;
			if (this.useProgressionUnlock && !CL_ProgressionManager.HasProgressionUnlock(this.unlockName))
			{
				return 0f;
			}
			if (this.useFlags)
			{
				using (List<string>.Enumerator enumerator = this.flagBlacklist.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						if (CL_GameManager.HasActiveFlag(enumerator.Current))
						{
							return 0f;
						}
					}
				}
				if (this.flagWhitelist.Count > 0)
				{
					bool flag = false;
					using (List<string>.Enumerator enumerator = this.flagWhitelist.GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							if (CL_GameManager.HasActiveFlag(enumerator.Current))
							{
								flag = true;
							}
						}
					}
					if (!flag)
					{
						return 0f;
					}
				}
			}
			if (this.useDifficulty)
			{
				float num2 = Mathf.Clamp01((CL_GameManager.currentDifficulty - this.difficultyMin) / (this.difficultyMax - this.difficultyMin));
				num *= num2;
			}
			if (this.useStats)
			{
				float num3 = Mathf.Clamp01(((float)StatManager.GetStatisticInt(StatManager.sessionStats, this.sessionStat) - this.sessionStatMin) / (this.sessionStatMax - this.sessionStatMin));
				num *= this.sessionStatModifier.Evaluate(num3);
			}
			if (this.useHardMode)
			{
				if (CL_GameManager.IsHardmode())
				{
					num *= this.hardModeMult;
				}
				else if (this.hardModeOnly)
				{
					return 0f;
				}
			}
			if (this.checkStatList)
			{
				foreach (SpawnTable.SpawnSettings.StatCheck statCheck in this.statCheckers)
				{
					if (!this.CheckStat(statCheck))
					{
						return 0f;
					}
				}
			}
			float num4 = Mathf.Clamp01((CL_GameManager.gMan.GetPlayerAscent() - this.ascentMin) / (this.ascentMax - this.ascentMin));
			num *= (this.useAscent ? this.ascentSpawnModifier.Evaluate(num4) : 1f);
			return num;
		}

		// Token: 0x06000EBC RID: 3772 RVA: 0x000590CC File Offset: 0x000572CC
		public bool RandomCheck()
		{
			float value = Random.value;
			return this.GetEffectiveSpawnChance() > value;
		}

		// Token: 0x06000EBD RID: 3773 RVA: 0x000590EC File Offset: 0x000572EC
		public bool CheckStat(SpawnTable.SpawnSettings.StatCheck check)
		{
			bool flag = false;
			int num;
			if (check.session)
			{
				num = StatManager.GetStatisticInt(StatManager.sessionStats, check.statName);
			}
			else
			{
				num = StatManager.GetTotalStatisticInt(check.statName);
			}
			if (check.evaluation == SpawnTable.SpawnSettings.StatCheck.Evaluation.equals)
			{
				if (num == check.evaluationInteger)
				{
					flag = true;
				}
			}
			else if (check.evaluation == SpawnTable.SpawnSettings.StatCheck.Evaluation.greaterThan)
			{
				if (num > check.evaluationInteger)
				{
					flag = true;
				}
			}
			else if (check.evaluation == SpawnTable.SpawnSettings.StatCheck.Evaluation.lesserThan && num < check.evaluationInteger)
			{
				flag = true;
			}
			if (check.invertFlag)
			{
				flag = !flag;
			}
			return flag;
		}

		// Token: 0x0400114B RID: 4427
		[Range(0f, 5f)]
		public float spawnChance = 1f;

		// Token: 0x0400114C RID: 4428
		public bool useProgressionUnlock;

		// Token: 0x0400114D RID: 4429
		public string unlockName;

		// Token: 0x0400114E RID: 4430
		public bool useXP;

		// Token: 0x0400114F RID: 4431
		public float xpMin;

		// Token: 0x04001150 RID: 4432
		public float xpMax = 1f;

		// Token: 0x04001151 RID: 4433
		public AnimationCurve xpSpawnModifier;

		// Token: 0x04001152 RID: 4434
		public bool useDifficulty;

		// Token: 0x04001153 RID: 4435
		public float difficultyMin;

		// Token: 0x04001154 RID: 4436
		public float difficultyMax = 1f;

		// Token: 0x04001155 RID: 4437
		public AnimationCurve difficultySpawnModifier;

		// Token: 0x04001156 RID: 4438
		public bool useAscent;

		// Token: 0x04001157 RID: 4439
		public float ascentMin;

		// Token: 0x04001158 RID: 4440
		public float ascentMax = 1f;

		// Token: 0x04001159 RID: 4441
		public AnimationCurve ascentSpawnModifier;

		// Token: 0x0400115A RID: 4442
		public bool useFlags;

		// Token: 0x0400115B RID: 4443
		public List<string> flagWhitelist;

		// Token: 0x0400115C RID: 4444
		public List<string> flagBlacklist;

		// Token: 0x0400115D RID: 4445
		public bool useStats;

		// Token: 0x0400115E RID: 4446
		public string sessionStat;

		// Token: 0x0400115F RID: 4447
		public float sessionStatMin;

		// Token: 0x04001160 RID: 4448
		public float sessionStatMax = 1f;

		// Token: 0x04001161 RID: 4449
		public AnimationCurve sessionStatModifier;

		// Token: 0x04001162 RID: 4450
		public bool checkStatList;

		// Token: 0x04001163 RID: 4451
		public List<SpawnTable.SpawnSettings.StatCheck> statCheckers;

		// Token: 0x04001164 RID: 4452
		public bool useHardMode;

		// Token: 0x04001165 RID: 4453
		public bool hardModeOnly;

		// Token: 0x04001166 RID: 4454
		public float hardModeMult = 1f;

		// Token: 0x02000317 RID: 791
		[Serializable]
		public class StatCheck
		{
			// Token: 0x0400132C RID: 4908
			public string statName;

			// Token: 0x0400132D RID: 4909
			public bool session = true;

			// Token: 0x0400132E RID: 4910
			public SpawnTable.SpawnSettings.StatCheck.Evaluation evaluation;

			// Token: 0x0400132F RID: 4911
			public int evaluationInteger;

			// Token: 0x04001330 RID: 4912
			public bool invertFlag;

			// Token: 0x02000320 RID: 800
			public enum Evaluation
			{
				// Token: 0x0400134C RID: 4940
				equals,
				// Token: 0x0400134D RID: 4941
				lesserThan,
				// Token: 0x0400134E RID: 4942
				greaterThan
			}
		}
	}

	// Token: 0x020002B2 RID: 690
	[Serializable]
	public class SpawnTableAsset
	{
		// Token: 0x06000EBF RID: 3775 RVA: 0x000591CC File Offset: 0x000573CC
		public float GetEffectiveSpawnChance()
		{
			float num = this.spawnChance;
			if (this.useFlags)
			{
				using (List<string>.Enumerator enumerator = this.flagBlacklist.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						if (CL_GameManager.HasActiveFlag(enumerator.Current))
						{
							return 0f;
						}
					}
				}
				if (this.flagWhitelist.Count <= 0)
				{
					goto IL_00A5;
				}
				bool flag = false;
				using (List<string>.Enumerator enumerator = this.flagWhitelist.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						if (CL_GameManager.HasActiveFlag(enumerator.Current))
						{
							flag = true;
						}
					}
				}
				if (!flag)
				{
					return 0f;
				}
			}
			IL_00A5:
			float num2 = Mathf.Clamp01((CL_GameManager.gMan.GetPlayerAscent() - this.ascentMin) / (this.ascentMax - this.ascentMin));
			num *= (this.useAscent ? this.ascentSpawnModifier.Evaluate(num2) : 1f);
			return num;
		}

		// Token: 0x06000EC0 RID: 3776 RVA: 0x000592E0 File Offset: 0x000574E0
		public GameObject GetRandomPrefab()
		{
			if (this.prefabs == null || this.prefabs.Count == 0)
			{
				return null;
			}
			return this.prefabs[Random.Range(0, this.prefabs.Count)];
		}

		// Token: 0x04001167 RID: 4455
		public string name;

		// Token: 0x04001168 RID: 4456
		public List<string> tags;

		// Token: 0x04001169 RID: 4457
		public List<GameObject> prefabs;

		// Token: 0x0400116A RID: 4458
		public SpawnTable.SpawnSettings spawnSettings;

		// Token: 0x0400116B RID: 4459
		[Range(0f, 5f)]
		public float spawnChance;

		// Token: 0x0400116C RID: 4460
		public bool useXP;

		// Token: 0x0400116D RID: 4461
		public float xpMin;

		// Token: 0x0400116E RID: 4462
		public float xpMax = 1f;

		// Token: 0x0400116F RID: 4463
		public AnimationCurve xpSpawnModifier;

		// Token: 0x04001170 RID: 4464
		public bool useDifficulty;

		// Token: 0x04001171 RID: 4465
		public float difficultyMin;

		// Token: 0x04001172 RID: 4466
		public float difficultyMax = 1f;

		// Token: 0x04001173 RID: 4467
		public AnimationCurve difficultySpawnModifier;

		// Token: 0x04001174 RID: 4468
		public bool useAscent;

		// Token: 0x04001175 RID: 4469
		public float ascentMin;

		// Token: 0x04001176 RID: 4470
		public float ascentMax = 1f;

		// Token: 0x04001177 RID: 4471
		public AnimationCurve ascentSpawnModifier;

		// Token: 0x04001178 RID: 4472
		public bool useFlags;

		// Token: 0x04001179 RID: 4473
		public List<string> flagWhitelist;

		// Token: 0x0400117A RID: 4474
		public List<string> flagBlacklist;
	}
}
