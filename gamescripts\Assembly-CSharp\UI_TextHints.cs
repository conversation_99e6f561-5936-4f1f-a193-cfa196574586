﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.InputSystem;

// Token: 0x02000141 RID: 321
public class UI_TextHints : MonoBehaviour
{
	// Token: 0x06000948 RID: 2376 RVA: 0x000402EA File Offset: 0x0003E4EA
	private void Awake()
	{
		this.input = InputManager.GetPlayerInput();
		this.currentScheme = this.input.currentControlScheme;
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.ReloadText));
	}

	// Token: 0x06000949 RID: 2377 RVA: 0x00040328 File Offset: 0x0003E528
	private void OnEnable()
	{
		if (this.text == null)
		{
			this.text = base.GetComponent<TMP_Text>();
			this.originalText = this.text.text;
			if (this.text == null)
			{
				return;
			}
		}
		this.ReloadText();
	}

	// Token: 0x0600094A RID: 2378 RVA: 0x00040378 File Offset: 0x0003E578
	private void Update()
	{
		if (this.hideUnlessGamepad && !InputManager.IsGamepad())
		{
			this.text.enabled = false;
			return;
		}
		this.text.enabled = true;
		if (this.input.currentControlScheme != this.currentScheme)
		{
			this.currentScheme = this.input.currentControlScheme;
			this.ReloadText();
		}
	}

	// Token: 0x0600094B RID: 2379 RVA: 0x000403DC File Offset: 0x0003E5DC
	public void ReloadText()
	{
		this.text.text = this.originalText;
		foreach (InputAction inputAction in this.input.user.actions)
		{
			string text = InputManager.QuickGetBindingDisplayString((this.forceSchemeName == "") ? this.input.currentControlScheme : this.forceSchemeName, inputAction);
			this.text.text = this.text.text.Replace("{" + inputAction.name + "}", text);
		}
	}

	// Token: 0x0600094C RID: 2380 RVA: 0x000404A0 File Offset: 0x0003E6A0
	public void RefreshText()
	{
		this.originalText = this.text.text;
		foreach (InputAction inputAction in this.input.user.actions)
		{
			string text = InputManager.QuickGetBindingDisplayString((this.forceSchemeName == "") ? this.input.currentControlScheme : this.forceSchemeName, inputAction);
			this.text.text = this.text.text.Replace("{" + inputAction.name + "}", text);
		}
	}

	// Token: 0x0600094D RID: 2381 RVA: 0x00040564 File Offset: 0x0003E764
	public static string GetHintText(string s)
	{
		foreach (InputAction inputAction in InputManager.GetPlayerInput().user.actions)
		{
			string text = InputManager.QuickGetBindingDisplayString(InputManager.GetPlayerInput().currentControlScheme, inputAction);
			s = s.Replace("{" + inputAction.name + "}", text);
		}
		return s;
	}

	// Token: 0x04000A94 RID: 2708
	private TMP_Text text;

	// Token: 0x04000A95 RID: 2709
	private string originalText;

	// Token: 0x04000A96 RID: 2710
	private string currentScheme;

	// Token: 0x04000A97 RID: 2711
	private PlayerInput input;

	// Token: 0x04000A98 RID: 2712
	public bool hideUnlessGamepad;

	// Token: 0x04000A99 RID: 2713
	public string forceSchemeName = "";
}
