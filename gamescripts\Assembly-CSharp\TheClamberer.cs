﻿using System;
using System.Collections.Generic;
using DitzelGames.FastIK;
using Pathfinding;
using UnityEngine;

// Token: 0x0200003D RID: 61
[RequireComponent(typeof(UT_AudioClipHandler))]
public class TheClamberer : MonoBehaviour
{
	// Token: 0x06000242 RID: 578 RVA: 0x00013A00 File Offset: 0x00011C00
	private void Start()
	{
		this.rigid = base.GetComponent<Rigidbody>();
		this.seeker = base.GetComponent<Seeker>();
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		this.randomSoundTime = this.randomSoundInterval;
	}

	// Token: 0x06000243 RID: 579 RVA: 0x00013A34 File Offset: 0x00011C34
	private void Update()
	{
		if (this.target == null)
		{
			return;
		}
		this.targetDistance = Vector3.Distance(base.transform.position, this.target.position);
		this.DecisionTree();
		this.Movement();
		this.RandomNoises();
		if (CL_UIManager.debug)
		{
			CL_DebugView.draw.SphereOutline(base.transform.position, 0.3f, Color.yellow);
			CL_DebugView.draw.Arrowhead(base.transform.position + this.rigid.velocity, this.rigid.velocity, 0.5f, Color.yellow);
			CL_DebugView.draw.Arrowhead(base.transform.position + this.surfaceDir.normalized, this.surfaceDir, 0.25f, Color.cyan);
			CL_DebugView.draw.Label2D(base.transform.position + Vector3.up, "Clamberer : " + this.aiState.ToString(), 10f, Color.yellow);
			if (this.CanSeeTarget(this.target, this.sightDistance))
			{
				CL_DebugView.draw.Label2D(base.transform.position + Vector3.up * 1.2f, "TARGET VISIBLE", 16f, Color.red);
			}
			CL_DebugView.draw.Cross(this.memoryTarget, 0.8f, Color.blue);
			CL_DebugView.draw.SphereOutline(this.memoryTarget, 2f, Color.blue);
		}
	}

	// Token: 0x06000244 RID: 580 RVA: 0x00013C0C File Offset: 0x00011E0C
	private void DecisionTree()
	{
		if (this.CanSeeTarget(this.target, this.sightDistance))
		{
			if (!this.targetVisible)
			{
				this.clipHandler.PlaySound("clamberer:target-sighted");
			}
			this.targetVisible = true;
			if (this.targetDistance < 5f)
			{
				this.aiState = TheClamberer.ClambererAIStates.charging;
			}
			else
			{
				this.aiState = TheClamberer.ClambererAIStates.chasing;
			}
			this.memoryTarget = this.target.position;
			this.curMemory = this.memoryTime;
		}
		else if (this.curMemory >= 0f)
		{
			this.curMemory -= Time.deltaTime;
			this.aiState = TheClamberer.ClambererAIStates.chasing;
			if (this.reachedEndOfPath)
			{
				this.memoryTarget = this.target.position + Random.insideUnitSphere * 10f;
				this.reachedEndOfPath = false;
			}
		}
		else
		{
			if (this.targetVisible)
			{
				this.targetVisible = false;
				this.clipHandler.PlaySound("clamberer:target-lost");
			}
			this.aiState = TheClamberer.ClambererAIStates.hunting;
			if (this.reachedEndOfPath)
			{
				this.memoryTarget = this.target.position + Random.insideUnitSphere * 10f;
				this.reachedEndOfPath = false;
			}
		}
		if (this.pathCheckTime > 0f)
		{
			this.pathCheckTime -= Time.deltaTime;
			return;
		}
		if (!this.findingPath)
		{
			this.findingPath = true;
			this.seeker.StartPath(base.transform.position, this.memoryTarget, new OnPathDelegate(this.OnPathComplete));
		}
	}

	// Token: 0x06000245 RID: 581 RVA: 0x00013D9C File Offset: 0x00011F9C
	private void Movement()
	{
		Vector3 vector = this.target.position - base.transform.position;
		Vector3 vector2 = this.target.position - vector.normalized * this.stopDistance;
		float num = Vector3.Dot(this.surfaceDir.normalized, Vector3.up);
		DebugMenu.UpdateDebugText("creature-surface", num.ToString() ?? "");
		float num2 = 1f;
		base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(Vector3.Scale(this.surfaceDir.normalized, new Vector3(1f, 0f, 1f))), Time.deltaTime * this.rotationSpeed);
		if (this.aiState == TheClamberer.ClambererAIStates.chasing || this.aiState == TheClamberer.ClambererAIStates.charging)
		{
			num2 *= this.chaseSpeed;
		}
		else
		{
			num2 *= this.huntSpeed;
		}
		if (this.path == null)
		{
			return;
		}
		Vector3 vector3 = Vector3.zero;
		if (this.aiState != TheClamberer.ClambererAIStates.charging)
		{
			this.reachedEndOfPath = false;
			float num3;
			for (;;)
			{
				num3 = Vector3.Distance(base.transform.position, this.path.vectorPath[this.currentWaypoint]);
				if (num3 >= this.nextWaypointDistance * 1.5f)
				{
					goto IL_017C;
				}
				if (this.currentWaypoint + 1 >= this.path.vectorPath.Count)
				{
					break;
				}
				this.currentWaypoint++;
			}
			this.reachedEndOfPath = true;
			IL_017C:
			float num4 = (this.reachedEndOfPath ? Mathf.Sqrt(num3 / this.nextWaypointDistance) : 1f);
			vector = (this.path.vectorPath[this.currentWaypoint] - base.transform.position).normalized;
			vector3 = vector * num2 * num4;
		}
		else
		{
			vector3 = (vector2 - base.transform.position).normalized * num2;
		}
		this.ScanAround();
		float num5 = (1f - this.grabPoints) * -16f;
		vector3.y += num5;
		this.rigid.velocity += vector3 * Time.deltaTime;
	}

	// Token: 0x06000246 RID: 582 RVA: 0x00013FF1 File Offset: 0x000121F1
	private void RandomNoises()
	{
		if (this.randomSoundTime >= 0f)
		{
			this.randomSoundTime -= Time.deltaTime;
			return;
		}
		this.randomSoundTime = this.randomSoundInterval;
		this.clipHandler.PlaySound("clamberer:random");
	}

	// Token: 0x06000247 RID: 583 RVA: 0x00014030 File Offset: 0x00012230
	public void ScanAround()
	{
		this.grabPoints = 0f;
		int num = 32;
		Vector3[] array = this.GenerateEquidistantVectors(num);
		Vector3 vector = Vector3.zero;
		vector = this.rigid.velocity.normalized * 0.5f;
		float num2 = 3f;
		this.foundHits.Clear();
		for (int i = 0; i < num; i++)
		{
			Vector3 vector2 = array[i] - Vector3.ClampMagnitude(this.rigid.velocity * 0.25f, 0.9f);
			RaycastHit raycastHit;
			if (Physics.Raycast(base.transform.position, vector2, out raycastHit, num2, this.levelMask))
			{
				if (raycastHit.distance < num2 / 4f)
				{
					this.rigid.velocity += -vector2 * Time.deltaTime * 1f;
				}
				else
				{
					this.rigid.velocity += vector2 * Time.deltaTime * 1f;
				}
				this.foundHits.Add(raycastHit.point);
				vector += vector2.normalized * 0.8f;
				this.grabPoints = Mathf.Clamp(this.grabPoints + 0.5f, 0f, 1f);
				if (CL_UIManager.debug)
				{
					CL_DebugView.draw.Line(base.transform.position, raycastHit.point, new Color(1f, 0f, 0f, 0.1f));
					CL_DebugView.draw.Cross(raycastHit.point, 0.3f, new Color(1f, 0f, 0f, 0.1f));
				}
			}
			else if (CL_UIManager.debug)
			{
				CL_DebugView.draw.DashedLine(base.transform.position, base.transform.position + vector2 * 3f, 0.1f, 0.1f, new Color(0f, 1f, 0f, 0.02f));
			}
		}
		this.surfaceDir = Vector3.Lerp(this.surfaceDir, vector, Time.deltaTime * 10f);
	}

	// Token: 0x06000248 RID: 584 RVA: 0x000142A4 File Offset: 0x000124A4
	public bool CanSeeTarget(Transform t, float dist = 10f)
	{
		RaycastHit raycastHit;
		return Physics.Raycast(base.transform.position, t.position - base.transform.position, out raycastHit, dist) && raycastHit.collider.transform == t;
	}

	// Token: 0x06000249 RID: 585 RVA: 0x000142F8 File Offset: 0x000124F8
	public Vector3[] GenerateEquidistantVectors(int numPoints)
	{
		Vector3[] array = new Vector3[numPoints];
		float num = 3.1415927f * (3f - Mathf.Sqrt(5f));
		for (int i = 0; i < numPoints; i++)
		{
			float num2 = 1f - (float)i / (float)(numPoints - 1) * 2f;
			float num3 = Mathf.Sqrt(1f - num2 * num2);
			float num4 = num * (float)i;
			float num5 = Mathf.Cos(num4) * num3;
			float num6 = Mathf.Sin(num4) * num3;
			array[i] = new Vector3(num5, num2, num6);
		}
		return array;
	}

	// Token: 0x0600024A RID: 586 RVA: 0x0001437E File Offset: 0x0001257E
	public void OnPathComplete(Path p)
	{
		this.findingPath = false;
		this.pathCheckTime = 1f;
		if (!p.error)
		{
			this.path = p;
			this.currentWaypoint = 0;
		}
	}

	// Token: 0x040002FF RID: 767
	[Header("Movement")]
	public float huntSpeed = 1f;

	// Token: 0x04000300 RID: 768
	public float chaseSpeed;

	// Token: 0x04000301 RID: 769
	public float stopDistance = 3f;

	// Token: 0x04000302 RID: 770
	private Vector3 velocity;

	// Token: 0x04000303 RID: 771
	private Rigidbody rigid;

	// Token: 0x04000304 RID: 772
	[Header("AI")]
	public TheClamberer.ClambererAIStates aiState = TheClamberer.ClambererAIStates.hunting;

	// Token: 0x04000305 RID: 773
	public TheClamberer.AIMoveStates moveState;

	// Token: 0x04000306 RID: 774
	public Transform target;

	// Token: 0x04000307 RID: 775
	public float sightDistance = 10f;

	// Token: 0x04000308 RID: 776
	public float memoryTime = 10f;

	// Token: 0x04000309 RID: 777
	private float curMemory;

	// Token: 0x0400030A RID: 778
	private Vector3 memoryTarget;

	// Token: 0x0400030B RID: 779
	private float targetDistance;

	// Token: 0x0400030C RID: 780
	private bool targetVisible;

	// Token: 0x0400030D RID: 781
	public LayerMask levelMask;

	// Token: 0x0400030E RID: 782
	private float grabPoints;

	// Token: 0x0400030F RID: 783
	private Vector3 surfaceDir = Vector3.forward;

	// Token: 0x04000310 RID: 784
	private List<Vector3> foundHits = new List<Vector3>();

	// Token: 0x04000311 RID: 785
	private float pathCheckTime = 1f;

	// Token: 0x04000312 RID: 786
	private bool findingPath;

	// Token: 0x04000313 RID: 787
	private Seeker seeker;

	// Token: 0x04000314 RID: 788
	private Path path;

	// Token: 0x04000315 RID: 789
	private float nextWaypointDistance = 3f;

	// Token: 0x04000316 RID: 790
	private int currentWaypoint;

	// Token: 0x04000317 RID: 791
	private bool reachedEndOfPath;

	// Token: 0x04000318 RID: 792
	[Header("Audio")]
	private UT_AudioClipHandler clipHandler;

	// Token: 0x04000319 RID: 793
	public float randomSoundInterval = 4f;

	// Token: 0x0400031A RID: 794
	private float randomSoundTime;

	// Token: 0x0400031B RID: 795
	[Header("Animation")]
	public float rotationSpeed = 5f;

	// Token: 0x02000224 RID: 548
	[Serializable]
	public class Limb
	{
		// Token: 0x06000D17 RID: 3351 RVA: 0x0005145C File Offset: 0x0004F65C
		public void Initialize()
		{
			this.limbDirection = this.parent.up;
		}

		// Token: 0x06000D18 RID: 3352 RVA: 0x00051470 File Offset: 0x0004F670
		public bool ShouldLetGo(Vector3 dVel, Vector3 dPos)
		{
			this.grabTarget - dPos;
			Vector3 up = this.parent.up;
			float num = Vector3.Dot(this.grabTarget, up);
			float num2 = Vector3.Distance(this.parent.position, this.grabTarget);
			return num < -this.releaseValue || num2 > this.maxReach;
		}

		// Token: 0x06000D19 RID: 3353 RVA: 0x000514D0 File Offset: 0x0004F6D0
		private float CalculateGrabHeuristic(Vector3 hitPos, Vector3 dPos, Vector3 dVel)
		{
			Vector3 vector = hitPos - dPos;
			float num = hitPos.y - dPos.y;
			Vector3 up = this.parent.up;
			float num2 = Vector3.Dot(vector, up);
			float num3 = Vector3.Distance(this.parent.position, hitPos);
			if (num2 < 0f)
			{
				return float.NegativeInfinity;
			}
			if (num3 > this.maxReach)
			{
				return float.NegativeInfinity;
			}
			return (Vector3.Dot(vector.normalized, dVel.normalized) + 1f + num * 0.3f) * (num2 + 1f);
		}

		// Token: 0x06000D1A RID: 3354 RVA: 0x00051564 File Offset: 0x0004F764
		public void ChooseGrabTarget(Vector3[] hits, Vector3 dPos, Vector3 dVel)
		{
			Vector3 vector = hits[0];
			float num = -10f;
			bool flag = false;
			for (int i = 0; i < hits.Length; i++)
			{
				flag = true;
				float num2 = this.CalculateGrabHeuristic(hits[i], dPos, dVel);
				if (num2 != float.NegativeInfinity && num2 > num)
				{
					num = num2;
					vector = hits[i];
				}
			}
			if (this.grabbing)
			{
				float num3 = this.CalculateGrabHeuristic(this.grabTarget, dPos, dVel);
				if (num3 > num)
				{
					num = num3;
					vector = this.grabTarget;
				}
			}
			CL_DebugView.draw.Label2D(this.ikSolver.transform.position, "H: " + num.ToString(), 12f, Color.white);
			if (num > 0.5f && this.releaseWaitTime <= 0f && flag)
			{
				this.grabTarget = vector;
				this.grabbing = true;
				this.releaseWaitTime = this.releaseWait;
			}
		}

		// Token: 0x06000D1B RID: 3355 RVA: 0x00051655 File Offset: 0x0004F855
		public void Release()
		{
			this.grabbing = false;
			this.releaseWaitTime = this.releaseWait;
		}

		// Token: 0x06000D1C RID: 3356 RVA: 0x0005166C File Offset: 0x0004F86C
		public void Animate(Vector3 dVel, Vector3 dPos)
		{
			if (this.releaseWaitTime > 0f)
			{
				this.releaseWaitTime -= Time.deltaTime;
			}
			Vector3 up = this.parent.up;
			CL_DebugView.draw.Line(this.parent.position, this.parent.position + up, Color.red);
			if (this.grabbing && this.ShouldLetGo(dVel, dPos))
			{
				this.Release();
			}
			if (this.grabbing)
			{
				this.ikSolver.Target.position = Vector3.Lerp(this.ikSolver.Target.position, this.grabTarget, Time.deltaTime * 6f);
				CL_DebugView.draw.SphereOutline(this.grabTarget, 0.3f, Color.red);
				if (this.reachValue < 1f)
				{
					this.reachValue += Time.deltaTime;
					this.limbState = TheClamberer.Limb.LimbState.reaching;
				}
				else
				{
					this.reachValue = 1f;
					this.limbState = TheClamberer.Limb.LimbState.grabbed;
				}
				Vector3.Distance(this.parent.position, this.grabTarget);
				return;
			}
			this.ikSolver.Target.position = Vector3.Lerp(this.ikSolver.Target.position, this.ikSolver.transform.position + Vector3.down * 0.1f, Time.deltaTime * 6f);
			if (this.reachValue > 0f)
			{
				this.reachValue -= Time.deltaTime;
				this.limbState = TheClamberer.Limb.LimbState.reaching;
				return;
			}
			this.reachValue = 0f;
			this.limbState = TheClamberer.Limb.LimbState.idle;
		}

		// Token: 0x04000E69 RID: 3689
		public FastIKFabric ikSolver;

		// Token: 0x04000E6A RID: 3690
		public Transform parent;

		// Token: 0x04000E6B RID: 3691
		public bool grabbing;

		// Token: 0x04000E6C RID: 3692
		public Vector3 grabTarget;

		// Token: 0x04000E6D RID: 3693
		public float maxReach;

		// Token: 0x04000E6E RID: 3694
		private float reachValue;

		// Token: 0x04000E6F RID: 3695
		public float releaseValue = 0.5f;

		// Token: 0x04000E70 RID: 3696
		public float releaseWait = 1f;

		// Token: 0x04000E71 RID: 3697
		private float releaseWaitTime;

		// Token: 0x04000E72 RID: 3698
		public TheClamberer.Limb.LimbState limbState;

		// Token: 0x04000E73 RID: 3699
		private Vector3 limbDirection;

		// Token: 0x02000303 RID: 771
		public enum LimbState
		{
			// Token: 0x040012D9 RID: 4825
			idle,
			// Token: 0x040012DA RID: 4826
			reaching,
			// Token: 0x040012DB RID: 4827
			grabbed
		}
	}

	// Token: 0x02000225 RID: 549
	public enum ClambererAIStates
	{
		// Token: 0x04000E75 RID: 3701
		idling,
		// Token: 0x04000E76 RID: 3702
		chasing,
		// Token: 0x04000E77 RID: 3703
		charging,
		// Token: 0x04000E78 RID: 3704
		hunting,
		// Token: 0x04000E79 RID: 3705
		waiting,
		// Token: 0x04000E7A RID: 3706
		running
	}

	// Token: 0x02000226 RID: 550
	public enum AIMoveStates
	{
		// Token: 0x04000E7C RID: 3708
		normal,
		// Token: 0x04000E7D RID: 3709
		jumping,
		// Token: 0x04000E7E RID: 3710
		climbing,
		// Token: 0x04000E7F RID: 3711
		crawling,
		// Token: 0x04000E80 RID: 3712
		falling
	}
}
