﻿using System;
using UnityEngine;

// Token: 0x02000139 RID: 313
public class UI_SafeToggle : MonoBehaviour
{
	// Token: 0x06000916 RID: 2326 RVA: 0x0003F494 File Offset: 0x0003D694
	private void OnEnable()
	{
		if (this.type == UI_SafeToggle.SafeToggleType.onSafe)
		{
			this.toggleObject.SetActive(CL_GameManager.IsSafe() ? (!this.invertCheck) : this.invertCheck);
			return;
		}
		bool flag = CL_SaveManager.GetMostRecentSaveStateWithFlag("session", true) != null;
		bool flag2 = CL_SaveManager.GetMostRecentSaveStateWithFlag("disk", true) != null;
		string[] array = new string[6];
		array[0] = "Safe Toggle: ";
		int num = 1;
		GameObject gameObject = this.toggleObject;
		array[num] = ((gameObject != null) ? gameObject.ToString() : null);
		array[2] = " : ";
		array[3] = ((flag || flag2) ? (!this.invertCheck) : this.invertCheck).ToString();
		array[4] = " Has Session: ";
		array[5] = flag.ToString();
		Debug.Log(string.Concat(array));
		this.toggleObject.SetActive((flag || flag2) ? (!this.invertCheck) : this.invertCheck);
	}

	// Token: 0x04000A64 RID: 2660
	public UI_SafeToggle.SafeToggleType type;

	// Token: 0x04000A65 RID: 2661
	public GameObject toggleObject;

	// Token: 0x04000A66 RID: 2662
	public bool invertCheck;

	// Token: 0x020002BE RID: 702
	public enum SafeToggleType
	{
		// Token: 0x040011A8 RID: 4520
		onSafe,
		// Token: 0x040011A9 RID: 4521
		onHasSession
	}
}
