﻿using System;
using UnityEngine;

// Token: 0x020001B5 RID: 437
public class UT_Unparent : MonoBehaviour
{
	// Token: 0x06000B67 RID: 2919 RVA: 0x00048B5A File Offset: 0x00046D5A
	private void Start()
	{
		if (this.onStart)
		{
			this.UnParent();
		}
	}

	// Token: 0x06000B68 RID: 2920 RVA: 0x00048B6A File Offset: 0x00046D6A
	public void UnParent()
	{
		base.transform.parent = null;
	}

	// Token: 0x04000C4F RID: 3151
	public bool onStart;
}
