﻿using System;
using Drawing;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020000CB RID: 203
public class UT_Door : MonoBehaviourGizmos
{
	// Token: 0x0600069D RID: 1693 RVA: 0x00035328 File Offset: 0x00033528
	private void Start()
	{
		this.aud = base.GetComponent<AudioSource>();
		this.CalculateMovement();
		if (this.startOpen)
		{
			this.Open();
			return;
		}
		this.Close();
	}

	// Token: 0x0600069E RID: 1694 RVA: 0x00035354 File Offset: 0x00033554
	private void FixedUpdate()
	{
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		if (this.open)
		{
			this.lerpAmount = Mathf.Clamp01(this.lerpAmount + Time.fixedDeltaTime * this.moveSpeed);
			if (this.lerpAmount >= 1f && !this.latched)
			{
				this.latched = true;
				this.finishEvent.Invoke();
			}
		}
		else
		{
			this.lerpAmount = Mathf.Clamp01(this.lerpAmount - Time.fixedDeltaTime * this.moveSpeed);
			if (this.lerpAmount <= 0f && !this.latched)
			{
				this.latched = true;
				this.finishEvent.Invoke();
			}
		}
		float num = (this.useCurve ? this.animationCurve.Evaluate(this.lerpAmount) : this.lerpAmount);
		if (this.space == UT_Door.UseSpace.World)
		{
			base.transform.position = Vector3.LerpUnclamped(this.startPos, this.endPos, num);
			return;
		}
		base.transform.localPosition = Vector3.LerpUnclamped(this.startPos, this.endPos, num);
	}

	// Token: 0x0600069F RID: 1695 RVA: 0x00035468 File Offset: 0x00033668
	public void Open()
	{
		if (this.open)
		{
			return;
		}
		this.open = true;
		this.active = true;
		this.openEvent.Invoke();
		this.latched = false;
		if (this.openClip != null)
		{
			if (this.aud != null)
			{
				this.aud.clip = this.openClip;
				this.aud.Play();
				return;
			}
			AudioManager.PlaySound(this.openClip, base.transform.position, 1f, 1f, 1f, false, 1f, null);
		}
	}

	// Token: 0x060006A0 RID: 1696 RVA: 0x00035504 File Offset: 0x00033704
	public void Close()
	{
		if (!this.open)
		{
			return;
		}
		this.open = false;
		this.active = true;
		this.closeEvent.Invoke();
		this.latched = false;
		if (this.openClip != null)
		{
			if (this.aud != null)
			{
				this.aud.clip = this.closeClip;
				this.aud.Play();
				return;
			}
			AudioManager.PlaySound(this.closeClip, base.transform.position, 1f, 1f, 1f, false, 1f, null);
		}
	}

	// Token: 0x060006A1 RID: 1697 RVA: 0x0003559F File Offset: 0x0003379F
	public void Toggle()
	{
		if (this.open)
		{
			this.Close();
			return;
		}
		this.Open();
	}

	// Token: 0x060006A2 RID: 1698 RVA: 0x000355B6 File Offset: 0x000337B6
	public void SetNewOpen(Vector3 newMove)
	{
		this.endPos = this.startPos + newMove;
	}

	// Token: 0x060006A3 RID: 1699 RVA: 0x000355CC File Offset: 0x000337CC
	public void CalculateMovement()
	{
		if (this.space == UT_Door.UseSpace.Local)
		{
			this.startPos = base.transform.localPosition;
			this.endPos = this.startPos + this.move;
			this.startRot = base.transform.localRotation;
			this.endRot = this.startRot * Quaternion.Euler(this.rotate);
			return;
		}
		if (this.space == UT_Door.UseSpace.LocalRelativeToSelf)
		{
			this.startPos = base.transform.localPosition;
			Vector3 vector = base.transform.forward * this.move.y + base.transform.right * this.move.x + base.transform.up * this.move.z;
			this.endPos = this.startPos + vector;
			this.startRot = base.transform.localRotation;
			this.endRot = this.startRot * Quaternion.Euler(this.rotate);
			return;
		}
		this.startPos = base.transform.position;
		this.endPos = this.startPos + this.move;
		this.startRot = base.transform.rotation;
		this.endRot = this.startRot * Quaternion.Euler(this.rotate);
	}

	// Token: 0x060006A4 RID: 1700 RVA: 0x00035745 File Offset: 0x00033945
	private void OnInspectorUpdate()
	{
		if (Application.isPlaying)
		{
			return;
		}
		this.CalculateMovement();
	}

	// Token: 0x060006A5 RID: 1701 RVA: 0x00035758 File Offset: 0x00033958
	public override void DrawGizmos()
	{
		if (!Application.isPlaying)
		{
			this.CalculateMovement();
		}
		if (this.space == UT_Door.UseSpace.World || base.transform.parent == null)
		{
			Draw.Arrow(this.startPos, this.endPos, base.transform.up, 0.5f, Color.green);
			return;
		}
		Draw.Arrow(base.transform.parent.TransformPoint(this.startPos), base.transform.parent.TransformPoint(this.endPos), base.transform.up, 0.5f, Color.green);
	}

	// Token: 0x04000816 RID: 2070
	public bool startOpen = true;

	// Token: 0x04000817 RID: 2071
	private bool open;

	// Token: 0x04000818 RID: 2072
	private bool active;

	// Token: 0x04000819 RID: 2073
	public UT_Door.UseSpace space;

	// Token: 0x0400081A RID: 2074
	public Vector3 move = Vector3.zero;

	// Token: 0x0400081B RID: 2075
	public Vector3 rotate = Vector3.zero;

	// Token: 0x0400081C RID: 2076
	public float moveSpeed = 1f;

	// Token: 0x0400081D RID: 2077
	public float rotateSpeed = 1f;

	// Token: 0x0400081E RID: 2078
	private Vector3 startPos;

	// Token: 0x0400081F RID: 2079
	private Vector3 endPos;

	// Token: 0x04000820 RID: 2080
	private Quaternion startRot;

	// Token: 0x04000821 RID: 2081
	private Quaternion endRot;

	// Token: 0x04000822 RID: 2082
	public bool useCurve;

	// Token: 0x04000823 RID: 2083
	public AnimationCurve animationCurve;

	// Token: 0x04000824 RID: 2084
	public UnityEvent openEvent;

	// Token: 0x04000825 RID: 2085
	public UnityEvent closeEvent;

	// Token: 0x04000826 RID: 2086
	public UnityEvent finishEvent;

	// Token: 0x04000827 RID: 2087
	public AudioClip openClip;

	// Token: 0x04000828 RID: 2088
	public AudioClip closeClip;

	// Token: 0x04000829 RID: 2089
	private AudioSource aud;

	// Token: 0x0400082A RID: 2090
	private bool latched = true;

	// Token: 0x0400082B RID: 2091
	private float lerpAmount;

	// Token: 0x0200028F RID: 655
	public enum UseSpace
	{
		// Token: 0x04001093 RID: 4243
		Local,
		// Token: 0x04001094 RID: 4244
		LocalRelativeToSelf,
		// Token: 0x04001095 RID: 4245
		World
	}
}
