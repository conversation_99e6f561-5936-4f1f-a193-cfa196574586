﻿using System;
using UnityEngine;

// Token: 0x020000F3 RID: 243
public class ETAS_Manager : MonoBehaviour
{
	// Token: 0x17000006 RID: 6
	// (get) Token: 0x0600076D RID: 1901 RVA: 0x00038C02 File Offset: 0x00036E02
	public static bool isOnTop
	{
		get
		{
			return ETAS_Manager.onTop;
		}
	}

	// Token: 0x0600076E RID: 1902 RVA: 0x00038C09 File Offset: 0x00036E09
	private void Start()
	{
	}

	// Token: 0x0600076F RID: 1903 RVA: 0x00038C0B File Offset: 0x00036E0B
	private void Update()
	{
		ETAS_Manager.onTop = this.IsOnTop();
	}

	// Token: 0x06000770 RID: 1904 RVA: 0x00038C18 File Offset: 0x00036E18
	private bool IsOnTop()
	{
		return !(base.transform.parent != null) || base.transform.GetSiblingIndex() == base.transform.parent.childCount - 1;
	}

	// Token: 0x040008E7 RID: 2279
	private static bool onTop;
}
