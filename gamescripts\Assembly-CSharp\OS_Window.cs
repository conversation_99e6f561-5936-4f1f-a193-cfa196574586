﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000101 RID: 257
public class OS_Window : MonoBehaviour
{
	// Token: 0x060007DA RID: 2010 RVA: 0x0003A9B4 File Offset: 0x00038BB4
	public virtual void Start()
	{
		this.ui = base.GetComponent<UI_Window>();
		if (this.ui != null && this.ui.GetTitleBar() != null)
		{
			this.ui.GetTitleBar().SetTitle(this.windowName);
		}
		this.canvasGroup = base.GetComponent<CanvasGroup>();
		if (this.canvasGroup == null)
		{
			this.canvasGroup = base.gameObject.AddComponent<CanvasGroup>();
		}
	}

	// Token: 0x060007DB RID: 2011 RVA: 0x0003AA2F File Offset: 0x00038C2F
	private void Update()
	{
		if (this.forceFront)
		{
			base.transform.SetAsLastSibling();
		}
		this.CheckFirstSelect();
	}

	// Token: 0x060007DC RID: 2012 RVA: 0x0003AA4A File Offset: 0x00038C4A
	public virtual void CloseApp()
	{
		OS_Manager.soundPlayer.PlaySound("os:file-close");
		this.closeEvent.Invoke();
		this.file.CloseFile();
		this.os.CloseAppWindow(this, this.id);
	}

	// Token: 0x060007DD RID: 2013 RVA: 0x0003AA83 File Offset: 0x00038C83
	public virtual void QuitApp()
	{
		OS_Manager.activeComputer.QuitAppWindow(this, this.id);
		if (this.file)
		{
			this.file.CloseFile();
		}
		this.closeEvent.Invoke();
	}

	// Token: 0x060007DE RID: 2014 RVA: 0x0003AABC File Offset: 0x00038CBC
	public virtual void OpenApp(OS_File f)
	{
		this.file = f;
		if (this.file)
		{
			this.file.open = true;
			this.file.fileInfo.open = true;
		}
		this.openEvent.Invoke();
		this.SetWindowName(this.file.fileInfo.name);
		if (this.openAction != null)
		{
			this.openAction();
		}
	}

	// Token: 0x060007DF RID: 2015 RVA: 0x0003AB2E File Offset: 0x00038D2E
	public void MoveToTop()
	{
		base.transform.SetAsLastSibling();
	}

	// Token: 0x060007E0 RID: 2016 RVA: 0x0003AB3B File Offset: 0x00038D3B
	public void SetID(string s)
	{
		this.id = s;
	}

	// Token: 0x060007E1 RID: 2017 RVA: 0x0003AB44 File Offset: 0x00038D44
	public void Initialize(OS_Manager o)
	{
		this.os = o;
	}

	// Token: 0x060007E2 RID: 2018 RVA: 0x0003AB50 File Offset: 0x00038D50
	internal void SetWindowName(string name)
	{
		this.windowName = this.file.fileInfo.name;
		base.gameObject.name = this.file.fileInfo.name;
		if (this.ui != null && this.ui.GetTitleBar() != null)
		{
			this.ui.GetTitleBar().SetTitle(this.windowName);
		}
	}

	// Token: 0x060007E3 RID: 2019 RVA: 0x0003ABC8 File Offset: 0x00038DC8
	public void CheckFirstSelect()
	{
		if (!InputManager.IsGamepad())
		{
			return;
		}
		if (EventSystem.current.currentSelectedGameObject == null)
		{
			this.SelectFirst();
			return;
		}
		if (!EventSystem.current.currentSelectedGameObject.activeInHierarchy || !EventSystem.current.currentSelectedGameObject.GetComponent<Selectable>().IsInteractable())
		{
			this.SelectFirst();
		}
	}

	// Token: 0x060007E4 RID: 2020 RVA: 0x0003AC23 File Offset: 0x00038E23
	public virtual void SelectFirst()
	{
		if (this.firstSelect == null || !this.firstSelect.interactable)
		{
			return;
		}
		this.firstSelect.Select();
	}

	// Token: 0x04000955 RID: 2389
	public string windowName = "NULLNAME";

	// Token: 0x04000956 RID: 2390
	public OS_File file;

	// Token: 0x04000957 RID: 2391
	public UnityEvent openEvent;

	// Token: 0x04000958 RID: 2392
	public UnityEvent closeEvent;

	// Token: 0x04000959 RID: 2393
	public UnityEvent moveEvent;

	// Token: 0x0400095A RID: 2394
	private UI_Window ui;

	// Token: 0x0400095B RID: 2395
	internal string id;

	// Token: 0x0400095C RID: 2396
	public bool forceFront;

	// Token: 0x0400095D RID: 2397
	internal OS_Manager os;

	// Token: 0x0400095E RID: 2398
	public Action openAction;

	// Token: 0x0400095F RID: 2399
	public Selectable firstSelect;

	// Token: 0x04000960 RID: 2400
	private CanvasGroup canvasGroup;
}
