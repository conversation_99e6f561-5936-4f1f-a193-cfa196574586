﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020001BF RID: 447
public class UT_AnimatedTexture : MonoBehaviour
{
	// Token: 0x06000B81 RID: 2945 RVA: 0x000499BE File Offset: 0x00047BBE
	private void Start()
	{
		this.m = base.GetComponent<MeshRenderer>().material;
		this.frameTime = this.offset;
	}

	// Token: 0x06000B82 RID: 2946 RVA: 0x000499E0 File Offset: 0x00047BE0
	private void Update()
	{
		if (this.frameTime <= 0f)
		{
			this.frameTime = 1f / this.framerate;
			this.currentFrame++;
			if (this.currentFrame >= this.frames.Count)
			{
				this.currentFrame = 0;
			}
			this.m.mainTexture = this.frames[this.currentFrame];
		}
		this.frameTime -= Time.deltaTime;
	}

	// Token: 0x04000C86 RID: 3206
	private Material m;

	// Token: 0x04000C87 RID: 3207
	public List<Texture2D> frames;

	// Token: 0x04000C88 RID: 3208
	public float framerate = 30f;

	// Token: 0x04000C89 RID: 3209
	private int currentFrame;

	// Token: 0x04000C8A RID: 3210
	public float offset;

	// Token: 0x04000C8B RID: 3211
	private float frameTime;
}
