﻿using System;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x020000E9 RID: 233
public class App_PerkPage_Card : MonoBehaviour, ISelectHandler, IEventSystemHandler, ISubmitHandler, IPointerClickHandler
{
	// Token: 0x06000735 RID: 1845 RVA: 0x00037890 File Offset: 0x00035A90
	private void Update()
	{
	}

	// Token: 0x06000736 RID: 1846 RVA: 0x00037894 File Offset: 0x00035A94
	public void Initialize(App_PerkPage page, Perk p)
	{
		this.button = base.GetComponent<Button>();
		this.tooltip = base.GetComponent<Tooltip>();
		this.purchasedText.gameObject.SetActive(false);
		this.perkPage = page;
		this.perk = p;
		this.tooltip.tip = this.perk.title + "\n<color=\"grey\">" + this.perk.description + "</color>";
		this.cardArt.sprite = this.perk.perkCard;
		this.frameArt.sprite = this.perk.perkFrame;
		this.titleText.text = this.perk.title;
		this.UpdateCard();
	}

	// Token: 0x06000737 RID: 1847 RVA: 0x00037950 File Offset: 0x00035B50
	public void CheckCost()
	{
		if (this.locked || !this.active)
		{
			return;
		}
		if (this.perk.cost > 0)
		{
			this.costText.text = this.perk.cost.ToString();
			return;
		}
		this.costRoot.gameObject.SetActive(false);
	}

	// Token: 0x06000738 RID: 1848 RVA: 0x000379A9 File Offset: 0x00035BA9
	void ISubmitHandler.OnSubmit(BaseEventData eventData)
	{
		if (this.locked || !this.active || !this.canSubmit)
		{
			return;
		}
		this.Submit();
	}

	// Token: 0x06000739 RID: 1849 RVA: 0x000379CA File Offset: 0x00035BCA
	public void OnSelect(BaseEventData eventData)
	{
		if (this.locked || !this.active || !this.canSubmit)
		{
			return;
		}
		this.Select();
	}

	// Token: 0x0600073A RID: 1850 RVA: 0x000379EB File Offset: 0x00035BEB
	public void Select()
	{
		if (this.locked || !this.active || !this.canSubmit)
		{
			return;
		}
		OS_Manager.activeComputer.audioClipHandler.PlaySound("perk:highlight");
		this.perkPage.SelectCard(this);
	}

	// Token: 0x0600073B RID: 1851 RVA: 0x00037A26 File Offset: 0x00035C26
	public void Submit()
	{
		if (this.locked || !this.active || !this.canSubmit)
		{
			return;
		}
		OS_Manager.activeComputer.audioClipHandler.PlaySound("perk:select");
		this.perkPage.SubmitCard(this);
	}

	// Token: 0x0600073C RID: 1852 RVA: 0x00037A61 File Offset: 0x00035C61
	public void OnPointerClick(PointerEventData eventData)
	{
		if (this.locked || !this.active || !this.canSubmit)
		{
			return;
		}
		this.Submit();
	}

	// Token: 0x0600073D RID: 1853 RVA: 0x00037A84 File Offset: 0x00035C84
	public void SubmitCard()
	{
		if (this.locked || !this.active || !this.canSubmit)
		{
			return;
		}
		this.cardArt.color = Color.white;
		this.frameArt.color = Color.white;
		this.titleText.color = Color.red;
		this.outline.effectColor = Color.red;
		DOTween.Complete(base.transform, false);
		base.transform.DOScale(1f, 0.2f);
		base.transform.DOPunchRotation(new Vector3(0f, 0f, 1f), 0.5f, 10, 1f);
		base.transform.DOPunchScale(Vector3.one * 0.05f, 0.2f, 10, 1f);
	}

	// Token: 0x0600073E RID: 1854 RVA: 0x00037B60 File Offset: 0x00035D60
	public void SelectCard()
	{
		if (this.locked || !this.active || !this.canSubmit)
		{
			return;
		}
		this.cardArt.color = Color.white;
		this.frameArt.color = Color.white;
		this.titleText.color = Color.white;
		this.outline.effectColor = Color.clear;
		DOTween.Complete(base.transform, false);
		base.transform.DOScale(1f, 0.2f);
		base.transform.DOPunchRotation(new Vector3(0f, 0f, 1f), 0.5f, 10, 1f);
	}

	// Token: 0x0600073F RID: 1855 RVA: 0x00037C18 File Offset: 0x00035E18
	public void DeselectCard()
	{
		if (this.locked || !this.active || !this.canSubmit)
		{
			return;
		}
		this.cardArt.color = Color.grey;
		this.frameArt.color = Color.grey;
		this.outline.effectColor = Color.clear;
		DOTween.Complete(base.transform, false);
		this.titleText.color = Color.grey;
		base.transform.DOScale(0.95f, 0.2f);
	}

	// Token: 0x06000740 RID: 1856 RVA: 0x00037CA4 File Offset: 0x00035EA4
	public void DisableCard()
	{
		this.active = false;
		this.titleText.color = Color.red;
		DOTween.Complete(base.transform, false);
		this.cardArt.color = Color.red;
		this.frameArt.color = Color.red;
	}

	// Token: 0x06000741 RID: 1857 RVA: 0x00037CF5 File Offset: 0x00035EF5
	public void LockCard()
	{
		this.locked = true;
		DOTween.Complete(base.transform, false);
		base.transform.DOScale(1f, 0.2f);
	}

	// Token: 0x06000742 RID: 1858 RVA: 0x00037D24 File Offset: 0x00035F24
	public void BuyCard()
	{
		this.purchasedText.gameObject.SetActive(true);
		this.purchasedText.localScale = Vector3.zero;
		DOTween.Complete(base.transform, false);
		this.purchasedText.DOScale(1f, 0.5f);
		base.transform.DOPunchRotation(new Vector3(0f, 0f, 2f), 0.5f, 10, 1f);
		CL_GameManager.AddRoaches(-this.perk.cost);
	}

	// Token: 0x06000743 RID: 1859 RVA: 0x00037DB4 File Offset: 0x00035FB4
	public void UpdateCard()
	{
		this.CheckCost();
		if (!this.active || this.locked)
		{
			this.canSubmit = false;
			return;
		}
		if (CL_GameManager.roaches < this.perk.cost)
		{
			this.cardArt.color = Color.red;
			this.frameArt.color = Color.red;
			this.titleText.color = Color.red;
			this.canSubmit = false;
			return;
		}
		this.canSubmit = true;
	}

	// Token: 0x06000744 RID: 1860 RVA: 0x00037E35 File Offset: 0x00036035
	public bool CanAfford()
	{
		return CL_GameManager.roaches >= this.perk.cost;
	}

	// Token: 0x040008B0 RID: 2224
	public Perk perk;

	// Token: 0x040008B1 RID: 2225
	private Button button;

	// Token: 0x040008B2 RID: 2226
	private App_PerkPage perkPage;

	// Token: 0x040008B3 RID: 2227
	private Tooltip tooltip;

	// Token: 0x040008B4 RID: 2228
	public TMP_Text titleText;

	// Token: 0x040008B5 RID: 2229
	public TMP_Text costText;

	// Token: 0x040008B6 RID: 2230
	public Transform costRoot;

	// Token: 0x040008B7 RID: 2231
	public Transform purchasedText;

	// Token: 0x040008B8 RID: 2232
	private bool active = true;

	// Token: 0x040008B9 RID: 2233
	private bool locked;

	// Token: 0x040008BA RID: 2234
	private bool canSubmit = true;

	// Token: 0x040008BB RID: 2235
	public Image cardArt;

	// Token: 0x040008BC RID: 2236
	public Image frameArt;

	// Token: 0x040008BD RID: 2237
	public Outline outline;
}
