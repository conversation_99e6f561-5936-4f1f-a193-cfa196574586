﻿using System;
using UnityEngine;

// Token: 0x0200017B RID: 379
public class UT_LadderNightmare_Controller : MonoBehaviour
{
	// Token: 0x06000A7D RID: 2685 RVA: 0x00044CD6 File Offset: 0x00042ED6
	public void SetNightmareDistance(float f)
	{
		DEN_LadderNightmare.SetPlayerOffset(f);
	}

	// Token: 0x06000A7E RID: 2686 RVA: 0x00044CDE File Offset: 0x00042EDE
	public void SetNightmareCanPeek(bool b)
	{
		DEN_LadderNightmare.SetCanPeek(b);
	}

	// Token: 0x06000A7F RID: 2687 RVA: 0x00044CE6 File Offset: 0x00042EE6
	public void SetNightmareCanAttack(bool b)
	{
		DEN_LadderNightmare.SetCanAttack(b);
	}

	// Token: 0x06000A80 RID: 2688 RVA: 0x00044CEE File Offset: 0x00042EEE
	public void SetNightmareBreathing(bool b)
	{
		DEN_LadderNightmare.SetBreathing(b);
	}
}
