﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000120 RID: 288
public class UI_CapsuleContainer : MonoBehaviour
{
	// Token: 0x06000896 RID: 2198 RVA: 0x0003D7AD File Offset: 0x0003B9AD
	private void OnEnable()
	{
		this.ShowEffect();
	}

	// Token: 0x06000897 RID: 2199 RVA: 0x0003D7B5 File Offset: 0x0003B9B5
	public void ShowEffect()
	{
		this.buttons = new List<UI_CapsuleButton>();
		if (this.showOnEnable)
		{
			this.buttons.AddRange(base.GetComponentsInChildren<UI_CapsuleButton>());
			base.StartCoroutine(this.ShowButtons());
		}
	}

	// Token: 0x06000898 RID: 2200 RVA: 0x0003D7E8 File Offset: 0x0003B9E8
	private IEnumerator ShowButtons()
	{
		yield return new WaitForSeconds(this.showWait);
		int num;
		for (int i = 0; i < this.buttons.Count; i = num + 1)
		{
			if (this.buttons[i].gameObject.activeSelf)
			{
				this.buttons[i].Show();
				yield return new WaitForSeconds(this.showDelay);
			}
			num = i;
		}
		yield break;
	}

	// Token: 0x06000899 RID: 2201 RVA: 0x0003D7F7 File Offset: 0x0003B9F7
	private void Update()
	{
	}

	// Token: 0x040009E7 RID: 2535
	public bool showOnEnable = true;

	// Token: 0x040009E8 RID: 2536
	private List<UI_CapsuleButton> buttons = new List<UI_CapsuleButton>();

	// Token: 0x040009E9 RID: 2537
	public float showDelay = 0.1f;

	// Token: 0x040009EA RID: 2538
	public float showWait = 1f;
}
