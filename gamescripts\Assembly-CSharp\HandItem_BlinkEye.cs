﻿using System;
using System.Collections;
using System.Runtime.CompilerServices;
using UnityEngine;

// Token: 0x02000089 RID: 137
public class HandItem_BlinkEye : HandItem
{
	// Token: 0x060004A5 RID: 1189 RVA: 0x00028794 File Offset: 0x00026994
	public override void Initialize(Item i, ENT_Player.Hand h)
	{
		base.Initialize(i, h);
		int.TryParse(this.item.GetFirstDataStringByType("uses", false), out this.uses);
		this.anim.SetInteger("Uses", this.uses);
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		base.StartCoroutine(this.<Initialize>g__WaitOneFrame|15_0());
	}

	// Token: 0x060004A6 RID: 1190 RVA: 0x000287F8 File Offset: 0x000269F8
	public override void Use()
	{
		int.TryParse(this.item.GetFirstDataStringByType("uses", false), out this.uses);
		if (this.uses <= 0)
		{
			return;
		}
		this.used = true;
		this.clipHandler.PlaySound("eye:squish");
		this.anim.SetBool("Squeeze", true);
		this.holdTime = 0f;
		base.Use();
	}

	// Token: 0x060004A7 RID: 1191 RVA: 0x00028868 File Offset: 0x00026A68
	public override void StopUse()
	{
		if (this.holdTime < 0.2f && this.quickTime <= 0f)
		{
			this.anim.SetBool("Squeeze", false);
			this.used = false;
			return;
		}
		if (!this.used)
		{
			return;
		}
		this.used = false;
		this.uses--;
		this.uses = Mathf.Max(this.uses, 0);
		this.anim.SetInteger("Uses", this.uses);
		this.anim.SetBool("Squeeze", false);
		this.item.SetFirstDataStringsofType("uses", this.uses.ToString());
		ENT_Player.playerObject.DropHang(0f);
		ENT_Player.playerObject.Teleport(this.blinkTargetPosition);
		ENT_Player.playerObject.SetCrouched(true);
		ENT_Player.playerObject.ClearGrapple();
		this.clipHandler.PlaySound("eye:warp");
		if (this.uses == 0)
		{
			this.clipHandler.PlaySound("eye:crush");
		}
		this.quickTime = 0.5f;
		if (this.hitBlinkable != null)
		{
			this.hitBlinkable.OnBlinkEnd();
			this.hitBlinkable.OnBlinkTeleport();
			this.hitBlinkable = null;
		}
		this.UpdateStats();
		base.StopUse();
	}

	// Token: 0x060004A8 RID: 1192 RVA: 0x000289B2 File Offset: 0x00026BB2
	public void Crush()
	{
		this.crushEffect.Play();
	}

	// Token: 0x060004A9 RID: 1193 RVA: 0x000289C0 File Offset: 0x00026BC0
	private void LateUpdate()
	{
		if (this.quickTime > 0f)
		{
			this.quickTime -= Time.deltaTime;
		}
		if (this.used)
		{
			this.eyeSprite.material.SetFloat("_Shimmer", 20f);
			this.holdTime += Time.deltaTime;
			if (!this.squeezeAudioSource.isPlaying)
			{
				this.squeezeAudioSource.Play();
			}
			this.squeezeAudioSource.volume = Mathf.Lerp(this.squeezeAudioSource.volume, this.squeezeAudioVolume, Time.deltaTime * 2f);
			this.blinkTargetPosition = ENT_Player.playerObject.cam.transform.position + base.transform.forward * this.blinkDistance;
			base.transform.position + ENT_Player.playerObject.cCon.center + Vector3.up * -ENT_Player.playerObject.cCon.height * 0.5f + Vector3.up * ENT_Player.playerObject.cCon.height;
			Vector3 position = ENT_Player.playerObject.cam.transform.position;
			Physics.queriesHitBackfaces = true;
			RaycastHit raycastHit;
			if (Physics.SphereCast(position, ENT_Player.playerObject.cCon.radius, base.transform.forward, out raycastHit, this.blinkDistance, this.blinkMask))
			{
				this.blinkTargetPosition = position + base.transform.forward * raycastHit.distance;
				Blinkable component = raycastHit.collider.GetComponent<Blinkable>();
				if (component != null)
				{
					this.hitBlinkable = component;
					this.hitBlinkable.OnBlinkStart();
				}
				else if (this.hitBlinkable != null)
				{
					this.hitBlinkable.OnBlinkEnd();
					this.hitBlinkable = null;
				}
			}
			Physics.queriesHitBackfaces = false;
			this.blinkTargetEffect.transform.position = this.blinkTargetPosition;
			this.blinkTargetEffect.transform.rotation = Quaternion.identity;
			if (!this.blinkTargetEffect.isPlaying)
			{
				this.blinkTargetEffect.Play();
				this.blinkHoldEffect.Play();
			}
			this.blinkTargetLamp.lampActive = true;
			return;
		}
		this.eyeSprite.material.SetFloat("_Shimmer", 0f);
		this.squeezeAudioSource.volume = Mathf.Lerp(this.squeezeAudioSource.volume, 0f, Time.deltaTime);
		this.blinkTargetEffect.transform.position = this.blinkTargetPosition;
		this.blinkTargetEffect.transform.rotation = Quaternion.identity;
		if (this.blinkTargetEffect.isPlaying)
		{
			this.blinkTargetEffect.Stop();
			this.blinkHoldEffect.Stop();
		}
		this.blinkTargetLamp.lampActive = false;
	}

	// Token: 0x060004AA RID: 1194 RVA: 0x00028CAE File Offset: 0x00026EAE
	public override bool CanDrop()
	{
		return this.uses != 0 && !this.used;
	}

	// Token: 0x060004AC RID: 1196 RVA: 0x00028CE1 File Offset: 0x00026EE1
	[CompilerGenerated]
	private IEnumerator <Initialize>g__WaitOneFrame|15_0()
	{
		yield return null;
		this.anim.SetInteger("Uses", this.uses);
		yield break;
	}

	// Token: 0x04000623 RID: 1571
	private int uses;

	// Token: 0x04000624 RID: 1572
	public ParticleSystem blinkTargetEffect;

	// Token: 0x04000625 RID: 1573
	public CL_Lamp blinkTargetLamp;

	// Token: 0x04000626 RID: 1574
	public ParticleSystem blinkHoldEffect;

	// Token: 0x04000627 RID: 1575
	public ParticleSystem crushEffect;

	// Token: 0x04000628 RID: 1576
	public SpriteRenderer eyeSprite;

	// Token: 0x04000629 RID: 1577
	public LayerMask blinkMask;

	// Token: 0x0400062A RID: 1578
	public float blinkDistance = 10f;

	// Token: 0x0400062B RID: 1579
	public AudioSource squeezeAudioSource;

	// Token: 0x0400062C RID: 1580
	public float squeezeAudioVolume = 0.8f;

	// Token: 0x0400062D RID: 1581
	private Vector3 blinkTargetPosition;

	// Token: 0x0400062E RID: 1582
	private UT_AudioClipHandler clipHandler;

	// Token: 0x0400062F RID: 1583
	private float holdTime;

	// Token: 0x04000630 RID: 1584
	private float quickTime;

	// Token: 0x04000631 RID: 1585
	private Blinkable hitBlinkable;
}
