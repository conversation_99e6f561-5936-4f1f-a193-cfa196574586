﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

// Token: 0x0200011D RID: 285
public class UI_ButtonSettings : MonoBehaviour
{
	// Token: 0x06000882 RID: 2178 RVA: 0x0003D39C File Offset: 0x0003B59C
	private void Start()
	{
		this.button = base.GetComponent<Button>();
		this.button.onClick.AddListener(new UnityAction(this.OnButtonPress));
	}

	// Token: 0x06000883 RID: 2179 RVA: 0x0003D3C6 File Offset: 0x0003B5C6
	private void OnButtonPress()
	{
		this.button.Select();
	}

	// Token: 0x040009DA RID: 2522
	private Button button;
}
