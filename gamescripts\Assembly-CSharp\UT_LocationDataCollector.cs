﻿using System;
using UnityEngine;

// Token: 0x0200017D RID: 381
public class UT_LocationDataCollector : MonoBehaviour
{
	// Token: 0x06000A87 RID: 2695 RVA: 0x00044DD4 File Offset: 0x00042FD4
	public void LogData()
	{
		StatManager.sessionStats.UpdateStatistic("ldc-" + this.locationName + "-besttimetoreach", CL_GameManager.gMan.GetGameTime(), StatManager.Statistic.DataType.Float, StatManager.Statistic.ModType.Min, StatManager.Statistic.DisplayType.Time, StatManager.Statistic.ModType.Min);
		StatManager.sessionStats.UpdateStatistic("ldc-" + this.locationName + "-totaltimesreached", 1, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Add, StatManager.Statistic.DisplayType.De<PERSON>ult, StatManager.Statistic.ModType.Add);
		int totalStatisticInt = StatManager.GetTotalStatisticInt("ldc-" + this.locationName + "-totaltimesreached");
		Debug.Log("Reach Amount: " + totalStatisticInt.ToString());
		Debug.Log("Reached " + this.locationName + " At " + CL_GameManager.gMan.GetGameTime().ToString());
		if (this.unlockAchievement && totalStatisticInt >= this.unlockVisitAmount)
		{
			CL_AchievementManager.SetAchievementValue(this.unlockName, true);
		}
	}

	// Token: 0x04000B87 RID: 2951
	public string locationName;

	// Token: 0x04000B88 RID: 2952
	public bool unlockAchievement;

	// Token: 0x04000B89 RID: 2953
	public int unlockVisitAmount = 1;

	// Token: 0x04000B8A RID: 2954
	public string unlockName;
}
