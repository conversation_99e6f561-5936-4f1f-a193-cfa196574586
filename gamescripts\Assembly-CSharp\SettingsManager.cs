﻿using System;
using System.Collections;
using System.IO;
using System.Reflection;
using System.Runtime.CompilerServices;
using UnityEngine;
using UnityEngine.Audio;

// Token: 0x02000112 RID: 274
public class SettingsManager : MonoBehaviour
{
	// Token: 0x0600083D RID: 2109 RVA: 0x0003BAC4 File Offset: 0x00039CC4
	private void Awake()
	{
		if (SettingsManager.instance == null)
		{
			SettingsManager.instance = this;
		}
		this.filePath = Path.Combine(Application.persistentDataPath, "settings.json");
		if (SettingsManager.settings == null)
		{
			this.LoadSettings();
		}
		SettingsManager.buildType = SettingsManager.BuildType.standard;
		SettingsManager.settings.unlockAll = false;
	}

	// Token: 0x0600083E RID: 2110 RVA: 0x0003BB17 File Offset: 0x00039D17
	private void Start()
	{
		SettingsManager.RefreshSettings("");
	}

	// Token: 0x0600083F RID: 2111 RVA: 0x0003BB23 File Offset: 0x00039D23
	private void OnDestroy()
	{
		this.SaveSettings();
	}

	// Token: 0x06000840 RID: 2112 RVA: 0x0003BB2C File Offset: 0x00039D2C
	public void SaveSettings()
	{
		string text = JsonUtility.ToJson(SettingsManager.settings, true);
		File.WriteAllText(this.filePath, text);
	}

	// Token: 0x06000841 RID: 2113 RVA: 0x0003BB51 File Offset: 0x00039D51
	public static void WipeSettings()
	{
		File.Delete(Path.Combine(Application.persistentDataPath, "settings.json"));
		if (SettingsManager.instance != null)
		{
			SettingsManager.instance.LoadSettings();
		}
	}

	// Token: 0x06000842 RID: 2114 RVA: 0x0003BB80 File Offset: 0x00039D80
	public void LoadSettings()
	{
		if (File.Exists(this.filePath))
		{
			string text = File.ReadAllText(this.filePath);
			if (text.Length > 0)
			{
				SettingsManager.settings = JsonUtility.FromJson<SettingsManager.GameSettings>(text);
			}
			else
			{
				SettingsManager.settings = new SettingsManager.GameSettings();
				float num = (float)Screen.currentResolution.height;
				SettingsManager.SetSetting(new string[]
				{
					"screenResX",
					((float)Screen.currentResolution.width).ToString()
				});
				SettingsManager.SetSetting(new string[]
				{
					"screenResY",
					num.ToString()
				});
			}
		}
		else
		{
			SettingsManager.settings = new SettingsManager.GameSettings();
			float num2 = (float)Screen.currentResolution.height;
			float num3 = (float)Screen.currentResolution.width;
			if (num2 >= 1000f)
			{
				SettingsManager.settings.UIScale = 0.75f;
			}
			SettingsManager.SetSetting(new string[]
			{
				"screenResX",
				num3.ToString()
			});
			SettingsManager.SetSetting(new string[]
			{
				"screenResY",
				num2.ToString()
			});
			SettingsManager.SetSetting(new string[]
			{
				"UIScale",
				SettingsManager.settings.UIScale.ToString()
			});
		}
		AudioListener.volume = SettingsManager.settings.masterVolume;
		SettingsManager.RefreshSettings("");
	}

	// Token: 0x06000843 RID: 2115 RVA: 0x0003BCE4 File Offset: 0x00039EE4
	public static void SetSetting(string[] args)
	{
		if (args.Length < 2)
		{
			Debug.LogError("Insufficient arguments provided. Expected format: [variableName, value]");
			return;
		}
		string text = args[0];
		string text2 = args[1];
		FieldInfo field = typeof(SettingsManager.GameSettings).GetField(text, BindingFlags.Instance | BindingFlags.Public);
		if (field == null)
		{
			Debug.LogError("Setting '" + text + "' not found.");
			return;
		}
		try
		{
			object obj = SettingsManager.ConvertValue(text2, field.FieldType);
			field.SetValue(SettingsManager.settings, obj);
		}
		catch (Exception ex)
		{
			Debug.LogError(string.Concat(new string[] { "Failed to set setting '", text, "' with value '", text2, "'. Error: ", ex.Message }));
		}
	}

	// Token: 0x06000844 RID: 2116 RVA: 0x0003BDA8 File Offset: 0x00039FA8
	private static object ConvertValue(string valueString, Type targetType)
	{
		if (targetType == typeof(bool))
		{
			bool flag;
			if (bool.TryParse(valueString, out flag))
			{
				return flag;
			}
			throw new ArgumentException("Cannot convert '" + valueString + "' to bool.");
		}
		else if (targetType == typeof(float))
		{
			float num;
			if (float.TryParse(valueString, out num))
			{
				return num;
			}
			throw new ArgumentException("Cannot convert '" + valueString + "' to float.");
		}
		else if (targetType == typeof(int))
		{
			int num2;
			if (int.TryParse(valueString, out num2))
			{
				return num2;
			}
			throw new ArgumentException("Cannot convert '" + valueString + "' to int.");
		}
		else
		{
			if (targetType == typeof(string))
			{
				return valueString;
			}
			throw new ArgumentException(string.Format("Unsupported target type '{0}'.", targetType));
		}
	}

	// Token: 0x06000845 RID: 2117 RVA: 0x0003BE84 File Offset: 0x0003A084
	public static string GetSetting(string variableName)
	{
		FieldInfo field = typeof(SettingsManager.GameSettings).GetField(variableName, BindingFlags.Instance | BindingFlags.Public);
		if (field == null)
		{
			Debug.LogError("Setting '" + variableName + "' not found.");
			return null;
		}
		object value = field.GetValue(SettingsManager.settings);
		if (value == null)
		{
			return "null";
		}
		return value.ToString();
	}

	// Token: 0x06000846 RID: 2118 RVA: 0x0003BEE0 File Offset: 0x0003A0E0
	public static void RefreshSettings(string setting = "")
	{
		bool flag = setting == "";
		if (SettingsManager.settings.itemHighVis)
		{
			Shader.SetGlobalFloat("_OUTLINEBRIGHT", 4f);
		}
		else
		{
			Shader.SetGlobalFloat("_OUTLINEBRIGHT", 1f);
		}
		Shader.SetGlobalFloat("_DITHEREFFECT", (float)(SettingsManager.settings.ditherEffect ? 1 : 0));
		Shader.SetGlobalFloat("_USEJITTER", (float)(SettingsManager.settings.vertexJitter ? 1 : 0));
		if (!CL_AchievementManager.GetAchievementValue("ACH_ABYSS"))
		{
			SettingsManager.settings.g_hard = false;
			SettingsManager.settings.g_competitive = false;
		}
		if ((double)SettingsManager.settings.masterVolume < 0.05)
		{
			SettingsManager.settings.masterVolume = 0f;
		}
		AudioListener.volume = SettingsManager.settings.masterVolume;
		if (SettingsManager.settings.musicVolume > 0f)
		{
			SettingsManager.instance.musicMixer.SetFloat("MasterVolume", Mathf.Lerp(-30f, 0f, SettingsManager.settings.musicVolume));
		}
		else
		{
			SettingsManager.instance.musicMixer.SetFloat("MasterVolume", -80f);
		}
		if (SettingsManager.settings.vsync)
		{
			QualitySettings.vSyncCount = 1;
		}
		else
		{
			QualitySettings.vSyncCount = 0;
		}
		if (!SettingsManager.settings.showSubtitlesBacking && CL_UIManager.instance != null)
		{
			CL_UIManager.instance.subtitleBacking.color = Color.clear;
		}
		if (flag || setting == "fullscreen" || setting == "refreshRate" || setting == "screenResX" || setting == "screenRexY")
		{
			Screen.fullScreen = SettingsManager.settings.fullscreen;
			if (SettingsManager.instance != null)
			{
				SettingsManager.instance.StartCoroutine(SettingsManager.<RefreshSettings>g__SetResolutionWait|18_0());
			}
			else
			{
				SettingsManager.<RefreshSettings>g__ChangeResolution|18_1();
			}
		}
		if (SettingsManager.SettingsRefreshUpdate != null)
		{
			FXManager.UpdateHandholdMaterialSettings();
			SettingsManager.SettingsRefreshUpdate();
		}
	}

	// Token: 0x06000847 RID: 2119 RVA: 0x0003C0D1 File Offset: 0x0003A2D1
	public static int GetTargetFramerate()
	{
		if (SettingsManager.settings.unlockFramerate)
		{
			return 1000;
		}
		return SettingsManager.settings.targetFramerate;
	}

	// Token: 0x06000849 RID: 2121 RVA: 0x0003C0F7 File Offset: 0x0003A2F7
	[CompilerGenerated]
	internal static IEnumerator <RefreshSettings>g__SetResolutionWait|18_0()
	{
		yield return new WaitForSeconds(0.1f);
		SettingsManager.<RefreshSettings>g__ChangeResolution|18_1();
		yield return new WaitForSeconds(0.1f);
		yield break;
	}

	// Token: 0x0600084A RID: 2122 RVA: 0x0003C100 File Offset: 0x0003A300
	[CompilerGenerated]
	internal static void <RefreshSettings>g__ChangeResolution|18_1()
	{
		FullScreenMode fullScreenMode;
		if (SettingsManager.settings.fullscreen)
		{
			fullScreenMode = FullScreenMode.FullScreenWindow;
		}
		else
		{
			fullScreenMode = FullScreenMode.Windowed;
		}
		RefreshRate refreshRate = default(RefreshRate);
		float num = (float)Screen.currentResolution.refreshRateRatio.value;
		float.TryParse(SettingsManager.settings.refreshRate, out num);
		if (float.IsNaN(num) || num == 0f || num > (float)Screen.currentResolution.refreshRateRatio.value)
		{
			SettingsManager.settings.refreshRate = Screen.currentResolution.refreshRateRatio.value.ToString();
		}
		refreshRate.numerator = (uint)Mathf.RoundToInt(num * 1000f);
		refreshRate.denominator = 1000U;
		if (Screen.fullScreen)
		{
			Screen.SetResolution(SettingsManager.settings.screenResX, SettingsManager.settings.screenResY, fullScreenMode, refreshRate);
		}
		SettingsManager.settings.targetFramerate = (int)refreshRate.value;
		Application.targetFrameRate = SettingsManager.GetTargetFramerate();
	}

	// Token: 0x040009A9 RID: 2473
	public static SettingsManager.GameSettings settings;

	// Token: 0x040009AA RID: 2474
	private string filePath;

	// Token: 0x040009AB RID: 2475
	public AudioMixer musicMixer;

	// Token: 0x040009AC RID: 2476
	public AudioMixer effectMixer;

	// Token: 0x040009AD RID: 2477
	public static SettingsManager instance;

	// Token: 0x040009AE RID: 2478
	public static Action SettingsRefreshUpdate;

	// Token: 0x040009AF RID: 2479
	public static SettingsManager.BuildType buildType;

	// Token: 0x020002AE RID: 686
	public class GameSettings
	{
		// Token: 0x04001117 RID: 4375
		public int version = 1;

		// Token: 0x04001118 RID: 4376
		public bool fullscreen = true;

		// Token: 0x04001119 RID: 4377
		public int screenResX;

		// Token: 0x0400111A RID: 4378
		public int screenResY;

		// Token: 0x0400111B RID: 4379
		public float playerFOV = 90f;

		// Token: 0x0400111C RID: 4380
		public float masterVolume = 0.75f;

		// Token: 0x0400111D RID: 4381
		public float musicVolume = 0.75f;

		// Token: 0x0400111E RID: 4382
		public float mouseSensitivity = 0.5f;

		// Token: 0x0400111F RID: 4383
		public float controllerAcceleration = 0.2f;

		// Token: 0x04001120 RID: 4384
		public bool useControllerGyro;

		// Token: 0x04001121 RID: 4385
		public float controllerGyroSensitivity = 1f;

		// Token: 0x04001122 RID: 4386
		public float brightness = 1f;

		// Token: 0x04001123 RID: 4387
		public string refreshRate;

		// Token: 0x04001124 RID: 4388
		public bool toggleInventory;

		// Token: 0x04001125 RID: 4389
		public bool toggleCrouch;

		// Token: 0x04001126 RID: 4390
		public bool alwaysSprint;

		// Token: 0x04001127 RID: 4391
		public bool directionalSwing = true;

		// Token: 0x04001128 RID: 4392
		public bool inverseRopeSwing;

		// Token: 0x04001129 RID: 4393
		public bool invertY;

		// Token: 0x0400112A RID: 4394
		public bool vsync = true;

		// Token: 0x0400112B RID: 4395
		public bool g_competitive;

		// Token: 0x0400112C RID: 4396
		public bool g_timer;

		// Token: 0x0400112D RID: 4397
		public bool g_hard;

		// Token: 0x0400112E RID: 4398
		public bool ditherEffect = true;

		// Token: 0x0400112F RID: 4399
		public bool crtEffect = true;

		// Token: 0x04001130 RID: 4400
		public bool vertexJitter = true;

		// Token: 0x04001131 RID: 4401
		public int controlScheme;

		// Token: 0x04001132 RID: 4402
		public bool autoswitchControls = true;

		// Token: 0x04001133 RID: 4403
		public bool forceKeyboard;

		// Token: 0x04001134 RID: 4404
		public int voice;

		// Token: 0x04001135 RID: 4405
		public float UIScale = 0.75f;

		// Token: 0x04001136 RID: 4406
		public float crosshairScale = 1f;

		// Token: 0x04001137 RID: 4407
		public int crosshair;

		// Token: 0x04001138 RID: 4408
		public float handIconScale = 0.8f;

		// Token: 0x04001139 RID: 4409
		public bool showSubtitles;

		// Token: 0x0400113A RID: 4410
		public bool showSubtitlesBacking;

		// Token: 0x0400113B RID: 4411
		public bool disableSprintFov;

		// Token: 0x0400113C RID: 4412
		public bool unlockAll;

		// Token: 0x0400113D RID: 4413
		public bool handholdHighVis;

		// Token: 0x0400113E RID: 4414
		public bool itemHighVis;

		// Token: 0x0400113F RID: 4415
		public bool extraHandholdShimmer;

		// Token: 0x04001140 RID: 4416
		public int cloudQuality = 2;

		// Token: 0x04001141 RID: 4417
		public int effectQuality = 2;

		// Token: 0x04001142 RID: 4418
		public bool lowQualityFog;

		// Token: 0x04001143 RID: 4419
		public bool debugCamDepthDisable;

		// Token: 0x04001144 RID: 4420
		public bool unlockFramerate;

		// Token: 0x04001145 RID: 4421
		public int targetFramerate = 144;
	}

	// Token: 0x020002AF RID: 687
	public enum BuildType
	{
		// Token: 0x04001147 RID: 4423
		standard,
		// Token: 0x04001148 RID: 4424
		dev
	}
}
