﻿using System;
using UnityEngine;

// Token: 0x0200008E RID: 142
public class HandItem_Hammer : HandItem
{
	// Token: 0x060004C3 RID: 1219 RVA: 0x0002915B File Offset: 0x0002735B
	private void OnEnable()
	{
		this.used = false;
	}

	// Token: 0x060004C4 RID: 1220 RVA: 0x00029164 File Offset: 0x00027364
	private void Update()
	{
		RaycastHit raycastHit;
		if (CL_UIManager.debug && Physics.SphereCast(Camera.main.transform.position, 0.1f, Camera.main.transform.forward, out raycastHit, 2.8f, this.hitMask))
		{
			CL_DebugView.draw.SphereOutline(raycastHit.point, 0.1f, Color.magenta);
		}
		this.HitCast();
		if (this.hasHit && this.hitTagger != null && this.hitTagger.HasTag("Hammerable") && this.hitTagger.HasTag("Highlightable"))
		{
			UT_Highlight component = this.hitTagger.GetComponent<UT_Highlight>();
			if (component != null)
			{
				component.Highlight();
			}
		}
	}

	// Token: 0x060004C5 RID: 1221 RVA: 0x0002922F File Offset: 0x0002742F
	public override void Use()
	{
		base.Use();
		if (this.used)
		{
			return;
		}
		this.used = true;
		this.anim.SetTrigger("Use");
		CL_CameraControl.Shake(0.01f);
	}

	// Token: 0x060004C6 RID: 1222 RVA: 0x00029268 File Offset: 0x00027468
	public void HitCast()
	{
		float num = 0.1f;
		if (Physics.SphereCast(Camera.main.transform.position, num, Camera.main.transform.forward, out this.hit, 2.8f, this.hitMask))
		{
			this.hasHit = true;
			this.hitTagger = this.hit.collider.GetComponent<ObjectTagger>();
		}
		else
		{
			this.hasHit = false;
			this.hitTagger = null;
		}
		if (InputManager.IsGamepad())
		{
			Vector3 vector = Camera.main.transform.position + Camera.main.transform.forward * 2.5f;
			if (this.hasHit)
			{
				vector = this.hit.point;
			}
			Collider[] array = Physics.OverlapCapsule(vector, Camera.main.transform.position, this.hammerAssistRadius, this.hitMask);
			for (int i = 0; i < array.Length; i++)
			{
				ObjectTagger component = array[i].GetComponent<ObjectTagger>();
				if (component != null && component.HasTag("Hammerable") && component.HasTag("Highlightable"))
				{
					CL_Handhold component2 = component.GetComponent<CL_Handhold>();
					if (!component2.secure)
					{
						Vector3 normalized = (component2.transform.position - (Camera.main.transform.forward + Camera.main.transform.position)).normalized;
						Vector3 vector2 = (Camera.main.transform.forward - normalized).normalized;
						vector2 = Camera.main.transform.InverseTransformVector(vector2);
						Vector3 vector3 = new Vector3(vector2.y, -vector2.x, 0f);
						ENT_Player.playerObject.AddRotationVelocity(vector3 * this.hammerAssist * Time.deltaTime);
						this.hasHit = true;
						this.hitTagger = component;
						return;
					}
				}
			}
		}
	}

	// Token: 0x060004C7 RID: 1223 RVA: 0x00029478 File Offset: 0x00027678
	public void Hit()
	{
		float buff = ENT_Player.playerObject.curBuffs.GetBuff("addStrike");
		float num = (float)this.damage + buff;
		if (!this.hasHit)
		{
			this.aud.GetGroup("hammer").PlayAudioSet("swing");
			return;
		}
		if (this.banHammer)
		{
			Object.Destroy(this.hit.collider.gameObject);
			this.aud.GetGroup("hammer").PlayAudioSet("hit");
			return;
		}
		if (this.hitTagger != null)
		{
			if (this.hitTagger.HasTag("Creature"))
			{
				this.aud.GetGroup("hammer").PlayAudioSet("hit-barnacle");
			}
			else if (this.hitTagger.HasTag("Damageable"))
			{
				this.aud.GetGroup("hammer").PlayAudioSet("hit");
			}
			else
			{
				this.aud.GetGroup("hammer").PlayAudioSet("hit");
			}
			if (this.hitTagger.HasTag("Damageable"))
			{
				this.hit.collider.GetComponent<Damageable>().Damage(num, "Hammer");
			}
			else if (this.hitTagger.HasTag("Handhold"))
			{
				CL_Handhold component = this.hitTagger.GetComponent<CL_Handhold>();
				if (component != null && component.canHammer)
				{
					float buff2 = ENT_Player.playerObject.curBuffs.GetBuff("addHammer");
					component.HammerIn(this.hammerAmount + buff2);
					if (!component.secure)
					{
						this.aud.GetGroup("hammer").PlayAudioSet("hit-piton");
					}
					else
					{
						this.aud.GetGroup("hammer").PlayAudioSet("hit-secure");
					}
				}
			}
			else if (this.hitTagger.HasTag("Button"))
			{
				CL_Button component2 = this.hitTagger.GetComponent<CL_Button>();
				if (component2 != null && component2.hammerCanPress)
				{
					component2.Interact();
				}
			}
			if (this.hitTagger.HasTag("Hitbox"))
			{
				GameEntity entity = this.hitTagger.GetComponent<ENT_Hitbox>().GetEntity();
				if (entity.GetTagger().HasTag("Prop"))
				{
					entity.AddForceAtPosition(Camera.main.transform.forward * 40f, this.hit.point);
					CL_Prop component3 = base.GetComponent<CL_Prop>();
					if (component3 != null)
					{
						component3.AddBreakingVelocity(Camera.main.transform.forward);
					}
				}
				else
				{
					entity.AddForce(Camera.main.transform.forward * 20f);
				}
			}
			else if (this.hitTagger.HasTag("Entity"))
			{
				GameEntity component4 = this.hitTagger.GetComponent<GameEntity>();
				if (this.hitTagger.HasTag("Prop"))
				{
					component4.AddForceAtPosition(Camera.main.transform.forward * 40f, this.hit.point);
					CL_Prop component5 = base.GetComponent<CL_Prop>();
					if (component5 != null)
					{
						component5.AddBreakingVelocity(Camera.main.transform.forward);
					}
				}
				else if (component4 != null)
				{
					component4.AddForce(Camera.main.transform.forward * 20f);
				}
			}
		}
		else
		{
			this.aud.GetGroup("hammer").PlayAudioSet("hit");
		}
		Object.Instantiate<GameObject>(this.hitEffect, this.hit.point, Quaternion.Euler(this.hit.normal), null);
		CL_CameraControl.Shake(0.015f);
		this.used = true;
	}

	// Token: 0x060004C8 RID: 1224 RVA: 0x00029857 File Offset: 0x00027A57
	public void Ready()
	{
		this.used = false;
	}

	// Token: 0x04000644 RID: 1604
	public LayerMask hitMask;

	// Token: 0x04000645 RID: 1605
	public UT_AudioClipHandler aud;

	// Token: 0x04000646 RID: 1606
	public GameObject hitEffect;

	// Token: 0x04000647 RID: 1607
	private RaycastHit hit;

	// Token: 0x04000648 RID: 1608
	private bool hasHit;

	// Token: 0x04000649 RID: 1609
	private ObjectTagger hitTagger;

	// Token: 0x0400064A RID: 1610
	public int damage = 1;

	// Token: 0x0400064B RID: 1611
	public float hammerAmount = 0.35f;

	// Token: 0x0400064C RID: 1612
	public float hammerAssist = 50f;

	// Token: 0x0400064D RID: 1613
	public float hammerAssistRadius = 1f;

	// Token: 0x0400064E RID: 1614
	public bool banHammer;
}
