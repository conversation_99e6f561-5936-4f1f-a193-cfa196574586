﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020000E3 RID: 227
public class UT_Timer : MonoBehaviour
{
	// Token: 0x06000707 RID: 1799 RVA: 0x0003698C File Offset: 0x00034B8C
	private void Start()
	{
		this.curTime = this.time;
		this.SetupEvents();
	}

	// Token: 0x06000708 RID: 1800 RVA: 0x000369A0 File Offset: 0x00034BA0
	private void Awake()
	{
		if (this.runOnAwake)
		{
			this.running = true;
		}
	}

	// Token: 0x06000709 RID: 1801 RVA: 0x000369B1 File Offset: 0x00034BB1
	private void OnValidate()
	{
		this.SetupEvents();
	}

	// Token: 0x0600070A RID: 1802 RVA: 0x000369BC File Offset: 0x00034BBC
	private void SetupEvents()
	{
		foreach (UT_Timer.IntermediateEvent intermediateEvent in this.otherEvents)
		{
			intermediateEvent.SetParentTimer(this);
		}
	}

	// Token: 0x0600070B RID: 1803 RVA: 0x00036A10 File Offset: 0x00034C10
	private void Update()
	{
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		if (this.running)
		{
			if (this.unscaledTime)
			{
				this.curTime -= Time.unscaledDeltaTime;
			}
			else
			{
				this.curTime -= Time.deltaTime;
			}
			foreach (UT_Timer.IntermediateEvent intermediateEvent in this.otherEvents)
			{
				if (this.curTime <= intermediateEvent.time && !intermediateEvent.hasRun)
				{
					intermediateEvent.hasRun = true;
					intermediateEvent.timeEvent.Invoke();
				}
			}
			if (this.curTime <= 0f)
			{
				this.running = false;
				this.endEvent.Invoke();
				if (this.repeat)
				{
					this.Play(true);
				}
			}
		}
	}

	// Token: 0x0600070C RID: 1804 RVA: 0x00036AF8 File Offset: 0x00034CF8
	public void Play(bool reset = false)
	{
		this.running = true;
		if (reset)
		{
			this.Reset();
		}
	}

	// Token: 0x0600070D RID: 1805 RVA: 0x00036B0A File Offset: 0x00034D0A
	public void Pause()
	{
		this.running = false;
	}

	// Token: 0x0600070E RID: 1806 RVA: 0x00036B14 File Offset: 0x00034D14
	public void Reset()
	{
		this.curTime = this.time;
		foreach (UT_Timer.IntermediateEvent intermediateEvent in this.otherEvents)
		{
			intermediateEvent.hasRun = false;
		}
	}

	// Token: 0x0600070F RID: 1807 RVA: 0x00036B74 File Offset: 0x00034D74
	public void Stop()
	{
		this.running = false;
		this.Reset();
	}

	// Token: 0x06000710 RID: 1808 RVA: 0x00036B83 File Offset: 0x00034D83
	public void AddTime(float t)
	{
		this.curTime += t;
	}

	// Token: 0x04000880 RID: 2176
	public string description;

	// Token: 0x04000881 RID: 2177
	public float time = 60f;

	// Token: 0x04000882 RID: 2178
	private float curTime;

	// Token: 0x04000883 RID: 2179
	private bool running;

	// Token: 0x04000884 RID: 2180
	public bool runOnAwake;

	// Token: 0x04000885 RID: 2181
	public bool repeat;

	// Token: 0x04000886 RID: 2182
	public bool unscaledTime;

	// Token: 0x04000887 RID: 2183
	public UnityEvent endEvent;

	// Token: 0x04000888 RID: 2184
	public List<UT_Timer.IntermediateEvent> otherEvents = new List<UT_Timer.IntermediateEvent>();

	// Token: 0x02000294 RID: 660
	[Serializable]
	public class IntermediateEvent
	{
		// Token: 0x06000E69 RID: 3689 RVA: 0x000571F2 File Offset: 0x000553F2
		public void SetParentTimer(UT_Timer t)
		{
			this.parentTimer = t;
		}

		// Token: 0x06000E6A RID: 3690 RVA: 0x000571FB File Offset: 0x000553FB
		public float GetTime()
		{
			if (!this.timeFromStart)
			{
				return this.time;
			}
			return this.parentTimer.time - this.time;
		}

		// Token: 0x040010A9 RID: 4265
		public float time;

		// Token: 0x040010AA RID: 4266
		public UnityEvent timeEvent;

		// Token: 0x040010AB RID: 4267
		public bool hasRun;

		// Token: 0x040010AC RID: 4268
		public bool timeFromStart;

		// Token: 0x040010AD RID: 4269
		private UT_Timer parentTimer;
	}
}
