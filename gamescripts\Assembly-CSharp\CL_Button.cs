﻿using System;
using System.Collections;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200007F RID: 127
public class CL_Button : MonoBehaviour, Clickable
{
	// Token: 0x06000433 RID: 1075 RVA: 0x00025EE8 File Offset: 0x000240E8
	private void Start()
	{
		this.rendererComponent = base.GetComponent<MeshRenderer>();
		this.ChooseMaterial();
	}

	// Token: 0x06000434 RID: 1076 RVA: 0x00025EFC File Offset: 0x000240FC
	public virtual void Interact()
	{
		if (this.cooldown)
		{
			return;
		}
		if (this.maxPresses > 0 && this.presses >= this.maxPresses)
		{
			return;
		}
		if (!this.active)
		{
			return;
		}
		this.presses++;
		base.StartCoroutine(this.Cooldown());
		this.activeEvent.Invoke();
		this.hasBeenPressed = true;
		AudioManager.PlaySound(this.pressSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
	}

	// Token: 0x06000435 RID: 1077 RVA: 0x00025F8C File Offset: 0x0002418C
	public virtual void Interact(ENT_Player p, string s = "")
	{
		this.Interact();
	}

	// Token: 0x06000436 RID: 1078 RVA: 0x00025F94 File Offset: 0x00024194
	private IEnumerator Cooldown()
	{
		this.cooldown = true;
		this.ChooseMaterial();
		yield return new WaitForSeconds(this.pressCooldown);
		this.cooldown = false;
		this.ChooseMaterial();
		AudioManager.PlaySound(this.cooldownEndSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
		yield break;
	}

	// Token: 0x06000437 RID: 1079 RVA: 0x00025FA3 File Offset: 0x000241A3
	public void Reset()
	{
		this.hasBeenPressed = false;
		this.presses = 0;
		this.ChooseMaterial();
	}

	// Token: 0x06000438 RID: 1080 RVA: 0x00025FB9 File Offset: 0x000241B9
	public bool CanInteract(ENT_Player p, ENT_Player.Hand curHand)
	{
		return !this.cooldown && (this.presses < this.maxPresses || this.maxPresses <= 0) && this.active;
	}

	// Token: 0x06000439 RID: 1081 RVA: 0x00025FE4 File Offset: 0x000241E4
	public GameObject GetGameObject()
	{
		return base.gameObject;
	}

	// Token: 0x0600043A RID: 1082 RVA: 0x00025FEC File Offset: 0x000241EC
	public ObjectTagger GetTagger()
	{
		return base.gameObject.GetComponent<ObjectTagger>();
	}

	// Token: 0x0600043B RID: 1083 RVA: 0x00025FF9 File Offset: 0x000241F9
	public void SetInteractable(bool b)
	{
		this.active = b;
		this.ChooseMaterial();
	}

	// Token: 0x0600043C RID: 1084 RVA: 0x00026008 File Offset: 0x00024208
	internal virtual void ChooseMaterial()
	{
		if (this.active && (!this.cooldown || !this.inactiveMaterialOnCooldown) && (this.maxPresses <= 0 || this.presses < this.maxPresses))
		{
			this.SetMaterial(this.activeMaterial);
			return;
		}
		this.SetMaterial(this.inactiveMaterial);
	}

	// Token: 0x0600043D RID: 1085 RVA: 0x00026060 File Offset: 0x00024260
	public void SetMaterial(Material mat)
	{
		if (this.rendererComponent != null && mat != null)
		{
			Material[] materials = this.rendererComponent.materials;
			materials[this.materialIndex] = mat;
			this.rendererComponent.SetMaterials(materials.ToList<Material>());
		}
	}

	// Token: 0x0400057F RID: 1407
	public bool active = true;

	// Token: 0x04000580 RID: 1408
	[Header("Events")]
	public UnityEvent activeEvent;

	// Token: 0x04000581 RID: 1409
	[HideInInspector]
	public bool hasBeenPressed;

	// Token: 0x04000582 RID: 1410
	public int maxPresses;

	// Token: 0x04000583 RID: 1411
	public float pressCooldown = 0.05f;

	// Token: 0x04000584 RID: 1412
	private int presses;

	// Token: 0x04000585 RID: 1413
	public bool projectilesCanPress = true;

	// Token: 0x04000586 RID: 1414
	public bool hammerCanPress = true;

	// Token: 0x04000587 RID: 1415
	internal bool cooldown;

	// Token: 0x04000588 RID: 1416
	[Header("Audio")]
	public AudioClip pressSound;

	// Token: 0x04000589 RID: 1417
	public AudioClip cooldownEndSound;

	// Token: 0x0400058A RID: 1418
	[Header("Materials")]
	public int materialIndex;

	// Token: 0x0400058B RID: 1419
	public bool inactiveMaterialOnCooldown;

	// Token: 0x0400058C RID: 1420
	public Material activeMaterial;

	// Token: 0x0400058D RID: 1421
	public Material inactiveMaterial;

	// Token: 0x0400058E RID: 1422
	private MeshRenderer rendererComponent;
}
