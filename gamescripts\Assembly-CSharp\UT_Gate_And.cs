﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200016E RID: 366
public class UT_Gate_And : MonoBehaviour
{
	// Token: 0x06000A42 RID: 2626 RVA: 0x000441F1 File Offset: 0x000423F1
	public void SetA(bool b)
	{
		this.inputA = b;
		this.Check();
	}

	// Token: 0x06000A43 RID: 2627 RVA: 0x00044200 File Offset: 0x00042400
	public void SetB(bool b)
	{
		this.inputB = b;
		this.Check();
	}

	// Token: 0x06000A44 RID: 2628 RVA: 0x00044210 File Offset: 0x00042410
	public void Check()
	{
		if (!this.nand)
		{
			if (this.inputA && this.inputB)
			{
				this.trueEvent.Invoke();
				return;
			}
			this.falseEvent.Invoke();
			return;
		}
		else
		{
			if (!this.inputA || !this.inputB)
			{
				this.trueEvent.Invoke();
				return;
			}
			this.falseEvent.Invoke();
			return;
		}
	}

	// Token: 0x04000B4D RID: 2893
	public bool inputA;

	// Token: 0x04000B4E RID: 2894
	public bool inputB;

	// Token: 0x04000B4F RID: 2895
	[Header("Settings")]
	public bool nand;

	// Token: 0x04000B50 RID: 2896
	public UnityEvent trueEvent;

	// Token: 0x04000B51 RID: 2897
	public UnityEvent falseEvent;
}
