﻿using System;

// Token: 0x02000094 RID: 148
[Serializable]
public abstract class Item_Data
{
	// Token: 0x06000506 RID: 1286 RVA: 0x0002B1FC File Offset: 0x000293FC
	public virtual void Initialize(Item i)
	{
		this.item = i;
	}

	// Token: 0x06000507 RID: 1287 RVA: 0x0002B205 File Offset: 0x00029405
	public virtual string GetDataType()
	{
		return "null";
	}

	// Token: 0x06000508 RID: 1288 RVA: 0x0002B20C File Offset: 0x0002940C
	public virtual void OnPickup()
	{
	}

	// Token: 0x06000509 RID: 1289 RVA: 0x0002B20E File Offset: 0x0002940E
	public virtual void OnDrop()
	{
	}

	// Token: 0x0600050A RID: 1290 RVA: 0x0002B210 File Offset: 0x00029410
	public virtual string GetSaveData()
	{
		return "null";
	}

	// Token: 0x0600050B RID: 1291 RVA: 0x0002B217 File Offset: 0x00029417
	public virtual void LoadSaveData(string s)
	{
	}

	// Token: 0x0400069C RID: 1692
	internal Item item;
}
