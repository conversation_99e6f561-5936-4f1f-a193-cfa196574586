﻿using System;
using System.Collections.Generic;
using System.Linq;
using Drawing;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020001AC RID: 428
public class UT_Locator : MonoBehaviourGizmos
{
	// Token: 0x06000B3B RID: 2875 RVA: 0x000482A5 File Offset: 0x000464A5
	private void Start()
	{
		this.Initialize();
	}

	// Token: 0x06000B3C RID: 2876 RVA: 0x000482AD File Offset: 0x000464AD
	public void Initialize()
	{
		if (!this.removeOnDisable && !this.initialized)
		{
			this.initialized = true;
			this.AddToLocators();
		}
	}

	// Token: 0x06000B3D RID: 2877 RVA: 0x000482CC File Offset: 0x000464CC
	private void OnEnable()
	{
		if (this.removeOnDisable)
		{
			this.AddToLocators();
		}
	}

	// Token: 0x06000B3E RID: 2878 RVA: 0x000482DC File Offset: 0x000464DC
	private void OnDisable()
	{
		if (this.removeOnDisable)
		{
			this.RemoveFromLocators();
		}
	}

	// Token: 0x06000B3F RID: 2879 RVA: 0x000482EC File Offset: 0x000464EC
	private void OnDestroy()
	{
		if (!this.removeOnDisable)
		{
			this.RemoveFromLocators();
		}
	}

	// Token: 0x06000B40 RID: 2880 RVA: 0x000482FC File Offset: 0x000464FC
	private void AddToLocators()
	{
		if (UT_Locator.locators == null)
		{
			UT_Locator.locators = new Dictionary<string, UT_Locator.LocatorGroup>();
		}
		if (UT_Locator.locators.ContainsKey(this.locatorID))
		{
			UT_Locator.locators[this.locatorID].AddLocator(this);
			return;
		}
		UT_Locator.LocatorGroup locatorGroup = new UT_Locator.LocatorGroup();
		locatorGroup.id = this.locatorID;
		locatorGroup.AddLocator(this);
		UT_Locator.locators.Add(this.locatorID, locatorGroup);
	}

	// Token: 0x06000B41 RID: 2881 RVA: 0x0004836D File Offset: 0x0004656D
	private void RemoveFromLocators()
	{
		if (UT_Locator.locators == null)
		{
			return;
		}
		if (UT_Locator.locators.ContainsKey(this.locatorID))
		{
			UT_Locator.locators[this.locatorID].RemoveLocator(this);
		}
	}

	// Token: 0x06000B42 RID: 2882 RVA: 0x0004839F File Offset: 0x0004659F
	public void RunEvent()
	{
		this.locatorEvent.Invoke();
	}

	// Token: 0x06000B43 RID: 2883 RVA: 0x000483AC File Offset: 0x000465AC
	public static UT_Locator GetLocator(string id)
	{
		return UT_Locator.locators[id].GetFirstLocator();
	}

	// Token: 0x06000B44 RID: 2884 RVA: 0x000483BE File Offset: 0x000465BE
	public static UT_Locator.LocatorGroup GetLocatorGroup(string id)
	{
		if (UT_Locator.locators.ContainsKey(id))
		{
			return UT_Locator.locators[id];
		}
		return null;
	}

	// Token: 0x06000B45 RID: 2885 RVA: 0x000483DC File Offset: 0x000465DC
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			Draw.Label2D(base.transform.position + Vector3.up, base.name + " : " + this.locatorID, 12f, LabelAlignment.Center);
		}
	}

	// Token: 0x06000B46 RID: 2886 RVA: 0x00048430 File Offset: 0x00046630
	private void OnDrawGizmos()
	{
		if (this.locatorID == "creature-fly-spawn")
		{
			Gizmos.DrawIcon(base.transform.position, "gizmo-mosquito", true, Color.yellow);
			return;
		}
		if (this.locatorID == "creature-spawn")
		{
			Gizmos.DrawIcon(base.transform.position, "gizmo-bug", true, Color.yellow);
			return;
		}
		if (this.locatorID == "object-spawn")
		{
			Gizmos.DrawIcon(base.transform.position, "gizmo-radio", true, Color.yellow);
			return;
		}
		Gizmos.DrawIcon(base.transform.position, "gizmo-crosshair", true, Color.yellow);
	}

	// Token: 0x06000B47 RID: 2887 RVA: 0x000484E2 File Offset: 0x000466E2
	public void SetParentLevel(M_Level p)
	{
		this.parentLevel = p;
	}

	// Token: 0x06000B48 RID: 2888 RVA: 0x000484EB File Offset: 0x000466EB
	public M_Level GetParentLevel()
	{
		return this.parentLevel;
	}

	// Token: 0x04000C30 RID: 3120
	public string locatorID;

	// Token: 0x04000C31 RID: 3121
	public bool removeOnDisable;

	// Token: 0x04000C32 RID: 3122
	[SerializeField]
	private M_Level parentLevel;

	// Token: 0x04000C33 RID: 3123
	public UnityEvent locatorEvent;

	// Token: 0x04000C34 RID: 3124
	public static Dictionary<string, UT_Locator.LocatorGroup> locators;

	// Token: 0x04000C35 RID: 3125
	private bool initialized;

	// Token: 0x020002E6 RID: 742
	public class LocatorGroup
	{
		// Token: 0x06000F5F RID: 3935 RVA: 0x0005AD34 File Offset: 0x00058F34
		public void AddLocator(UT_Locator locator)
		{
			if (this.locatorHash == null)
			{
				this.locatorHash = new HashSet<UT_Locator>();
				this.locators = new List<UT_Locator>();
			}
			if (this.locatorHash.Contains(locator))
			{
				return;
			}
			this.locators.Add(locator);
			this.locatorHash.Add(locator);
			this.locators.RemoveAll((UT_Locator item) => item == null);
		}

		// Token: 0x06000F60 RID: 3936 RVA: 0x0005ADB2 File Offset: 0x00058FB2
		public void RemoveLocator(UT_Locator locator)
		{
			if (!this.locatorHash.Contains(locator))
			{
				return;
			}
			this.locatorHash.Remove(locator);
			this.locators.Remove(locator);
		}

		// Token: 0x06000F61 RID: 3937 RVA: 0x0005ADE0 File Offset: 0x00058FE0
		public List<UT_Locator> GetClosestLocators(Vector3 pos, int count, bool onlyActive = true)
		{
			if (this.locators == null || this.locators.Count == 0 || count <= 0)
			{
				return new List<UT_Locator>();
			}
			return (from l in this.locators
				where l != null && (!onlyActive || l.gameObject.activeInHierarchy)
				orderby (l.transform.position - pos).sqrMagnitude
				select l).Take(count).ToList<UT_Locator>();
		}

		// Token: 0x06000F62 RID: 3938 RVA: 0x0005AE54 File Offset: 0x00059054
		public UT_Locator GetLocatorClosestToPosition(Vector3 pos, bool onlyActive = true)
		{
			if (this.locators == null || this.locators.Count == 0)
			{
				return null;
			}
			UT_Locator ut_Locator = null;
			float num = float.MaxValue;
			foreach (UT_Locator ut_Locator2 in this.locators)
			{
				if (!(ut_Locator2 == null) && (!onlyActive || ut_Locator2.gameObject.activeInHierarchy))
				{
					float sqrMagnitude = (ut_Locator2.transform.position - pos).sqrMagnitude;
					if (sqrMagnitude < num)
					{
						num = sqrMagnitude;
						ut_Locator = ut_Locator2;
					}
				}
			}
			return ut_Locator;
		}

		// Token: 0x06000F63 RID: 3939 RVA: 0x0005AF00 File Offset: 0x00059100
		public UT_Locator GetFirstLocator()
		{
			return this.locators[0];
		}

		// Token: 0x04001274 RID: 4724
		public string id;

		// Token: 0x04001275 RID: 4725
		private List<UT_Locator> locators;

		// Token: 0x04001276 RID: 4726
		private HashSet<UT_Locator> locatorHash;
	}
}
