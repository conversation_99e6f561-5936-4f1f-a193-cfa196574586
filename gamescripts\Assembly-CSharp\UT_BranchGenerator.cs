﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200015D RID: 349
public class UT_BranchGenerator : MonoBehaviour
{
	// Token: 0x060009EA RID: 2538 RVA: 0x00043048 File Offset: 0x00041248
	public void Generate()
	{
		if (WorldLoader.instance == null)
		{
			return;
		}
		this.selectedBranch = Object.Instantiate<M_GenerationBranch>(this.branch[Random.Range(0, this.branch.Count)]);
		M_GenerationBranch m_GenerationBranch = this.selectedBranch;
		m_GenerationBranch.id += Time.frameCount.ToString();
		WorldLoader.instance.GenerateBranchFromLevel(this.currentLevel, this.startTransform, this.selectedBranch, this.selectedBranch);
		if (this.switchToBranchAfterGenerating)
		{
			this.MovePlayerIntoBranch();
		}
	}

	// Token: 0x060009EB RID: 2539 RVA: 0x000430DD File Offset: 0x000412DD
	public void MovePlayerIntoBranch()
	{
		WorldLoader.instance.ChangeBranch(this.selectedBranch.id);
	}

	// Token: 0x04000B04 RID: 2820
	public List<M_GenerationBranch> branch;

	// Token: 0x04000B05 RID: 2821
	public M_Level currentLevel;

	// Token: 0x04000B06 RID: 2822
	public Transform startTransform;

	// Token: 0x04000B07 RID: 2823
	private M_GenerationBranch selectedBranch;

	// Token: 0x04000B08 RID: 2824
	public bool switchToBranchAfterGenerating = true;
}
