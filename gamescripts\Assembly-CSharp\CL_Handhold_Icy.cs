﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000086 RID: 134
public class CL_Handhold_Icy : CL_Handhold
{
	// Token: 0x0600048A RID: 1162 RVA: 0x00027F64 File Offset: 0x00026164
	internal override void Update()
	{
		base.Update();
		if (base.GetHolding() && this.hands.Count > 0)
		{
			for (int i = 0; i < this.hands.Count; i++)
			{
				if (this.handHoldTime.ContainsKey(this.hands[i]))
				{
					bool flag = this.handHoldTime[this.hands[i]] > this.handShakeHoldTime;
					Dictionary<ENT_Player.Hand, float> dictionary = this.handHoldTime;
					ENT_Player.Hand hand = this.hands[i];
					dictionary[hand] += Time.deltaTime;
					this.hands[i].OffsetHoldPosition(Vector3.down * Time.deltaTime * this.slipSpeed);
					if (this.handHoldTime[this.hands[i]] > this.handShakeHoldTime)
					{
						this.hands[i].ShakeHand(this.handShakeAmount * Time.deltaTime);
						CL_CameraControl.Shake(0.0005f);
						if (!flag && this.shakeStartSound != null)
						{
							AudioManager.PlaySound(this.shakeStartSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
						}
					}
					if (this.handHoldTime[this.hands[i]] > this.maxHoldTime)
					{
						this.hands[i].DropHand(false);
						if (this.slipSound != null)
						{
							AudioManager.PlaySound(this.slipSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
						}
						this.slipEvent.Invoke();
						return;
					}
				}
			}
		}
	}

	// Token: 0x0600048B RID: 1163 RVA: 0x00028140 File Offset: 0x00026340
	public override void Interact(ENT_Player p, ENT_Player.Hand hand)
	{
		if (!this.handHoldTime.ContainsKey(hand))
		{
			this.handHoldTime.Add(hand, 0f);
		}
		base.Interact(p, hand);
		Vector3 holdPosition = hand.GetHoldPosition();
		holdPosition.y = 0.2f;
		hand.SetHoldPosition(holdPosition);
	}

	// Token: 0x0600048C RID: 1164 RVA: 0x0002818E File Offset: 0x0002638E
	public override void StopInteract(ENT_Player p, ENT_Player.Hand dropHand, string s = "")
	{
		if (this.handHoldTime.ContainsKey(dropHand))
		{
			this.handHoldTime.Remove(dropHand);
		}
		base.StopInteract(p, dropHand, s);
	}

	// Token: 0x0400060C RID: 1548
	public float maxHoldTime = 3f;

	// Token: 0x0400060D RID: 1549
	public float slipSpeed = 1f;

	// Token: 0x0400060E RID: 1550
	public float handShakeHoldTime = 1f;

	// Token: 0x0400060F RID: 1551
	public float handShakeAmount = 0.1f;

	// Token: 0x04000610 RID: 1552
	[Space]
	[Header("Audio")]
	public AudioClip slipSound;

	// Token: 0x04000611 RID: 1553
	public AudioClip shakeStartSound;

	// Token: 0x04000612 RID: 1554
	public UnityEvent slipEvent;

	// Token: 0x04000613 RID: 1555
	private Dictionary<ENT_Player.Hand, float> handHoldTime = new Dictionary<ENT_Player.Hand, float>();
}
