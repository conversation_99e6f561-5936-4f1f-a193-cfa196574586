﻿using System;
using UnityEngine;

// Token: 0x020000D7 RID: 215
public class UT_ObjectSpawner : MonoBehaviour
{
	// Token: 0x060006CF RID: 1743 RVA: 0x00036096 File Offset: 0x00034296
	public void Spawn()
	{
		Object.Instantiate<GameObject>(this.spawnObject, this.spawnLocation.position, this.spawnLocation.rotation);
	}

	// Token: 0x060006D0 RID: 1744 RVA: 0x000360BA File Offset: 0x000342BA
	public void Spawn(GameObject o)
	{
		Object.Instantiate<GameObject>(o, this.spawnLocation.position, this.spawnLocation.rotation);
	}

	// Token: 0x04000854 RID: 2132
	public GameObject spawnObject;

	// Token: 0x04000855 RID: 2133
	public Transform spawnLocation;
}
