﻿using System;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x02000026 RID: 38
public class DEN_SimpleChaseKiller : AIGameEntity
{
	// Token: 0x06000173 RID: 371 RVA: 0x0000B524 File Offset: 0x00009724
	public override void Start()
	{
		base.Start();
		this.playerTrackPositions = new Queue<Vector3>();
		this.curSpeed = this.speed;
		base.transform.parent = null;
		this.rigid = base.GetComponent<Rigidbody>();
		this.target = ENT_Player.playerObject.transform;
	}

	// Token: 0x06000174 RID: 372 RVA: 0x0000B578 File Offset: 0x00009778
	public override void Update()
	{
		base.Update();
		this.TrackPlayerPosition();
		this.Movement();
		this.contactDir = Vector3.Lerp(this.contactDir, Vector3.zero, Time.deltaTime * 2f);
		if (ENT_Player.playerObject.IsDead())
		{
			Object.Destroy(base.gameObject);
			return;
		}
		if (CL_UIManager.debug)
		{
			CL_DebugView.draw.Cross(base.transform.position, 0.3f, new Color(1f, 0f, 0f, 0.5f));
			CL_DebugView.draw.Label2D(base.transform.position + Vector3.up, "Using F5 to spy is cringe.", 16f, LabelAlignment.Center);
			CL_DebugView.draw.Label2D(base.transform.position + Vector3.up * 2f, "Mood: Pissed Off.", 14f, LabelAlignment.Center);
			CL_DebugView.draw.Label2D(base.transform.position + Vector3.up * -1f, "Speed: " + this.curSpeed.ToString(), 14f, LabelAlignment.Center);
		}
	}

	// Token: 0x06000175 RID: 373 RVA: 0x0000B6D0 File Offset: 0x000098D0
	private void Movement()
	{
		if (this.playerTrackPositions.Count > 0)
		{
			this.curSpeed += Time.deltaTime * this.speedIncreaseRate;
			Vector3 vector = this.playerTrackPositions.Peek();
			Vector3 normalized = (vector - base.transform.position).normalized;
			float num = Vector3.Distance(this.target.position, base.transform.position);
			if ((num > 10f && !this.targetVisible) || this.ignoreCollisions)
			{
				this.rigid.isKinematic = true;
				base.transform.position += normalized * Time.deltaTime * this.curSpeed;
			}
			else
			{
				this.rigid.isKinematic = false;
				this.rigid.velocity = normalized * this.curSpeed + this.contactDir * this.contactPushForce;
			}
			if (Vector3.Distance(base.transform.position, vector) < this.trackPositionDistance)
			{
				this.playerTrackPositions.Dequeue();
			}
			if (num < 2f)
			{
				ENT_Player.playerObject.Kill(this.objectType);
			}
		}
	}

	// Token: 0x06000176 RID: 374 RVA: 0x0000B810 File Offset: 0x00009A10
	private void TrackPlayerPosition()
	{
		this.targetVisible = this.sight.CanSeeTarget(this.target);
		if (this.targetVisible)
		{
			this.playerTrackPositions.Clear();
			this.playerTrackPositions.Enqueue(this.target.position);
		}
		if (Vector3.Distance(this.lastPlayerTrackPosition, ENT_Player.playerObject.transform.position) > this.trackPositionDistance)
		{
			this.playerTrackPositions.Enqueue(ENT_Player.playerObject.transform.position);
			this.lastPlayerTrackPosition = ENT_Player.playerObject.transform.position;
		}
	}

	// Token: 0x06000177 RID: 375 RVA: 0x0000B8B0 File Offset: 0x00009AB0
	private void OnCollisionStay(Collision collision)
	{
		this.contactDir = Vector3.zero;
		foreach (ContactPoint contactPoint in collision.contacts)
		{
			this.contactDir += contactPoint.normal;
		}
	}

	// Token: 0x04000152 RID: 338
	private Queue<Vector3> playerTrackPositions;

	// Token: 0x04000153 RID: 339
	private Vector3 lastPlayerTrackPosition;

	// Token: 0x04000154 RID: 340
	public float speed = 1f;

	// Token: 0x04000155 RID: 341
	private float curSpeed = 1f;

	// Token: 0x04000156 RID: 342
	public float speedIncreaseRate = 0.1f;

	// Token: 0x04000157 RID: 343
	public float trackPositionDistance = 0.5f;

	// Token: 0x04000158 RID: 344
	private Rigidbody rigid;

	// Token: 0x04000159 RID: 345
	public float contactPushForce = 1f;

	// Token: 0x0400015A RID: 346
	private Vector3 contactDir;

	// Token: 0x0400015B RID: 347
	private bool targetVisible;

	// Token: 0x0400015C RID: 348
	public bool ignoreCollisions;
}
