﻿using System;

// Token: 0x0200006D RID: 109
[Serializable]
public class SessionEventModule_StopThisEvent : SessionEventModule
{
	// Token: 0x060003E2 RID: 994 RVA: 0x00023A03 File Offset: 0x00021C03
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		if (this.onInitialization)
		{
			s.StopEvent();
		}
	}

	// Token: 0x060003E3 RID: 995 RVA: 0x00023A1A File Offset: 0x00021C1A
	public override void Activate()
	{
		base.Activate();
		this.sessionEvent.StopEvent();
	}

	// Token: 0x0400053B RID: 1339
	public bool onInitialization = true;
}
