﻿using System;
using Drawing;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200015A RID: 346
public class UT_TriggerZone : MonoBehaviourGizmos
{
	// Token: 0x060009D6 RID: 2518 RVA: 0x00042C30 File Offset: 0x00040E30
	private void OnTriggerEnter(Collider other)
	{
		if (this.runOnce && this.hasRun)
		{
			return;
		}
		ObjectTagger component = other.GetComponent<ObjectTagger>();
		if (component != null && component.HasTagInList(this.entityTags))
		{
			this.hasRun = true;
			this.onEnter.Invoke(other.transform);
		}
	}

	// Token: 0x060009D7 RID: 2519 RVA: 0x00042C84 File Offset: 0x00040E84
	private void OnTriggerExit(Collider other)
	{
		if (this.runOnce && this.hasRunExit)
		{
			return;
		}
		ObjectTagger component = other.GetComponent<ObjectTagger>();
		if (component != null && component.HasTagInList(this.entityTags))
		{
			this.hasRunExit = true;
			this.onExit.Invoke(other.transform);
		}
	}

	// Token: 0x060009D8 RID: 2520 RVA: 0x00042CD8 File Offset: 0x00040ED8
	public override void DrawGizmos()
	{
		Draw.Label2D(base.transform.position + Vector3.up * 0.2f, base.name, new Color(0f, 1f, 1f, 0.1f));
	}

	// Token: 0x060009D9 RID: 2521 RVA: 0x00042D2D File Offset: 0x00040F2D
	public void Reset()
	{
		this.hasRun = false;
		this.hasRunExit = false;
	}

	// Token: 0x04000AF9 RID: 2809
	public bool runOnce;

	// Token: 0x04000AFA RID: 2810
	private bool hasRun;

	// Token: 0x04000AFB RID: 2811
	private bool hasRunExit;

	// Token: 0x04000AFC RID: 2812
	public string[] entityTags;

	// Token: 0x04000AFD RID: 2813
	public UnityEvent<Transform> onEnter;

	// Token: 0x04000AFE RID: 2814
	public UnityEvent<Transform> onExit;
}
