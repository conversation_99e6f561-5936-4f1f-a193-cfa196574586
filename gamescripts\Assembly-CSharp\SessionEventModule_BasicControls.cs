﻿using System;

// Token: 0x02000061 RID: 97
[Serializable]
public class SessionEventModule_BasicControls : SessionEventModule
{
	// Token: 0x060003BB RID: 955 RVA: 0x00022E7B File Offset: 0x0002107B
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		if (this.screenShake > 0f)
		{
			CL_CameraControl.Shake(this.screenShake);
		}
	}

	// Token: 0x04000513 RID: 1299
	public float screenShake;
}
