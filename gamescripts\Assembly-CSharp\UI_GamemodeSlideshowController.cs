﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000129 RID: 297
public class UI_GamemodeSlideshowController : MonoBehaviour
{
	// Token: 0x060008C5 RID: 2245 RVA: 0x0003E1A8 File Offset: 0x0003C3A8
	private void Start()
	{
		if (this.slides == null || this.slides.Count == 0)
		{
			Debug.LogWarning("No slides assigned to the slideshow.");
			return;
		}
		foreach (UI_GamemodeSlideshowController.SlideHolder slideHolder in this.slides)
		{
			if (slideHolder.IsUnlocked())
			{
				this.currentSlides.Add(slideHolder);
			}
		}
	}

	// Token: 0x060008C6 RID: 2246 RVA: 0x0003E228 File Offset: 0x0003C428
	private void OnEnable()
	{
		base.StopAllCoroutines();
		base.StartCoroutine(this.SlideshowLoop());
	}

	// Token: 0x060008C7 RID: 2247 RVA: 0x0003E23D File Offset: 0x0003C43D
	private IEnumerator SlideshowLoop()
	{
		yield return null;
		this.imageForeground.sprite = this.currentSlides[0].GetRandomSlideSprite();
		this.SetImageAlpha(this.imageForeground, 1f);
		this.SetImageAlpha(this.imageBackground, 0f);
		int currentIndex = 0;
		yield return new WaitForSeconds(this.displayTime);
		for (;;)
		{
			int nextIndex = (currentIndex + 1) % this.currentSlides.Count;
			Sprite randomSlideSprite = this.currentSlides[nextIndex].GetRandomSlideSprite();
			if (this.isForegroundActive)
			{
				this.imageBackground.sprite = randomSlideSprite;
				this.SetImageAlpha(this.imageBackground, 1f);
				yield return base.StartCoroutine(this.FadeOut(this.imageForeground));
				this.isForegroundActive = false;
			}
			else
			{
				this.imageForeground.sprite = randomSlideSprite;
				this.SetImageAlpha(this.imageForeground, 0f);
				yield return base.StartCoroutine(this.FadeIn(this.imageForeground));
				this.SetImageAlpha(this.imageBackground, 0f);
				this.isForegroundActive = true;
			}
			currentIndex = nextIndex;
			yield return new WaitForSeconds(this.displayTime);
		}
		yield break;
	}

	// Token: 0x060008C8 RID: 2248 RVA: 0x0003E24C File Offset: 0x0003C44C
	private IEnumerator FadeIn(Image img)
	{
		float elapsed = 0f;
		this.SetImageAlpha(img, 0f);
		while (elapsed < this.fadeDuration)
		{
			elapsed += Time.deltaTime;
			float num = Mathf.Clamp01(elapsed / this.fadeDuration);
			this.SetImageAlpha(img, num);
			yield return null;
		}
		this.SetImageAlpha(img, 1f);
		yield break;
	}

	// Token: 0x060008C9 RID: 2249 RVA: 0x0003E262 File Offset: 0x0003C462
	private IEnumerator FadeOut(Image img)
	{
		float elapsed = 0f;
		this.SetImageAlpha(img, 1f);
		while (elapsed < this.fadeDuration)
		{
			elapsed += Time.deltaTime;
			float num = Mathf.Clamp01(elapsed / this.fadeDuration);
			this.SetImageAlpha(img, 1f - num);
			yield return null;
		}
		this.SetImageAlpha(img, 0f);
		yield break;
	}

	// Token: 0x060008CA RID: 2250 RVA: 0x0003E278 File Offset: 0x0003C478
	private void SetImageAlpha(Image img, float alpha)
	{
		if (img == null)
		{
			return;
		}
		Color color = img.color;
		color.a = alpha;
		img.color = color;
	}

	// Token: 0x04000A0F RID: 2575
	[Header("Slide Data")]
	public List<UI_GamemodeSlideshowController.SlideHolder> slides;

	// Token: 0x04000A10 RID: 2576
	private List<UI_GamemodeSlideshowController.SlideHolder> currentSlides = new List<UI_GamemodeSlideshowController.SlideHolder>();

	// Token: 0x04000A11 RID: 2577
	[Header("UI References")]
	public Image imageForeground;

	// Token: 0x04000A12 RID: 2578
	public Image imageBackground;

	// Token: 0x04000A13 RID: 2579
	[Header("Timing")]
	public float displayTime = 2f;

	// Token: 0x04000A14 RID: 2580
	public float fadeDuration = 1f;

	// Token: 0x04000A15 RID: 2581
	private bool isForegroundActive = true;

	// Token: 0x020002B9 RID: 697
	[Serializable]
	public class SlideHolder
	{
		// Token: 0x06000EE0 RID: 3808 RVA: 0x00059741 File Offset: 0x00057941
		public bool IsUnlocked()
		{
			return this.unlockFlagID == "" || StatManager.saveData.GetFlagState(this.unlockFlagID);
		}

		// Token: 0x06000EE1 RID: 3809 RVA: 0x00059768 File Offset: 0x00057968
		public Sprite GetRandomSlideSprite()
		{
			if (this.slideImages == null || this.slideImages.Count == 0)
			{
				return null;
			}
			int num = Random.Range(0, this.slideImages.Count);
			return this.slideImages[num];
		}

		// Token: 0x04001190 RID: 4496
		public List<Sprite> slideImages;

		// Token: 0x04001191 RID: 4497
		public string unlockFlagID;
	}
}
