﻿using System;
using UnityEngine;

// Token: 0x02000185 RID: 389
public class UT_PlaySoundOnStart : MonoBehaviour
{
	// Token: 0x06000AB0 RID: 2736 RVA: 0x00046478 File Offset: 0x00044678
	private void Start()
	{
		this.PlaySound();
	}

	// Token: 0x06000AB1 RID: 2737 RVA: 0x00046480 File Offset: 0x00044680
	private void PlaySound()
	{
		if (this.hasRun && this.runOnce)
		{
			return;
		}
		this.hasRun = true;
		AudioManager.PlaySound(this.clip, base.transform.position, this.volume, this.pitch, this.spacialBlend, false, 1f, null);
	}

	// Token: 0x04000BAE RID: 2990
	public AudioClip clip;

	// Token: 0x04000BAF RID: 2991
	[Range(0f, 1f)]
	public float volume = 1f;

	// Token: 0x04000BB0 RID: 2992
	[Range(0f, 1f)]
	public float spacialBlend = 1f;

	// Token: 0x04000BB1 RID: 2993
	[Range(-3f, 3f)]
	public float pitch = 1f;

	// Token: 0x04000BB2 RID: 2994
	public bool runOnce = true;

	// Token: 0x04000BB3 RID: 2995
	private bool hasRun;
}
