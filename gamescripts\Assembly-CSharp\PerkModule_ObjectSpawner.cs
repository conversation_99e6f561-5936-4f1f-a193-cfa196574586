﻿using System;
using UnityEngine;

// Token: 0x0200004A RID: 74
public class PerkModule_ObjectSpawner : PerkModule
{
	// Token: 0x06000328 RID: 808 RVA: 0x0001FF44 File Offset: 0x0001E144
	public override void Update()
	{
		base.Update();
		if (!CL_GameManager.gMan.isPaused)
		{
			if (this.spawnTimer <= 0f)
			{
				this.spawnAttemptTimer -= Time.deltaTime;
				if (this.spawnAttemptTimer <= 0f)
				{
					if (this.Spawn())
					{
						this.spawnTimer = this.spawnFrequency;
						return;
					}
					this.spawnAttemptTimer = 2f;
					return;
				}
			}
			else
			{
				this.spawnTimer -= Time.deltaTime;
			}
		}
	}

	// Token: 0x06000329 RID: 809 RVA: 0x0001FFC4 File Offset: 0x0001E1C4
	public bool Spawn()
	{
		Vector3 vector = Vector3.up + Random.insideUnitSphere * 0.8f;
		Vector3 vector2 = ENT_Player.playerObject.transform.position;
		RaycastHit raycastHit;
		for (int i = 0; i < 3; i++)
		{
			Vector3 vector3;
			if (Physics.Raycast(vector2, vector, out raycastHit, this.checkStepDistance, this.hitMask))
			{
				vector3 = raycastHit.point;
			}
			else
			{
				vector3 = vector2 + vector * this.checkStepDistance;
			}
			vector2 = vector3;
			vector = Vector3.up + Random.insideUnitSphere * 1f;
		}
		if (!Physics.Raycast(vector2, Vector3.down, out raycastHit, 20f, this.hitMask))
		{
			return false;
		}
		PerkModule_ObjectSpawner.<>c__DisplayClass13_0 CS$<>8__locals1 = new PerkModule_ObjectSpawner.<>c__DisplayClass13_0();
		if (Vector3.Distance(raycastHit.point, ENT_Player.playerObject.transform.position) < this.minimumDistance)
		{
			return false;
		}
		Transform transform = null;
		if (WorldLoader.initialized)
		{
			transform = WorldLoader.instance.GetCurrentLevel().level.transform;
		}
		CS$<>8__locals1.spawnObject = Object.Instantiate<GameObject>(this.asset, raycastHit.point + raycastHit.normal * this.hitOffset, Quaternion.Euler(this.spawnRotation), transform);
		ENT_Player.playerObject.StartCoroutine(CS$<>8__locals1.<Spawn>g__ScaleUpObject|0());
		return true;
	}

	// Token: 0x04000452 RID: 1106
	public GameObject asset;

	// Token: 0x04000453 RID: 1107
	public float spawnFrequency = 60f;

	// Token: 0x04000454 RID: 1108
	private float spawnTimer;

	// Token: 0x04000455 RID: 1109
	private float spawnAttemptTimer;

	// Token: 0x04000456 RID: 1110
	private bool hasSpawned;

	// Token: 0x04000457 RID: 1111
	public int numberToSpawn;

	// Token: 0x04000458 RID: 1112
	private int currentSpawned;

	// Token: 0x04000459 RID: 1113
	public float minimumDistance = 5f;

	// Token: 0x0400045A RID: 1114
	public float checkStepDistance = 10f;

	// Token: 0x0400045B RID: 1115
	public LayerMask hitMask;

	// Token: 0x0400045C RID: 1116
	public float hitOffset = 0.5f;

	// Token: 0x0400045D RID: 1117
	public Vector3 spawnRotation;
}
