﻿using System;
using UnityEngine;

// Token: 0x02000063 RID: 99
[Serializable]
public class SessionEventModule_FXZone : SessionEventModule
{
	// Token: 0x060003C0 RID: 960 RVA: 0x00022FB3 File Offset: 0x000211B3
	public override void Update()
	{
		base.Update();
		this.blend = Mathf.Lerp(this.blend, this.blendTarget, 0.5f * Time.deltaTime);
		this.DataLerp();
	}

	// Token: 0x060003C1 RID: 961 RVA: 0x00022FE4 File Offset: 0x000211E4
	private void DataLerp()
	{
		FXManager.FXZoneStore fxzoneStore = new FXManager.FXZoneStore();
		fxzoneStore.blend = this.blend;
		fxzoneStore.priority = this.priority;
		fxzoneStore.data = this.fxData;
		FXManager.fxZones.Enqueue(fxzoneStore, this.priority);
	}

	// Token: 0x060003C2 RID: 962 RVA: 0x0002302C File Offset: 0x0002122C
	public override void SendMessage(string m)
	{
		base.SendMessage(m);
		if (m == "fadein")
		{
			this.blendTarget = 1f;
			return;
		}
		if (m == "fadeout")
		{
			this.blendTarget = 0f;
		}
	}

	// Token: 0x0400051B RID: 1307
	public FXManager.FXData fxData;

	// Token: 0x0400051C RID: 1308
	public float blendTarget;

	// Token: 0x0400051D RID: 1309
	private float blend;

	// Token: 0x0400051E RID: 1310
	public byte priority = 10;
}
