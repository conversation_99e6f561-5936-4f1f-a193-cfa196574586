﻿using System;
using System.Collections.Generic;

// Token: 0x02000067 RID: 103
[Serializable]
public class SessionEventModule_PrintToConsole : SessionEventModule
{
	// Token: 0x060003D2 RID: 978 RVA: 0x000236E1 File Offset: 0x000218E1
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		this.Activate();
	}

	// Token: 0x060003D3 RID: 979 RVA: 0x000236F0 File Offset: 0x000218F0
	public override void Activate()
	{
		if (this.consoleMessages.Count > 0)
		{
			foreach (string text in this.consoleMessages)
			{
				CommandConsole.Log(text, false);
			}
		}
		if (this.consoleCommand != "")
		{
			CommandConsole.instance.ExecuteCommand(this.consoleCommand);
		}
		base.Activate();
	}

	// Token: 0x04000530 RID: 1328
	public List<string> consoleMessages;

	// Token: 0x04000531 RID: 1329
	public string consoleCommand;
}
