﻿using System;
using UnityEngine;

// Token: 0x02000105 RID: 261
public class UI_HeldItem : MonoBehaviour
{
	// Token: 0x060007F8 RID: 2040 RVA: 0x0003AFE3 File Offset: 0x000391E3
	private void Start()
	{
	}

	// Token: 0x060007F9 RID: 2041 RVA: 0x0003AFE8 File Offset: 0x000391E8
	private void OnEnable()
	{
		Vector3 vector = OS_Manager.mousePosition;
		vector.x = Mathf.Round(vector.x / 2f) * 2f;
		vector.y = Mathf.Round(vector.y / 2f) * 2f;
		base.transform.position = vector;
	}

	// Token: 0x060007FA RID: 2042 RVA: 0x0003B048 File Offset: 0x00039248
	private void LateUpdate()
	{
		Vector3 vector = Vector3.Lerp(base.transform.position, OS_Manager.mousePosition, Time.deltaTime * 10f);
		base.transform.position = vector;
	}
}
