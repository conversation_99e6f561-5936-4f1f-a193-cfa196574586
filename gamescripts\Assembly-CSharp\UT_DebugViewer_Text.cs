﻿using System;
using Drawing;
using UnityEngine;

// Token: 0x020001C0 RID: 448
public class UT_DebugViewer_Text : MonoBehaviour
{
	// Token: 0x06000B84 RID: 2948 RVA: 0x00049A78 File Offset: 0x00047C78
	private void Update()
	{
		if (CL_UIManager.debug || this.alwaysShow)
		{
			CL_DebugView.draw.Label2D(base.transform.position + this.offset, this.text, this.size, this.alignToCenter ? LabelAlignment.Center : LabelAlignment.BottomLeft, this.color);
		}
	}

	// Token: 0x06000B85 RID: 2949 RVA: 0x00049ADF File Offset: 0x00047CDF
	public void SetText(string t)
	{
		this.text = t;
	}

	// Token: 0x06000B86 RID: 2950 RVA: 0x00049AE8 File Offset: 0x00047CE8
	public void SetColor(string c)
	{
		ColorUtility.TryParseHtmlString(c, out this.color);
	}

	// Token: 0x06000B87 RID: 2951 RVA: 0x00049AF7 File Offset: 0x00047CF7
	public void SetColor(Color c)
	{
		this.color = c;
	}

	// Token: 0x06000B88 RID: 2952 RVA: 0x00049B00 File Offset: 0x00047D00
	public void SetSize(float s)
	{
		this.size = s;
	}

	// Token: 0x04000C8C RID: 3212
	public string text = "Debug Text";

	// Token: 0x04000C8D RID: 3213
	public float size = 14f;

	// Token: 0x04000C8E RID: 3214
	public Color color = Color.white;

	// Token: 0x04000C8F RID: 3215
	public Vector3 offset;

	// Token: 0x04000C90 RID: 3216
	public bool alignToCenter = true;

	// Token: 0x04000C91 RID: 3217
	public bool alwaysShow;
}
