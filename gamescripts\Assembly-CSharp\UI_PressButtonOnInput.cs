﻿using System;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000134 RID: 308
public class UI_PressButtonOnInput : MonoBehaviour
{
	// Token: 0x06000902 RID: 2306 RVA: 0x0003F03A File Offset: 0x0003D23A
	private void Start()
	{
		this.button = base.GetComponent<Button>();
	}

	// Token: 0x06000903 RID: 2307 RVA: 0x0003F048 File Offset: 0x0003D248
	private void Update()
	{
		if (this.button.IsInteractable() && InputManager.GetButton(this.input).Down)
		{
			this.button.onClick.Invoke();
		}
		if (this.disableButtonIfGamepad && InputManager.IsGamepad())
		{
			this.button.interactable = false;
			return;
		}
		this.button.interactable = true;
	}

	// Token: 0x04000A4D RID: 2637
	public string input;

	// Token: 0x04000A4E RID: 2638
	private Button button;

	// Token: 0x04000A4F RID: 2639
	public bool disableButtonIfGamepad;
}
