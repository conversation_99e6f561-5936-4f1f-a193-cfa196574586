﻿using System;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200002B RID: 43
public class FX_ShaderVariableController : MonoBehaviour
{
	// Token: 0x06000190 RID: 400 RVA: 0x0000BE18 File Offset: 0x0000A018
	private void Start()
	{
		this.rendererComponent = base.GetComponent<RawImage>();
	}

	// Token: 0x06000191 RID: 401 RVA: 0x0000BE26 File Offset: 0x0000A026
	[ExecuteInEditMode]
	private void Update()
	{
		this.rendererComponent.material.SetVector("_ObjectWorldPosition", base.transform.position);
	}

	// Token: 0x04000175 RID: 373
	private RawImage rendererComponent;
}
