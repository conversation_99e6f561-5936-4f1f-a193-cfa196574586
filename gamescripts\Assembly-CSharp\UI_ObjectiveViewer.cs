﻿using System;
using System.Collections.Generic;
using DG.Tweening;
using TMPro;
using UnityEngine;

// Token: 0x02000147 RID: 327
public class UI_ObjectiveViewer : MonoBehaviour
{
	// Token: 0x06000966 RID: 2406 RVA: 0x00040A21 File Offset: 0x0003EC21
	private void Awake()
	{
		UI_ObjectiveViewer.instance = this;
		UI_ObjectiveViewer.objectiveDict = new Dictionary<string, UI_ObjectiveViewer.Objective>();
		base.gameObject.SetActive(false);
	}

	// Token: 0x06000967 RID: 2407 RVA: 0x00040A3F File Offset: 0x0003EC3F
	public void ShowViewer()
	{
		base.gameObject.SetActive(true);
	}

	// Token: 0x06000968 RID: 2408 RVA: 0x00040A4D File Offset: 0x0003EC4D
	public void HideViewer()
	{
		base.gameObject.SetActive(false);
	}

	// Token: 0x06000969 RID: 2409 RVA: 0x00040A5C File Offset: 0x0003EC5C
	public static void CreateOrUpdateObjective(string id, string title, string desc, bool punch = false)
	{
		if (UI_ObjectiveViewer.instance == null)
		{
			return;
		}
		if (!UI_ObjectiveViewer.objectiveDict.ContainsKey(id))
		{
			UI_ObjectiveViewer.Objective objective = new UI_ObjectiveViewer.Objective();
			objective.id = id;
			objective.title = title;
			objective.desc = desc;
			objective.assetObject = Object.Instantiate<GameObject>(UI_ObjectiveViewer.instance.objectiveAsset, UI_ObjectiveViewer.instance.objectiveParent);
			UI_ObjectiveViewer.objectiveDict.Add(id, objective);
		}
		string text = title + "\n";
		UI_ObjectiveViewer.objectiveDict[id].assetObject.GetComponent<TMP_Text>().text = text + desc;
		UI_ObjectiveViewer.instance.ShowViewer();
		if (punch)
		{
			UI_ObjectiveViewer.objectiveDict[id].PunchAssetObject();
		}
		UI_ObjectiveViewer.instance.GetComponent<RectTransform>().sizeDelta = UI_ObjectiveViewer.instance.objectiveParent.GetComponent<RectTransform>().sizeDelta;
	}

	// Token: 0x0600096A RID: 2410 RVA: 0x00040B37 File Offset: 0x0003ED37
	public static void RemoveObjective(string id)
	{
		if (UI_ObjectiveViewer.instance == null)
		{
			return;
		}
		if (UI_ObjectiveViewer.objectiveDict.ContainsKey(id))
		{
			Object.Destroy(UI_ObjectiveViewer.objectiveDict[id].assetObject);
			UI_ObjectiveViewer.objectiveDict.Remove(id);
		}
	}

	// Token: 0x0600096B RID: 2411 RVA: 0x00040B75 File Offset: 0x0003ED75
	public static void PlayEarnSound()
	{
		if (UI_ObjectiveViewer.instance == null)
		{
			return;
		}
		AudioManager.PlayUISound(UI_ObjectiveViewer.instance.objectiveEarnSound, 1f, 1f);
	}

	// Token: 0x0600096C RID: 2412 RVA: 0x00040BA0 File Offset: 0x0003EDA0
	public static void SetTitle(string s)
	{
		string text = "Setting Title: ";
		string text2 = " :: ";
		UI_ObjectiveViewer ui_ObjectiveViewer = UI_ObjectiveViewer.instance;
		Debug.Log(text + s + text2 + ((ui_ObjectiveViewer != null) ? ui_ObjectiveViewer.ToString() : null));
		if (UI_ObjectiveViewer.instance == null)
		{
			return;
		}
		UI_ObjectiveViewer.instance.objectiveViewerTitle.text = s;
	}

	// Token: 0x04000AB0 RID: 2736
	private static Dictionary<string, UI_ObjectiveViewer.Objective> objectiveDict;

	// Token: 0x04000AB1 RID: 2737
	public Transform objectiveParent;

	// Token: 0x04000AB2 RID: 2738
	public GameObject objectiveAsset;

	// Token: 0x04000AB3 RID: 2739
	private static UI_ObjectiveViewer instance;

	// Token: 0x04000AB4 RID: 2740
	public AudioClip objectiveEarnSound;

	// Token: 0x04000AB5 RID: 2741
	public TMP_Text objectiveViewerTitle;

	// Token: 0x020002C9 RID: 713
	public class Objective
	{
		// Token: 0x06000F1D RID: 3869 RVA: 0x0005A0D8 File Offset: 0x000582D8
		public void SpawnAssetObject()
		{
			this.assetObject = Object.Instantiate<GameObject>(UI_ObjectiveViewer.instance.objectiveAsset, UI_ObjectiveViewer.instance.objectiveParent);
			this.assetObject.transform.DOPunchScale(Vector3.one * 0.1f, 0.5f, 10, 1f);
		}

		// Token: 0x06000F1E RID: 3870 RVA: 0x0005A130 File Offset: 0x00058330
		public void PunchAssetObject()
		{
			DOTween.Complete(this.assetObject.transform, false);
			this.assetObject.transform.DOPunchScale(Vector3.one * 0.1f, 0.5f, 10, 1f);
		}

		// Token: 0x040011D1 RID: 4561
		public string id;

		// Token: 0x040011D2 RID: 4562
		public string title;

		// Token: 0x040011D3 RID: 4563
		public string desc;

		// Token: 0x040011D4 RID: 4564
		public GameObject assetObject;
	}
}
