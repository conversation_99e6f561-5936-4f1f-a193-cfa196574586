﻿using System;
using UnityEngine;

// Token: 0x02000002 RID: 2
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
public class DropdownAttribute : PropertyAttribute
{
	// Token: 0x17000001 RID: 1
	// (get) Token: 0x06000001 RID: 1 RVA: 0x00002050 File Offset: 0x00000250
	// (set) Token: 0x06000002 RID: 2 RVA: 0x00002058 File Offset: 0x00000258
	public DropdownAttribute.MethodLocation Location { get; private set; }

	// Token: 0x17000002 RID: 2
	// (get) Token: 0x06000003 RID: 3 RVA: 0x00002061 File Offset: 0x00000261
	// (set) Token: 0x06000004 RID: 4 RVA: 0x00002069 File Offset: 0x00000269
	public string MethodName { get; private set; }

	// Token: 0x17000003 RID: 3
	// (get) Token: 0x06000005 RID: 5 RVA: 0x00002072 File Offset: 0x00000272
	// (set) Token: 0x06000006 RID: 6 RVA: 0x0000207A File Offset: 0x0000027A
	public Type MethodOwnerType { get; private set; }

	// Token: 0x06000007 RID: 7 RVA: 0x00002083 File Offset: 0x00000283
	public DropdownAttribute(string methodName)
	{
		this.Location = DropdownAttribute.MethodLocation.PropertyClass;
		this.MethodName = methodName;
	}

	// Token: 0x06000008 RID: 8 RVA: 0x00002099 File Offset: 0x00000299
	public DropdownAttribute(Type methodOwner, string methodName)
	{
		this.Location = DropdownAttribute.MethodLocation.StaticClass;
		this.MethodOwnerType = methodOwner;
		this.MethodName = methodName;
	}

	// Token: 0x020001E3 RID: 483
	public enum MethodLocation
	{
		// Token: 0x04000D30 RID: 3376
		PropertyClass,
		// Token: 0x04000D31 RID: 3377
		StaticClass
	}
}
