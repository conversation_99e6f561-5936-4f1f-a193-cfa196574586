﻿using System;
using UnityEngine;

// Token: 0x02000087 RID: 135
public class CL_Handhold_Rope : CL_Handhold
{
	// Token: 0x0600048E RID: 1166 RVA: 0x000281F3 File Offset: 0x000263F3
	public void InitializeRope(ENV_Rope r, ENV_Rope_Node n)
	{
		this.rope = r;
		this.node = n;
	}

	// Token: 0x0600048F RID: 1167 RVA: 0x00028204 File Offset: 0x00026404
	private void FixedUpdate()
	{
		if (!base.GetHolding())
		{
			return;
		}
		Vector3 vector = Vector3.zero;
		bool flag = false;
		if ((InputManager.GetButton("Sprint").Pressed && !SettingsManager.settings.inverseRopeSwing) || (SettingsManager.settings.inverseRopeSwing && !InputManager.GetButton("Sprint").Pressed))
		{
			flag = true;
		}
		if (SettingsManager.settings.directionalSwing)
		{
			flag = false;
			if (Mathf.Abs(Vector3.Dot(base.GetPlayer().cam.transform.forward, Vector3.up)) < 0.5f)
			{
				flag = true;
			}
		}
		if (flag)
		{
			this.swinging = true;
			vector += base.GetPlayer().transform.forward * InputManager.GetVector("Move").vector.y * this.swingSpeed * 2.5f;
			vector += base.GetPlayer().transform.right * InputManager.GetVector("Move").vector.x * this.swingSpeed * 2.5f;
			foreach (ENT_Player.Hand hand in this.hands)
			{
				hand.dragMult = this.swingDrag;
			}
			this.rope.SetCollision(false);
		}
		else
		{
			this.swinging = false;
			foreach (ENT_Player.Hand hand2 in this.hands)
			{
				hand2.dragMult = this.dragMult;
			}
			this.rope.SetCollision(true);
		}
		this.CalculateFacingValue(base.transform.position, vector.normalized, this.rope.transform.position);
		this.rope.AddVelocity(vector);
	}

	// Token: 0x06000490 RID: 1168 RVA: 0x00028414 File Offset: 0x00026614
	private float CalculateFacingValue(Vector3 sourcePosition, Vector3 sourceForward, Vector3 targetPosition)
	{
		sourcePosition.y = 0f;
		targetPosition.y = 0f;
		sourceForward.y = 0f;
		Vector3 normalized = (targetPosition - sourcePosition).normalized;
		return Mathf.Clamp01(Vector3.Dot(sourceForward.normalized, normalized));
	}

	// Token: 0x06000491 RID: 1169 RVA: 0x00028468 File Offset: 0x00026668
	public override void StopInteract(ENT_Player p, ENT_Player.Hand dropHand, string s = "")
	{
		if (s == "jump")
		{
			p.AddForce(this.rope.GetTargetRigidbody().velocity * 0.02f);
			this.rope.DropRope();
		}
		base.StopInteract(p, dropHand, s);
	}

	// Token: 0x06000492 RID: 1170 RVA: 0x000284B8 File Offset: 0x000266B8
	public override void Interact(ENT_Player p, ENT_Player.Hand h)
	{
		Vector3 vector = this.AddVectorsWithLimit(h.GetPlayer().cCon.velocity * this.rope.grabVelocityMult, this.rope.GetTargetRigidbody().velocity);
		Debug.Log("Is Rope in Use: " + base.GetInUse().ToString());
		if (!base.GetInUse())
		{
			this.rope.AddVelocity(vector * this.rope.grabVelocityTransfer);
		}
		this.rope.GrabRope();
		base.Interact(p, h);
	}

	// Token: 0x06000493 RID: 1171 RVA: 0x00028550 File Offset: 0x00026750
	private Vector3 AddVectorsWithLimit(Vector3 vector1, Vector3 vector2)
	{
		Vector3 normalized = vector1.normalized;
		float magnitude = vector1.magnitude;
		float num = Vector3.Dot(vector2, normalized);
		float num2 = magnitude;
		float num3 = num + num2;
		if (Mathf.Sign(num) == Mathf.Sign(num2) && num != 0f)
		{
			if (Mathf.Abs(num) >= magnitude)
			{
				num3 = num;
			}
			else if (Mathf.Abs(num3) > magnitude)
			{
				num3 = magnitude * Mathf.Sign(num3);
			}
		}
		else if (Mathf.Abs(num3) > magnitude)
		{
			num3 = -magnitude * Mathf.Sign(num3);
		}
		return vector2 - normalized * num + normalized * num3;
	}

	// Token: 0x06000494 RID: 1172 RVA: 0x000285E7 File Offset: 0x000267E7
	public override float GetClimbMult()
	{
		if (this.swinging)
		{
			return this.swingClimb;
		}
		return this.climbMult;
	}

	// Token: 0x04000614 RID: 1556
	private ENV_Rope rope;

	// Token: 0x04000615 RID: 1557
	private ENV_Rope_Node node;

	// Token: 0x04000616 RID: 1558
	public float swingSpeed = 4f;

	// Token: 0x04000617 RID: 1559
	public float swingDrag = 20f;

	// Token: 0x04000618 RID: 1560
	public float swingClimb;

	// Token: 0x04000619 RID: 1561
	private bool swinging;
}
