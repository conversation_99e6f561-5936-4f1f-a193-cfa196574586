﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020000FF RID: 255
public class OS_SoundEffects : MonoBehaviour
{
	// Token: 0x060007D1 RID: 2001 RVA: 0x0003A8FA File Offset: 0x00038AFA
	private void OnEnable()
	{
		OS_SoundEffects.PlayMusicTrack = (Action<AudioClip>)Delegate.Combine(OS_SoundEffects.PlayMusicTrack, new Action<AudioClip>(this.PlayMusic));
	}

	// Token: 0x060007D2 RID: 2002 RVA: 0x0003A91C File Offset: 0x00038B1C
	private void OnDisable()
	{
		OS_SoundEffects.PlayMusicTrack = (Action<AudioClip>)Delegate.Remove(OS_SoundEffects.PlayMusicTrack, new Action<AudioClip>(this.PlayMusic));
	}

	// Token: 0x060007D3 RID: 2003 RVA: 0x0003A93E File Offset: 0x00038B3E
	private void Start()
	{
		this.aud = base.GetComponent<AudioSource>();
	}

	// Token: 0x060007D4 RID: 2004 RVA: 0x0003A94C File Offset: 0x00038B4C
	private void Update()
	{
	}

	// Token: 0x060007D5 RID: 2005 RVA: 0x0003A94E File Offset: 0x00038B4E
	public void PlayMusic(AudioClip clip)
	{
		this.aud.clip = clip;
		this.aud.Play();
	}

	// Token: 0x04000949 RID: 2377
	public List<AudioClip> clickSounds;

	// Token: 0x0400094A RID: 2378
	public List<AudioClip> dragStartSounds;

	// Token: 0x0400094B RID: 2379
	public List<AudioClip> dragEndSounds;

	// Token: 0x0400094C RID: 2380
	public List<AudioClip> tapSounds;

	// Token: 0x0400094D RID: 2381
	public List<AudioClip> errorSounds;

	// Token: 0x0400094E RID: 2382
	public List<AudioClip> openSounds;

	// Token: 0x0400094F RID: 2383
	public AudioSource aud;

	// Token: 0x04000950 RID: 2384
	public static Action<OS_SoundEffects.SoundType> PlaySystemSound;

	// Token: 0x04000951 RID: 2385
	public static Action<AudioClip> PlayMusicTrack;

	// Token: 0x020002A7 RID: 679
	public enum SoundType
	{
		// Token: 0x040010FE RID: 4350
		click,
		// Token: 0x040010FF RID: 4351
		tap,
		// Token: 0x04001100 RID: 4352
		dragStart,
		// Token: 0x04001101 RID: 4353
		dragEnd,
		// Token: 0x04001102 RID: 4354
		error,
		// Token: 0x04001103 RID: 4355
		open
	}
}
