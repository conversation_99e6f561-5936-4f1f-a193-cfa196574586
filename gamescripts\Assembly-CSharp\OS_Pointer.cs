﻿using System;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000FD RID: 253
public class OS_Pointer : MonoBehaviour
{
	// Token: 0x060007C7 RID: 1991 RVA: 0x0003A6D1 File Offset: 0x000388D1
	public void Initialize(OS_Manager o)
	{
		this.os = o;
		this.rectTransform = base.GetComponent<RectTransform>();
		this.cursorImage = base.GetComponent<Image>();
	}

	// Token: 0x060007C8 RID: 1992 RVA: 0x0003A6F4 File Offset: 0x000388F4
	private void Update()
	{
		if (!this.os.canControl)
		{
			return;
		}
		Vector2 anchoredPosition = this.rectTransform.anchoredPosition;
		anchoredPosition.x = Mathf.Clamp(anchoredPosition.x, this.minPosition.x, this.maxPosition.x);
		anchoredPosition.y = Mathf.Clamp(anchoredPosition.y, this.minPosition.y, this.maxPosition.y);
		this.rectTransform.anchoredPosition = anchoredPosition;
	}

	// Token: 0x060007C9 RID: 1993 RVA: 0x0003A777 File Offset: 0x00038977
	public void SetSprite(Sprite s)
	{
		this.cursorImage.sprite = s;
	}

	// Token: 0x060007CA RID: 1994 RVA: 0x0003A785 File Offset: 0x00038985
	public Image GetImage()
	{
		return this.cursorImage;
	}

	// Token: 0x0400093E RID: 2366
	private OS_Manager os;

	// Token: 0x0400093F RID: 2367
	public Vector2 maxPosition;

	// Token: 0x04000940 RID: 2368
	public Vector2 minPosition;

	// Token: 0x04000941 RID: 2369
	private RectTransform rectTransform;

	// Token: 0x04000942 RID: 2370
	private Image cursorImage;
}
