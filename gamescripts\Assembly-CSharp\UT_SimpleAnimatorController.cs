﻿using System;
using UnityEngine;

// Token: 0x02000192 RID: 402
public class UT_SimpleAnimatorController : MonoBehaviour
{
	// Token: 0x06000AD8 RID: 2776 RVA: 0x00046C87 File Offset: 0x00044E87
	private void Start()
	{
		this.animator = base.GetComponent<Animator>();
		if (this.animator == null)
		{
			return;
		}
		this.animator.keepAnimatorStateOnDisable = true;
	}

	// Token: 0x06000AD9 RID: 2777 RVA: 0x00046CB0 File Offset: 0x00044EB0
	public void SetAnimatorBool(string s)
	{
		if (this.animator == null)
		{
			return;
		}
		string[] array = s.Split(":", StringSplitOptions.None);
		this.animator.SetBool(array[0], bool.Parse(array[1]));
	}

	// Token: 0x04000BD8 RID: 3032
	private Animator animator;
}
