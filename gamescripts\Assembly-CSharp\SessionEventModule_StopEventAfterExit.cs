﻿using System;
using UnityEngine;

// Token: 0x0200006B RID: 107
[Serializable]
public class SessionEventModule_StopEventAfterExit : SessionEventModule
{
	// Token: 0x060003DD RID: 989 RVA: 0x00023864 File Offset: 0x00021A64
	public override void Update()
	{
		base.Update();
		switch (this.exitEvent)
		{
		case SessionEventModule_StopEventAfterExit.ExitEvent.afterRegion:
			if (this.sessionEvent.startRegion != null && CL_EventManager.currentRegion != null && this.sessionEvent.startRegion.regionName != CL_EventManager.currentRegion.regionName)
			{
				this.sessionEvent.StopEvent();
				return;
			}
			break;
		case SessionEventModule_StopEventAfterExit.ExitEvent.afterSubregion:
			if (this.sessionEvent.startSubregion == null)
			{
				if (this.sessionEvent.startSubregion != CL_EventManager.currentSubregion)
				{
					this.sessionEvent.StopEvent();
					return;
				}
			}
			else if (this.sessionEvent.startSubregion.subregionName != CL_EventManager.currentSubregion.subregionName)
			{
				this.sessionEvent.StopEvent();
				Debug.Log(string.Format("Ending Event {0} - Moved from {1} to {2}", this.sessionEvent, this.sessionEvent.startSubregion.subregionName, CL_EventManager.currentSubregion.subregionName));
				return;
			}
			break;
		case SessionEventModule_StopEventAfterExit.ExitEvent.afterLevel:
			if (this.sessionEvent.startLevel != CL_EventManager.currentLevel)
			{
				this.sessionEvent.StopEvent();
			}
			break;
		default:
			return;
		}
	}

	// Token: 0x04000538 RID: 1336
	public SessionEventModule_StopEventAfterExit.ExitEvent exitEvent;

	// Token: 0x0200024E RID: 590
	public enum ExitEvent
	{
		// Token: 0x04000F48 RID: 3912
		afterRegion,
		// Token: 0x04000F49 RID: 3913
		afterSubregion,
		// Token: 0x04000F4A RID: 3914
		afterLevel
	}
}
