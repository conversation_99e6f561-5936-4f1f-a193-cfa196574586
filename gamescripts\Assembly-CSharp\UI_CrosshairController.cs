﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000123 RID: 291
public class UI_CrosshairController : MonoBehaviour
{
	// Token: 0x060008A8 RID: 2216 RVA: 0x0003DAAC File Offset: 0x0003BCAC
	private void OnEnable()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.Refresh));
		this.Refresh();
	}

	// Token: 0x060008A9 RID: 2217 RVA: 0x0003DAD4 File Offset: 0x0003BCD4
	private void OnDisable()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Remove(SettingsManager.SettingsRefreshUpdate, new Action(this.Refresh));
	}

	// Token: 0x060008AA RID: 2218 RVA: 0x0003DAF8 File Offset: 0x0003BCF8
	private void Refresh()
	{
		this.handsRoot.localScale = Vector3.one * SettingsManager.settings.handIconScale;
		this.crosshair.localScale = Vector3.one * SettingsManager.settings.crosshairScale;
		this.crosshairRenderer.sprite = this.crosshairSprites[SettingsManager.settings.crosshair];
	}

	// Token: 0x040009F5 RID: 2549
	public Transform crosshair;

	// Token: 0x040009F6 RID: 2550
	public Transform handsRoot;

	// Token: 0x040009F7 RID: 2551
	public Image crosshairRenderer;

	// Token: 0x040009F8 RID: 2552
	public List<Sprite> crosshairSprites;
}
