﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x020000FC RID: 252
public class OS_Manager : MonoBehaviour
{
	// Token: 0x060007B0 RID: 1968 RVA: 0x00039FBA File Offset: 0x000381BA
	private void OnEnable()
	{
		OS_Manager.soundPlayer = this.audioClipHandler;
	}

	// Token: 0x060007B1 RID: 1969 RVA: 0x00039FC7 File Offset: 0x000381C7
	private void OnDisable()
	{
	}

	// Token: 0x060007B2 RID: 1970 RVA: 0x00039FC9 File Offset: 0x000381C9
	private void OnDestroy()
	{
		if (OS_Manager.activeComputer == this)
		{
			OS_Manager.activeComputer = null;
		}
	}

	// Token: 0x060007B3 RID: 1971 RVA: 0x00039FE0 File Offset: 0x000381E0
	private void Awake()
	{
		this.cursor.Initialize(this);
		this.diskController = new OS_Manager.DiskController();
		this.diskController.Initialize(this);
		this.SetCursorImage(OS_Manager.cursorType.pointer);
		this.apps = new List<OS_Manager.Application>();
		this.appDict = new Dictionary<string, OS_Manager.Application>();
		this.filesystem = base.GetComponent<OS_Filesystem>();
		this.osRoot.SetActive(false);
		this.computerEventSystem = this.customInputModule.GetComponent<EventSystem>();
	}

	// Token: 0x060007B4 RID: 1972 RVA: 0x0003A058 File Offset: 0x00038258
	private void Update()
	{
		if (!this.hasInitialized)
		{
			return;
		}
		if (!OS_Manager.inUse || !this.canControl)
		{
			return;
		}
		this.SetDesktopInteractable();
		this.CheckAppSelectability();
		Cursor.visible = false;
		if (InputManager.IsGamepad())
		{
			OS_Manager.mousePosition = this.cursor.GetImage().rectTransform.anchoredPosition;
			Vector3 vector = new Vector3(Mathf.Sin(Time.unscaledTime), Mathf.Cos(Time.unscaledTime * 1.23f), 0f);
			if (OS_Manager.mouseEnabled && this.computerEventSystem.currentSelectedGameObject != null)
			{
				this.cursor.transform.position = Vector3.Lerp(this.cursor.transform.position, this.computerEventSystem.currentSelectedGameObject.transform.position + vector * 5f, Time.deltaTime * 10f);
			}
			this.lastMousePosition = OS_Manager.mousePosition;
			OS_Manager.mouseRealPosition = this.cursor.transform.position;
		}
		else
		{
			OS_Manager.mousePosition = this.cursor.GetImage().rectTransform.anchoredPosition;
			if (OS_Manager.mouseEnabled)
			{
				this.cursor.transform.position += new Vector3(Input.GetAxis("Mouse X"), Input.GetAxis("Mouse Y"), 0f) * 5f;
			}
			this.lastMousePosition = OS_Manager.mousePosition;
			OS_Manager.mouseRealPosition = this.cursor.transform.position;
		}
		if (this.canControl)
		{
			for (int i = 0; i < ENT_Player.playerObject.hands.Length; i++)
			{
				ENT_Player.playerObject.hands[i].MoveTo(this.worldInterface.handPositions[i].position);
				ENT_Player.playerObject.hands[i].handModel.localScale = Vector3.Lerp(ENT_Player.playerObject.hands[i].handModel.localScale, Vector3.one * 0.8f, Time.deltaTime * 5f);
				ENT_Player.playerObject.hands[i].SetSprite(ENT_Player.playerObject.hands[i].grabSprite);
			}
		}
	}

	// Token: 0x060007B5 RID: 1973 RVA: 0x0003A2B4 File Offset: 0x000384B4
	public OS_Window CreateAppWindow(OS_Window appAsset, string id)
	{
		if (!this.appDict.ContainsKey(id))
		{
			OS_Window component = Object.Instantiate<OS_Window>(appAsset, this.appParent.position, Quaternion.identity, this.appParent).GetComponent<OS_Window>();
			component.Initialize(this);
			OS_Manager.Application application = new OS_Manager.Application();
			application.id = id;
			application.app = component;
			this.apps.Add(application);
			this.appDict.Add(id, application);
			component.SetID(id);
			return component;
		}
		return this.appDict[id].app;
	}

	// Token: 0x060007B6 RID: 1974 RVA: 0x0003A33F File Offset: 0x0003853F
	public void CloseAppWindow(OS_Window app, string id)
	{
		app.gameObject.SetActive(false);
	}

	// Token: 0x060007B7 RID: 1975 RVA: 0x0003A34D File Offset: 0x0003854D
	public void QuitAppWindow(OS_Window app, string id)
	{
		if (this.appDict.ContainsKey(id))
		{
			this.apps.Remove(this.appDict[id]);
			this.appDict.Remove(id);
		}
		Object.Destroy(app.gameObject);
	}

	// Token: 0x060007B8 RID: 1976 RVA: 0x0003A390 File Offset: 0x00038590
	public void SetDesktopInteractable()
	{
		bool flag = false;
		bool flag2 = false;
		if (this.messageManager.showingMessage)
		{
			flag = true;
		}
		if (this.apps.Count > 0)
		{
			using (List<OS_Manager.Application>.Enumerator enumerator = this.apps.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					if (enumerator.Current.app.gameObject.activeInHierarchy)
					{
						if (InputManager.IsGamepad())
						{
							flag = true;
						}
						flag2 = true;
						break;
					}
				}
			}
		}
		if (this.exitButton != null)
		{
			this.exitButton.interactable = !flag;
			this.exitButton.GetComponent<UI_CloseButtonOnBack>().enabled = !flag2;
		}
		this.filesystem.desktop.GetComponent<CanvasGroup>().interactable = !flag;
	}

	// Token: 0x060007B9 RID: 1977 RVA: 0x0003A464 File Offset: 0x00038664
	private void CheckAppSelectability()
	{
		if (this.appParent.childCount == 0)
		{
			return;
		}
		CanvasGroup canvasGroup = null;
		for (int i = 0; i < this.appParent.childCount; i++)
		{
			CanvasGroup component = this.appParent.GetChild(i).GetComponent<CanvasGroup>();
			if (component != null)
			{
				if (this.messageManager.showingMessage)
				{
					component.interactable = false;
				}
				else
				{
					component.interactable = false;
					if (component.gameObject.activeInHierarchy)
					{
						canvasGroup = component;
					}
				}
			}
		}
		if (canvasGroup != null && !this.messageManager.showingMessage)
		{
			canvasGroup.interactable = true;
		}
	}

	// Token: 0x060007BA RID: 1978 RVA: 0x0003A4FB File Offset: 0x000386FB
	public void SetCursorImage(OS_Manager.cursorType t)
	{
		switch (t)
		{
		case OS_Manager.cursorType.pointer:
			this.cursor.SetSprite(this.cursorTexture);
			return;
		case OS_Manager.cursorType.finger:
		case OS_Manager.cursorType.hand:
		case OS_Manager.cursorType.magnifyingGlass:
			break;
		case OS_Manager.cursorType.crosshairs:
			this.cursor.SetSprite(this.crosshairTexture);
			break;
		default:
			return;
		}
	}

	// Token: 0x060007BB RID: 1979 RVA: 0x0003A53B File Offset: 0x0003873B
	public void Activate()
	{
		base.StartCoroutine(this.ActivateComputer());
	}

	// Token: 0x060007BC RID: 1980 RVA: 0x0003A54A File Offset: 0x0003874A
	public void SetInteractable(bool b)
	{
		this.computerGroup.interactable = b;
	}

	// Token: 0x060007BD RID: 1981 RVA: 0x0003A558 File Offset: 0x00038758
	public void DeactivateEventSystem()
	{
		this.computerEventSystem.enabled = false;
	}

	// Token: 0x060007BE RID: 1982 RVA: 0x0003A566 File Offset: 0x00038766
	public void ActivateEventSystem()
	{
		this.computerEventSystem.enabled = true;
	}

	// Token: 0x060007BF RID: 1983 RVA: 0x0003A574 File Offset: 0x00038774
	private IEnumerator ActivateComputer()
	{
		if (!this.hasInitialized)
		{
			base.GetComponent<OS_Filesystem>().Initialize(this);
			this.hasInitialized = true;
		}
		OS_Manager.activeComputer = this;
		ENT_Player.playerObject.LetGo();
		yield return null;
		this.canControl = true;
		OS_Manager.inUse = true;
		OS_Manager.mouseEnabled = true;
		this.customInputModule.SetActive(true);
		this.computerEventSystem.enabled = true;
		CL_UIManager.instance.standardInputModule.SetActive(false);
		base.transform.GetChild(0).gameObject.SetActive(true);
		this.activateEvent.Invoke();
		CL_UIManager.instance.SetCrosshairVisibility(false);
		this.osRoot.SetActive(true);
		this.osRoot.transform.rotation = Quaternion.identity;
		this.cameraControl.TakeoverCamera();
		this.SetInteractable(true);
		while (Vector3.Distance(ENT_Player.playerObject.transform.position, this.worldInterface.playerPosition.position) > 0.1f)
		{
			ENT_Player.playerObject.transform.position = Vector3.Lerp(ENT_Player.playerObject.transform.position, this.worldInterface.playerPosition.position, Time.deltaTime * 5f);
			Quaternion quaternion = Quaternion.Euler(0f, this.worldInterface.playerPosition.rotation.eulerAngles.y, 0f);
			ENT_Player.playerObject.transform.rotation = Quaternion.Lerp(ENT_Player.playerObject.transform.rotation, quaternion, Time.deltaTime * 5f);
			yield return null;
		}
		if (this.diskController != null && this.diskController.newDiskLoaded)
		{
			base.StartCoroutine(this.diskController.DiskLoadSequence());
		}
		InputManager.ActivateMap(1);
		yield break;
	}

	// Token: 0x060007C0 RID: 1984 RVA: 0x0003A584 File Offset: 0x00038784
	public void Deactivate()
	{
		ENT_Player.playerObject.transform.position = this.worldInterface.playerPosition.position;
		OS_Manager.inUse = false;
		OS_Manager.mouseEnabled = false;
		this.canControl = false;
		this.customInputModule.SetActive(false);
		CL_UIManager.instance.standardInputModule.SetActive(true);
		base.transform.GetChild(0).gameObject.SetActive(false);
		this.renderTexture.Release();
		if (OS_Manager.activeComputer == this)
		{
			OS_Manager.activeComputer = null;
		}
		CL_UIManager.instance.SetCrosshairVisibility(true);
		for (int i = 0; i < ENT_Player.playerObject.hands.Length; i++)
		{
			ENT_Player.playerObject.hands[i].handModel.localScale = Vector3.one * 1f;
		}
		this.osRoot.SetActive(false);
		this.cameraControl.EndTakeover();
		OS_Manager.soundPlayer.PlaySound("os:logoff");
		ENT_Player.playerObject.FreezeInputForFrame();
	}

	// Token: 0x060007C1 RID: 1985 RVA: 0x0003A68B File Offset: 0x0003888B
	public static OS_Manager GetCurrentOS()
	{
		return OS_Manager.activeComputer;
	}

	// Token: 0x060007C2 RID: 1986 RVA: 0x0003A692 File Offset: 0x00038892
	public OS_Filesystem GetFilesystem()
	{
		return this.filesystem;
	}

	// Token: 0x060007C3 RID: 1987 RVA: 0x0003A69A File Offset: 0x0003889A
	public void LoadFloppy(Item floppy)
	{
		this.diskController.LoadDisk(floppy);
	}

	// Token: 0x060007C4 RID: 1988 RVA: 0x0003A6A8 File Offset: 0x000388A8
	public OS_Manager.DiskController GetDiskController()
	{
		return this.diskController;
	}

	// Token: 0x060007C5 RID: 1989 RVA: 0x0003A6B0 File Offset: 0x000388B0
	internal bool IsInUse()
	{
		return OS_Manager.inUse;
	}

	// Token: 0x0400091C RID: 2332
	public static bool inUse;

	// Token: 0x0400091D RID: 2333
	public static OS_Manager activeComputer;

	// Token: 0x0400091E RID: 2334
	public UnityEvent activateEvent;

	// Token: 0x0400091F RID: 2335
	private OS_Manager.DiskController diskController;

	// Token: 0x04000920 RID: 2336
	public Message_Manager messageManager;

	// Token: 0x04000921 RID: 2337
	public OS_ResourceManager resources;

	// Token: 0x04000922 RID: 2338
	public Transform appParent;

	// Token: 0x04000923 RID: 2339
	public GameObject customInputModule;

	// Token: 0x04000924 RID: 2340
	public GameObject osRoot;

	// Token: 0x04000925 RID: 2341
	private EventSystem computerEventSystem;

	// Token: 0x04000926 RID: 2342
	public Selectable exitButton;

	// Token: 0x04000927 RID: 2343
	public CanvasGroup computerGroup;

	// Token: 0x04000928 RID: 2344
	private OS_Filesystem filesystem;

	// Token: 0x04000929 RID: 2345
	public UT_AudioClipHandler audioClipHandler;

	// Token: 0x0400092A RID: 2346
	public static UT_AudioClipHandler soundPlayer;

	// Token: 0x0400092B RID: 2347
	public RenderTexture renderTexture;

	// Token: 0x0400092C RID: 2348
	public OS_Pointer cursor;

	// Token: 0x0400092D RID: 2349
	public CursorMode cursorMode = CursorMode.ForceSoftware;

	// Token: 0x0400092E RID: 2350
	public Sprite cursorTexture;

	// Token: 0x0400092F RID: 2351
	public Sprite crosshairTexture;

	// Token: 0x04000930 RID: 2352
	public Sprite handTexture;

	// Token: 0x04000931 RID: 2353
	public Sprite fingerTexture;

	// Token: 0x04000932 RID: 2354
	public Sprite magnifyingGlasstexture;

	// Token: 0x04000933 RID: 2355
	public float cursorFixDivisor = 2f;

	// Token: 0x04000934 RID: 2356
	public static Vector2 mousePosition;

	// Token: 0x04000935 RID: 2357
	public static Vector2 mouseRealPosition;

	// Token: 0x04000936 RID: 2358
	private Vector2 lastMousePosition;

	// Token: 0x04000937 RID: 2359
	public static bool mouseEnabled;

	// Token: 0x04000938 RID: 2360
	private Dictionary<string, OS_Manager.Application> appDict;

	// Token: 0x04000939 RID: 2361
	public bool canControl;

	// Token: 0x0400093A RID: 2362
	public List<OS_Manager.Application> apps;

	// Token: 0x0400093B RID: 2363
	public UT_CameraTakeover cameraControl;

	// Token: 0x0400093C RID: 2364
	public OS_Computer_Interface worldInterface;

	// Token: 0x0400093D RID: 2365
	private bool hasInitialized;

	// Token: 0x020002A3 RID: 675
	public class DiskController
	{
		// Token: 0x06000E98 RID: 3736 RVA: 0x0005855E File Offset: 0x0005675E
		public void Initialize(OS_Manager o)
		{
			this.os = o;
			this.disks = new List<Item>();
			this.loadedDisks = new List<Item>();
		}

		// Token: 0x06000E99 RID: 3737 RVA: 0x0005857D File Offset: 0x0005677D
		public void LoadDisk(Item floppy)
		{
			this.disks.Add(floppy);
			this.newDiskLoaded = true;
			if (this.os.canControl)
			{
				this.os.StartCoroutine(this.DiskLoadSequence());
			}
		}

		// Token: 0x06000E9A RID: 3738 RVA: 0x000585B1 File Offset: 0x000567B1
		public List<Item> GetDisks()
		{
			return this.loadedDisks;
		}

		// Token: 0x06000E9B RID: 3739 RVA: 0x000585B9 File Offset: 0x000567B9
		public IEnumerator DiskLoadSequence()
		{
			if (this.isLoadingDisks)
			{
				yield break;
			}
			this.isLoadingDisks = true;
			this.os.SetInteractable(false);
			yield return new WaitForSeconds(0.5f);
			this.newDiskLoaded = false;
			Message_Manager.Message_Packet message_Packet = new Message_Manager.Message_Packet();
			message_Packet.type = "loading";
			message_Packet.closeText = "OK";
			message_Packet.message = "New disk detected. Loading..";
			message_Packet.screenPos = new Vector2(0f, 0f);
			message_Packet.data = new List<string> { "1" };
			Message message = this.os.messageManager.CreateMessage(message_Packet);
			OS_Manager.soundPlayer.PlaySound("os:floppy-load");
			while (message != null)
			{
				yield return null;
			}
			this.os.SetInteractable(true);
			message_Packet = new Message_Manager.Message_Packet();
			message_Packet.type = "default";
			message_Packet.closeText = "OK";
			message_Packet.message = "Disk mounted on desktop.";
			message_Packet.screenPos = new Vector2(0f, 0f);
			this.os.messageManager.CreateMessage(message_Packet);
			this.isLoadingDisks = false;
			this.CreateDiskContents();
			yield break;
		}

		// Token: 0x06000E9C RID: 3740 RVA: 0x000585C8 File Offset: 0x000567C8
		private void CreateDiskContents()
		{
			for (int i = 0; i < this.disks.Count; i++)
			{
				Item item = this.disks[i];
				if (!this.loadedDisks.Contains(item))
				{
					this.loadedDisks.Add(item);
					Vector2 vector = default(Vector2);
					vector.x = -60f;
					vector.y = -Mathf.Repeat((float)(50 * (i + 2)), 300f);
					OS_File os_File = null;
					if (item.data[0].Contains("diskdat:"))
					{
						OS_DiskData os_DiskData = Object.Instantiate<OS_DiskData>(CL_AssetManager.GetDiskDataAsset(item.data[0].Split(':', StringSplitOptions.None)[1], ""));
						os_DiskData.Initialize();
						os_File = this.os.filesystem.desktop.NewFolder(vector, os_DiskData.GetDiskName(), this.os.filesystem.GetIcon("disk"), false);
						os_File.fileInfo.children = new List<OS_Filesystem.FileInfo>();
						os_File.fileInfo.children.AddRange(os_DiskData.GetFiles());
					}
					if (item.data[0].Contains("name:"))
					{
						string text = "Disk " + i.ToString();
						text = item.data[0].Split(':', StringSplitOptions.None)[1];
						os_File = this.os.filesystem.desktop.NewFolder(vector, text, this.os.filesystem.GetIcon("disk"), false);
						if (os_File.fileInfo.children == null)
						{
							os_File.fileInfo.children = new List<OS_Filesystem.FileInfo>();
						}
						int num = 0;
						foreach (string text2 in item.data)
						{
							if (text2.Contains(":"))
							{
								string[] array = text2.Split(":", StringSplitOptions.None);
								if (array[0] == "file")
								{
									num++;
									string text3 = array[1];
									string text4 = array[2];
									string text5 = array[3];
									OS_Filesystem.FileInfo defaultFiletype = this.os.GetFilesystem().GetDefaultFiletype(text3);
									defaultFiletype.name = text4;
									defaultFiletype.data = text5;
									defaultFiletype.position = new Vector2
									{
										x = -Mathf.Repeat((float)(70 * num), 300f),
										y = -25f
									};
									os_File.fileInfo.children.Add(defaultFiletype);
								}
							}
						}
					}
					this.diskFolders.Add(os_File);
				}
			}
		}

		// Token: 0x06000E9D RID: 3741 RVA: 0x00058890 File Offset: 0x00056A90
		internal void WipeDisks()
		{
			Debug.Log(this.diskFolders.Count);
			for (int i = this.diskFolders.Count - 1; i >= 0; i--)
			{
				if (this.diskFolders[i] != null)
				{
					Debug.Log("Removing this/Test");
					this.diskFolders[i].CloseFileRecursive();
					this.diskFolders[i].DeleteFile();
				}
			}
			this.diskFolders.Clear();
			this.loadedDisks.Clear();
			this.disks.Clear();
		}

		// Token: 0x040010EC RID: 4332
		private OS_Manager os;

		// Token: 0x040010ED RID: 4333
		public bool newDiskLoaded;

		// Token: 0x040010EE RID: 4334
		public List<Item> disks;

		// Token: 0x040010EF RID: 4335
		private List<Item> loadedDisks = new List<Item>();

		// Token: 0x040010F0 RID: 4336
		private List<OS_File> diskFolders = new List<OS_File>();

		// Token: 0x040010F1 RID: 4337
		private bool isLoadingDisks;
	}

	// Token: 0x020002A4 RID: 676
	public enum cursorType
	{
		// Token: 0x040010F3 RID: 4339
		pointer,
		// Token: 0x040010F4 RID: 4340
		finger,
		// Token: 0x040010F5 RID: 4341
		hand,
		// Token: 0x040010F6 RID: 4342
		magnifyingGlass,
		// Token: 0x040010F7 RID: 4343
		crosshairs
	}

	// Token: 0x020002A5 RID: 677
	public class Application
	{
		// Token: 0x040010F8 RID: 4344
		public string id;

		// Token: 0x040010F9 RID: 4345
		public OS_Window app;
	}
}
