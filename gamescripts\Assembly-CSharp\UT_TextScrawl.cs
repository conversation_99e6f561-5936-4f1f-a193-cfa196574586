﻿using System;
using System.Collections;
using Febucci.UI.Core;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020000E2 RID: 226
public class UT_TextScrawl : MonoBehaviour
{
	// Token: 0x060006FF RID: 1791 RVA: 0x00036820 File Offset: 0x00034A20
	private void Start()
	{
		this.aud = base.GetComponent<AudioSource>();
		this.typewriter = base.GetComponent<TypewriterCore>();
		this.typewriter.onCharacterVisible.AddListener(new UnityAction<char>(this.PlayTypeSound));
		if (this.typewriter != null)
		{
			this.typewriter.ShowText("");
		}
	}

	// Token: 0x06000700 RID: 1792 RVA: 0x0003687F File Offset: 0x00034A7F
	private void OnEnable()
	{
		if (this.typewriter != null)
		{
			this.typewriter.ShowText("");
		}
	}

	// Token: 0x06000701 RID: 1793 RVA: 0x000368A0 File Offset: 0x00034AA0
	public void ShowText(string s)
	{
		if (base.gameObject == null || this == null || s == null)
		{
			return;
		}
		s = UI_TextHints.GetHintText(s);
		if (this.typewriter != null)
		{
			this.typewriter.StopDisappearingText();
			this.typewriter.ShowText(s);
		}
		this.typing = true;
		base.StopAllCoroutines();
	}

	// Token: 0x06000702 RID: 1794 RVA: 0x00036902 File Offset: 0x00034B02
	public void HideText()
	{
		this.typewriter.StartDisappearingText();
	}

	// Token: 0x06000703 RID: 1795 RVA: 0x0003690F File Offset: 0x00034B0F
	private void Update()
	{
		if (!this.typewriter.isShowingText && this.typing)
		{
			this.typing = false;
			base.StartCoroutine(this.DisappearWait());
		}
	}

	// Token: 0x06000704 RID: 1796 RVA: 0x0003693A File Offset: 0x00034B3A
	private IEnumerator DisappearWait()
	{
		yield return new WaitForSeconds(3f);
		this.typewriter.StartDisappearingText();
		yield break;
	}

	// Token: 0x06000705 RID: 1797 RVA: 0x00036949 File Offset: 0x00034B49
	public void PlayTypeSound(char c)
	{
		if (this.aud == null || this.typeSound == null)
		{
			return;
		}
		this.aud.clip = this.typeSound;
		this.aud.Play();
	}

	// Token: 0x0400087C RID: 2172
	public TypewriterCore typewriter;

	// Token: 0x0400087D RID: 2173
	public AudioClip typeSound;

	// Token: 0x0400087E RID: 2174
	private AudioSource aud;

	// Token: 0x0400087F RID: 2175
	private bool typing;
}
