﻿using System;
using System.Collections;
using System.Collections.Generic;
using DarkMachine.Collections;
using UnityEngine;

// Token: 0x02000150 RID: 336
[ExecuteInEditMode]
public class FXManager : MonoBehaviour
{
	// Token: 0x06000989 RID: 2441 RVA: 0x00040E3C File Offset: 0x0003F03C
	public void SetMapVars(Color fC, Color sC, Color fmC, Texture2D mG, Color sT, Color sB, float fH, float fF, Color wT, Color cT)
	{
		Shader.SetGlobalVectorArray("_LIGHT", this.lampInfo);
		Shader.SetGlobalVectorArray("_LIGHTCOL", this.lampColor);
		FXManager.defaultData.worldTint = wT;
	}

	// Token: 0x0600098A RID: 2442 RVA: 0x00040E6C File Offset: 0x0003F06C
	private void Awake()
	{
		this.gMan = CL_GameManager.gMan;
		FXManager.OnPreRenderFX = null;
		FXManager.regionFX = new FXManager.FXZoneStore();
		FXManager.regionFX.data = new FXManager.FXData();
		this.corruptionHeight = -50000f;
		FXManager.fxMan = this;
		FXManager.defaultData = this.defaultLook;
		FXManager.fxData = new FXManager.FXData();
		FXManager.fxData = FXManager.defaultData;
		if (this.lights == null)
		{
			this.lights = new List<CL_Lamp>();
		}
		else if (Application.isPlaying)
		{
			this.lights.Clear();
		}
		FXManager.FXRender();
		this.FXLights(Camera.main);
		base.StopAllCoroutines();
		base.StartCoroutine(this.QuickSortLoop());
		FXManager.fullBright = false;
		FXManager.showFog = true;
		if (Application.isPlaying)
		{
			CommandConsole.AddCommand("fullbright", new Action<string[]>(FXManager.Fullbright), true);
		}
	}

	// Token: 0x0600098B RID: 2443 RVA: 0x00040F48 File Offset: 0x0003F148
	private void Update()
	{
		if (FXManager.regionFX == null)
		{
			FXManager.regionFX = new FXManager.FXZoneStore();
			FXManager.regionFX.data = new FXManager.FXData();
		}
		if (WorldLoader.instance != null && WorldLoader.instance.GetCurrentLevel() != null && WorldLoader.instance.GetCurrentLevel().level != null && WorldLoader.instance.GetCurrentLevel().level.region != null)
		{
			if (WorldLoader.instance.GetCurrentLevel().level.region.useCustomFXSettings)
			{
				FXManager.regionFX.data = FXManager.BlendFXData(FXManager.regionFX.data, WorldLoader.instance.GetCurrentLevel().level.region.fxData, Time.deltaTime);
			}
		}
		else
		{
			FXManager.regionFX.data = FXManager.BlendFXData(FXManager.regionFX.data, FXManager.defaultData, Time.deltaTime);
		}
		FXManager.regionFX.blend = 1f;
		FXManager.regionFX.priority = 0;
	}

	// Token: 0x0600098C RID: 2444 RVA: 0x00041058 File Offset: 0x0003F258
	private void OnRenderObject()
	{
		if (Application.isPlaying)
		{
			this.FXLights(Camera.main);
		}
	}

	// Token: 0x0600098D RID: 2445 RVA: 0x0004106C File Offset: 0x0003F26C
	public static void CameraFX(Camera camera)
	{
		if (FXManager.fxMan != null)
		{
			FXManager.defaultData = FXManager.fxMan.defaultLook;
		}
		FXManager.fxZones.Enqueue(FXManager.regionFX, FXManager.regionFX.priority);
		if (FXManager.OnPreRenderFX != null)
		{
			FXManager.OnPreRenderFX(camera);
		}
		FXManager.FXData fxdata = new FXManager.FXData();
		while (FXManager.fxZones.Count > 0)
		{
			FXManager.FXZoneStore fxzoneStore = FXManager.fxZones.Dequeue();
			fxdata = FXManager.BlendFXData(fxdata, fxzoneStore.data, fxzoneStore.blend);
		}
		FXManager.fxData = fxdata;
		FXManager.FXRender();
	}

	// Token: 0x0600098E RID: 2446 RVA: 0x00041100 File Offset: 0x0003F300
	[ExecuteAlways]
	private void LateUpdate()
	{
		FXManager.defaultData = this.defaultLook;
		Shader.SetGlobalFloat("_UNSCALEDTIME", Time.unscaledTime);
		foreach (FXManager.AnimatedTexture animatedTexture in this.animatedTextures)
		{
			animatedTexture.Update();
		}
	}

	// Token: 0x0600098F RID: 2447 RVA: 0x0004116C File Offset: 0x0003F36C
	private void FXLights(Camera camera)
	{
		int num = 32;
		CL_Lamp[] array = new CL_Lamp[num];
		int num2 = 0;
		foreach (CL_Lamp cl_Lamp in this.lights)
		{
			if (num2 >= num - 1)
			{
				break;
			}
			if (cl_Lamp == null)
			{
				this.lights.Remove(cl_Lamp);
			}
			if (cl_Lamp.lampActive && cl_Lamp.enabled && cl_Lamp.gameObject.activeInHierarchy && Application.isPlaying && Vector3.Distance(camera.transform.position, cl_Lamp.transform.position) < this.defaultLook.worldLightDistance)
			{
				array[num2] = cl_Lamp;
				num2++;
			}
		}
		for (int i = 0; i < num; i++)
		{
			if (array.Length > i && array[i] != null)
			{
				CL_Lamp cl_Lamp2 = array[i];
				if (cl_Lamp2.lampActive && cl_Lamp2.gameObject.activeInHierarchy && cl_Lamp2.enabled)
				{
					this.lampInfo[i].x = cl_Lamp2.transform.position.x;
					this.lampInfo[i].y = cl_Lamp2.transform.position.y;
					this.lampInfo[i].z = cl_Lamp2.transform.position.z;
					this.lampInfo[i].w = cl_Lamp2.curRange;
					this.lampColor[i] = cl_Lamp2.color;
					this.lampColor[i].w = cl_Lamp2.GetCurrentIntensity() * cl_Lamp2.GetFade();
					Vector3 forward = cl_Lamp2.transform.forward;
					this.lampDirections[i].x = forward.x;
					this.lampDirections[i].y = forward.y;
					this.lampDirections[i].z = forward.z;
					this.lampDirections[i].w = cl_Lamp2.angle;
					this.lampEffects[i].x = cl_Lamp2.bypassLightmap;
					this.lampEffects[i].y = (float)cl_Lamp2.attentuation;
					this.lampEffects[i].z = cl_Lamp2.emissiveMult;
					this.lampEffects[i].w = 0f;
				}
				else
				{
					this.lampInfo[i] = new Vector4(0f, 0f, 0f, 0f);
					this.lampColor[i] = new Vector4(0f, 0f, 0f, 0f);
					this.lampDirections[i] = new Vector4(0f, 0f, 0f, 0f);
					this.lampEffects[i] = new Vector4(0f, 0f, 0f, 0f);
				}
			}
			else
			{
				this.lampInfo[i] = new Vector4(0f, 0f, 0f, 0f);
				this.lampColor[i] = new Vector4(0f, 0f, 0f, 0f);
				this.lampDirections[i] = new Vector4(0f, 0f, 0f, 0f);
				this.lampEffects[i] = new Vector4(0f, 0f, 0f, 0f);
			}
		}
		Shader.SetGlobalVectorArray("_LIGHT", this.lampInfo);
		Shader.SetGlobalVectorArray("_LIGHTCOL", this.lampColor);
		Shader.SetGlobalVectorArray("_LIGHTDIR", this.lampDirections);
		Shader.SetGlobalVectorArray("_LIGHTFX", this.lampEffects);
	}

	// Token: 0x06000990 RID: 2448 RVA: 0x000415B0 File Offset: 0x0003F7B0
	public static void FXRender()
	{
		if (Camera.main == null)
		{
			return;
		}
		if (FXManager.fxData == null)
		{
			FXManager.fxData = new FXManager.FXData();
			return;
		}
		Shader.SetGlobalInt("_ROUND", FXManager.fxData.roundVerts);
		Shader.SetGlobalInt("_DITHERLEVELS", Mathf.RoundToInt(FXManager.fxData.ditherLevels));
		Shader.SetGlobalFloat("_FOGDITHERAMOUNT", FXManager.fxData.fogDitherAmount);
		Shader.SetGlobalInt("_FOGDITHERLEVELS", FXManager.fxData.fogDitherLevels);
		Shader.SetGlobalFloat("_LIGHTMAPMULT", FXManager.fxData.lightmapMult);
		if (FXManager.fxMan != null)
		{
			Shader.SetGlobalFloat("_CORRUPTHEIGHT", FXManager.fxMan.corruptionHeight);
			Shader.SetGlobalTexture("_DITHERTEXTURE", FXManager.fxMan.ditherTexture);
		}
		Shader.SetGlobalVector("_WORLDWIGGLE", FXManager.fxData.worldWiggle);
		Shader.SetGlobalVector("_WORLDWARP", FXManager.fxData.worldWarp);
		Vector4 vector = new Vector4(FXManager.fxData.fog.r, FXManager.fxData.fog.g, FXManager.fxData.fog.b, FXManager.fxData.fogMultiplier);
		if (FXManager.showFog)
		{
			Shader.SetGlobalVector("_FOG", vector);
		}
		else
		{
			Shader.SetGlobalVector("_FOG", Vector4.zero);
		}
		if (FXManager.fullBright)
		{
			Shader.SetGlobalFloat("_WORLDBRIGHT", 2f);
			Shader.SetGlobalInt("_DITHERMINIMUM", 100);
			Shader.SetGlobalFloat("_FULLBRIGHT", 1f);
		}
		else
		{
			Shader.SetGlobalFloat("_WORLDBRIGHT", FXManager.fxData.worldBright);
			Shader.SetGlobalInt("_DITHERMINIMUM", FXManager.fxData.ditherMinimum);
			Shader.SetGlobalFloat("_FULLBRIGHT", 0f);
		}
		Shader.SetGlobalVector("_HSV", FXManager.fxData.hsv);
		Shader.SetGlobalVector("_NOISE", FXManager.fxData.noise);
		Shader.SetGlobalVector("_SCREENWARP", FXManager.fxData.screenWarp);
		Shader.SetGlobalFloat("_OFFSET", FXManager.fxData.offset);
		Shader.SetGlobalVector("_WORLDMIN", FXManager.fxData.minCol);
		Shader.SetGlobalVector("_WORLDTINT", FXManager.fxData.worldTint);
		if (Application.isPlaying)
		{
			if (SettingsManager.settings != null)
			{
				Shader.SetGlobalFloat("_GAMMA", SettingsManager.settings.brightness + FXManager.fxData.gamma);
			}
		}
		else
		{
			Shader.SetGlobalFloat("_GAMMA", 1f + FXManager.fxData.gamma);
		}
		FX_PlayerDust.SetDustAmount(FXManager.fxData.dustMult);
		FX_PlayerDust.SetDustColor(FXManager.fxData.dustColor);
		FX_PlayerDust.SetDustWind(FXManager.fxData.dustWind, FXManager.fxData.dustNoise);
	}

	// Token: 0x06000991 RID: 2449 RVA: 0x0004187F File Offset: 0x0003FA7F
	public void UpdateLamp(CL_Lamp z)
	{
		if (!this.lightHash.Contains(z))
		{
			this.AddLamp(z);
		}
	}

	// Token: 0x06000992 RID: 2450 RVA: 0x00041896 File Offset: 0x0003FA96
	public void AddLamp(CL_Lamp z)
	{
		if (this.lights.Contains(z))
		{
			return;
		}
		this.lights.Add(z);
		this.lightHash.Add(z);
	}

	// Token: 0x06000993 RID: 2451 RVA: 0x000418C0 File Offset: 0x0003FAC0
	public void DeleteLamp(CL_Lamp z)
	{
		if (this.lights.Contains(z))
		{
			this.lights.Remove(z);
			this.lightHash.Remove(z);
		}
	}

	// Token: 0x06000994 RID: 2452 RVA: 0x000418EA File Offset: 0x0003FAEA
	public void MoveLampToFrontOfQueue(CL_Lamp z)
	{
		if (this.lights.Contains(z) && this.lights.IndexOf(z) != 0)
		{
			this.lights.Remove(z);
			this.lights.Insert(0, z);
		}
	}

	// Token: 0x06000995 RID: 2453 RVA: 0x00041924 File Offset: 0x0003FB24
	private int partition(List<CL_Lamp> arr, int low, int high)
	{
		if (high >= arr.Count)
		{
			return high;
		}
		CL_Lamp cl_Lamp = arr[high];
		int num = low - 1;
		for (int i = low; i < high; i++)
		{
			if (Application.isPlaying && this.gMan != null && this.gMan.PlayerDistance(arr[i].transform.position) < this.gMan.PlayerDistance(cl_Lamp.transform.position))
			{
				num++;
				CL_Lamp cl_Lamp2 = arr[num];
				arr[num] = arr[i];
				arr[i] = cl_Lamp2;
			}
		}
		CL_Lamp cl_Lamp3 = arr[num + 1];
		arr[num + 1] = arr[high];
		arr[high] = cl_Lamp3;
		return num + 1;
	}

	// Token: 0x06000996 RID: 2454 RVA: 0x000419E6 File Offset: 0x0003FBE6
	private IEnumerator QuickSortLoop()
	{
		for (;;)
		{
			if (this.lights.Count > 1000)
			{
				yield return new WaitForEndOfFrame();
			}
			else
			{
				yield return this.QuickSortByDistance(this.lights, 0, this.lights.Count - 1);
			}
		}
		yield break;
	}

	// Token: 0x06000997 RID: 2455 RVA: 0x000419F5 File Offset: 0x0003FBF5
	private IEnumerator QuickSortByDistance(List<CL_Lamp> arr, int low, int high)
	{
		if (low < high)
		{
			int pi = this.partition(arr, low, high);
			yield return new WaitForEndOfFrame();
			yield return this.QuickSortByDistance(arr, low, pi - 1);
			yield return this.QuickSortByDistance(arr, pi + 1, high);
		}
		yield break;
	}

	// Token: 0x06000998 RID: 2456 RVA: 0x00041A1C File Offset: 0x0003FC1C
	public static FXManager.FXData BlendFXData(FXManager.FXData d1, FXManager.FXData d2, float blend)
	{
		if (d1 == null || d2 == null)
		{
			return null;
		}
		FXManager.FXData fxdata = new FXManager.FXData();
		if (d2.affectGeneral)
		{
			fxdata.ditherLevels = Mathf.Lerp(d1.ditherLevels, d2.ditherLevels, blend);
			fxdata.ditherMinimum = (int)Mathf.Lerp((float)d1.ditherMinimum, (float)d2.ditherMinimum, blend);
			fxdata.roundVerts = (int)Mathf.Lerp((float)d1.roundVerts, (float)d2.roundVerts, blend);
			fxdata.worldBright = Mathf.Lerp(d1.worldBright, d2.worldBright, blend);
			fxdata.worldWiggle = Vector3.Lerp(d1.worldWiggle, d2.worldWiggle, blend);
			fxdata.worldWarp = Vector4.Lerp(d1.worldWarp, d2.worldWarp, blend);
			fxdata.lightmapMult = Mathf.Lerp(d1.lightmapMult, d2.lightmapMult, blend);
			fxdata.affectGeneral = true;
		}
		else if (d1.affectGeneral)
		{
			fxdata.ditherLevels = d1.ditherLevels;
			fxdata.ditherMinimum = d1.ditherMinimum;
			fxdata.roundVerts = d1.roundVerts;
			fxdata.worldBright = d1.worldBright;
			fxdata.worldWiggle = d1.worldWiggle;
			fxdata.worldWarp = d1.worldWarp;
			fxdata.lightmapMult = d1.lightmapMult;
			fxdata.affectGeneral = true;
		}
		if (d2.affectColor)
		{
			fxdata.minCol = Color.Lerp(d1.minCol, d2.minCol, blend);
			fxdata.worldTint = Color.Lerp(d1.worldTint, d2.worldTint, blend);
			fxdata.gamma = Mathf.Lerp(d1.gamma, d2.gamma, blend);
			fxdata.offset = Mathf.Lerp(d1.offset, d2.offset, blend);
			fxdata.affectColor = true;
		}
		else if (d1.affectColor)
		{
			fxdata.minCol = d1.minCol;
			fxdata.worldTint = d1.worldTint;
			fxdata.gamma = d1.gamma;
			fxdata.offset = d1.offset;
			fxdata.affectColor = true;
		}
		if (d2.affectFog)
		{
			fxdata.fog = Color.Lerp(d1.fog, d2.fog, blend);
			fxdata.fogMultiplier = Mathf.Lerp(d1.fogMultiplier, d2.fogMultiplier, blend);
			fxdata.fogDitherAmount = Mathf.Lerp(d1.fogDitherAmount, d2.fogDitherAmount, blend);
			fxdata.fogDitherLevels = (int)Mathf.Lerp((float)d1.fogDitherLevels, (float)d2.fogDitherLevels, blend);
			fxdata.affectFog = true;
		}
		else if (d1.affectFog)
		{
			fxdata.fog = d1.fog;
			fxdata.fogMultiplier = d1.fogMultiplier;
			fxdata.fogDitherAmount = d1.fogDitherAmount;
			fxdata.fogDitherLevels = d1.fogDitherLevels;
			fxdata.affectFog = true;
		}
		if (d2.affectHSV)
		{
			fxdata.hsv = Vector3.Lerp(d1.hsv, d2.hsv, blend);
			fxdata.affectHSV = true;
		}
		else if (d1.affectHSV)
		{
			fxdata.hsv = d1.hsv;
			fxdata.affectHSV = true;
		}
		if (d2.affectNoise)
		{
			fxdata.noise = Vector2.Lerp(d1.noise, d2.noise, blend);
			fxdata.affectNoise = true;
		}
		else if (d1.affectNoise)
		{
			fxdata.noise = d1.noise;
			fxdata.affectNoise = true;
		}
		if (d2.affectScreenWarp)
		{
			fxdata.screenWarp = Vector4.Lerp(d1.screenWarp, d2.screenWarp, blend);
			fxdata.affectScreenWarp = true;
		}
		else if (d1.affectScreenWarp)
		{
			fxdata.screenWarp = d1.screenWarp;
			fxdata.affectScreenWarp = true;
		}
		if (d2.affectDust)
		{
			fxdata.dustMult = Mathf.Lerp(d1.dustMult, d2.dustMult, blend);
			fxdata.dustColor = Color.Lerp(d1.dustColor, d2.dustColor, blend);
			fxdata.dustWind = Vector3.Lerp(d1.dustWind, d2.dustWind, blend);
			fxdata.dustNoise = Mathf.Lerp(d1.dustNoise, d2.dustNoise, blend);
			fxdata.affectDust = true;
		}
		else if (d1.affectDust)
		{
			fxdata.dustMult = d1.dustMult;
			fxdata.dustColor = d1.dustColor;
			fxdata.dustWind = d1.dustWind;
			fxdata.dustNoise = d1.dustNoise;
			fxdata.affectDust = true;
		}
		return fxdata;
	}

	// Token: 0x06000999 RID: 2457 RVA: 0x00041E3C File Offset: 0x0004003C
	public static void Fullbright(string[] args)
	{
		if (args.Length == 0)
		{
			FXManager.fullBright = !FXManager.fullBright;
		}
		else
		{
			bool flag;
			if (!bool.TryParse(args[0], out flag))
			{
				CommandConsole.Log("Unable to parse " + args[0] + " arg needs to be a boolean (true/false/0/1).", false);
				return;
			}
			FXManager.fullBright = Convert.ToBoolean(args[0]);
		}
		CommandConsole.Log("Fullbright set to " + FXManager.fullBright.ToString(), false);
		FXManager.showFog = !FXManager.fullBright;
	}

	// Token: 0x0600099A RID: 2458 RVA: 0x00041EB8 File Offset: 0x000400B8
	public static Material GetSharedHandholdMaterial(Material m)
	{
		if (FXManager.handholdMaterialDict == null)
		{
			FXManager.handholdMaterialDict = new Dictionary<string, Material>();
			FXManager.handholdOriginalMaterialDict = new Dictionary<string, Material>();
			FXManager.handholdMaterials = new List<Material>();
		}
		if (FXManager.handholdMaterialDict.ContainsKey(m.name))
		{
			return FXManager.handholdMaterialDict[m.name];
		}
		Material material = new Material(m);
		FXManager.handholdMaterialDict.Add(m.name, material);
		FXManager.handholdOriginalMaterialDict.Add(m.name, m);
		FXManager.handholdMaterials.Add(material);
		FXManager.UpdateHandholdMaterialSettings();
		Debug.Log("Added new Handhold Material - " + m.name + " -- " + m.shader.name);
		return FXManager.handholdMaterialDict[m.name];
	}

	// Token: 0x0600099B RID: 2459 RVA: 0x00041F7C File Offset: 0x0004017C
	public static void UpdateHandholdMaterialSettings()
	{
		if (FXManager.handholdMaterials == null || FXManager.handholdOriginalMaterialDict == null)
		{
			return;
		}
		foreach (Material material in FXManager.handholdMaterials)
		{
			if (FXManager.handholdOriginalMaterialDict.ContainsKey(material.name) && !(material.shader.name != "Dark Machine/SHDR_Base"))
			{
				Material material2 = FXManager.handholdOriginalMaterialDict[material.name];
				float @float = material2.GetFloat("_EmissionMultiplier");
				float float2 = material2.GetFloat("_Shimmer");
				float float3 = material2.GetFloat("_ShimmerOver");
				float float4 = material2.GetFloat("_ShimmerOffset");
				Color color = material2.GetColor("_ShimmerColor");
				if (SettingsManager.settings.handholdHighVis)
				{
					if (@float > 0f)
					{
						material.SetFloat("_EmissionMultiplier", 8f);
					}
					else
					{
						material.SetFloat("_EmissionMultiplier", 0.25f);
					}
				}
				else
				{
					material.SetFloat("_EmissionMultiplier", @float);
				}
				if (SettingsManager.settings.extraHandholdShimmer)
				{
					material.SetFloat("_ShimmerOver", 3f);
					material.SetFloat("_Shimmer", Mathf.Min(float2 + 0.2f, 1f));
					material.SetColor("_ShimmerColor", Color.Lerp(color, new Color(1f, 0.7f, 0.4f), 0.9f));
					material.SetFloat("_ShimmerOffset", 0f);
					material.SetFloat("_ShimmerTextureMix", 0.95f);
				}
				else
				{
					material.SetFloat("_ShimmerOver", float3);
					material.SetFloat("_Shimmer", float2);
					material.SetColor("_ShimmerColor", color);
					material.SetFloat("_ShimmerOffset", float4);
					material.SetFloat("_ShimmerTextureMix", 1f);
				}
			}
		}
	}

	// Token: 0x04000ACC RID: 2764
	public static FXManager fxMan;

	// Token: 0x04000ACD RID: 2765
	private CL_GameManager gMan;

	// Token: 0x04000ACE RID: 2766
	public FXManager.FXData defaultLook;

	// Token: 0x04000ACF RID: 2767
	private static FXManager.FXZoneStore regionFX;

	// Token: 0x04000AD0 RID: 2768
	public static FXManager.FXData defaultData;

	// Token: 0x04000AD1 RID: 2769
	public static FXManager.FXData fxData;

	// Token: 0x04000AD2 RID: 2770
	public static PriorityQueue<FXManager.FXZoneStore, byte> fxZones = new PriorityQueue<FXManager.FXZoneStore, byte>();

	// Token: 0x04000AD3 RID: 2771
	public static Action<Camera> OnPreRenderFX;

	// Token: 0x04000AD4 RID: 2772
	private static List<Material> handholdMaterials;

	// Token: 0x04000AD5 RID: 2773
	private static Dictionary<string, Material> handholdMaterialDict;

	// Token: 0x04000AD6 RID: 2774
	private static Dictionary<string, Material> handholdOriginalMaterialDict;

	// Token: 0x04000AD7 RID: 2775
	private static Texture2D whiteTex;

	// Token: 0x04000AD8 RID: 2776
	public static bool showFog = true;

	// Token: 0x04000AD9 RID: 2777
	public static bool fullBright = false;

	// Token: 0x04000ADA RID: 2778
	public Texture2D corruptionTexture;

	// Token: 0x04000ADB RID: 2779
	public float corruptionHeight;

	// Token: 0x04000ADC RID: 2780
	public Texture2D ditherTexture;

	// Token: 0x04000ADD RID: 2781
	private List<CL_Lamp> lights = new List<CL_Lamp>();

	// Token: 0x04000ADE RID: 2782
	private HashSet<CL_Lamp> lightHash = new HashSet<CL_Lamp>();

	// Token: 0x04000ADF RID: 2783
	private Vector4[] lampInfo = new Vector4[32];

	// Token: 0x04000AE0 RID: 2784
	private Vector4[] lampColor = new Vector4[32];

	// Token: 0x04000AE1 RID: 2785
	private Vector4[] lampDirections = new Vector4[32];

	// Token: 0x04000AE2 RID: 2786
	private Vector4[] lampEffects = new Vector4[32];

	// Token: 0x04000AE3 RID: 2787
	public List<FXManager.AnimatedTexture> animatedTextures = new List<FXManager.AnimatedTexture>();

	// Token: 0x020002CD RID: 717
	[Serializable]
	public class AnimatedTexture
	{
		// Token: 0x06000F29 RID: 3881 RVA: 0x0005A3B4 File Offset: 0x000585B4
		public void Update()
		{
			if (this.material == null)
			{
				return;
			}
			if (this.countdown <= 0f)
			{
				this.countdown = this.speed;
				if (this.useRandomVariation)
				{
					this.countdown += (Random.value - 0.5f) * this.randomVariationAmount * 2f;
				}
				if (this.order == FXManager.AnimatedTexture.AnimatedTextureOrder.forward)
				{
					this.currentFrame++;
					if (this.currentFrame >= this.textures.Count)
					{
						this.currentFrame = 0;
					}
				}
				else
				{
					this.currentFrame = Random.Range(0, this.textures.Count);
				}
				if (this.type == FXManager.AnimatedTexture.AnimatedTextureType.main)
				{
					this.material.SetTexture("_MainTex", this.textures[this.currentFrame]);
				}
				else if (this.type == FXManager.AnimatedTexture.AnimatedTextureType.emission)
				{
					this.material.SetTexture("_Emission", this.textures[this.currentFrame]);
				}
				else
				{
					this.material.SetTexture(this.customTextureName, this.textures[this.currentFrame]);
				}
			}
			this.countdown -= Time.deltaTime;
		}

		// Token: 0x040011DE RID: 4574
		public string id;

		// Token: 0x040011DF RID: 4575
		public FXManager.AnimatedTexture.AnimatedTextureType type;

		// Token: 0x040011E0 RID: 4576
		public FXManager.AnimatedTexture.AnimatedTextureOrder order;

		// Token: 0x040011E1 RID: 4577
		public string customTextureName;

		// Token: 0x040011E2 RID: 4578
		public Material material;

		// Token: 0x040011E3 RID: 4579
		public List<Texture2D> textures;

		// Token: 0x040011E4 RID: 4580
		public float speed = 1f;

		// Token: 0x040011E5 RID: 4581
		public bool useRandomVariation;

		// Token: 0x040011E6 RID: 4582
		public float randomVariationAmount = 0.3f;

		// Token: 0x040011E7 RID: 4583
		private float countdown;

		// Token: 0x040011E8 RID: 4584
		private int currentFrame;

		// Token: 0x02000319 RID: 793
		public enum AnimatedTextureType
		{
			// Token: 0x04001336 RID: 4918
			main,
			// Token: 0x04001337 RID: 4919
			emission,
			// Token: 0x04001338 RID: 4920
			custom
		}

		// Token: 0x0200031A RID: 794
		public enum AnimatedTextureOrder
		{
			// Token: 0x0400133A RID: 4922
			forward,
			// Token: 0x0400133B RID: 4923
			random
		}
	}

	// Token: 0x020002CE RID: 718
	[Serializable]
	public class FXData
	{
		// Token: 0x040011E9 RID: 4585
		public bool affectGeneral = true;

		// Token: 0x040011EA RID: 4586
		[Header("Vertex Jitter")]
		public int roundVerts = 160;

		// Token: 0x040011EB RID: 4587
		[Space]
		[Header("Dither")]
		public float ditherLevels = 32f;

		// Token: 0x040011EC RID: 4588
		public int ditherMinimum = 4;

		// Token: 0x040011ED RID: 4589
		[Space]
		[Header("Lighting")]
		public float worldLightDistance = 30f;

		// Token: 0x040011EE RID: 4590
		public float worldBright;

		// Token: 0x040011EF RID: 4591
		public float lightmapMult;

		// Token: 0x040011F0 RID: 4592
		[Space]
		[Header("Wiggle")]
		public Vector3 worldWiggle;

		// Token: 0x040011F1 RID: 4593
		public Vector4 worldWarp;

		// Token: 0x040011F2 RID: 4594
		public bool affectFog = true;

		// Token: 0x040011F3 RID: 4595
		[Range(0f, 1f)]
		public float fogMultiplier;

		// Token: 0x040011F4 RID: 4596
		[Range(1f, 32f)]
		public int fogDitherLevels = 4;

		// Token: 0x040011F5 RID: 4597
		[Range(0f, 1f)]
		public float fogDitherAmount = 0.3f;

		// Token: 0x040011F6 RID: 4598
		public Color fog;

		// Token: 0x040011F7 RID: 4599
		public bool affectColor = true;

		// Token: 0x040011F8 RID: 4600
		public Color minCol;

		// Token: 0x040011F9 RID: 4601
		public Color worldTint = Color.white;

		// Token: 0x040011FA RID: 4602
		public float gamma;

		// Token: 0x040011FB RID: 4603
		public float offset;

		// Token: 0x040011FC RID: 4604
		[Space]
		public bool affectHSV = true;

		// Token: 0x040011FD RID: 4605
		public Vector3 hsv = new Vector3(0f, 1f, 1f);

		// Token: 0x040011FE RID: 4606
		[Space]
		public bool affectNoise = true;

		// Token: 0x040011FF RID: 4607
		public Vector2 noise = Vector2.zero;

		// Token: 0x04001200 RID: 4608
		[Space]
		public bool affectScreenWarp = true;

		// Token: 0x04001201 RID: 4609
		public Vector4 screenWarp;

		// Token: 0x04001202 RID: 4610
		public bool affectDust;

		// Token: 0x04001203 RID: 4611
		public float dustMult = 1f;

		// Token: 0x04001204 RID: 4612
		public Color dustColor = Color.grey;

		// Token: 0x04001205 RID: 4613
		public Vector3 dustWind = Vector3.zero;

		// Token: 0x04001206 RID: 4614
		public float dustNoise = 1f;
	}

	// Token: 0x020002CF RID: 719
	public class FXZoneStore
	{
		// Token: 0x04001207 RID: 4615
		public float blend;

		// Token: 0x04001208 RID: 4616
		public byte priority;

		// Token: 0x04001209 RID: 4617
		public FXManager.FXData data;
	}
}
