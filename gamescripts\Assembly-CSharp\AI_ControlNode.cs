﻿using System;
using Drawing;
using Pathfinding;
using UnityEngine;

// Token: 0x0200000C RID: 12
public class AI_ControlNode : MonoBehaviourGizmos
{
	// Token: 0x06000042 RID: 66 RVA: 0x000043F4 File Offset: 0x000025F4
	private void Start()
	{
		this.link = base.GetComponent<NodeLink2>();
	}

	// Token: 0x06000043 RID: 67 RVA: 0x00004402 File Offset: 0x00002602
	public NodeLink2 GetLinkNode()
	{
		return this.link;
	}

	// Token: 0x06000044 RID: 68 RVA: 0x0000440A File Offset: 0x0000260A
	public Transform GetEndNode()
	{
		return this.link.end;
	}

	// Token: 0x06000045 RID: 69 RVA: 0x00004418 File Offset: 0x00002618
	public override void DrawGizmos()
	{
		if (this.link == null)
		{
			this.link = base.GetComponent<NodeLink2>();
		}
		Color green = Color.green;
		string text = "AI " + this.aiNodeType.ToString();
		if (this.aiNodeType == AI_ControlNode.NodeType.shortLeap)
		{
			green = new Color(0f, 1f, 1f, 0.5f);
		}
		Draw.Label2D(base.transform.position, text, 12f, LabelAlignment.Center, green * new Color(1f, 1f, 1f, 0.1f));
		if (this.link != null)
		{
			Draw.DashedLine(base.transform.position, this.link.end.position, 0.5f, 0.5f, green * new Color(1f, 1f, 1f, 0.5f));
			Draw.DashedLine(base.transform.position + base.transform.right, this.link.end.position + this.link.end.right, 0.5f, 0.5f, green * new Color(1f, 1f, 1f, 0.25f));
			Draw.DashedLine(base.transform.position - base.transform.right, this.link.end.position - this.link.end.right, 0.5f, 0.5f, green * new Color(1f, 1f, 1f, 0.25f));
			Draw.Arrow(base.transform.position, base.transform.position + base.transform.up, Color.white);
			Draw.Arrow(base.transform.position, base.transform.position + base.transform.forward, Color.blue);
			Draw.WireHexagon(base.transform.position, Quaternion.LookRotation(base.transform.up, base.transform.forward), 1f, green);
			Draw.Arrow(this.link.end.position, this.link.end.position + this.link.end.up, Color.white);
			Draw.WireHexagon(this.link.end.position, Quaternion.LookRotation(this.link.end.up, this.link.end.forward), 1f, green);
			Draw.Arrow(this.link.end.position, this.link.end.position + this.link.end.forward, new Color(0f, 1f, 1f, 0.3f));
		}
	}

	// Token: 0x04000053 RID: 83
	public AI_ControlNode.NodeType aiNodeType;

	// Token: 0x04000054 RID: 84
	private NodeLink2 link;

	// Token: 0x020001EE RID: 494
	public enum NodeType
	{
		// Token: 0x04000D65 RID: 3429
		normal,
		// Token: 0x04000D66 RID: 3430
		farLeap,
		// Token: 0x04000D67 RID: 3431
		shortLeap,
		// Token: 0x04000D68 RID: 3432
		burrow
	}
}
