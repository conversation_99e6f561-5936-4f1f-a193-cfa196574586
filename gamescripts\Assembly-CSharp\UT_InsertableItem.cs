﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020000D2 RID: 210
public class UT_InsertableItem : MonoBehaviour, Clickable
{
	// Token: 0x060006BD RID: 1725 RVA: 0x00035E0A File Offset: 0x0003400A
	public void Interact()
	{
		this.activeEvent.Invoke();
		this.hasBeenPressed = true;
	}

	// Token: 0x060006BE RID: 1726 RVA: 0x00035E1E File Offset: 0x0003401E
	public void Interact(ENT_Player p, string s = "")
	{
		this.activeEvent.Invoke();
		this.hasBeenPressed = true;
	}

	// Token: 0x060006BF RID: 1727 RVA: 0x00035E32 File Offset: 0x00034032
	public void Reset()
	{
		this.hasBeenPressed = false;
	}

	// Token: 0x060006C0 RID: 1728 RVA: 0x00035E3B File Offset: 0x0003403B
	public bool CanInteract(ENT_Player p, ENT_Player.Hand hand)
	{
		return this.active && (hand.interactState == ENT_Player.InteractType.item && hand.inventoryHand.currentItem.interactType == Item.InteractType.usable);
	}

	// Token: 0x060006C1 RID: 1729 RVA: 0x00035E66 File Offset: 0x00034066
	public GameObject GetGameObject()
	{
		return base.gameObject;
	}

	// Token: 0x060006C2 RID: 1730 RVA: 0x00035E6E File Offset: 0x0003406E
	public ObjectTagger GetTagger()
	{
		return base.gameObject.GetComponent<ObjectTagger>();
	}

	// Token: 0x060006C3 RID: 1731 RVA: 0x00035E7B File Offset: 0x0003407B
	public void SetInteractable(bool b)
	{
		this.active = b;
	}

	// Token: 0x04000843 RID: 2115
	public bool active = true;

	// Token: 0x04000844 RID: 2116
	public UnityEvent activeEvent;

	// Token: 0x04000845 RID: 2117
	[HideInInspector]
	public bool hasBeenPressed;
}
