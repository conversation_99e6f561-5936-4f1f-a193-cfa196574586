﻿using System;
using System.Collections.Generic;
using LibBSP;
using UnityEngine;

// Token: 0x0200002F RID: 47
[RequireComponent(typeof(UT_AudioClipHandler))]
public class DEN_Barnacle : GameEntity, AIGameEntity.Grappler
{
	// Token: 0x060001A7 RID: 423 RVA: 0x0000C43C File Offset: 0x0000A63C
	public override void Start()
	{
		base.Start();
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		foreach (DEN_Barnacle.Tongue tongue in this.tongues)
		{
			tongue.Initialize(this);
		}
		this.startRotation = this.geo.localRotation;
		if (this.spawnPolyps)
		{
			Vector3 vector = base.transform.position + base.transform.forward * 0.5f;
			for (int i = 0; i < this.polypCount; i++)
			{
				for (int j = 0; j < 10; j++)
				{
					RaycastHit raycastHit;
					if (Physics.Raycast(vector, -base.transform.forward * 0.8f + Random.onUnitSphere, out raycastHit, 4f, this.hitMask))
					{
						Object.Instantiate<GameObject>(this.polyps[Random.Range(0, this.polyps.Count)], raycastHit.point, Quaternion.LookRotation(raycastHit.normal), base.transform);
						break;
					}
				}
			}
		}
	}

	// Token: 0x060001A8 RID: 424 RVA: 0x0000C58C File Offset: 0x0000A78C
	private void LateUpdate()
	{
		this.HurtHeld();
		if (this.vomitTimer > 0f && !this.hasVomited)
		{
			float num = 20f;
			Vector3 vector = new Vector3(Mathf.PerlinNoise(Time.time * num, this.geo.position.y) - 0.5f, Mathf.PerlinNoise(-Time.time * num, this.geo.position.x) - 0.5f, Mathf.PerlinNoise(Time.time * num, this.geo.position.z) - 0.5f);
			this.geo.localRotation = this.startRotation * Quaternion.Euler(vector * 5f);
			this.vomitTimer -= Time.deltaTime;
		}
		if (this.vomitTimer <= 0f && !this.hasVomited)
		{
			this.Vomit();
		}
		this.geo.localRotation = Quaternion.Lerp(this.geo.localRotation, this.startRotation, Time.deltaTime * 3f);
		foreach (DEN_Barnacle.Tongue tongue in this.tongues)
		{
			tongue.AnimateTongue();
		}
		if (this.eating)
		{
			if (this.audioState != DEN_Barnacle.AudioState.eating)
			{
				this.clipHandler.StopSound("barnacle:pull-target");
				this.clipHandler.StopSound("barnacle:loop-idle");
			}
			this.audioState = DEN_Barnacle.AudioState.eating;
			this.clipHandler.FadeIn("barnacle:bite", Time.deltaTime * 15f, true);
			if (this.spikeSystem != null && !this.spikeSystem.isPlaying)
			{
				this.spikeSystem.Play();
				return;
			}
		}
		else
		{
			if (this.IsPulling())
			{
				if (this.audioState != DEN_Barnacle.AudioState.pulling)
				{
					this.clipHandler.StopSound("barnacle:bite");
					this.clipHandler.StopSound("barnacle:loop-idle");
				}
				this.audioState = DEN_Barnacle.AudioState.pulling;
				this.clipHandler.FadeIn("barnacle:pull-target", Time.deltaTime * 15f, true);
				return;
			}
			this.audioState = DEN_Barnacle.AudioState.idle;
			this.clipHandler.FadeSound("barnacle:bite", 0f, Time.deltaTime * 15f, true);
			this.clipHandler.FadeSound("barnacle:pull-target", 0f, Time.deltaTime * 15f, true);
			this.clipHandler.FadeIn("barnacle:loop-idle", Time.deltaTime * 15f, true);
			if (this.spikeSystem != null && this.spikeSystem.isPlaying)
			{
				this.spikeSystem.Stop();
			}
		}
	}

	// Token: 0x060001A9 RID: 425 RVA: 0x0000C854 File Offset: 0x0000AA54
	public override void Kill(string type = "")
	{
		if (this.dead)
		{
			return;
		}
		base.Kill("");
		this.clipHandler.StopSound("barnacle:loop-idle");
		foreach (DEN_Barnacle.Tongue tongue in this.tongues)
		{
			tongue.Die();
		}
		this.clipHandler.PlaySound("barnacle:die");
		if (this.animator != null)
		{
			this.animator.SetBool("dead", true);
		}
	}

	// Token: 0x060001AA RID: 426 RVA: 0x0000C8F8 File Offset: 0x0000AAF8
	public override bool Damage(float amount, string type)
	{
		if (this.dead)
		{
			return false;
		}
		this.clipHandler.PlaySound("barnacle:hurt");
		if (this.animator != null)
		{
			this.animator.SetTrigger("hurt");
		}
		if (this.dropOnDamage)
		{
			this.ReleaseGrapple();
		}
		return base.Damage(amount, type);
	}

	// Token: 0x060001AB RID: 427 RVA: 0x0000C954 File Offset: 0x0000AB54
	public void HurtHeld()
	{
		this.attackTime -= Time.deltaTime;
		if (this.attackTime > 0f)
		{
			return;
		}
		this.eating = false;
		this.attackTime = this.attackRate;
		foreach (DEN_Barnacle.Tongue tongue in this.tongues)
		{
			if (tongue.CanBite())
			{
				if (tongue.GetGrabbed().GetTagger().HasTagInList(this.vomitTags.ToArray()) || tongue.GetGrabbed().maxHealth == 0f)
				{
					tongue.Retract();
					this.Vomit();
					break;
				}
				if (tongue.GetGrabbed() != null)
				{
					if (tongue.GetGrabbed().Damage(this.damage, "barnacle"))
					{
						this.hasVomited = false;
					}
				}
				else
				{
					tongue.Retract();
				}
				this.eating = true;
				this.vomitTimer = 1f;
				break;
			}
		}
	}

	// Token: 0x060001AC RID: 428 RVA: 0x0000CA6C File Offset: 0x0000AC6C
	private void Vomit()
	{
		if (this.vomitSystem != null)
		{
			this.vomitSystem.Play();
		}
		this.hasVomited = true;
		this.clipHandler.PlaySound("barnacle:burp");
	}

	// Token: 0x060001AD RID: 429 RVA: 0x0000CAA0 File Offset: 0x0000ACA0
	private bool IsPulling()
	{
		using (List<DEN_Barnacle.Tongue>.Enumerator enumerator = this.tongues.GetEnumerator())
		{
			while (enumerator.MoveNext())
			{
				if (enumerator.Current.GetState() == DEN_Barnacle.Tongue.TongueState.grabbing)
				{
					return true;
				}
			}
		}
		return false;
	}

	// Token: 0x060001AE RID: 430 RVA: 0x0000CAFC File Offset: 0x0000ACFC
	public void ReleaseGrapple()
	{
		foreach (DEN_Barnacle.Tongue tongue in this.tongues)
		{
			tongue.KillTongue();
		}
	}

	// Token: 0x060001AF RID: 431 RVA: 0x0000CB4C File Offset: 0x0000AD4C
	public override void OffsetEntity(float amount)
	{
		base.OffsetEntity(amount);
		foreach (DEN_Barnacle.Tongue tongue in this.tongues)
		{
			tongue.OffsetTargetPoint(Vector3.up * amount);
		}
	}

	// Token: 0x0400018C RID: 396
	public List<DEN_Barnacle.Tongue> tongues;

	// Token: 0x0400018D RID: 397
	public LayerMask hitMask;

	// Token: 0x0400018E RID: 398
	public LayerMask playerMask;

	// Token: 0x0400018F RID: 399
	public Transform geo;

	// Token: 0x04000190 RID: 400
	public bool spawnPolyps = true;

	// Token: 0x04000191 RID: 401
	public int polypCount = 5;

	// Token: 0x04000192 RID: 402
	public List<GameObject> polyps;

	// Token: 0x04000193 RID: 403
	public float tongueScatter = 0.1f;

	// Token: 0x04000194 RID: 404
	public float scanDistance = 5f;

	// Token: 0x04000195 RID: 405
	public float tongueGravity = 1f;

	// Token: 0x04000196 RID: 406
	public float tongueSpeed = 1f;

	// Token: 0x04000197 RID: 407
	public float tongueRetractTime = 5f;

	// Token: 0x04000198 RID: 408
	public float tongueReachTimeMin = 10f;

	// Token: 0x04000199 RID: 409
	public float tongueReachTimeMax = 40f;

	// Token: 0x0400019A RID: 410
	public float tongueWiggle = 0.1f;

	// Token: 0x0400019B RID: 411
	public float pullStrength = 0.1f;

	// Token: 0x0400019C RID: 412
	private float bodyWiggle;

	// Token: 0x0400019D RID: 413
	public float damage = 1f;

	// Token: 0x0400019E RID: 414
	public float attackRate = 0.5f;

	// Token: 0x0400019F RID: 415
	private float attackTime;

	// Token: 0x040001A0 RID: 416
	private float vomitTimer;

	// Token: 0x040001A1 RID: 417
	private bool hasVomited = true;

	// Token: 0x040001A2 RID: 418
	public bool dropOnDamage;

	// Token: 0x040001A3 RID: 419
	public ParticleSystem vomitSystem;

	// Token: 0x040001A4 RID: 420
	public ParticleSystem spikeSystem;

	// Token: 0x040001A5 RID: 421
	private Quaternion startRotation;

	// Token: 0x040001A6 RID: 422
	private bool eating;

	// Token: 0x040001A7 RID: 423
	public Animator animator;

	// Token: 0x040001A8 RID: 424
	public List<string> grabTags = new List<string> { "Player", "Prop" };

	// Token: 0x040001A9 RID: 425
	public List<string> ignoreTags = new List<string> { "BarnacleIgnore" };

	// Token: 0x040001AA RID: 426
	public List<string> vomitTags = new List<string> { "BarnacleBad" };

	// Token: 0x040001AB RID: 427
	private UT_AudioClipHandler clipHandler;

	// Token: 0x040001AC RID: 428
	private DEN_Barnacle.AudioState audioState;

	// Token: 0x02000210 RID: 528
	[Serializable]
	public class Tongue
	{
		// Token: 0x06000CE7 RID: 3303 RVA: 0x0004FD50 File Offset: 0x0004DF50
		public void Initialize(DEN_Barnacle b)
		{
			this.barnacle = b;
			this.ropeSim = new RopeSimulation(5, 5f, 0.5f, Vector3.up * this.barnacle.tongueGravity);
			this.visualObject = this.tongue.GetChild(0);
			this.tongueMaterial = this.visualObject.GetComponent<Renderer>().material;
			this.tongueDamage = this.visualObject.GetComponent<UT_Damage>();
			this.reachPoint = this.tongue.position;
			this.targetPoint = this.tongue.position;
			if (this.tongueDamage != null)
			{
				UT_Damage ut_Damage = this.tongueDamage;
				ut_Damage.DamageAction = (Action<float>)Delegate.Combine(ut_Damage.DamageAction, new Action<float>(this.DamageTongue));
				UT_Damage ut_Damage2 = this.tongueDamage;
				ut_Damage2.KillAction = (Action)Delegate.Combine(ut_Damage2.KillAction, new Action(this.KillTongue));
			}
			this.state = DEN_Barnacle.Tongue.TongueState.searching;
			this.tongueHandhold = this.tongue.GetComponentInChildren<CL_Handhold>();
		}

		// Token: 0x06000CE8 RID: 3304 RVA: 0x0004FE5E File Offset: 0x0004E05E
		public void DamageTongue(float amount)
		{
			if (this.state == DEN_Barnacle.Tongue.TongueState.searching)
			{
				this.barnacle.Damage(amount, "tongue");
			}
			this.barnacle.clipHandler.PlaySound("barnacle:hurt");
		}

		// Token: 0x06000CE9 RID: 3305 RVA: 0x0004FE90 File Offset: 0x0004E090
		public void KillTongue()
		{
			this.Retract();
			if (this.barnacle != null)
			{
				this.barnacle.clipHandler.PlaySound("barnacle:hurt");
			}
		}

		// Token: 0x06000CEA RID: 3306 RVA: 0x0004FEBC File Offset: 0x0004E0BC
		public void FindTarget()
		{
			Vector3 vector = this.tongue.forward + Random.onUnitSphere * this.barnacle.tongueScatter;
			if (CL_GameManager.IsHardmode() && ENT_Player.playerObject != null && Vector3.Distance(ENT_Player.playerObject.transform.position, this.tongue.position) < this.barnacle.scanDistance)
			{
				Vector3 velocity = ENT_Player.playerObject.cCon.velocity;
				float num = 1f;
				Vector3 vector2 = ENT_Player.playerObject.transform.position + velocity * num - this.tongue.position;
				if (Vector3.Angle(this.tongue.forward, vector2.normalized) < 60f)
				{
					vector = vector2.normalized + Random.onUnitSphere * this.barnacle.tongueScatter * 0.05f;
				}
			}
			RaycastHit raycastHit;
			if (Physics.Raycast(this.tongue.position, vector, out raycastHit, this.barnacle.scanDistance, this.barnacle.hitMask))
			{
				this.targetFound = true;
				this.targetPoint = raycastHit.point;
				this.reachVelocity += Vector3.up * this.barnacle.tongueGravity * 10f;
				this.state = DEN_Barnacle.Tongue.TongueState.searching;
				return;
			}
			this.targetFound = false;
			this.targetPoint = this.tongue.position;
		}

		// Token: 0x06000CEB RID: 3307 RVA: 0x0005005C File Offset: 0x0004E25C
		public void AnimateTongue()
		{
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.SphereOutline(this.reachPoint, 0.1f, Color.magenta);
				CL_DebugView.draw.Label2D(this.reachPoint, "State : " + this.state.ToString(), 10f, Color.magenta);
			}
			Vector3 position = this.targetPoint;
			float num = Vector3.Distance(position, this.reachPoint);
			if (this.grabbedEntity == null)
			{
				this.reachVelocity = Vector3.ClampMagnitude(position - this.reachPoint, this.barnacle.tongueSpeed) * this.barnacle.tongueSpeed + Vector3.up;
				float num2 = Mathf.PerlinNoise(this.reachPoint.x * 0.5f, this.reachPoint.y * 0.5f);
				Vector3 vector = new Vector3(num2 - 0.5f, -num2 - 0.5f, num2 - 0.5f);
				this.reachVelocity += Vector3.ClampMagnitude(vector * num, this.barnacle.tongueSpeed * 0.5f);
				this.reachPoint += this.reachVelocity * Time.deltaTime;
				if ((this.reachPoint - this.visualObject.position).MagnitudeSquared() > 0f)
				{
					this.visualObject.rotation = Quaternion.LookRotation(this.reachPoint - this.visualObject.position);
				}
				this.visualObject.localScale = new Vector3(1f, 1f, Vector3.Distance(this.visualObject.position, this.reachPoint));
				this.tongueMaterial.SetFloat("_Wiggle", this.barnacle.tongueWiggle * num * 0.25f);
				this.tongueMaterial.SetFloat("_Length", this.visualObject.localScale.z);
				this.tongueSlack = Mathf.Lerp(this.tongueSlack, num * this.barnacle.tongueGravity, Time.deltaTime);
				this.tongueMaterial.SetFloat("_Slack", this.tongueSlack);
			}
			else
			{
				if (this.grabbedEntity.IsDead())
				{
					this.grabbedEntity = null;
					return;
				}
				this.reachPoint = this.grabbedEntity.transform.position + Vector3.down * 0.5f + this.grabbedEntity.transform.forward * 0.25f;
				position = this.reachPoint;
				float num3 = Vector3.Distance(this.visualObject.position, this.reachPoint);
				this.visualObject.localScale = new Vector3(1f, 1f, num3);
				this.visualObject.rotation = Quaternion.LookRotation(this.reachPoint - this.visualObject.position);
				this.tongueMaterial.SetFloat("_Wiggle", this.barnacle.tongueWiggle * 0.5f);
				this.tongueMaterial.SetFloat("_Length", this.visualObject.localScale.z);
				this.tongueSlack = Mathf.Lerp(this.tongueSlack, 0.2f * this.barnacle.tongueGravity, Time.deltaTime);
				this.tongueMaterial.SetFloat("_Slack", this.tongueSlack);
				float num4 = Vector3.Distance(this.grabbedEntity.transform.position, this.tongue.position);
				if (num4 < 2f)
				{
					this.canBite = true;
				}
				else
				{
					float num5 = 2f;
					if (this.grabbedEntity.transform.position.y > this.barnacle.transform.position.y)
					{
						num5 = 1f;
					}
					if (num4 > this.barnacle.scanDistance * num5)
					{
						this.Retract();
					}
					this.canBite = false;
				}
			}
			if (this.barnacle.dead)
			{
				return;
			}
			switch (this.state)
			{
			case DEN_Barnacle.Tongue.TongueState.retracting:
				if (Vector3.Distance(this.reachPoint, this.targetPoint) < 0.5f)
				{
					this.state = DEN_Barnacle.Tongue.TongueState.searching;
					this.retractTime = this.barnacle.tongueRetractTime;
				}
				break;
			case DEN_Barnacle.Tongue.TongueState.reaching:
				this.reachTime -= Time.deltaTime;
				if (this.reachTime < 0f)
				{
					this.Retract();
					return;
				}
				if (num < 0.5f && !this.hasLatched)
				{
					this.hasLatched = true;
					this.barnacle.clipHandler.PlaySound("barnacle:latch", this.reachPoint);
				}
				this.tongueHandhold.climbMult = 1f;
				this.ScanForPlayer();
				break;
			case DEN_Barnacle.Tongue.TongueState.searching:
				if (this.retractTime > 0f)
				{
					this.retractTime -= Time.deltaTime;
					position = this.tongue.position;
				}
				else
				{
					if (!this.targetFound)
					{
						this.FindTarget();
					}
					if (this.targetFound)
					{
						this.Extend();
					}
				}
				break;
			case DEN_Barnacle.Tongue.TongueState.grabbing:
				this.PullTarget();
				break;
			}
			if (this.state != DEN_Barnacle.Tongue.TongueState.grabbing)
			{
				this.canBite = false;
			}
		}

		// Token: 0x06000CEC RID: 3308 RVA: 0x000505C0 File Offset: 0x0004E7C0
		public void Extend()
		{
			this.state = DEN_Barnacle.Tongue.TongueState.reaching;
			this.barnacle.clipHandler.PlaySound("barnacle:reach");
			this.goopParticle.Play();
			this.reachTime = Random.Range(this.barnacle.tongueReachTimeMin, this.barnacle.tongueReachTimeMax);
		}

		// Token: 0x06000CED RID: 3309 RVA: 0x00050618 File Offset: 0x0004E818
		public void Retract()
		{
			this.targetFound = false;
			this.state = DEN_Barnacle.Tongue.TongueState.retracting;
			if (this.grabbedEntity != null && this.grabbedEntity.GetTagger().HasTag("Player"))
			{
				ENT_Player component = this.grabbedEntity.GetComponent<ENT_Player>();
				if (component != null)
				{
					component.SetGrappled(false, this.barnacle);
				}
			}
			if (this.barnacle == null)
			{
				return;
			}
			this.grabbedEntity = null;
			if (this.barnacle.clipHandler != null)
			{
				this.barnacle.clipHandler.PlaySound("barnacle:retract");
			}
			this.tongueDamage.ResetHealth();
			this.hasLatched = false;
			this.targetPoint = this.tongue.position;
		}

		// Token: 0x06000CEE RID: 3310 RVA: 0x000506DC File Offset: 0x0004E8DC
		public void DropTarget()
		{
			if (this.grabbedEntity != null && this.grabbedEntity.GetTagger().HasTag("Player"))
			{
				ENT_Player component = this.grabbedEntity.GetComponent<ENT_Player>();
				if (component != null)
				{
					component.SetGrappled(false, this.barnacle);
				}
			}
			this.targetFound = false;
			this.state = DEN_Barnacle.Tongue.TongueState.retracting;
			this.grabbedEntity = null;
			this.barnacle.clipHandler.PlaySound("barnacle:retract", this.reachPoint);
			this.tongueDamage.ResetHealth();
			this.hasLatched = false;
			this.targetPoint = this.tongue.position;
		}

		// Token: 0x06000CEF RID: 3311 RVA: 0x00050783 File Offset: 0x0004E983
		public void Die()
		{
			this.DropTarget();
		}

		// Token: 0x06000CF0 RID: 3312 RVA: 0x0005078C File Offset: 0x0004E98C
		private void PullTarget()
		{
			if (this.grabbedEntity == null)
			{
				this.Retract();
				return;
			}
			if (CL_GameManager.IsHardmode())
			{
				this.pullStrength = Mathf.Lerp(this.pullStrength, this.barnacle.pullStrength * 1.5f, Time.deltaTime * 0.8f);
			}
			else
			{
				this.pullStrength = Mathf.Lerp(this.pullStrength, this.barnacle.pullStrength, Time.deltaTime * 0.2f);
			}
			float num = Mathf.Max(Vector3.Distance(this.tongue.position, this.grabbedEntity.transform.position) / this.barnacle.scanDistance, 1f);
			if (this.grabbedEntity.GetTagger().HasTag("Player"))
			{
				ENT_Player component = this.grabbedEntity.GetComponent<ENT_Player>();
				if (component != null)
				{
					component.SetGrappled(true, this.barnacle);
				}
			}
			this.grabbedEntity.TonguePull((this.tongue.position - this.grabbedEntity.transform.position).normalized * Time.deltaTime * this.pullStrength * num);
			if (this.grabbedEntity.transform.position.y > this.tongue.position.y)
			{
				this.tongueHandhold.climbMult = 0f;
				return;
			}
			this.tongueHandhold.climbMult = 1f;
		}

		// Token: 0x06000CF1 RID: 3313 RVA: 0x00050911 File Offset: 0x0004EB11
		private void ArriveAtTarget()
		{
		}

		// Token: 0x06000CF2 RID: 3314 RVA: 0x00050914 File Offset: 0x0004EB14
		private void ScanForPlayer()
		{
			RaycastHit raycastHit;
			if (Physics.Linecast(this.tongue.position, this.reachPoint, out raycastHit, this.barnacle.playerMask))
			{
				this.grabbedEntity = raycastHit.collider.GetComponent<GameEntity>();
				if (this.grabbedEntity == null)
				{
					return;
				}
				if (!this.grabbedEntity.GetTagger().HasTagInList(this.barnacle.grabTags.ToArray()) && this.grabbedEntity.GetTagger().HasTagInList(this.barnacle.ignoreTags.ToArray()))
				{
					this.grabbedEntity = null;
					return;
				}
				if (this.grabbedEntity.IsDead())
				{
					this.grabbedEntity = null;
					if (this.state == DEN_Barnacle.Tongue.TongueState.grabbing)
					{
						this.state = DEN_Barnacle.Tongue.TongueState.retracting;
					}
					return;
				}
				this.state = DEN_Barnacle.Tongue.TongueState.grabbing;
				this.pullStrength = 0f;
				this.grabbedEntity.Damage(0f, "Barnacle");
				this.barnacle.clipHandler.PlaySound("barnacle:grabbed-object");
			}
		}

		// Token: 0x06000CF3 RID: 3315 RVA: 0x00050A1D File Offset: 0x0004EC1D
		public GameEntity GetGrabbed()
		{
			return this.grabbedEntity;
		}

		// Token: 0x06000CF4 RID: 3316 RVA: 0x00050A25 File Offset: 0x0004EC25
		public bool CanBite()
		{
			return this.canBite;
		}

		// Token: 0x06000CF5 RID: 3317 RVA: 0x00050A2D File Offset: 0x0004EC2D
		public DEN_Barnacle.Tongue.TongueState GetState()
		{
			return this.state;
		}

		// Token: 0x06000CF6 RID: 3318 RVA: 0x00050A35 File Offset: 0x0004EC35
		public void OffsetTargetPoint(Vector3 o)
		{
			this.targetPoint += o;
			this.reachPoint += o;
		}

		// Token: 0x04000DED RID: 3565
		private DEN_Barnacle barnacle;

		// Token: 0x04000DEE RID: 3566
		public Transform tongue;

		// Token: 0x04000DEF RID: 3567
		private CL_Handhold tongueHandhold;

		// Token: 0x04000DF0 RID: 3568
		private Vector3 targetPoint;

		// Token: 0x04000DF1 RID: 3569
		private Transform hitTarget;

		// Token: 0x04000DF2 RID: 3570
		public ParticleSystem goopParticle;

		// Token: 0x04000DF3 RID: 3571
		private Vector3 reachPoint;

		// Token: 0x04000DF4 RID: 3572
		private Vector3 reachVelocity;

		// Token: 0x04000DF5 RID: 3573
		private bool targetFound;

		// Token: 0x04000DF6 RID: 3574
		private bool hasLatched;

		// Token: 0x04000DF7 RID: 3575
		private float retractTime;

		// Token: 0x04000DF8 RID: 3576
		private float reachTime;

		// Token: 0x04000DF9 RID: 3577
		private float tongueSlack;

		// Token: 0x04000DFA RID: 3578
		private float pullStrength;

		// Token: 0x04000DFB RID: 3579
		private Transform visualObject;

		// Token: 0x04000DFC RID: 3580
		private Material tongueMaterial;

		// Token: 0x04000DFD RID: 3581
		public RopeSimulation ropeSim;

		// Token: 0x04000DFE RID: 3582
		private GameEntity grabbedEntity;

		// Token: 0x04000DFF RID: 3583
		public UT_Damage tongueDamage;

		// Token: 0x04000E00 RID: 3584
		private DEN_Barnacle.Tongue.TongueState state;

		// Token: 0x04000E01 RID: 3585
		private bool canBite;

		// Token: 0x02000302 RID: 770
		public enum TongueState
		{
			// Token: 0x040012D4 RID: 4820
			retracting,
			// Token: 0x040012D5 RID: 4821
			reaching,
			// Token: 0x040012D6 RID: 4822
			searching,
			// Token: 0x040012D7 RID: 4823
			grabbing
		}
	}

	// Token: 0x02000211 RID: 529
	public enum AudioState
	{
		// Token: 0x04000E03 RID: 3587
		idle,
		// Token: 0x04000E04 RID: 3588
		pulling,
		// Token: 0x04000E05 RID: 3589
		eating
	}
}
