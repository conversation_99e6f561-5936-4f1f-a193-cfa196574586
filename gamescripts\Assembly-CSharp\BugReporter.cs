﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Runtime.CompilerServices;
using UnityEngine;

// Token: 0x020000B1 RID: 177
public class BugReporter
{
	// Token: 0x060005CB RID: 1483 RVA: 0x000300A0 File Offset: 0x0002E2A0
	public static void CreateBugReport()
	{
		try
		{
			string persistentDataPath = Application.persistentDataPath;
			string text = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
			string text2 = Path.Combine(persistentDataPath, "Reports", "Report-" + text);
			Directory.CreateDirectory(text2);
			string text3 = Path.Combine(persistentDataPath, "Player.log");
			string text4 = Path.Combine(persistentDataPath, "Player-prev.log");
			if (File.Exists(text3))
			{
				File.Copy(text3, Path.Combine(text2, "Player.log"), true);
			}
			if (File.Exists(text4))
			{
				File.Copy(text4, Path.Combine(text2, "Player-prev.log"), true);
			}
			if (File.Exists(Path.Combine(persistentDataPath, "save.json")))
			{
				File.Copy(Path.Combine(persistentDataPath, "save.json"), Path.Combine(text2, "save.json"), true);
			}
			if (File.Exists(Path.Combine(persistentDataPath, "settings.json")))
			{
				File.Copy(Path.Combine(persistentDataPath, "settings.json"), Path.Combine(text2, "settings.json"), true);
			}
			File.WriteAllText(Path.Combine(text2, "sessionreport.txt"), BugReporter.GetBugReportString());
			CL_GameManager.gMan.UnPause();
			Screenshot.TakeScreenshot(1, text2);
			string text5 = text2.Replace("/", "\\");
			Process.Start("explorer.exe", "/select," + text5);
			CL_GameManager.gMan.StartCoroutine(BugReporter.<CreateBugReport>g__WaitOneFrame|0_0());
			Debug.Log("Bug report generated successfully at: " + text2);
		}
		catch (Exception ex)
		{
			Debug.LogError("Error creating bug report: " + ex.Message);
		}
	}

	// Token: 0x060005CC RID: 1484 RVA: 0x00030238 File Offset: 0x0002E438
	public static string GetBugReportString()
	{
		string text = "";
		List<string> list = new List<string>();
		list.Add("Report: " + DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss"));
		list.Add("Version: " + Application.version);
		list.Add("");
		list.Add("Gamemode: " + CL_GameManager.GetGamemodeName(true, false));
		list.Add(string.Format("Ascent: {0}", CL_GameManager.gMan.GetPlayerAscent()));
		list.Add(string.Format("Ascent Rate: {0}", CL_GameManager.gMan.GetPlayerAscentRate()));
		list.Add(string.Format("Current Score: {0}", CL_GameManager.gMan.GetPlayerAscentRate() * CL_GameManager.gMan.GetPlayerAscent()));
		list.Add(string.Format("Player Corrected Height: {0}", CL_GameManager.gMan.GetPlayerCorrectedHeight()));
		list.Add(string.Format("Roaches: {0}", CL_GameManager.roaches));
		list.Add("");
		if (WorldLoader.initialized)
		{
			list.Add(string.Format("WorldLoader - Current Level: {0} @ {1} || pos - {2}", WorldLoader.instance.GetCurrentLevel().level.levelName, WorldLoader.instance.GetCurrentBranch().levelTracker.IndexOf(WorldLoader.instance.GetCurrentLevel()), WorldLoader.instance.GetCurrentLevel().level.transform.position));
			list.Add(string.Format("WorldLoader - Current Seed: {0}", WorldLoader.instance.seed));
			list.Add(string.Format("WorldLoader - Starting Seed: {0}", WorldLoader.instance.startingSeed));
			list.Add(string.Format("WorldLoader - Loaded Levels: {0}", WorldLoader.instance.GetCurrentBranch().levelTracker.Count));
			using (List<WorldLoader.LevelInfo>.Enumerator enumerator = WorldLoader.instance.GetCurrentBranch().levelTracker.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					WorldLoader.LevelInfo levelInfo = enumerator.Current;
					list.Add("Level - " + levelInfo.level.levelName);
				}
				goto IL_0247;
			}
		}
		list.Add("WorldLoader: Not Initialized");
		IL_0247:
		list.Add("");
		list.Add("Player Info");
		list.Add(string.Format("Player-Pos: {0}", ENT_Player.playerObject.transform.position));
		list.Add(string.Format("Player-Health: {0}", ENT_Player.playerObject.health));
		list.Add("Player-LastHit: " + ENT_Player.playerObject.GetLastHitSource());
		list.Add("--");
		list.Add(string.Format("Player-Perks: {0}", ENT_Player.playerObject.perks.Count));
		foreach (Perk perk in ENT_Player.playerObject.perks)
		{
			list.Add("Perk - " + perk.id);
		}
		list.Add("--");
		list.Add(string.Format("Player-Buffs: {0}", ENT_Player.playerObject.curBuffs.currentBuffs.Count));
		foreach (BuffContainer buffContainer in ENT_Player.playerObject.curBuffs.currentBuffs)
		{
			list.Add(string.Format("BuffContainer - {0} || Current Mult:{1}", buffContainer.id, buffContainer.GetMultiplier()));
			foreach (BuffContainer.Buff buff in buffContainer.buffs)
			{
				list.Add(string.Format("-- Buff - id: {0} || amount: {1}", buff.id, buff.amount));
			}
		}
		list.Add("");
		if (CL_ProgressionManager.instance != null)
		{
			list.Add(string.Format("Progression Unlocks: {0}", CL_ProgressionManager.GetUnlockList().Count));
			foreach (ProgressionUnlock progressionUnlock in CL_ProgressionManager.GetUnlockList())
			{
				list.Add(string.Format("Unlock - {0} || {1}", progressionUnlock.id, progressionUnlock.state));
			}
		}
		list.Add("");
		if (CL_GameManager.gMan.sessionFlags != null)
		{
			list.Add(string.Format("Session Flags: {0}", CL_GameManager.gMan.sessionFlags.Count));
			foreach (CL_GameManager.SessionFlag sessionFlag in CL_GameManager.gMan.sessionFlags)
			{
				list.Add(string.Format("Flag - {0} || {1} || {2}", sessionFlag.name, sessionFlag.state, sessionFlag.data));
			}
		}
		list.Add("");
		if (DEN_DeathFloor.instance != null)
		{
			list.Add(string.Format("DeathGoo - Active: {0}", DEN_DeathFloor.instance.IsActive()));
			list.Add(string.Format("DeathGoo - Height: {0}", DEN_DeathFloor.instance.transform.position.y));
			list.Add(string.Format("DeathGoo - Speed: {0}", DEN_DeathFloor.instance.GetCurrentSpeed()));
			list.Add(string.Format("DeathGoo - Speed Multiplier: {0}", DEN_DeathFloor.instance.GetCurrentSpeedMult()));
		}
		list.Add("");
		if (CommandConsole.instance != null)
		{
			list.Add("Command Console: Active");
			foreach (string text2 in CommandConsole.instance.GetCommandHistory())
			{
				list.Add("CC || " + text2);
			}
		}
		foreach (string text3 in list)
		{
			text += text3;
			text += "\n";
		}
		return text;
	}

	// Token: 0x060005CE RID: 1486 RVA: 0x00030948 File Offset: 0x0002EB48
	[CompilerGenerated]
	internal static IEnumerator <CreateBugReport>g__WaitOneFrame|0_0()
	{
		yield return null;
		CL_GameManager.gMan.Pause();
		yield break;
	}
}
