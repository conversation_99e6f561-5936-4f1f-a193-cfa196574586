﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

// Token: 0x0200008F RID: 143
public class HandItem_Note : HandItem
{
	// Token: 0x060004CA RID: 1226 RVA: 0x00029890 File Offset: 0x00027A90
	public override void Initialize(Item i, ENT_Player.Hand h)
	{
		base.Initialize(i, h);
		List<Item_Data_Text> allDataModulesByType = this.item.GetAllDataModulesByType<Item_Data_Text>();
		string firstDataStringByType;
		if (allDataModulesByType == null || allDataModulesByType.Count == 0)
		{
			firstDataStringByType = this.item.GetFirstDataStringByType("text", true);
		}
		else
		{
			firstDataStringByType = allDataModulesByType[0].GetText();
		}
		List<Item_Data_HandSprite> allDataModulesByType2 = this.item.GetAllDataModulesByType<Item_Data_HandSprite>();
		if (allDataModulesByType2 != null && allDataModulesByType2.Count > 0)
		{
			this.noteObject.sprite = allDataModulesByType2[0].handSprite;
		}
		this.text.text = firstDataStringByType.TrimStart();
		if (base.transform.localScale.x < 0f)
		{
			this.noteObject.transform.localScale = new Vector3(-1f, 1f, 1f);
		}
		this.noteStartPosition = this.handRoot.localPosition;
		this.noteStartScale = this.handRoot.localScale;
		if (h.id == 0)
		{
			this.zPos = -0.05f;
		}
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
	}

	// Token: 0x060004CB RID: 1227 RVA: 0x0002999F File Offset: 0x00027B9F
	public override void Use()
	{
		base.Use();
		this.isUsing = true;
		HandItem_Note.currentActiveNote = this;
		if (this.clipHandler != null)
		{
			this.clipHandler.PlaySound("note:examine");
		}
	}

	// Token: 0x060004CC RID: 1228 RVA: 0x000299D2 File Offset: 0x00027BD2
	public override void StopUse()
	{
		base.StopUse();
		this.isUsing = false;
		if (HandItem_Note.currentActiveNote == this)
		{
			HandItem_Note.currentActiveNote = null;
		}
		if (this.clipHandler != null)
		{
			this.clipHandler.PlaySound("note:examine");
		}
	}

	// Token: 0x060004CD RID: 1229 RVA: 0x00029A14 File Offset: 0x00027C14
	private void Update()
	{
		if (this.isUsing && HandItem_Note.currentActiveNote == this)
		{
			Vector3 vector = ENT_Player.playerObject.cam.transform.forward * this.zPos;
			this.handRoot.position = Vector3.Lerp(this.handRoot.position, ENT_Player.playerObject.cam.transform.position + ENT_Player.playerObject.cam.transform.forward + vector, Time.deltaTime * 5f);
			this.handRoot.localScale = Vector3.Lerp(this.handRoot.localScale, this.noteStartScale * this.useScale, Time.deltaTime * 5f);
			return;
		}
		this.handRoot.localPosition = Vector3.Lerp(this.handRoot.localPosition, this.noteStartPosition, Time.deltaTime * 5f);
		this.handRoot.localScale = Vector3.Lerp(this.handRoot.localScale, this.noteStartScale, Time.deltaTime * 5f);
	}

	// Token: 0x0400064F RID: 1615
	public List<Sprite> noteImages;

	// Token: 0x04000650 RID: 1616
	public Transform handRoot;

	// Token: 0x04000651 RID: 1617
	public SpriteRenderer noteObject;

	// Token: 0x04000652 RID: 1618
	public TMP_Text text;

	// Token: 0x04000653 RID: 1619
	private bool isUsing;

	// Token: 0x04000654 RID: 1620
	private Vector3 noteStartScale;

	// Token: 0x04000655 RID: 1621
	private Vector3 noteStartPosition;

	// Token: 0x04000656 RID: 1622
	public float useScale = 1.5f;

	// Token: 0x04000657 RID: 1623
	private float zPos;

	// Token: 0x04000658 RID: 1624
	private UT_AudioClipHandler clipHandler;

	// Token: 0x04000659 RID: 1625
	public static HandItem_Note currentActiveNote;
}
