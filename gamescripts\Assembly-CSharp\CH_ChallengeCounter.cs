﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000014 RID: 20
public class CH_ChallengeCounter : MonoBehaviour
{
	// Token: 0x06000076 RID: 118 RVA: 0x0000541B File Offset: 0x0000361B
	private void Start()
	{
		this.CheckCounter();
	}

	// Token: 0x06000077 RID: 119 RVA: 0x00005423 File Offset: 0x00003623
	public void AddToCounter(int i)
	{
		this.counter += i;
		this.CheckCounter();
	}

	// Token: 0x06000078 RID: 120 RVA: 0x0000543C File Offset: 0x0000363C
	public void CheckCounter()
	{
		using (List<CH_ChallengeCounter.ObjectiveCounter>.Enumerator enumerator = this.objectives.GetEnumerator())
		{
			while (enumerator.MoveNext())
			{
				if (!enumerator.Current.CheckCompletion(this.counter))
				{
					break;
				}
			}
		}
	}

	// Token: 0x04000076 RID: 118
	public int counter;

	// Token: 0x04000077 RID: 119
	public List<CH_ChallengeCounter.ObjectiveCounter> objectives;

	// Token: 0x020001F4 RID: 500
	[Serializable]
	public class ObjectiveCounter
	{
		// Token: 0x06000C82 RID: 3202 RVA: 0x0004ED40 File Offset: 0x0004CF40
		public bool CheckCompletion(int i)
		{
			string descString = this.GetDescString(this.objectiveDesc, i);
			string text = "yellow";
			if (i >= this.targetAmount && !this.hasCompleted)
			{
				this.hasCompleted = true;
				this.counterSuccessEvent.Invoke();
				text = "green";
				UI_ObjectiveViewer.CreateOrUpdateObjective(this.objectiveTitle, this.objectiveTitle ?? "", string.Concat(new string[] { "<color=", text, ">", descString, "</color>" }), true);
				if (this.finishedHeaderDesc != "")
				{
					string descString2 = this.GetDescString(this.finishedHeaderDesc, i);
					CL_UIManager.instance.ascentHeader.ShowText(descString2);
				}
				UI_ObjectiveViewer.PlayEarnSound();
			}
			if (!this.hasCompleted && i != this.currentCounter)
			{
				UI_ObjectiveViewer.CreateOrUpdateObjective(this.objectiveTitle, this.objectiveTitle ?? "", string.Concat(new string[] { "<color=", text, ">", descString, "</color>" }), true);
				if (i != 0)
				{
					if (this.progressHeaderDesc != "")
					{
						string descString3 = this.GetDescString(this.progressHeaderDesc, i);
						CL_UIManager.instance.ascentHeader.ShowText(descString3);
					}
					this.counterProgressEvent.Invoke();
				}
			}
			this.currentCounter = i;
			return this.hasCompleted;
		}

		// Token: 0x06000C83 RID: 3203 RVA: 0x0004EEB4 File Offset: 0x0004D0B4
		public string GetDescString(string s, int i)
		{
			int num = this.targetAmount - i;
			s = s.Replace("{count}", i.ToString()).Replace("{goal}", this.targetAmount.ToString()).Replace("{remain}", num.ToString());
			return s;
		}

		// Token: 0x04000D83 RID: 3459
		public string objectiveTitle;

		// Token: 0x04000D84 RID: 3460
		public string objectiveDesc;

		// Token: 0x04000D85 RID: 3461
		public string progressHeaderDesc;

		// Token: 0x04000D86 RID: 3462
		public string finishedHeaderDesc;

		// Token: 0x04000D87 RID: 3463
		public int targetAmount;

		// Token: 0x04000D88 RID: 3464
		public UnityEvent counterSuccessEvent;

		// Token: 0x04000D89 RID: 3465
		public UnityEvent counterProgressEvent;

		// Token: 0x04000D8A RID: 3466
		private bool hasCompleted;

		// Token: 0x04000D8B RID: 3467
		public int bonusScore;

		// Token: 0x04000D8C RID: 3468
		public int extraScore;

		// Token: 0x04000D8D RID: 3469
		private int currentCounter = -1;
	}
}
