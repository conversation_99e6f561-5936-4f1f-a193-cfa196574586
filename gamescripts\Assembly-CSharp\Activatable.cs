﻿using System;
using UnityEngine;

// Token: 0x0200014E RID: 334
public interface Activatable
{
	// Token: 0x06000982 RID: 2434
	void Deactivate();

	// Token: 0x06000983 RID: 2435
	void Activate();

	// Token: 0x06000984 RID: 2436
	void ToggleActivated();

	// Token: 0x06000985 RID: 2437 RVA: 0x00040DDC File Offset: 0x0003EFDC
	bool GetActiveState()
	{
		return false;
	}

	// Token: 0x06000986 RID: 2438
	GameObject GetGameObject();
}
