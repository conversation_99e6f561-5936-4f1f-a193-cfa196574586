﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200010E RID: 270
public class Sample_DataManager : MonoBehaviour
{
	// Token: 0x0600082E RID: 2094 RVA: 0x0003B7D0 File Offset: 0x000399D0
	private void Start()
	{
		for (int i = 0; i < 100; i++)
		{
			this.GetRandomDataClass();
		}
	}

	// Token: 0x0600082F RID: 2095 RVA: 0x0003B7F4 File Offset: 0x000399F4
	public DataClass GetRandomDataClass()
	{
		float num = 0f;
		foreach (DataClass dataClass in this.dataClasses)
		{
			num += dataClass.chance;
		}
		float num2 = Random.Range(0f, num);
		float num3 = 0f;
		foreach (DataClass dataClass2 in this.dataClasses)
		{
			num3 += dataClass2.chance;
			if (num2 <= num3)
			{
				Debug.Log(dataClass2.name);
				return dataClass2;
			}
		}
		return null;
	}

	// Token: 0x0400099C RID: 2460
	[SerializeField]
	private List<DataClass> dataClasses;
}
