﻿using System;
using UnityEngine;

// Token: 0x020001A2 RID: 418
public class UT_WorldLoaderController : MonoBehaviour
{
	// Token: 0x06000B1B RID: 2843 RVA: 0x00047FB9 File Offset: 0x000461B9
	public void SendToWorldLoader()
	{
		if (this.removeCurrentLevels && this.removePreviousLevels)
		{
			WorldLoader.instance.RestartGenerationFromLevelList(this.generationParameters);
		}
	}

	// Token: 0x06000B1C RID: 2844 RVA: 0x00047FDB File Offset: 0x000461DB
	public void UnloadPreviousLevels()
	{
		WorldLoader.instance.UnloadPreviousLevels(0);
	}

	// Token: 0x04000C22 RID: 3106
	public WorldLoader.GenerationParameters generationParameters;

	// Token: 0x04000C23 RID: 3107
	public bool removePreviousLevels;

	// Token: 0x04000C24 RID: 3108
	public bool removeCurrentLevels;

	// Token: 0x04000C25 RID: 3109
	public bool removeFutureLevels = true;
}
