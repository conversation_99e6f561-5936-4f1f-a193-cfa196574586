﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x020000F7 RID: 247
public class OS_EventSystem : MonoBehaviour
{
	// Token: 0x06000777 RID: 1911 RVA: 0x00038C6A File Offset: 0x00036E6A
	private void Start()
	{
		this.m_Raycaster = base.GetComponent<GraphicRaycaster>();
		this.m_EventSystem = base.GetComponent<EventSystem>();
	}

	// Token: 0x06000778 RID: 1912 RVA: 0x00038C84 File Offset: 0x00036E84
	private void Update()
	{
		this.PointerHandler();
	}

	// Token: 0x06000779 RID: 1913 RVA: 0x00038C8C File Offset: 0x00036E8C
	private void PointerHandler()
	{
		this.pointerData = new PointerEventData(this.m_EventSystem);
		this.pointerData.position = this.pointer.position;
		this.pointerData.delta = this.lastPointerPosition - this.pointerData.position;
		this.lastPointerPosition = this.pointer.position;
		if (Input.GetMouseButtonDown(0))
		{
			this.pointerData.button = PointerEventData.InputButton.Left;
			this.OnOSClick(this.pointerData);
		}
		if (Input.GetMouseButtonDown(1))
		{
			this.pointerData.button = PointerEventData.InputButton.Right;
			this.OnOSClick(this.pointerData);
		}
	}

	// Token: 0x0600077A RID: 1914 RVA: 0x00038D3C File Offset: 0x00036F3C
	private void OnOSClick(PointerEventData pointerEvent)
	{
		List<RaycastResult> list = new List<RaycastResult>();
		this.m_Raycaster.Raycast(this.pointerData, list);
		if (list.Count > 0)
		{
			OSPointerClickHandler component = list[0].gameObject.GetComponent<OSPointerClickHandler>();
			if (component != null)
			{
				component.OSPointerClick(pointerEvent);
			}
		}
		foreach (RaycastResult raycastResult in list)
		{
			Debug.Log("Hit " + raycastResult.gameObject.name);
		}
	}

	// Token: 0x040008EC RID: 2284
	public Transform pointer;

	// Token: 0x040008ED RID: 2285
	public PointerEventData pointerData;

	// Token: 0x040008EE RID: 2286
	public LayerMask layerMask;

	// Token: 0x040008EF RID: 2287
	private GraphicRaycaster m_Raycaster;

	// Token: 0x040008F0 RID: 2288
	private EventSystem m_EventSystem;

	// Token: 0x040008F1 RID: 2289
	private Vector2 lastPointerPosition;
}
