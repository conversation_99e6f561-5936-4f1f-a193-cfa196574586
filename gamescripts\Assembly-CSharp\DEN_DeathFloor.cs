﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using Drawing;
using UnityEngine;

// Token: 0x02000032 RID: 50
public class DEN_DeathFloor : GameEntity
{
	// Token: 0x060001CA RID: 458 RVA: 0x0000E864 File Offset: 0x0000CA64
	public override void Start()
	{
		base.transform.parent = null;
		if (!this.usePlayerStartHeight)
		{
			this.hasStarted = true;
		}
		if (DEN_DeathFloor.instance != null)
		{
			Object.Destroy(base.gameObject);
			return;
		}
		this.volumeTarget = 1f;
		DEN_DeathFloor.instance = this;
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		this.localAuds = base.GetComponentsInChildren<AudioSource>();
		this.player = ENT_Player.playerObject;
		for (int i = 0; i < this.audiosets.Count; i++)
		{
			DEN_DeathFloor.DistanceAudio distanceAudio = this.audiosets[i];
			distanceAudio.audioSource = base.gameObject.AddComponent<AudioSource>();
			distanceAudio.audioSource.volume = 0f;
			distanceAudio.audioSource.Stop();
			distanceAudio.audioSource.clip = this.audiosets[i].clip;
			distanceAudio.audioSource.loop = true;
			distanceAudio.audioSource.outputAudioMixerGroup = SettingsManager.instance.effectMixer.outputAudioMixerGroup;
		}
		this.playerDistance = float.PositiveInfinity;
		CommandConsole.AddCommand("deathgoo-stop", new Action<string[]>(this.DeathGooToggle), true);
		CommandConsole.AddCommand("deathgoo-cankill", new Action<string[]>(this.SetCanKill), true);
		CommandConsole.AddCommand("deathgoo-speed", new Action<string[]>(this.SetSpeed), true);
		CommandConsole.AddCommand("deathgoo-height", new Action<string[]>(this.SetHeight), true);
		Shader.SetGlobalTexture("_CORRUPTTEXTURE", this.corruptionTexture);
		if (CL_GameManager.IsHardmode())
		{
			this.speed *= 2f;
		}
		base.Start();
	}

	// Token: 0x060001CB RID: 459 RVA: 0x0000EA04 File Offset: 0x0000CC04
	private void Update()
	{
		this.DistanceSounds();
		if (this.player == null)
		{
			this.player = ENT_Player.playerObject;
			return;
		}
		this.playerDistance = Mathf.Abs(base.transform.position.y - this.player.transform.position.y);
		if (this.resetTime > 0f)
		{
			this.resetTime -= Time.deltaTime;
			return;
		}
		this.currentVolume = Mathf.Lerp(this.currentVolume, this.volumeTarget, Time.deltaTime);
		if (this.setCorruptionHeight)
		{
			FXManager.fxMan.corruptionHeight = base.transform.position.y;
		}
		if (!this.active)
		{
			return;
		}
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		if (this.canKill && this.player.transform.position.y < base.transform.position.y - 1f)
		{
			CL_GameManager.gMan.localPlayer.Kill(this.objectType);
			this.killedPlayer = true;
		}
		if (this.speedMult <= 0.01f)
		{
			return;
		}
		Vector3 up = Vector3.up;
		Vector3 position = base.transform.position;
		if (!this.hasStarted && CL_GameManager.gMan.GetPlayerCorrectedHeight() > this.playerStartHeight)
		{
			this.hasStarted = true;
			this.clipHandler.PlaySound("goo:roar");
			CL_CameraControl.Shake(this.roarShake);
		}
		if (this.playerDistance < this.closeShakeDistance)
		{
			CL_CameraControl.Shake(this.closeScreenShake * (1f - this.playerDistance / this.closeShakeDistance) * Time.deltaTime);
		}
		float num = 1f;
		float num2 = 1f;
		if (CL_GameManager.gamemode != null)
		{
			num = CL_GameManager.gamemode.gooSpeedIncreaseMult;
			num2 = CL_GameManager.gamemode.gooSpeedMult;
		}
		if (CL_GameManager.IsHardmode())
		{
			num2 *= 2f;
		}
		float num3 = this.speedMult * num2 * this.speedMultFrame;
		if (WorldLoader.initialized && WorldLoader.isLoaded && WorldLoader.instance.GetCurrentLevel() != null)
		{
			num3 *= WorldLoader.instance.GetCurrentLevel().level.massSpeedMult;
			if (WorldLoader.instance.GetCurrentLevel().level.subRegion != null)
			{
				num3 *= WorldLoader.instance.GetCurrentLevel().level.subRegion.massSpeedMult;
			}
		}
		float num4 = Mathf.Max(this.speed * (this.playerDistance * 0.07f * this.rubberbandMult), this.speed) * num3;
		if (this.playerDistance > 300f)
		{
			num4 *= 4f;
		}
		if (this.hasStarted)
		{
			position.x = this.player.transform.position.x;
			position.z = this.player.transform.position.z;
			position.y += Time.deltaTime * num4;
		}
		base.transform.position = position;
		float num5 = 1f;
		if (CL_GameManager.gMan.GetPlayerCorrectedHeight() > this.playerEaseHeight)
		{
			float num6 = this.maxSpeed;
			float num7 = 1f;
			if (CL_GameManager.gMan.GetPlayerCorrectedHeight() > 10000f)
			{
				num6 = (this.maxSpeed *= 8f);
			}
			else if (CL_GameManager.gMan.GetPlayerCorrectedHeight() > 5000f)
			{
				num6 = (this.maxSpeed *= 1.5f);
			}
			if (this.speed < num6)
			{
				if (num3 < 0.5f)
				{
					num5 *= num3;
				}
				this.speed += Time.deltaTime * this.speedIncreaseRate * this.speedIncreaseRateMultiplier * num * num5 * num7;
			}
		}
		if (CL_UIManager.debug)
		{
			CL_DebugView.draw.Label2D(base.transform.position, string.Format("Speed Mult: {0} - {1} {2} {3} || Adjust: {4} \n Natural Speed: {5}\nCurrent Speed: {6}\nDistance: {7}", new object[] { num3, this.speedMult, num2, this.speedMultFrame, num5, this.speed, num4, this.playerDistance }), 8f, LabelAlignment.Center, Color.red);
		}
		this.deathFloorRenderer.material.SetTextureOffset("_MainTex", new Vector2(position.z, position.x) / this.offsetScale);
	}

	// Token: 0x060001CC RID: 460 RVA: 0x0000EEA9 File Offset: 0x0000D0A9
	private void LateUpdate()
	{
		this.speedMultFrame = 1f;
	}

	// Token: 0x060001CD RID: 461 RVA: 0x0000EEB8 File Offset: 0x0000D0B8
	private void DistanceSounds()
	{
		for (int i = 0; i < this.audiosets.Count; i++)
		{
			DEN_DeathFloor.DistanceAudio distanceAudio = this.audiosets[i];
			float num = distanceAudio.maxDistance * this.maxDistanceMultiplier;
			if (this.playerDistance > num * 2f)
			{
				distanceAudio.audioSource.volume = 0f;
				distanceAudio.audioSource.Stop();
			}
			else if (ENT_Player.playerObject.IsDead())
			{
				distanceAudio.audioSource.volume = Mathf.Lerp(distanceAudio.audioSource.volume, 0f, Time.deltaTime * 8f);
			}
			else
			{
				distanceAudio.audioSource.spatialBlend = distanceAudio.spatialBlend;
				if (!distanceAudio.audioSource.isPlaying)
				{
					distanceAudio.audioSource.Play();
				}
				float num2 = this.playerDistance / num;
				float num3 = distanceAudio.volumeCurve.Evaluate(num2) * distanceAudio.volume;
				if (distanceAudio.lerpVolume)
				{
					distanceAudio.audioSource.volume = Mathf.Lerp(distanceAudio.audioSource.volume, num3 * this.currentVolume * this.globalVolume, Time.deltaTime * distanceAudio.lerpRate);
				}
				else
				{
					distanceAudio.audioSource.volume = num3 * this.currentVolume * this.globalVolume;
				}
			}
		}
	}

	// Token: 0x060001CE RID: 462 RVA: 0x0000F00C File Offset: 0x0000D20C
	public void DeathGooToggle(string[] args)
	{
		if (args.Length == 0)
		{
			this.active = !this.active;
		}
		else
		{
			bool flag;
			if (!bool.TryParse(args[0], out flag))
			{
				CommandConsole.Log("Unable to parse " + args[0] + " arg needs to be a boolean (true/false).", false);
				return;
			}
			this.active = Convert.ToBoolean(args[0]);
		}
		CommandConsole.Log("Death Goo Activity set to " + this.active.ToString(), false);
	}

	// Token: 0x060001CF RID: 463 RVA: 0x0000F080 File Offset: 0x0000D280
	public void SetHeight(string[] args)
	{
		if (args.Length == 0)
		{
			base.transform.position = this.player.transform.position - Vector3.up * 10f;
			CommandConsole.Log("Relative Goo Height set to -10", false);
			return;
		}
		base.transform.position = this.player.transform.position + Vector3.up * float.Parse(args[0]);
		CommandConsole.Log("Relative Goo Height set to " + args[0], false);
	}

	// Token: 0x060001D0 RID: 464 RVA: 0x0000F114 File Offset: 0x0000D314
	public void SetHeight(float h)
	{
		Vector3 position = this.player.transform.position;
		position.y = h;
		base.transform.position = position;
		FXManager.fxMan.corruptionHeight = base.transform.position.y;
	}

	// Token: 0x060001D1 RID: 465 RVA: 0x0000F160 File Offset: 0x0000D360
	public void MoveToHeightOfTransform(Transform t)
	{
		base.StartCoroutine(this.MoveToHeightSequence(t.position.y));
	}

	// Token: 0x060001D2 RID: 466 RVA: 0x0000F17A File Offset: 0x0000D37A
	public void RaiseOverTime(float amount)
	{
		base.StartCoroutine(this.RaiseOverTimeRoutine(amount));
	}

	// Token: 0x060001D3 RID: 467 RVA: 0x0000F18A File Offset: 0x0000D38A
	private IEnumerator RaiseOverTimeRoutine(float amount)
	{
		float time = 1f;
		while (time > 0f)
		{
			time -= Time.deltaTime;
			base.transform.position += Vector3.up * Time.deltaTime * amount;
			yield return null;
		}
		yield break;
	}

	// Token: 0x060001D4 RID: 468 RVA: 0x0000F1A0 File Offset: 0x0000D3A0
	private IEnumerator MoveToHeightSequence(float h)
	{
		while (Mathf.Abs(base.transform.position.y - h) > 0.1f)
		{
			base.transform.position = Vector3.Lerp(base.transform.position, new Vector3(base.transform.position.x, h, base.transform.position.z), Time.deltaTime * 1f);
			yield return null;
		}
		yield break;
	}

	// Token: 0x060001D5 RID: 469 RVA: 0x0000F1B8 File Offset: 0x0000D3B8
	public void SetHeightRelativeToTransform(float h, Transform t)
	{
		Vector3 position = t.position;
		position.y = t.position.y + h;
		base.transform.position = position;
	}

	// Token: 0x060001D6 RID: 470 RVA: 0x0000F1EC File Offset: 0x0000D3EC
	public void AdjustSpeed(float amount)
	{
		this.speed = Mathf.Max(this.speed + amount, 0f);
	}

	// Token: 0x060001D7 RID: 471 RVA: 0x0000F206 File Offset: 0x0000D406
	public void SetSpeedIncreaseMultiplier(float amount)
	{
		this.speedIncreaseRateMultiplier = amount;
	}

	// Token: 0x060001D8 RID: 472 RVA: 0x0000F20F File Offset: 0x0000D40F
	public void SetSpeed(string[] args)
	{
		if (args.Length == 0)
		{
			this.speed = 0f;
			CommandConsole.Log("Goo Speed set to 0", false);
			return;
		}
		this.SetSpeed(float.Parse(args[0]));
		CommandConsole.Log("Goo Speed set to " + args[0], false);
	}

	// Token: 0x060001D9 RID: 473 RVA: 0x0000F24D File Offset: 0x0000D44D
	public void SetSpeed(float s)
	{
		this.speed = s;
	}

	// Token: 0x060001DA RID: 474 RVA: 0x0000F258 File Offset: 0x0000D458
	public void SetCanKill(string[] args)
	{
		if (args.Length == 0)
		{
			this.canKill = !this.canKill;
			CommandConsole.Log("Goo Kill set to " + this.canKill.ToString(), false);
			return;
		}
		this.canKill = Convert.ToBoolean(args[0]);
		CommandConsole.Log("Goo Kill set to " + this.canKill.ToString(), false);
	}

	// Token: 0x060001DB RID: 475 RVA: 0x0000F2C0 File Offset: 0x0000D4C0
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			foreach (DEN_DeathFloor.DistanceAudio distanceAudio in this.audiosets)
			{
				if (distanceAudio != null)
				{
					Draw.Cross(base.transform.position + Vector3.up * distanceAudio.maxDistance * this.maxDistanceMultiplier, Color.white);
					Draw.Label2D(base.transform.position + Vector3.up * distanceAudio.maxDistance * this.maxDistanceMultiplier + Vector3.up * 0.1f, distanceAudio.name, Color.white);
				}
			}
		}
	}

	// Token: 0x060001DC RID: 476 RVA: 0x0000F3B4 File Offset: 0x0000D5B4
	public void SetUpdateSpeedMultiplier(float s)
	{
		this.speedMultFrame *= s;
	}

	// Token: 0x060001DD RID: 477 RVA: 0x0000F3C4 File Offset: 0x0000D5C4
	public void SetSpeedMultiplier(float s)
	{
		this.speedMult = s;
	}

	// Token: 0x060001DE RID: 478 RVA: 0x0000F3CD File Offset: 0x0000D5CD
	public void SetTargetVolume(float v)
	{
		this.volumeTarget = v;
	}

	// Token: 0x060001DF RID: 479 RVA: 0x0000F3D6 File Offset: 0x0000D5D6
	public void SetActive(bool b)
	{
		this.active = b;
	}

	// Token: 0x060001E0 RID: 480 RVA: 0x0000F3E0 File Offset: 0x0000D5E0
	public DEN_DeathFloor.SaveData GetSaveData()
	{
		return new DEN_DeathFloor.SaveData
		{
			relativeHeight = base.transform.position.y - ENT_Player.playerObject.transform.position.y,
			speed = this.speed,
			speedMult = this.speedMult,
			active = this.active
		};
	}

	// Token: 0x060001E1 RID: 481 RVA: 0x0000F444 File Offset: 0x0000D644
	public void LoadDataFromSave(DEN_DeathFloor.SaveData gooData)
	{
		base.transform.position = ENT_Player.playerObject.transform.position + Vector3.up * gooData.relativeHeight;
		this.speed = gooData.speed;
		this.speedMult = gooData.speedMult;
		this.active = gooData.active;
	}

	// Token: 0x060001E2 RID: 482 RVA: 0x0000F4A4 File Offset: 0x0000D6A4
	public float GetCurrentSpeed()
	{
		return this.speed;
	}

	// Token: 0x060001E3 RID: 483 RVA: 0x0000F4AC File Offset: 0x0000D6AC
	public float GetCurrentSpeedMult()
	{
		return this.speedMult * this.speedMultFrame;
	}

	// Token: 0x060001E4 RID: 484 RVA: 0x0000F4BB File Offset: 0x0000D6BB
	public bool IsActive()
	{
		return this.active;
	}

	// Token: 0x060001E5 RID: 485 RVA: 0x0000F4C3 File Offset: 0x0000D6C3
	public override void OffsetEntity(float amount)
	{
		base.OffsetEntity(amount);
		base.StartCoroutine(this.<OffsetEntity>g__WaitToHarm|63_0());
	}

	// Token: 0x060001E7 RID: 487 RVA: 0x0000F5B0 File Offset: 0x0000D7B0
	[CompilerGenerated]
	private IEnumerator <OffsetEntity>g__WaitToHarm|63_0()
	{
		bool wasActive = this.active;
		this.active = false;
		yield return new WaitForSeconds(1f);
		this.active = wasActive;
		yield break;
	}

	// Token: 0x040001EE RID: 494
	private ENT_Player player;

	// Token: 0x040001EF RID: 495
	public bool usePlayerStartHeight = true;

	// Token: 0x040001F0 RID: 496
	public float playerStartHeight = 16f;

	// Token: 0x040001F1 RID: 497
	public float speed = 1f;

	// Token: 0x040001F2 RID: 498
	public float maxSpeed = 2f;

	// Token: 0x040001F3 RID: 499
	public float speedIncreaseRate = 0.001f;

	// Token: 0x040001F4 RID: 500
	private float speedIncreaseRateMultiplier = 1f;

	// Token: 0x040001F5 RID: 501
	public float rubberbandMult = 1f;

	// Token: 0x040001F6 RID: 502
	public float playerEaseHeight = 150f;

	// Token: 0x040001F7 RID: 503
	public AnimationCurve playerEaseCurve;

	// Token: 0x040001F8 RID: 504
	private float playerDistance;

	// Token: 0x040001F9 RID: 505
	public MeshRenderer deathFloorRenderer;

	// Token: 0x040001FA RID: 506
	public float offsetScale = 10f;

	// Token: 0x040001FB RID: 507
	private float speedMult = 1f;

	// Token: 0x040001FC RID: 508
	private float speedMultFrame = 1f;

	// Token: 0x040001FD RID: 509
	public float roarShake = 0.06f;

	// Token: 0x040001FE RID: 510
	public List<DEN_DeathFloor.DistanceAudio> audiosets;

	// Token: 0x040001FF RID: 511
	public UT_AudioClipHandler clipHandler;

	// Token: 0x04000200 RID: 512
	public float globalVolume = 1f;

	// Token: 0x04000201 RID: 513
	private float currentVolume;

	// Token: 0x04000202 RID: 514
	private float volumeTarget = 1f;

	// Token: 0x04000203 RID: 515
	public float maxDistanceMultiplier = 1f;

	// Token: 0x04000204 RID: 516
	private AudioSource[] localAuds;

	// Token: 0x04000205 RID: 517
	private bool killedPlayer;

	// Token: 0x04000206 RID: 518
	private bool active = true;

	// Token: 0x04000207 RID: 519
	private bool canKill = true;

	// Token: 0x04000208 RID: 520
	private bool hasStarted;

	// Token: 0x04000209 RID: 521
	public static DEN_DeathFloor instance;

	// Token: 0x0400020A RID: 522
	public Texture2D corruptionTexture;

	// Token: 0x0400020B RID: 523
	public bool setCorruptionHeight = true;

	// Token: 0x0400020C RID: 524
	public float closeScreenShake;

	// Token: 0x0400020D RID: 525
	public float closeShakeDistance = 10f;

	// Token: 0x0400020E RID: 526
	public AnimationCurve closeShakeCurve;

	// Token: 0x0400020F RID: 527
	private float resetTime;

	// Token: 0x02000216 RID: 534
	[Serializable]
	public class SaveData
	{
		// Token: 0x04000E1A RID: 3610
		public float relativeHeight;

		// Token: 0x04000E1B RID: 3611
		public bool active;

		// Token: 0x04000E1C RID: 3612
		public float speed;

		// Token: 0x04000E1D RID: 3613
		public float speedMult;
	}

	// Token: 0x02000217 RID: 535
	[Serializable]
	public class DistanceAudio
	{
		// Token: 0x04000E1E RID: 3614
		public string name = "spooky sound";

		// Token: 0x04000E1F RID: 3615
		public AudioClip clip;

		// Token: 0x04000E20 RID: 3616
		public float maxDistance = 10f;

		// Token: 0x04000E21 RID: 3617
		public AnimationCurve volumeCurve;

		// Token: 0x04000E22 RID: 3618
		public float volume = 1f;

		// Token: 0x04000E23 RID: 3619
		public float spatialBlend = 1f;

		// Token: 0x04000E24 RID: 3620
		public bool lerpVolume;

		// Token: 0x04000E25 RID: 3621
		public float lerpRate = 1f;

		// Token: 0x04000E26 RID: 3622
		[HideInInspector]
		public AudioSource audioSource;
	}
}
