﻿using System;
using System.Collections.Generic;
using Sirenix.Utilities;
using UnityEngine;

// Token: 0x020000A9 RID: 169
[CreateAssetMenu(fileName = "New Generation Branch", menuName = "White Knuckle/World/Generation Branch", order = 0)]
public class M_GenerationBranch : ScriptableObject, WorldGenerator
{
	// Token: 0x06000568 RID: 1384 RVA: 0x0002CBE4 File Offset: 0x0002ADE4
	public List<M_Level> GetGenerationList(M_Region lastRegion = null, M_Region regionToGenerate = null, M_Subregion startAfterSubregion = null)
	{
		List<M_Level> list = new List<M_Level>();
		if (this.generationType == M_GenerationBranch.GenerationType.singleLevel)
		{
			list = new List<M_Level>();
			list.Add(this.playlistLevels[0]);
		}
		else if (this.generationType == M_GenerationBranch.GenerationType.levelPlaylist)
		{
			list.AddRange(this.playlistLevels);
		}
		else
		{
			if (regionToGenerate != null)
			{
				List<M_Level> regionLevels = this.GetRegionLevels(regionToGenerate, lastRegion, startAfterSubregion);
				list.AddRange(regionLevels);
				lastRegion = regionToGenerate;
				if (list.Count > this.levelsToGenerate)
				{
					list.SetLength(this.levelsToGenerate);
					this.FinalizeLevelList(ref list);
					return list;
				}
			}
			List<M_Region> list2 = new List<M_Region>();
			if (lastRegion == null || this.regions.Count == 1 || lastRegion == this.regions[this.regions.Count - 1])
			{
				list2.AddRange(this.regions);
			}
			else
			{
				bool flag = false;
				for (int i = 0; i < this.regions.Count; i++)
				{
					if (flag)
					{
						list2.Add(this.regions[i]);
					}
					else if (!flag && this.regions[i] == lastRegion)
					{
						flag = true;
					}
					if (i == this.regions.Count - 1)
					{
						list2.AddRange(this.regions);
					}
				}
			}
			while (list.Count < this.levelsToGenerate)
			{
				for (int j = 0; j < list2.Count; j++)
				{
					M_Region m_Region = list2[j];
					if (list2.Count <= 1 || !(m_Region == lastRegion))
					{
						List<M_Level> regionLevels2 = this.GetRegionLevels(m_Region, lastRegion, startAfterSubregion);
						list.AddRange(regionLevels2);
						lastRegion = m_Region;
						if (list.Count > this.levelsToGenerate)
						{
							this.FinalizeLevelList(ref list);
							return list;
						}
						if (j == list2.Count - 1)
						{
							list2 = this.regions;
						}
					}
				}
			}
		}
		this.FinalizeLevelList(ref list);
		return list;
	}

	// Token: 0x06000569 RID: 1385 RVA: 0x0002CDC2 File Offset: 0x0002AFC2
	private void FinalizeLevelList(ref List<M_Level> levelGenerationList)
	{
		if (this.branchEnd == M_GenerationBranch.BranchEnd.end)
		{
			levelGenerationList[levelGenerationList.Count - 1].pauseGeneration = true;
			return;
		}
		levelGenerationList[levelGenerationList.Count - 1].pauseGeneration = false;
	}

	// Token: 0x0600056A RID: 1386 RVA: 0x0002CDFC File Offset: 0x0002AFFC
	private List<M_Level> GetRegionLevels(M_Region region, M_Region lastRegion = null, M_Subregion lastSubregion = null)
	{
		List<M_Level> levels = region.GetLevels(lastRegion, lastSubregion);
		foreach (M_Level m_Level in levels)
		{
			if (!(m_Level == null))
			{
				m_Level.region = region;
			}
		}
		for (int i = levels.Count - 1; i >= 0; i--)
		{
			if (levels[i] == null)
			{
				levels.RemoveAt(i);
			}
		}
		return levels;
	}

	// Token: 0x04000704 RID: 1796
	public string id;

	// Token: 0x04000705 RID: 1797
	public string desc;

	// Token: 0x04000706 RID: 1798
	public M_GenerationBranch.BranchEnd branchEnd;

	// Token: 0x04000707 RID: 1799
	public List<M_Level> nextBranch;

	// Token: 0x04000708 RID: 1800
	public int levelsToGenerate = 4;

	// Token: 0x04000709 RID: 1801
	public M_GenerationBranch.GenerationType generationType;

	// Token: 0x0400070A RID: 1802
	public List<M_Level> playlistLevels;

	// Token: 0x0400070B RID: 1803
	public List<M_Region> regions;

	// Token: 0x02000266 RID: 614
	public enum GenerationType
	{
		// Token: 0x04000FB4 RID: 4020
		standard,
		// Token: 0x04000FB5 RID: 4021
		levelPlaylist,
		// Token: 0x04000FB6 RID: 4022
		shuffledLevelPlaylist,
		// Token: 0x04000FB7 RID: 4023
		endlessLevelPlaylist,
		// Token: 0x04000FB8 RID: 4024
		singleLevel,
		// Token: 0x04000FB9 RID: 4025
		singleRegion,
		// Token: 0x04000FBA RID: 4026
		shuffledRegionPlaylist
	}

	// Token: 0x02000267 RID: 615
	public enum BranchEnd
	{
		// Token: 0x04000FBC RID: 4028
		skipRegion,
		// Token: 0x04000FBD RID: 4029
		continueRegion,
		// Token: 0x04000FBE RID: 4030
		repeatBranch,
		// Token: 0x04000FBF RID: 4031
		otherBranch,
		// Token: 0x04000FC0 RID: 4032
		end
	}
}
