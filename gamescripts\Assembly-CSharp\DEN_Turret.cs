﻿using System;
using DarkMachine.AI;
using Drawing;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200003E RID: 62
public class DEN_Turret : GameEntity, Activatable
{
	// Token: 0x0600024C RID: 588 RVA: 0x00014430 File Offset: 0x00012630
	public override void Start()
	{
		this.rigid = base.GetComponent<Rigidbody>();
		base.Start();
		this.forwardStart = base.transform.InverseTransformDirection(this.turretGun.forward);
		this.barkSoundTime = this.barkSoundFrequency * Random.value + 2f;
		this.shootDelayTime = this.shootDelay;
	}

	// Token: 0x0600024D RID: 589 RVA: 0x00014490 File Offset: 0x00012690
	private void FixedUpdate()
	{
		if (!this.active)
		{
			return;
		}
		if (this.target == null)
		{
			if (ENT_Player.playerObject == null)
			{
				return;
			}
			this.target = ENT_Player.playerObject.transform;
		}
		if (this.canKnockOver)
		{
			if (this.knockedOver)
			{
				if (this.knockOverShootTime < 1f && !this.dead)
				{
					this.turretGun.localRotation = Quaternion.Lerp(this.turretGun.localRotation, Quaternion.Euler(Mathf.Cos(Time.time * this.spinScanSpeed * 3f) * this.spinScanAmount, Mathf.Sin(Time.time * this.spinScanSpeed * 5f) * this.spinScanAmount, 0f), Time.deltaTime * 5f);
					if (this.shootTime < this.shootSpeed)
					{
						this.shootTime += Time.deltaTime;
					}
					else
					{
						this.Shoot();
					}
					this.knockOverShootTime += Time.deltaTime;
					return;
				}
				if (this.knockOverShootTime >= 1f && !this.dead)
				{
					this.clipHandler.PlaySound("turret:pushover");
					this.Deactivate();
					this.dead = true;
					this.deathEvent.Invoke();
				}
				return;
			}
			else if (Vector3.Angle(base.transform.up, Vector3.up) > this.knockOverAngle)
			{
				this.knockedOver = true;
			}
		}
		this.vision.Update();
		Quaternion quaternion = Quaternion.LookRotation(this.target.position + (this.target.position - this.targetLastPos) * Time.fixedDeltaTime * this.leadMult - this.turretGun.position);
		Vector3 vector = base.transform.TransformDirection(this.forwardStart);
		float num = Vector3.Angle((this.target.position - this.turretGun.position).normalized, vector);
		bool flag = false;
		if (num < this.maxTurnAngle && this.vision.CanSeeTarget(this.target))
		{
			flag = true;
			if (!this.hasSightedTarget)
			{
				this.hasSightedTarget = true;
				this.clipHandler.PlaySound("turret:targetfound");
			}
			this.turretGun.rotation = Quaternion.Lerp(this.turretGun.rotation, quaternion, Time.deltaTime * this.lookSpeed);
			if (Vector3.Angle((this.target.position - this.turretGun.position).normalized, this.turretGun.forward) < this.maxShootAngle)
			{
				if (this.shootDelayTime > 0f)
				{
					this.shootDelayTime -= Time.deltaTime;
				}
				else
				{
					this.spreadMult = Mathf.Lerp(this.spreadMult, 1f, Time.fixedDeltaTime);
					if (this.shootTime < this.shootSpeed)
					{
						this.shootTime += Time.deltaTime;
					}
					else
					{
						this.Shoot();
					}
				}
			}
		}
		if (!flag)
		{
			this.spreadMult = Mathf.Lerp(this.spreadMult, 2f, Time.fixedDeltaTime);
			if (this.hasSightedTarget)
			{
				this.hasSightedTarget = false;
				this.clipHandler.PlaySound("turret:targetlost");
				this.shootDelayTime = this.shootDelay;
			}
			if (!this.vision.CanRemember())
			{
				this.turretGun.localRotation = Quaternion.Lerp(this.turretGun.localRotation, Quaternion.Euler(0f, Mathf.Sin(Time.time * this.spinScanSpeed) * this.spinScanAmount, 0f), Time.deltaTime * this.lookSpeed * 0.5f);
			}
			this.barkSoundTime -= Time.fixedDeltaTime;
			if (this.barkSoundTime < 0f)
			{
				this.barkSoundTime = this.barkSoundFrequency * Random.value + 2f;
				this.clipHandler.PlaySound("turret:randombark");
			}
		}
		this.targetLastPos = this.target.position;
	}

	// Token: 0x0600024E RID: 590 RVA: 0x000148B4 File Offset: 0x00012AB4
	public void Shoot()
	{
		this.shootTime = 0f;
		this.clipHandler.PlaySound("turret:shoot");
		Vector3 vector = this.turretBarrel.forward + Random.insideUnitSphere * this.bulletSpread * this.spreadMult;
		ParticleSystem.EmitParams emitParams = default(ParticleSystem.EmitParams);
		RaycastHit raycastHit;
		if (Physics.Raycast(this.turretBarrel.position, vector, out raycastHit, this.maxRange, this.shootMask))
		{
			emitParams.velocity = vector * 100f;
			emitParams.startLifetime = raycastHit.distance / 100f;
			ParticleSystem.EmitParams emitParams2 = default(ParticleSystem.EmitParams);
			emitParams2.position = raycastHit.point;
			emitParams2.applyShapeToPosition = true;
			this.hitParticles.Emit(emitParams2, 20);
			emitParams2.position = this.turretBarrel.position;
			this.flashParticles.Emit(emitParams2, 15);
			if (ObjectTagger.TagCheck(raycastHit.collider.gameObject, new string[] { "Damageable" }))
			{
				Damageable component = raycastHit.collider.GetComponent<Damageable>();
				if (component != null)
				{
					if (!this.knockedOver)
					{
						component.Damage(this.damage, this.damageType);
					}
					GameEntity component2 = raycastHit.collider.GetComponent<GameEntity>();
					if (component2 != null)
					{
						component2.AddForce(vector.normalized * 0.1f);
					}
				}
			}
		}
		else
		{
			emitParams.startLifetime = this.maxRange / 100f;
			emitParams.velocity = vector * 100f;
			ParticleSystem.EmitParams emitParams3 = default(ParticleSystem.EmitParams);
			emitParams3.applyShapeToPosition = true;
			emitParams3.position = this.turretBarrel.position;
			this.flashParticles.Emit(emitParams3, 15);
		}
		this.shootParticles.Emit(emitParams, 1);
	}

	// Token: 0x0600024F RID: 591 RVA: 0x00014A98 File Offset: 0x00012C98
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			float num = Mathf.Cos(this.maxTurnAngle * 0.00872665f);
			float num2 = Mathf.Sin(this.maxTurnAngle * 0.00872665f);
			Draw.Line(base.transform.position, base.transform.position + base.transform.forward * this.maxRange, Color.red);
			float num3 = 0.25f;
			Draw.DashedLine(base.transform.position, base.transform.position + (base.transform.forward * num + base.transform.up * num2).normalized * this.maxRange, num3, num3, Color.white);
			Draw.DashedLine(base.transform.position, base.transform.position + (base.transform.forward * num + -base.transform.up * num2).normalized * this.maxRange, num3, num3, Color.white);
			Draw.DashedLine(base.transform.position, base.transform.position + (base.transform.forward * num + -base.transform.right * num2).normalized * this.maxRange, num3, num3, Color.white);
			Draw.DashedLine(base.transform.position, base.transform.position + (base.transform.forward * num + base.transform.right * num2).normalized * this.maxRange, num3, num3, Color.white);
		}
	}

	// Token: 0x06000250 RID: 592 RVA: 0x00014CCB File Offset: 0x00012ECB
	public override void AddForce(Vector3 v)
	{
		this.prop.Unstick();
		this.rigid.AddForce(v * 3f);
		base.AddForce(v);
	}

	// Token: 0x06000251 RID: 593 RVA: 0x00014CF5 File Offset: 0x00012EF5
	public override void AddForceAtPosition(Vector3 v, Vector3 p)
	{
		this.prop.Unstick();
		base.AddForceAtPosition(v * 3f + Vector3.up * 0.5f, p);
	}

	// Token: 0x06000252 RID: 594 RVA: 0x00014D28 File Offset: 0x00012F28
	public override bool Damage(float amount, string type)
	{
		if (amount > 4f)
		{
			this.knockedOver = true;
			return true;
		}
		return false;
	}

	// Token: 0x06000253 RID: 595 RVA: 0x00014D3C File Offset: 0x00012F3C
	public override void Kill(string type = "")
	{
	}

	// Token: 0x06000254 RID: 596 RVA: 0x00014D3E File Offset: 0x00012F3E
	public void Deactivate()
	{
		if (this.dead)
		{
			return;
		}
		this.deactivateEvent.Invoke();
		this.clipHandler.PlaySound("turret:deactivate");
		this.active = false;
	}

	// Token: 0x06000255 RID: 597 RVA: 0x00014D6B File Offset: 0x00012F6B
	public void Activate()
	{
		if (this.dead)
		{
			return;
		}
		this.activateEvent.Invoke();
		this.clipHandler.PlaySound("turret:activate");
		this.active = true;
	}

	// Token: 0x06000256 RID: 598 RVA: 0x00014D98 File Offset: 0x00012F98
	public void ToggleActivated()
	{
		if (this.active)
		{
			this.Deactivate();
			return;
		}
		this.Activate();
	}

	// Token: 0x0400031C RID: 796
	private Transform target;

	// Token: 0x0400031D RID: 797
	public bool active = true;

	// Token: 0x0400031E RID: 798
	public float lookSpeed = 1f;

	// Token: 0x0400031F RID: 799
	public float maxTurnAngle = 90f;

	// Token: 0x04000320 RID: 800
	public float maxShootAngle = 10f;

	// Token: 0x04000321 RID: 801
	public float maxRange = 15f;

	// Token: 0x04000322 RID: 802
	public float shootSpeed = 1f;

	// Token: 0x04000323 RID: 803
	private float shootTime;

	// Token: 0x04000324 RID: 804
	public float shootDelay = 0.5f;

	// Token: 0x04000325 RID: 805
	private float shootDelayTime;

	// Token: 0x04000326 RID: 806
	[Header("Aim")]
	public float bulletSpread = 0.1f;

	// Token: 0x04000327 RID: 807
	public float leadMult = 1f;

	// Token: 0x04000328 RID: 808
	private float spreadMult = 1f;

	// Token: 0x04000329 RID: 809
	[Header("Damage")]
	public float damage = 0.5f;

	// Token: 0x0400032A RID: 810
	public string damageType;

	// Token: 0x0400032B RID: 811
	public LayerMask shootMask;

	// Token: 0x0400032C RID: 812
	[Header("Idle Animations")]
	public float spinScanAmount;

	// Token: 0x0400032D RID: 813
	public float spinScanSpeed = 1f;

	// Token: 0x0400032E RID: 814
	[Header("Other")]
	public bool canKnockOver = true;

	// Token: 0x0400032F RID: 815
	public float knockOverAngle = 30f;

	// Token: 0x04000330 RID: 816
	[Header("Events")]
	public UnityEvent activateEvent;

	// Token: 0x04000331 RID: 817
	public UnityEvent deactivateEvent;

	// Token: 0x04000332 RID: 818
	public UnityEvent deathEvent;

	// Token: 0x04000333 RID: 819
	public Transform turretGun;

	// Token: 0x04000334 RID: 820
	public Transform turretBarrel;

	// Token: 0x04000335 RID: 821
	public AISightComponent vision;

	// Token: 0x04000336 RID: 822
	[Header("Effects")]
	public ParticleSystem shootParticles;

	// Token: 0x04000337 RID: 823
	public ParticleSystem hitParticles;

	// Token: 0x04000338 RID: 824
	public ParticleSystem flashParticles;

	// Token: 0x04000339 RID: 825
	[Header("Audio")]
	public UT_AudioClipHandler clipHandler;

	// Token: 0x0400033A RID: 826
	public float barkSoundFrequency = 3f;

	// Token: 0x0400033B RID: 827
	private float barkSoundTime = 10f;

	// Token: 0x0400033C RID: 828
	public CL_Prop prop;

	// Token: 0x0400033D RID: 829
	private Vector3 forwardStart;

	// Token: 0x0400033E RID: 830
	private bool hasSightedTarget;

	// Token: 0x0400033F RID: 831
	private bool knockedOver;

	// Token: 0x04000340 RID: 832
	private float knockOverShootTime;

	// Token: 0x04000341 RID: 833
	private Vector3 targetLastPos;

	// Token: 0x04000342 RID: 834
	private Rigidbody rigid;
}
