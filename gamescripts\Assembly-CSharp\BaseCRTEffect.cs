﻿using System;
using UnityEngine;

// Token: 0x02000004 RID: 4
[ExecuteInEditMode]
[RequireComponent(typeof(Camera))]
public class BaseCRTEffect : MonoBehaviour
{
	// Token: 0x0600000A RID: 10 RVA: 0x000020CC File Offset: 0x000002CC
	public static void SetupDefaultPreset(BaseCRTEffect effect)
	{
		effect.blurSize = 0.7f;
		effect.blurStrength = 0.6f;
		effect.bleedingSize = 0.75f;
		effect.bleedingStrength = 0.5f;
		effect.chromaticAberrationOffset = 1.25f;
		effect.RGBMaskIntensivity = 0.6f;
		effect.RGBMaskStrength = 0.6f;
		effect.RGBMaskBleeding = 0.1f;
		effect.colorNoiseMode = BaseCRTEffect.NoiseMode.Add;
		effect.colorNoiseStrength = 0.15f;
		effect.whiteNoiseMode = BaseCRTEffect.NoiseMode.Lighten;
		effect.whiteNoiseStrength = 0.25f;
		effect.darkestLevel = Color.black;
		effect.brightestLevel = Color.Lerp(Color.black, Color.white, 0.92156863f);
		effect.darkestColor = Color.Lerp(Color.black, Color.white, 0.15686275f);
		effect.brightestColor = Color.white;
		effect.brightness = 0.2f;
		effect.contrast = 0.1f;
		effect.saturation = -0.05f;
		effect.interferenceWidth = 25f;
		effect.interferenceSpeed = 3f;
		effect.interferenceStrength = 0f;
		effect.interferenceSplit = 0.25f;
		effect.maskMode = BaseCRTEffect.MaskMode.Dense;
		effect.maskStrength = 0.35f;
		effect.curvatureX = 0.6f;
		effect.curvatureY = 0.6f;
		effect.overscan = 0f;
		effect.vignetteSize = 0.35f;
		effect.vignetteStrength = 0.1f;
		effect.textureScaling = BaseCRTEffect.TextureScalingMode.AdjustForHeight;
		effect.scalingPolicy = BaseCRTEffect.TextureScalingPolicy.DownscaleOnly;
		effect.textureSize = 768;
	}

	// Token: 0x0600000B RID: 11 RVA: 0x00002250 File Offset: 0x00000450
	public static void SetupKitchenTVPreset(BaseCRTEffect effect)
	{
		effect.blurSize = 0.7f;
		effect.blurStrength = 0.6f;
		effect.bleedingSize = 0.75f;
		effect.bleedingStrength = 0.5f;
		effect.chromaticAberrationOffset = 1.25f;
		effect.RGBMaskIntensivity = 0.4f;
		effect.RGBMaskStrength = 0.4f;
		effect.RGBMaskBleeding = 0.1f;
		effect.colorNoiseMode = BaseCRTEffect.NoiseMode.Add;
		effect.colorNoiseStrength = 0.15f;
		effect.whiteNoiseMode = BaseCRTEffect.NoiseMode.Lighten;
		effect.whiteNoiseStrength = 0.15f;
		effect.darkestLevel = Color.black;
		effect.brightestLevel = Color.Lerp(Color.black, Color.white, 0.92156863f);
		effect.darkestColor = Color.Lerp(Color.black, Color.white, 0.09803922f);
		effect.brightestColor = Color.white;
		effect.brightness = 0.1f;
		effect.contrast = 0.1f;
		effect.saturation = -0.05f;
		effect.interferenceWidth = 25f;
		effect.interferenceSpeed = 3f;
		effect.interferenceStrength = 0f;
		effect.interferenceSplit = 0.25f;
		effect.maskMode = BaseCRTEffect.MaskMode.Dense;
		effect.maskStrength = 0.25f;
		effect.curvatureX = 0.3f;
		effect.curvatureY = 0.3f;
		effect.overscan = 0f;
		effect.vignetteSize = 0.35f;
		effect.vignetteStrength = 0.1f;
		effect.textureScaling = BaseCRTEffect.TextureScalingMode.AdjustForHeight;
		effect.scalingPolicy = BaseCRTEffect.TextureScalingPolicy.DownscaleOnly;
		effect.textureSize = 768;
	}

	// Token: 0x0600000C RID: 12 RVA: 0x000023D4 File Offset: 0x000005D4
	public static void SetupMiniCRTPreset(BaseCRTEffect effect)
	{
		effect.blurSize = 0.8f;
		effect.blurStrength = 0.8f;
		effect.bleedingSize = 0.5f;
		effect.bleedingStrength = 1f;
		effect.chromaticAberrationOffset = 2.5f;
		effect.RGBMaskIntensivity = 0.8f;
		effect.RGBMaskStrength = 0.8f;
		effect.RGBMaskBleeding = 0.3f;
		effect.colorNoiseMode = BaseCRTEffect.NoiseMode.Add;
		effect.colorNoiseStrength = 0.25f;
		effect.whiteNoiseMode = BaseCRTEffect.NoiseMode.Lighten;
		effect.whiteNoiseStrength = 0.25f;
		effect.darkestLevel = Color.black;
		effect.brightestLevel = Color.Lerp(Color.black, Color.white, 0.88235295f);
		effect.darkestColor = Color.Lerp(Color.black, Color.white, 0.13725491f);
		effect.brightestColor = Color.white;
		effect.brightness = 0.3f;
		effect.contrast = 0.3f;
		effect.saturation = -0.1f;
		effect.interferenceWidth = 25f;
		effect.interferenceSpeed = 3f;
		effect.interferenceStrength = 0f;
		effect.interferenceSplit = 0.25f;
		effect.maskMode = BaseCRTEffect.MaskMode.Denser;
		effect.maskStrength = 1f;
		effect.curvatureX = 0.7f;
		effect.curvatureY = 0.7f;
		effect.overscan = 0f;
		effect.vignetteSize = 0.5f;
		effect.vignetteStrength = 0.425f;
		effect.textureScaling = BaseCRTEffect.TextureScalingMode.AdjustForHeight;
		effect.scalingPolicy = BaseCRTEffect.TextureScalingPolicy.DownscaleOnly;
		effect.textureSize = 768;
	}

	// Token: 0x0600000D RID: 13 RVA: 0x00002558 File Offset: 0x00000758
	public static void SetupColorTVPreset(BaseCRTEffect effect)
	{
		effect.blurSize = 0.9f;
		effect.blurStrength = 0.6f;
		effect.bleedingSize = 0.85f;
		effect.bleedingStrength = 0.75f;
		effect.chromaticAberrationOffset = 1.75f;
		effect.RGBMaskIntensivity = 0.4f;
		effect.RGBMaskStrength = 0.4f;
		effect.RGBMaskBleeding = 0.1f;
		effect.colorNoiseMode = BaseCRTEffect.NoiseMode.Add;
		effect.colorNoiseStrength = 0.3f;
		effect.whiteNoiseMode = BaseCRTEffect.NoiseMode.Lighten;
		effect.whiteNoiseStrength = 0.2f;
		effect.darkestLevel = Color.black;
		effect.brightestLevel = Color.Lerp(Color.black, Color.white, 0.92156863f);
		effect.darkestColor = Color.Lerp(Color.black, Color.white, 0.13725491f);
		effect.brightestColor = new Color(0.9607843f, 1f, 1f);
		effect.brightness = 0f;
		effect.contrast = 0.2f;
		effect.saturation = 0.1f;
		effect.interferenceWidth = 25f;
		effect.interferenceSpeed = 3f;
		effect.interferenceStrength = 0f;
		effect.interferenceSplit = 0.25f;
		effect.maskMode = BaseCRTEffect.MaskMode.Denser;
		effect.maskStrength = 0.2f;
		effect.curvatureX = 0.5f;
		effect.curvatureY = 0.5f;
		effect.overscan = 0.1f;
		effect.vignetteSize = 0.4f;
		effect.vignetteStrength = 0.5f;
		effect.textureScaling = BaseCRTEffect.TextureScalingMode.AdjustForHeight;
		effect.scalingPolicy = BaseCRTEffect.TextureScalingPolicy.DownscaleOnly;
		effect.textureSize = 768;
	}

	// Token: 0x0600000E RID: 14 RVA: 0x000026EC File Offset: 0x000008EC
	public static void SetupOldTVPreset(BaseCRTEffect effect)
	{
		effect.blurSize = 0.9f;
		effect.blurStrength = 0.8f;
		effect.bleedingSize = 0.95f;
		effect.bleedingStrength = 0.95f;
		effect.chromaticAberrationOffset = 1.9f;
		effect.RGBMaskIntensivity = 0.7f;
		effect.RGBMaskStrength = 0.7f;
		effect.RGBMaskBleeding = 0.3f;
		effect.colorNoiseMode = BaseCRTEffect.NoiseMode.Add;
		effect.colorNoiseStrength = 0.5f;
		effect.whiteNoiseMode = BaseCRTEffect.NoiseMode.Darken;
		effect.whiteNoiseStrength = 0.55f;
		effect.darkestLevel = Color.black;
		effect.brightestLevel = Color.Lerp(Color.black, Color.white, 0.92156863f);
		effect.darkestColor = Color.Lerp(Color.black, Color.white, 0.13725491f);
		effect.brightestColor = new Color(0.9607843f, 1f, 1f);
		effect.brightness = 0f;
		effect.contrast = -0.1f;
		effect.saturation = -0.05f;
		effect.interferenceWidth = 35f;
		effect.interferenceSpeed = 2f;
		effect.interferenceStrength = 0.075f;
		effect.interferenceSplit = 0.25f;
		effect.maskMode = BaseCRTEffect.MaskMode.Thin;
		effect.maskStrength = 0.75f;
		effect.curvatureX = 0.625f;
		effect.curvatureY = 0.625f;
		effect.overscan = 0.1f;
		effect.vignetteSize = 0.4f;
		effect.vignetteStrength = 0.5f;
		effect.textureScaling = BaseCRTEffect.TextureScalingMode.AdjustForHeight;
		effect.scalingPolicy = BaseCRTEffect.TextureScalingPolicy.DownscaleOnly;
		effect.textureSize = 768;
	}

	// Token: 0x0600000F RID: 15 RVA: 0x00002880 File Offset: 0x00000A80
	public static void SetupHighEndMonitorPreset(BaseCRTEffect effect)
	{
		effect.blurSize = 0.35f;
		effect.blurStrength = 0.5f;
		effect.bleedingSize = 0.5f;
		effect.bleedingStrength = 0.8f;
		effect.chromaticAberrationOffset = 0.5f;
		effect.RGBMaskIntensivity = 0.4f;
		effect.RGBMaskStrength = 0.4f;
		effect.RGBMaskBleeding = 0.1f;
		effect.colorNoiseMode = BaseCRTEffect.NoiseMode.Lighten;
		effect.colorNoiseStrength = 0.15f;
		effect.whiteNoiseMode = BaseCRTEffect.NoiseMode.Lighten;
		effect.whiteNoiseStrength = 0.1f;
		effect.darkestLevel = Color.black;
		effect.brightestLevel = Color.white;
		effect.darkestColor = Color.black;
		effect.brightestColor = Color.white;
		effect.brightness = 0.1f;
		effect.contrast = 0f;
		effect.saturation = 0.05f;
		effect.interferenceWidth = 25f;
		effect.interferenceSpeed = 3f;
		effect.interferenceStrength = 0f;
		effect.interferenceSplit = 0.25f;
		effect.maskMode = BaseCRTEffect.MaskMode.Thin;
		effect.maskStrength = 0.3f;
		effect.curvatureX = 0f;
		effect.curvatureY = 0f;
		effect.overscan = 0f;
		effect.vignetteSize = 0f;
		effect.vignetteStrength = 0f;
		effect.textureScaling = BaseCRTEffect.TextureScalingMode.AdjustForHeight;
		effect.scalingPolicy = BaseCRTEffect.TextureScalingPolicy.DownscaleOnly;
		effect.textureSize = 768;
	}

	// Token: 0x06000010 RID: 16 RVA: 0x000029E4 File Offset: 0x00000BE4
	public static void SetupArcadeDisplayPreset(BaseCRTEffect effect)
	{
		effect.blurSize = 0.5f;
		effect.blurStrength = 0.7f;
		effect.bleedingSize = 0.65f;
		effect.bleedingStrength = 0.8f;
		effect.chromaticAberrationOffset = 0.9f;
		effect.RGBMaskIntensivity = 0.4f;
		effect.RGBMaskStrength = 0.4f;
		effect.RGBMaskBleeding = 0.2f;
		effect.colorNoiseMode = BaseCRTEffect.NoiseMode.Lighten;
		effect.colorNoiseStrength = 0.15f;
		effect.whiteNoiseMode = BaseCRTEffect.NoiseMode.Lighten;
		effect.whiteNoiseStrength = 0.1f;
		effect.darkestLevel = Color.black;
		effect.brightestLevel = Color.white;
		effect.darkestColor = Color.black;
		effect.brightestColor = Color.white;
		effect.brightness = 0.1f;
		effect.contrast = 0.1f;
		effect.saturation = 0.1f;
		effect.interferenceWidth = 25f;
		effect.interferenceSpeed = 3f;
		effect.interferenceStrength = 0f;
		effect.interferenceSplit = 0.25f;
		effect.maskMode = BaseCRTEffect.MaskMode.Scanline;
		effect.maskStrength = 0.75f;
		effect.curvatureX = 0f;
		effect.curvatureY = 0f;
		effect.overscan = 0f;
		effect.vignetteSize = 0.3f;
		effect.vignetteStrength = 0.2f;
		effect.textureScaling = BaseCRTEffect.TextureScalingMode.AdjustForHeight;
		effect.scalingPolicy = BaseCRTEffect.TextureScalingPolicy.DownscaleOnly;
		effect.textureSize = 768;
	}

	// Token: 0x06000011 RID: 17 RVA: 0x00002B48 File Offset: 0x00000D48
	public static void SetupBrokenBlackAndWhitePreset(BaseCRTEffect effect)
	{
		effect.blurSize = 0.9f;
		effect.blurStrength = 1f;
		effect.bleedingSize = 0.75f;
		effect.bleedingStrength = 0.9f;
		effect.chromaticAberrationOffset = 2.5f;
		effect.RGBMaskIntensivity = 0.6f;
		effect.RGBMaskStrength = 0.6f;
		effect.RGBMaskBleeding = 0.1f;
		effect.colorNoiseMode = BaseCRTEffect.NoiseMode.Add;
		effect.colorNoiseStrength = 0.75f;
		effect.whiteNoiseMode = BaseCRTEffect.NoiseMode.Lighten;
		effect.whiteNoiseStrength = 0.5f;
		effect.darkestLevel = Color.Lerp(Color.black, Color.white, 0.05882353f);
		effect.brightestLevel = Color.Lerp(Color.black, Color.white, 0.88235295f);
		effect.darkestColor = Color.Lerp(Color.black, Color.white, 0.23529412f);
		effect.brightestColor = Color.white;
		effect.brightness = 0f;
		effect.contrast = -0.2f;
		effect.saturation = -1f;
		effect.interferenceWidth = 85f;
		effect.interferenceSpeed = 2.5f;
		effect.interferenceStrength = 0.05f;
		effect.interferenceSplit = 0f;
		effect.maskMode = BaseCRTEffect.MaskMode.Denser;
		effect.maskStrength = 0.15f;
		effect.curvatureX = 0.6f;
		effect.curvatureY = 0.6f;
		effect.overscan = 0.4f;
		effect.vignetteSize = 0.75f;
		effect.vignetteStrength = 0.5f;
		effect.textureScaling = BaseCRTEffect.TextureScalingMode.AdjustForHeight;
		effect.scalingPolicy = BaseCRTEffect.TextureScalingPolicy.DownscaleOnly;
		effect.textureSize = 768;
	}

	// Token: 0x06000012 RID: 18 RVA: 0x00002CDC File Offset: 0x00000EDC
	public static void SetupGreenTerminalPreset(BaseCRTEffect effect)
	{
		effect.blurSize = 0.9f;
		effect.blurStrength = 1f;
		effect.bleedingSize = 0.8f;
		effect.bleedingStrength = 0.65f;
		effect.chromaticAberrationOffset = 0f;
		effect.RGBMaskIntensivity = 0.7f;
		effect.RGBMaskStrength = 0.7f;
		effect.RGBMaskBleeding = 0.2f;
		effect.colorNoiseMode = BaseCRTEffect.NoiseMode.Add;
		effect.colorNoiseStrength = 0f;
		effect.whiteNoiseMode = BaseCRTEffect.NoiseMode.Lighten;
		effect.whiteNoiseStrength = 0f;
		effect.darkestLevel = Color.Lerp(Color.black, Color.white, 0.039215688f);
		effect.brightestLevel = Color.Lerp(Color.black, Color.white, 0.8039216f);
		effect.darkestColor = new Color(0f, 0.11764706f, 0f);
		effect.brightestColor = new Color(0.09803922f, 1f, 0.09803922f);
		effect.brightness = 0.4f;
		effect.contrast = -0.1f;
		effect.saturation = -0.8f;
		effect.interferenceWidth = 300f;
		effect.interferenceSpeed = 25f;
		effect.interferenceStrength = 0.0035f;
		effect.interferenceSplit = 0f;
		effect.maskMode = BaseCRTEffect.MaskMode.DenseScanline;
		effect.maskStrength = 0.25f;
		effect.curvatureX = 0.55f;
		effect.curvatureY = 0.55f;
		effect.overscan = 0f;
		effect.vignetteSize = 0.35f;
		effect.vignetteStrength = 0.35f;
		effect.textureScaling = BaseCRTEffect.TextureScalingMode.AdjustForHeight;
		effect.scalingPolicy = BaseCRTEffect.TextureScalingPolicy.DownscaleOnly;
		effect.textureSize = 768;
	}

	// Token: 0x06000013 RID: 19 RVA: 0x00002E7C File Offset: 0x0000107C
	public static void SetupYellowMonitorPreset(BaseCRTEffect effect)
	{
		effect.blurSize = 0.9f;
		effect.blurStrength = 0.6f;
		effect.bleedingSize = 0.85f;
		effect.bleedingStrength = 0.75f;
		effect.chromaticAberrationOffset = 1.75f;
		effect.RGBMaskIntensivity = 0.4f;
		effect.RGBMaskStrength = 0.4f;
		effect.RGBMaskBleeding = 0.1f;
		effect.colorNoiseMode = BaseCRTEffect.NoiseMode.Multiply;
		effect.colorNoiseStrength = 0.4f;
		effect.whiteNoiseMode = BaseCRTEffect.NoiseMode.Darken;
		effect.whiteNoiseStrength = 0.2f;
		effect.darkestLevel = Color.Lerp(Color.black, Color.white, 0.039215688f);
		effect.brightestLevel = Color.Lerp(Color.black, Color.white, 0.8039216f);
		effect.darkestColor = new Color(0.11764706f, 0.11764706f, 0f);
		effect.brightestColor = new Color(1f, 1f, 0.09803922f);
		effect.brightness = 0.5f;
		effect.contrast = -0.1f;
		effect.saturation = -1f;
		effect.interferenceWidth = 300f;
		effect.interferenceSpeed = 25f;
		effect.interferenceStrength = 0.0035f;
		effect.interferenceSplit = 0f;
		effect.maskMode = BaseCRTEffect.MaskMode.DenseScanline;
		effect.maskStrength = 0.25f;
		effect.curvatureX = 0.4f;
		effect.curvatureY = 0.4f;
		effect.overscan = 0f;
		effect.vignetteSize = 0.35f;
		effect.vignetteStrength = 0.35f;
		effect.textureScaling = BaseCRTEffect.TextureScalingMode.AdjustForHeight;
		effect.scalingPolicy = BaseCRTEffect.TextureScalingPolicy.DownscaleOnly;
		effect.textureSize = 768;
	}

	// Token: 0x06000014 RID: 20 RVA: 0x0000301C File Offset: 0x0000121C
	protected void InternalPreRender()
	{
		this.OnCameraPreRender();
	}

	// Token: 0x06000015 RID: 21 RVA: 0x00003024 File Offset: 0x00001224
	private void OnPostRender()
	{
		if (this.mainCamera.targetTexture != this.oldCameraTarget)
		{
			RenderTexture targetTexture = this.mainCamera.targetTexture;
			this.mainCamera.targetTexture = this.oldCameraTarget;
			this.OnCameraPostRender(targetTexture);
			if (targetTexture)
			{
				targetTexture.DiscardContents();
			}
		}
	}

	// Token: 0x06000016 RID: 22 RVA: 0x0000307C File Offset: 0x0000127C
	private void Awake()
	{
		this.mainCamera = base.GetComponent<Camera>();
		this.blurMaterial = new Material(Shader.Find("CRT/Blur"));
		this.postProMaterial = new Material(Shader.Find("CRT/Postprocess"));
		this.finalPostProMaterial = new Material(Shader.Find("CRT/FinalPostprocess"));
	}

	// Token: 0x06000017 RID: 23 RVA: 0x000030D4 File Offset: 0x000012D4
	private void Update()
	{
		this.seconds += Time.deltaTime;
		if (this.predefinedModel != this.preset)
		{
			this.preset = this.predefinedModel;
			switch (this.preset)
			{
			case BaseCRTEffect.Preset.Custom:
				break;
			case BaseCRTEffect.Preset.Default:
				BaseCRTEffect.SetupDefaultPreset(this);
				return;
			case BaseCRTEffect.Preset.KitchenTV:
				BaseCRTEffect.SetupKitchenTVPreset(this);
				return;
			case BaseCRTEffect.Preset.MiniCRT:
				BaseCRTEffect.SetupMiniCRTPreset(this);
				return;
			case BaseCRTEffect.Preset.ColorTV:
				BaseCRTEffect.SetupColorTVPreset(this);
				return;
			case BaseCRTEffect.Preset.OldTV:
				BaseCRTEffect.SetupOldTVPreset(this);
				return;
			case BaseCRTEffect.Preset.HighEndMonitor:
				BaseCRTEffect.SetupHighEndMonitorPreset(this);
				return;
			case BaseCRTEffect.Preset.ArcadeDisplay:
				BaseCRTEffect.SetupArcadeDisplayPreset(this);
				return;
			case BaseCRTEffect.Preset.BrokenBlackAndWhite:
				BaseCRTEffect.SetupBrokenBlackAndWhitePreset(this);
				return;
			case BaseCRTEffect.Preset.GreenTerminal:
				BaseCRTEffect.SetupGreenTerminalPreset(this);
				return;
			case BaseCRTEffect.Preset.YellowMonitor:
				BaseCRTEffect.SetupYellowMonitorPreset(this);
				break;
			default:
				return;
			}
		}
	}

	// Token: 0x06000018 RID: 24 RVA: 0x00003190 File Offset: 0x00001390
	private void OnDisable()
	{
		this.mainCamera.targetTexture = this.oldCameraTarget;
		if (this.otherCameras != null && this.otherCameras.Length != 0)
		{
			foreach (Camera camera in this.otherCameras)
			{
				if (!(camera == null))
				{
					camera.targetTexture = null;
				}
			}
		}
		if (this.cameraTarget != null)
		{
			this.cameraTarget.Release();
		}
		this.cameraTarget = null;
	}

	// Token: 0x06000019 RID: 25 RVA: 0x00003208 File Offset: 0x00001408
	protected virtual RenderTexture CreateCameraTexture(RenderTexture currentCameraTexture)
	{
		if (this.textureScaling == BaseCRTEffect.TextureScalingMode.Off || this.textureSize == 0)
		{
			return null;
		}
		float num = (float)Screen.width;
		float num2 = (float)Screen.height;
		int num3;
		int num4;
		if (this.textureScaling == BaseCRTEffect.TextureScalingMode.AdjustForHeight)
		{
			num3 = this.textureSize;
			num4 = Mathf.RoundToInt(num * (float)this.textureSize / num2);
		}
		else
		{
			num4 = this.textureSize;
			num3 = Mathf.RoundToInt(num2 * (float)this.textureSize / num);
		}
		if (this.scalingPolicy != BaseCRTEffect.TextureScalingPolicy.Always && (this.scalingPolicy != BaseCRTEffect.TextureScalingPolicy.DownscaleOnly || (num <= (float)num4 && num2 <= (float)num3)) && (this.scalingPolicy != BaseCRTEffect.TextureScalingPolicy.UpscaleOnly || (num >= (float)num4 && num2 >= (float)num3)))
		{
			return null;
		}
		if (currentCameraTexture == null || currentCameraTexture.width != num4 || currentCameraTexture.height != num3)
		{
			return new RenderTexture(num4, num3, 0, RenderTextureFormat.ARGB32, RenderTextureReadWrite.sRGB);
		}
		return currentCameraTexture;
	}

	// Token: 0x0600001A RID: 26 RVA: 0x000032C7 File Offset: 0x000014C7
	protected virtual void OnCameraPreRender()
	{
	}

	// Token: 0x0600001B RID: 27 RVA: 0x000032C9 File Offset: 0x000014C9
	protected virtual void OnCameraPostRender(Texture texture)
	{
	}

	// Token: 0x0600001C RID: 28 RVA: 0x000032CC File Offset: 0x000014CC
	protected void ProcessEffect(Texture src, RenderTexture dest)
	{
		RenderTexture temporary = RenderTexture.GetTemporary(src.width, src.height, 0);
		RenderTexture temporary2 = RenderTexture.GetTemporary(src.width, src.height, 0);
		float num = Mathf.Lerp(1E-05f, 1.99999f, this.blurSize);
		this.UpdateBlurKernel(num);
		float num2 = Mathf.Lerp(0.8f, 1.2f, (this.brightness + 1f) / 2f);
		float num3 = Mathf.Lerp(0.5f, 1.5f, (this.contrast + 1f) / 2f);
		float num4 = Mathf.Lerp(0f, 2f, (this.saturation + 1f) / 2f);
		this.UpdateColorMatrices(num2 - 1.5f, num3, num4);
		this.blurMaterial.SetFloat("pixelSizeX", (float)(1.0 / (double)src.width));
		this.blurMaterial.SetFloat("pixelSizeY", (float)(1.0 / (double)src.height));
		this.blurMaterial.SetFloat("blurSigma", num);
		this.blurMaterial.SetVector("blurKernel", new Vector4(this.blurKernel[0], this.blurKernel[1]));
		this.blurMaterial.SetFloat("blurZ", this.blurZ);
		this.postProMaterial.SetTexture("_BlurTex", temporary);
		this.postProMaterial.SetFloat("pixelSizeX", (float)(1.0 / (double)src.width));
		this.postProMaterial.SetFloat("pixelSizeY", (float)(1.0 / (double)src.height));
		this.postProMaterial.SetFloat("seconds", this.seconds);
		this.postProMaterial.SetFloat("blurStr", 1f - this.blurStrength);
		this.postProMaterial.SetFloat("bleedDist", this.bleedingSize);
		this.postProMaterial.SetFloat("bleedStr", this.bleedingStrength);
		this.postProMaterial.SetFloat("rgbMaskStr", Mathf.Lerp(0f, 0.3f, this.RGBMaskStrength));
		this.postProMaterial.SetFloat("rgbMaskSub", this.RGBMaskIntensivity);
		this.postProMaterial.SetFloat("rgbMaskSep", 1f - this.RGBMaskBleeding);
		this.postProMaterial.SetFloat("colorNoiseStr", Mathf.Lerp(0f, 0.4f, this.colorNoiseStrength));
		this.postProMaterial.SetInt("colorNoiseMode", (int)this.colorNoiseMode);
		this.postProMaterial.SetFloat("monoNoiseStr", Mathf.Lerp(0f, 0.4f, this.whiteNoiseStrength));
		this.postProMaterial.SetInt("monoNoiseMode", (int)this.whiteNoiseMode);
		this.postProMaterial.SetMatrix("colorMat", this.colorMat);
		this.postProMaterial.SetColor("minLevels", this.darkestLevel);
		this.postProMaterial.SetColor("maxLevels", this.brightestLevel);
		this.postProMaterial.SetColor("blackPoint", this.darkestColor);
		this.postProMaterial.SetColor("whitePoint", this.brightestColor);
		this.postProMaterial.SetFloat("interWidth", this.interferenceWidth);
		this.postProMaterial.SetFloat("interSpeed", this.interferenceSpeed);
		this.postProMaterial.SetFloat("interStr", this.interferenceStrength);
		this.postProMaterial.SetFloat("interSplit", this.interferenceSplit);
		this.postProMaterial.SetFloat("aberStr", -this.chromaticAberrationOffset);
		float num5 = Mathf.Lerp(0f, 0.45f, this.curvatureX);
		float num6 = Mathf.Lerp(0f, 0.45f, this.curvatureY);
		this.finalPostProMaterial.SetFloat("pixelSizeX", (float)(1.0 / (double)src.width));
		this.finalPostProMaterial.SetFloat("pixelSizeY", (float)(1.0 / (double)src.height));
		this.finalPostProMaterial.SetFloat("vignetteStr", this.vignetteStrength);
		this.finalPostProMaterial.SetFloat("vignetteSize", 1f - this.vignetteSize);
		this.finalPostProMaterial.SetFloat("maskStr", this.maskStrength / 10f);
		this.finalPostProMaterial.SetInt("maskMode", (int)this.maskMode);
		this.finalPostProMaterial.SetFloat("crtBendX", Mathf.Lerp(1f, 100f, (1f - num5) / Mathf.Exp(10f * num5)));
		this.finalPostProMaterial.SetFloat("crtBendY", Mathf.Lerp(1f, 100f, (1f - num6) / Mathf.Exp(10f * num6)));
		this.finalPostProMaterial.SetFloat("crtOverscan", Mathf.Lerp(0.1f, 0.25f, this.overscan));
		this.finalPostProMaterial.SetInt("flipV", (src.texelSize.y < 0f && this.cameraTarget != null) ? 1 : 0);
		if (SettingsManager.settings == null || SettingsManager.settings.crtEffect)
		{
			Graphics.Blit(src, temporary, this.blurMaterial);
			Graphics.Blit(src, temporary2, this.postProMaterial);
			if (dest == null)
			{
				this.DrawFullScreenQuad(temporary2, this.finalPostProMaterial);
			}
			else
			{
				Graphics.Blit(temporary2, dest, this.finalPostProMaterial);
			}
		}
		else
		{
			Graphics.Blit(src, temporary2, this.blurMaterial);
			if (dest == null)
			{
				this.DrawFullScreenQuad(temporary2, this.blurMaterial);
			}
			else
			{
				Graphics.Blit(temporary2, dest, this.blurMaterial);
			}
		}
		temporary.DiscardContents();
		temporary2.DiscardContents();
		RenderTexture.ReleaseTemporary(temporary);
		RenderTexture.ReleaseTemporary(temporary2);
	}

	// Token: 0x0600001D RID: 29 RVA: 0x000038BC File Offset: 0x00001ABC
	protected void DrawFullScreenQuad(Texture src, Material material)
	{
		RenderTexture.active = null;
		GL.PushMatrix();
		GL.LoadOrtho();
		GL.Viewport(new Rect(0f, 0f, (float)Screen.width, (float)Screen.height));
		material.mainTexture = src;
		for (int i = 0; i < material.passCount; i++)
		{
			material.SetPass(i);
			GL.Begin(7);
			GL.Color(Color.white);
			GL.TexCoord2(0f, 0f);
			GL.Vertex3(0f, 0f, 0.1f);
			GL.TexCoord2(1f, 0f);
			GL.Vertex3(1f, 0f, 0.1f);
			GL.TexCoord2(1f, 1f);
			GL.Vertex3(1f, 1f, 0.1f);
			GL.TexCoord2(0f, 1f);
			GL.Vertex3(0f, 1f, 0.1f);
			GL.End();
		}
		GL.PopMatrix();
		material.mainTexture = null;
	}

	// Token: 0x0600001E RID: 30 RVA: 0x000039CC File Offset: 0x00001BCC
	protected float CalculateBlurWeight(float x, float sigma)
	{
		return 0.39894f * Mathf.Exp(-0.5f * x * x / (sigma * sigma)) / sigma;
	}

	// Token: 0x0600001F RID: 31 RVA: 0x000039E8 File Offset: 0x00001BE8
	protected void UpdateBlurKernel(float sigma)
	{
		if (sigma == this.blurSigma)
		{
			return;
		}
		this.blurSigma = sigma;
		this.blurZ = 0f;
		for (int i = 0; i <= 1; i++)
		{
			float num = this.CalculateBlurWeight((float)i, sigma);
			this.blurKernel[1 - i] = num;
			if (i > 0)
			{
				this.blurZ += 2f * num;
			}
			else
			{
				this.blurZ += num;
			}
		}
		this.blurZ *= this.blurZ;
	}

	// Token: 0x06000020 RID: 32 RVA: 0x00003A70 File Offset: 0x00001C70
	protected void UpdateColorMatrices(float b, float c, float s)
	{
		bool flag = false;
		if (b != this.currentBrightness)
		{
			flag = true;
			this.currentBrightness = b;
			this.brightnessMat.SetColumn(0, new Vector4(1f, 0f, 0f, 0f));
			this.brightnessMat.SetColumn(1, new Vector4(0f, 1f, 0f, 0f));
			this.brightnessMat.SetColumn(2, new Vector4(0f, 0f, 1f, 0f));
			this.brightnessMat.SetColumn(3, new Vector4(b, b, b, 1f));
		}
		if (c != this.currentContrast)
		{
			flag = true;
			this.currentContrast = c;
			float num = (1f - this.contrast) / 2f;
			this.contrastMat.SetColumn(0, new Vector4(c, 0f, 0f, 0f));
			this.contrastMat.SetColumn(1, new Vector4(0f, c, 0f, 0f));
			this.contrastMat.SetColumn(2, new Vector4(0f, 0f, c, 0f));
			this.contrastMat.SetColumn(3, new Vector4(num, num, num, 1f));
		}
		if (s != this.currentSaturation)
		{
			flag = true;
			this.currentSaturation = s;
			Vector3 vector = new Vector3(0.3086f, 0.6094f, 0.082f);
			float num2 = 1f - s;
			Vector4 vector2 = new Vector4(vector.x * num2 + s, vector.x * num2, vector.x * num2, 0f);
			Vector4 vector3 = new Vector4(vector.y * num2, vector.y * num2 + s, vector.y * num2, 0f);
			Vector4 vector4 = new Vector4(vector.z * num2, vector.z * num2, vector.z * num2 + s, 0f);
			this.saturationMat.SetColumn(0, vector2);
			this.saturationMat.SetColumn(1, vector3);
			this.saturationMat.SetColumn(2, vector4);
			this.saturationMat.SetColumn(3, new Vector4(0f, 0f, 0f, 1f));
		}
		if (flag)
		{
			this.colorMat = this.brightnessMat * this.contrastMat * this.saturationMat;
		}
	}

	// Token: 0x04000006 RID: 6
	public BaseCRTEffect.Preset predefinedModel;

	// Token: 0x04000007 RID: 7
	protected BaseCRTEffect.Preset preset;

	// Token: 0x04000008 RID: 8
	[Header("Blur")]
	[Range(0f, 1f)]
	[Tooltip("Blur ammount. How blurry the blurred layer is.")]
	public float blurSize = 0.7f;

	// Token: 0x04000009 RID: 9
	[Range(0f, 1f)]
	[Tooltip("How much of the blurred image is mixed with the base image.")]
	public float blurStrength = 0.6f;

	// Token: 0x0400000A RID: 10
	[Header("Luminosity bleeding")]
	[Range(0f, 2f)]
	[Tooltip("How many adjacent pixels is going to be overlaped by a given brighter pixel.")]
	public float bleedingSize = 0.75f;

	// Token: 0x0400000B RID: 11
	[Range(0f, 1f)]
	[Tooltip("How much of the bleeded image is mixed with the base image.")]
	public float bleedingStrength = 0.5f;

	// Token: 0x0400000C RID: 12
	[Header("Chromatic aberration")]
	[Range(-2.5f, 2.5f)]
	[Tooltip("How many pixels the blurred layer is shifted for Red and Blue channels.")]
	public float chromaticAberrationOffset = 1.25f;

	// Token: 0x0400000D RID: 13
	[Header("RGB Mask")]
	[Range(0f, 1f)]
	[Tooltip("How much each channel blocks other channels for a given pixel. E.g. if set to 1.0 all red channel pixels will show no green or blue values.")]
	public float RGBMaskIntensivity = 0.6f;

	// Token: 0x0400000E RID: 14
	[Range(0f, 1f)]
	[Tooltip("How much of the masked image is mixed with the base image.")]
	public float RGBMaskStrength = 0.6f;

	// Token: 0x0400000F RID: 15
	[Range(0f, 1f)]
	[Tooltip("How much each channel passes through other channels for a given pixel. Basically opposite of RGB Mask Intensitity.")]
	public float RGBMaskBleeding = 0.1f;

	// Token: 0x04000010 RID: 16
	[Header("Noise")]
	[Tooltip("What blending mode is used when mixing base image pixels with color noise pixels.")]
	public BaseCRTEffect.NoiseMode colorNoiseMode;

	// Token: 0x04000011 RID: 17
	[Range(0f, 1f)]
	[Tooltip("How much of the generated color noise image is mixed with the base image.")]
	public float colorNoiseStrength = 0.15f;

	// Token: 0x04000012 RID: 18
	[Space(4f)]
	[Tooltip("What blending mode is used when mixing base image pixels with white noise pixels.")]
	public BaseCRTEffect.NoiseMode whiteNoiseMode = BaseCRTEffect.NoiseMode.Lighten;

	// Token: 0x04000013 RID: 19
	[Range(0f, 1f)]
	[Tooltip("How much of the generated white noise image is mixed with the base image.")]
	public float whiteNoiseStrength = 0.25f;

	// Token: 0x04000014 RID: 20
	[Header("Color adjustments")]
	[Tooltip("This color becomes the new darkest color (works similarly to Photoshop 'Levels' adjustment).")]
	public Color darkestLevel = Color.black;

	// Token: 0x04000015 RID: 21
	[Tooltip("This color becomes the new brightest color (works similarly to Photoshop 'Levels' adjustment).")]
	public Color brightestLevel = Color.Lerp(Color.black, Color.white, 0.92156863f);

	// Token: 0x04000016 RID: 22
	[Space(4f)]
	[Tooltip("Darkest color of the output image, makes image brigther and contrast lower, if brighter than black.")]
	public Color darkestColor = Color.Lerp(Color.black, Color.white, 0.15686275f);

	// Token: 0x04000017 RID: 23
	[Tooltip("Brightest color of the output image, makes image darker and contrast lower if darker than white.")]
	public Color brightestColor = Color.white;

	// Token: 0x04000018 RID: 24
	[Space(4f)]
	[Range(-1f, 1f)]
	[Tooltip("Brightness adjustment.")]
	public float brightness = 0.2f;

	// Token: 0x04000019 RID: 25
	[Range(-1f, 1f)]
	[Tooltip("Contrast adjustment.")]
	public float contrast = 0.1f;

	// Token: 0x0400001A RID: 26
	[Range(-1f, 1f)]
	[Tooltip("Saturation adjustment.")]
	public float saturation = -0.05f;

	// Token: 0x0400001B RID: 27
	[Header("Horizontal interference")]
	[Range(0f, 1000f)]
	[Tooltip("How wide (in pixels) is the horizontal interference bar.")]
	public float interferenceWidth = 25f;

	// Token: 0x0400001C RID: 28
	[Range(-25f, 25f)]
	[Tooltip("How fast does the horizontal interference bar move (in pixels per secons).")]
	public float interferenceSpeed = 3f;

	// Token: 0x0400001D RID: 29
	[Range(0f, 1f)]
	[Tooltip("How much of the interference is mixed with the base image.")]
	public float interferenceStrength;

	// Token: 0x0400001E RID: 30
	[Range(0f, 1f)]
	[Tooltip("How much the RGB channels of the interference are appart (in percent of interference bar width, it's basically chromatic aberration just for the interference).")]
	public float interferenceSplit = 0.25f;

	// Token: 0x0400001F RID: 31
	[Header("CRT")]
	[Tooltip("CRT mask type. Makes some pixels darker than the adjacent ones to simulate CRT display mask.")]
	public BaseCRTEffect.MaskMode maskMode = BaseCRTEffect.MaskMode.Dense;

	// Token: 0x04000020 RID: 32
	[Range(0f, 1f)]
	[Tooltip("How much of the mask is mixed with the base image.")]
	public float maskStrength = 0.35f;

	// Token: 0x04000021 RID: 33
	[Space(4f)]
	[Range(0f, 1f)]
	[Tooltip("How curvy the display is, on X-asis.")]
	public float curvatureX = 0.6f;

	// Token: 0x04000022 RID: 34
	[Range(0f, 1f)]
	[Tooltip("How curvy the display is, on Y-asis.")]
	public float curvatureY = 0.6f;

	// Token: 0x04000023 RID: 35
	[Range(0f, 1f)]
	[Tooltip("Enlarges the image.")]
	public float overscan;

	// Token: 0x04000024 RID: 36
	[Space(4f)]
	[Range(0f, 1f)]
	[Tooltip("How much of the center part is covered by vignette.")]
	public float vignetteSize = 0.35f;

	// Token: 0x04000025 RID: 37
	[Range(0f, 1f)]
	[Tooltip("How much of the vignette is mixed with the base image.")]
	public float vignetteStrength = 0.1f;

	// Token: 0x04000026 RID: 38
	[Header("Camera's texture")]
	[Tooltip("Internal texture (if used) will be created proportionally to camera's display size.")]
	public BaseCRTEffect.TextureScalingMode textureScaling = BaseCRTEffect.TextureScalingMode.AdjustForHeight;

	// Token: 0x04000027 RID: 39
	[Tooltip("Internal texture will be created only when the condition is met (e.g., for 'Downscale Only' it will be created only, if camera's display is bigger than the texture size).")]
	public BaseCRTEffect.TextureScalingPolicy scalingPolicy = BaseCRTEffect.TextureScalingPolicy.DownscaleOnly;

	// Token: 0x04000028 RID: 40
	[Range(0f, 4096f)]
	[Tooltip("How wide or high the internally used texture will be.")]
	public int textureSize = 768;

	// Token: 0x04000029 RID: 41
	[Header("Multi-camera setup workaround")]
	[Tooltip("If you're using more than one camera to render the scene (like with Pro Camera 2D parallax setup), drag your cameras onto this property and add the effect to the last camera on your rendering path.")]
	public Camera[] otherCameras;

	// Token: 0x0400002A RID: 42
	protected Camera mainCamera;

	// Token: 0x0400002B RID: 43
	public RenderTexture cameraTarget;

	// Token: 0x0400002C RID: 44
	protected RenderTexture oldCameraTarget;

	// Token: 0x0400002D RID: 45
	protected Material blurMaterial;

	// Token: 0x0400002E RID: 46
	protected Material postProMaterial;

	// Token: 0x0400002F RID: 47
	protected Material finalPostProMaterial;

	// Token: 0x04000030 RID: 48
	protected float seconds;

	// Token: 0x04000031 RID: 49
	protected float blurSigma = float.NaN;

	// Token: 0x04000032 RID: 50
	protected float[] blurKernel = new float[2];

	// Token: 0x04000033 RID: 51
	protected float blurZ = float.NaN;

	// Token: 0x04000034 RID: 52
	protected float currentBrightness = float.NaN;

	// Token: 0x04000035 RID: 53
	protected Matrix4x4 brightnessMat;

	// Token: 0x04000036 RID: 54
	protected float currentContrast = float.NaN;

	// Token: 0x04000037 RID: 55
	protected Matrix4x4 contrastMat;

	// Token: 0x04000038 RID: 56
	protected float currentSaturation = float.NaN;

	// Token: 0x04000039 RID: 57
	protected Matrix4x4 saturationMat;

	// Token: 0x0400003A RID: 58
	protected Matrix4x4 colorMat;

	// Token: 0x0400003B RID: 59
	protected Vector4 colorTransform;

	// Token: 0x020001E4 RID: 484
	public enum Preset
	{
		// Token: 0x04000D33 RID: 3379
		Custom,
		// Token: 0x04000D34 RID: 3380
		Default,
		// Token: 0x04000D35 RID: 3381
		KitchenTV,
		// Token: 0x04000D36 RID: 3382
		MiniCRT,
		// Token: 0x04000D37 RID: 3383
		ColorTV,
		// Token: 0x04000D38 RID: 3384
		OldTV,
		// Token: 0x04000D39 RID: 3385
		HighEndMonitor,
		// Token: 0x04000D3A RID: 3386
		ArcadeDisplay,
		// Token: 0x04000D3B RID: 3387
		BrokenBlackAndWhite,
		// Token: 0x04000D3C RID: 3388
		GreenTerminal,
		// Token: 0x04000D3D RID: 3389
		YellowMonitor
	}

	// Token: 0x020001E5 RID: 485
	public enum NoiseMode
	{
		// Token: 0x04000D3F RID: 3391
		Add,
		// Token: 0x04000D40 RID: 3392
		Subtract,
		// Token: 0x04000D41 RID: 3393
		Multiply,
		// Token: 0x04000D42 RID: 3394
		Divide,
		// Token: 0x04000D43 RID: 3395
		Lighten,
		// Token: 0x04000D44 RID: 3396
		Darken
	}

	// Token: 0x020001E6 RID: 486
	public enum MaskMode
	{
		// Token: 0x04000D46 RID: 3398
		Thin,
		// Token: 0x04000D47 RID: 3399
		Dense,
		// Token: 0x04000D48 RID: 3400
		Denser,
		// Token: 0x04000D49 RID: 3401
		ThinScanline,
		// Token: 0x04000D4A RID: 3402
		Scanline,
		// Token: 0x04000D4B RID: 3403
		DenseScanline
	}

	// Token: 0x020001E7 RID: 487
	public enum TextureScalingMode
	{
		// Token: 0x04000D4D RID: 3405
		Off,
		// Token: 0x04000D4E RID: 3406
		AdjustForWidth,
		// Token: 0x04000D4F RID: 3407
		AdjustForHeight
	}

	// Token: 0x020001E8 RID: 488
	public enum TextureScalingPolicy
	{
		// Token: 0x04000D51 RID: 3409
		Always,
		// Token: 0x04000D52 RID: 3410
		UpscaleOnly,
		// Token: 0x04000D53 RID: 3411
		DownscaleOnly
	}
}
