﻿using System;
using UnityEngine;

// Token: 0x020000BA RID: 186
public class DebugCameraController : MonoBehaviour
{
	// Token: 0x0600062F RID: 1583 RVA: 0x00032E22 File Offset: 0x00031022
	private void Start()
	{
		this.cam = base.GetComponent<Camera>();
		CommandConsole.AddCommand("freecam", new Action<string[]>(this.ToggleActive), true);
	}

	// Token: 0x06000630 RID: 1584 RVA: 0x00032E48 File Offset: 0x00031048
	private void Update()
	{
		if (!this.active)
		{
			return;
		}
		float axis = Input.GetAxis("Mouse ScrollWheel");
		if (Input.GetKey(KeyCode.LeftAlt))
		{
			this.turnSpeed = Mathf.Clamp(this.turnSpeed + axis * Time.deltaTime * 100f, 0f, 100f);
		}
		else if (Input.GetKey(KeyCode.LeftShift))
		{
			this.cam.fieldOfView += axis * Time.deltaTime * 10000f;
		}
		else
		{
			this.speed = Mathf.Max(this.speed + axis * 1000f * Time.deltaTime, 0f);
		}
		Vector3 vector = new Vector3(InputManager.GetVector("Move").vector.x, InputManager.GetVector("Move").vector.y, 0f);
		Vector3 vector2 = base.transform.forward * vector.y + base.transform.right * vector.x;
		vector2 += Vector3.up * InputManager.GetVector("Fly").vector.y;
		vector2 = Vector3.ClampMagnitude(vector2, 1f);
		this.targetMovement = Vector3.Lerp(this.targetMovement, vector2, Time.deltaTime * 4f);
		base.transform.position += this.targetMovement * Time.deltaTime * this.speed;
		Vector2 vector3 = new Vector2(InputManager.GetVector("Look").vector.x, InputManager.GetVector("Look").vector.y) * SettingsManager.settings.mouseSensitivity * this.turnSpeed;
		Vector3 vector4 = this.targetRotation;
		this.targetRotation = new Vector3(this.targetRotation.x - vector3.y, this.targetRotation.y + vector3.x, 0f);
		base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.Euler(this.targetRotation), Time.deltaTime * 2f);
	}

	// Token: 0x06000631 RID: 1585 RVA: 0x0003308C File Offset: 0x0003128C
	public void ToggleActive(string[] args)
	{
		if (args.Length == 0)
		{
			this.active = !this.active;
		}
		else
		{
			bool flag;
			if (!bool.TryParse(args[0], out flag))
			{
				CommandConsole.Log("Unable to parse " + args[0] + " arg needs to be a boolean (true/false).", false);
				return;
			}
			this.active = Convert.ToBoolean(args[0]);
		}
		CL_GameManager.gMan.freecam = this.active;
		this.cam.enabled = this.active;
		base.GetComponent<AudioListener>().enabled = this.active;
		base.transform.position = Camera.main.transform.position;
		base.transform.rotation = Camera.main.transform.rotation;
		CommandConsole.Log("Free Camera set to " + this.active.ToString(), false);
	}

	// Token: 0x040007B3 RID: 1971
	public float speed = 10f;

	// Token: 0x040007B4 RID: 1972
	private float turnSpeed = 1f;

	// Token: 0x040007B5 RID: 1973
	private Vector3 targetRotation;

	// Token: 0x040007B6 RID: 1974
	private Vector3 targetMovement;

	// Token: 0x040007B7 RID: 1975
	private Camera cam;

	// Token: 0x040007B8 RID: 1976
	private bool active;
}
