﻿using System;
using UnityEngine;

// Token: 0x0200019E RID: 414
public class UT_TrackObject : MonoBehaviour
{
	// Token: 0x06000B06 RID: 2822 RVA: 0x0004784C File Offset: 0x00045A4C
	private void Start()
	{
		CL_GameTracker.EventUpdate = (Action)Delegate.Combine(CL_GameTracker.EventUpdate, new Action(this.SessionUpdate));
		if (!this.active)
		{
			return;
		}
		if (this.updateType == UT_TrackObject.UpdateType.onSpawn)
		{
			CL_GameTracker.AddEventToSession(CL_GameTracker.CreateEvent(base.name, base.gameObject.GetInstanceID().ToString(), this.objectType, CL_GameTracker.Session.Tick.Event.EventType.spawn, base.transform.position));
		}
		this.lastPosition = base.transform.position;
	}

	// Token: 0x06000B07 RID: 2823 RVA: 0x000478D2 File Offset: 0x00045AD2
	private void Update()
	{
	}

	// Token: 0x06000B08 RID: 2824 RVA: 0x000478D4 File Offset: 0x00045AD4
	private void OnDestroy()
	{
		if (this.updateType == UT_TrackObject.UpdateType.onDestroy)
		{
			CL_GameTracker.AddEventToSession(CL_GameTracker.CreateEvent(base.name, base.gameObject.GetInstanceID().ToString(), this.objectType, CL_GameTracker.Session.Tick.Event.EventType.destroy, base.transform.position));
		}
		CL_GameTracker.EventUpdate = (Action)Delegate.Remove(CL_GameTracker.EventUpdate, new Action(this.SessionUpdate));
	}

	// Token: 0x06000B09 RID: 2825 RVA: 0x00047940 File Offset: 0x00045B40
	public virtual void SessionUpdate()
	{
		if ((this.updateType == UT_TrackObject.UpdateType.everything || this.updateType == UT_TrackObject.UpdateType.position) && Vector3.Distance(base.transform.position, this.lastPosition) > this.movementRecordDelta)
		{
			this.lastPosition = base.transform.position;
			CL_GameTracker.AddEventToSession(CL_GameTracker.CreateEvent(base.name, base.gameObject.GetInstanceID().ToString(), this.objectType, CL_GameTracker.Session.Tick.Event.EventType.move, base.transform.position));
		}
	}

	// Token: 0x04000C0A RID: 3082
	public string objectType = "object";

	// Token: 0x04000C0B RID: 3083
	public bool active = true;

	// Token: 0x04000C0C RID: 3084
	public UT_TrackObject.UpdateType updateType;

	// Token: 0x04000C0D RID: 3085
	public float movementRecordDelta = 0.5f;

	// Token: 0x04000C0E RID: 3086
	private Vector3 lastPosition = Vector3.zero;

	// Token: 0x020002E5 RID: 741
	public enum UpdateType
	{
		// Token: 0x04001270 RID: 4720
		everything,
		// Token: 0x04001271 RID: 4721
		onDestroy,
		// Token: 0x04001272 RID: 4722
		onSpawn,
		// Token: 0x04001273 RID: 4723
		position
	}
}
