﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020001A6 RID: 422
public class UT_CheatTrigger : MonoBehaviour
{
	// Token: 0x06000B27 RID: 2855 RVA: 0x000480F7 File Offset: 0x000462F7
	private void Update()
	{
		if (!this.hasDetectedCheats && CommandConsole.hasCheated)
		{
			this.hasDetectedCheats = true;
			this.hasCheatedEvent.Invoke();
		}
	}

	// Token: 0x04000C2A RID: 3114
	private bool hasDetectedCheats;

	// Token: 0x04000C2B RID: 3115
	public UnityEvent hasCheatedEvent;
}
