﻿using System;
using System.Collections.Generic;
using System.Globalization;
using DG.Tweening;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x0200011B RID: 283
public class UI_AnimateOnSelect : <PERSON>o<PERSON><PERSON><PERSON>our, ISelectHandler, IEventSystemHandler, IPointerClickHandler, ISubmitHandler
{
	// Token: 0x06000877 RID: 2167 RVA: 0x0003CE8E File Offset: 0x0003B08E
	private void Awake()
	{
		this.startScale = base.transform.localScale;
		this.selectable = base.GetComponent<Selectable>();
	}

	// Token: 0x06000878 RID: 2168 RVA: 0x0003CEAD File Offset: 0x0003B0AD
	public void OnPointerClick(PointerEventData eventData)
	{
		if (this.selectable && !this.selectable.IsInteractable())
		{
			return;
		}
		if (this.animateOnSubmit)
		{
			this.Animate();
		}
	}

	// Token: 0x06000879 RID: 2169 RVA: 0x0003CED8 File Offset: 0x0003B0D8
	public void OnSelect(BaseEventData eventData)
	{
		if (this.selectable && !this.selectable.IsInteractable())
		{
			return;
		}
		if (!this.animateOnSubmit)
		{
			this.Animate();
		}
	}

	// Token: 0x0600087A RID: 2170 RVA: 0x0003CF03 File Offset: 0x0003B103
	public void OnSubmit(BaseEventData eventData)
	{
		if (this.selectable && !this.selectable.IsInteractable())
		{
			return;
		}
		if (this.animateOnSubmit)
		{
			this.Animate();
		}
	}

	// Token: 0x0600087B RID: 2171 RVA: 0x0003CF2E File Offset: 0x0003B12E
	private void OnDisable()
	{
		DOTween.Complete(base.transform, false);
	}

	// Token: 0x0600087C RID: 2172 RVA: 0x0003CF40 File Offset: 0x0003B140
	private void Animate()
	{
		base.transform.DOComplete(false);
		foreach (string text in this.selectAnimations)
		{
			string[] array = text.Split(':', StringSplitOptions.None);
			string text2 = array[0];
			if (!(text2 == "punch-position"))
			{
				if (!(text2 == "punch-scale"))
				{
					if (text2 == "punch-rotation")
					{
						base.transform.DOPunchRotation(Vector3.one * float.Parse(array[1], NumberStyles.Float, CultureInfo.InvariantCulture), float.Parse(array[2], NumberStyles.Float, CultureInfo.InvariantCulture), 10, 1f).SetUpdate(true);
					}
				}
				else
				{
					base.transform.localScale = this.startScale;
					base.transform.DOPunchScale(Vector3.one * float.Parse(array[1], NumberStyles.Float, CultureInfo.InvariantCulture), float.Parse(array[2], NumberStyles.Float, CultureInfo.InvariantCulture), 10, 1f).SetUpdate(true);
				}
			}
			else
			{
				base.transform.DOPunchPosition(Vector3.one * float.Parse(array[1], NumberStyles.Float, CultureInfo.InvariantCulture), float.Parse(array[2], NumberStyles.Float, CultureInfo.InvariantCulture), 10, 1f, false).SetUpdate(true);
			}
		}
	}

	// Token: 0x040009D3 RID: 2515
	public bool animateOnSubmit;

	// Token: 0x040009D4 RID: 2516
	public List<string> selectAnimations;

	// Token: 0x040009D5 RID: 2517
	private Vector3 startScale;

	// Token: 0x040009D6 RID: 2518
	private Selectable selectable;
}
