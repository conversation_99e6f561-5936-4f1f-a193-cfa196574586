﻿using System;
using UnityEngine;

// Token: 0x0200005C RID: 92
[Serializable]
public class SessionEventModule
{
	// Token: 0x060003A2 RID: 930 RVA: 0x00022AAD File Offset: 0x00020CAD
	public virtual void Initialize(SessionEvent s)
	{
		this.sessionEvent = s;
		Debug.Log("Initialized Session event " + this.name);
	}

	// Token: 0x060003A3 RID: 931 RVA: 0x00022ACB File Offset: 0x00020CCB
	public virtual void OnModuleStart()
	{
	}

	// Token: 0x060003A4 RID: 932 RVA: 0x00022ACD File Offset: 0x00020CCD
	public virtual void OnModuleDestroy()
	{
	}

	// Token: 0x060003A5 RID: 933 RVA: 0x00022ACF File Offset: 0x00020CCF
	public virtual void Update()
	{
	}

	// Token: 0x060003A6 RID: 934 RVA: 0x00022AD1 File Offset: 0x00020CD1
	public virtual void LateUpdate()
	{
	}

	// Token: 0x060003A7 RID: 935 RVA: 0x00022AD3 File Offset: 0x00020CD3
	public virtual void OnCameraRender(Camera cam)
	{
	}

	// Token: 0x060003A8 RID: 936 RVA: 0x00022AD5 File Offset: 0x00020CD5
	public virtual void SetParentEvent(SessionEventModule m)
	{
		this.parentEvent = m;
	}

	// Token: 0x060003A9 RID: 937 RVA: 0x00022ADE File Offset: 0x00020CDE
	public virtual void SendMessage(string m)
	{
	}

	// Token: 0x060003AA RID: 938 RVA: 0x00022AE0 File Offset: 0x00020CE0
	public virtual void Activate()
	{
	}

	// Token: 0x04000500 RID: 1280
	public string name;

	// Token: 0x04000501 RID: 1281
	internal SessionEvent sessionEvent;

	// Token: 0x04000502 RID: 1282
	internal SessionEventModule parentEvent;
}
