﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000013 RID: 19
public class ObjectTagger : MonoBehaviour
{
	// Token: 0x06000070 RID: 112 RVA: 0x00005367 File Offset: 0x00003567
	public bool HasTag(string t)
	{
		return this.tags.Contains(t);
	}

	// Token: 0x06000071 RID: 113 RVA: 0x00005378 File Offset: 0x00003578
	public bool HasTagInList(string[] strings)
	{
		foreach (string text in strings)
		{
			if (this.tags.Contains(text))
			{
				return true;
			}
		}
		return false;
	}

	// Token: 0x06000072 RID: 114 RVA: 0x000053AC File Offset: 0x000035AC
	public static bool TagCheck(GameObject checkObject, string[] triggerTags)
	{
		ObjectTagger component = checkObject.GetComponent<ObjectTagger>();
		return component != null && component.HasTagInList(triggerTags);
	}

	// Token: 0x06000073 RID: 115 RVA: 0x000053D5 File Offset: 0x000035D5
	public static bool TagCheck(GameObject checkObject, string tag)
	{
		return ObjectTagger.TagCheck(checkObject, new string[] { tag });
	}

	// Token: 0x06000074 RID: 116 RVA: 0x000053E7 File Offset: 0x000035E7
	public static bool TagCheck(GameObject checkObject, string[] triggerTags, out ObjectTagger tagger)
	{
		tagger = checkObject.GetComponent<ObjectTagger>();
		return tagger != null && tagger.HasTagInList(triggerTags);
	}

	// Token: 0x04000075 RID: 117
	public List<string> tags = new List<string>();
}
