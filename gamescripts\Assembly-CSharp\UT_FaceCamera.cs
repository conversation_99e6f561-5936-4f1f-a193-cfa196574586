﻿using System;
using UnityEngine;

// Token: 0x020000D0 RID: 208
public class UT_FaceCamera : MonoBehaviour
{
	// Token: 0x060006B7 RID: 1719 RVA: 0x00035CE6 File Offset: 0x00033EE6
	private void OnEnable()
	{
		Camera.onPreRender = (Camera.CameraCallback)Delegate.Combine(Camera.onPreRender, new Camera.CameraCallback(this.OnPreRenderCallback));
	}

	// Token: 0x060006B8 RID: 1720 RVA: 0x00035D08 File Offset: 0x00033F08
	private void OnPreRenderCallback(Camera cam)
	{
		Vector3 position = cam.transform.position;
		Vector3 forward = cam.transform.forward;
		position.y *= this.verticalMult;
		Vector3 position2 = base.transform.position;
		position2.y *= this.verticalMult;
		forward.y *= this.verticalMult;
		base.transform.rotation = Quaternion.LookRotation(position2 - (position + forward * -1f), Vector3.up);
	}

	// Token: 0x060006B9 RID: 1721 RVA: 0x00035D98 File Offset: 0x00033F98
	private void OnDisable()
	{
		Camera.onPreRender = (Camera.CameraCallback)Delegate.Remove(Camera.onPreRender, new Camera.CameraCallback(this.OnPreRenderCallback));
	}

	// Token: 0x04000841 RID: 2113
	public float verticalMult = 1f;
}
