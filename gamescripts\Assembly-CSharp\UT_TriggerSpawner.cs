﻿using System;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x0200019F RID: 415
public class UT_TriggerSpawner : MonoBehaviourGizmos, SaveableObject
{
	// Token: 0x06000B0B RID: 2827 RVA: 0x000479F2 File Offset: 0x00045BF2
	private void Start()
	{
		if (this.spawnOnStart)
		{
			this.Spawn();
		}
	}

	// Token: 0x06000B0C RID: 2828 RVA: 0x00047A04 File Offset: 0x00045C04
	private void FixedUpdate()
	{
		if (CL_GameManager.gMan == null)
		{
			return;
		}
		if (!(CL_GameManager.gMan.localPlayer != null))
		{
			return;
		}
		if (CL_GameManager.gMan.PlayerDistance(base.transform.position) > this.playerVicinity)
		{
			return;
		}
		this.Spawn();
	}

	// Token: 0x06000B0D RID: 2829 RVA: 0x00047A58 File Offset: 0x00045C58
	public void Spawn()
	{
		Random.State state = Random.state;
		if (this.useSeed)
		{
			Random.InitState(this.seed);
		}
		int num = Random.Range(this.minSpawnAmount, this.maxSpawnAmount);
		for (int i = 0; i < num; i++)
		{
			if (this.useRays)
			{
				int j = 0;
				while (j < 5)
				{
					RaycastHit raycastHit;
					if (Physics.Raycast(base.transform.position, Random.onUnitSphere, out raycastHit, this.spawnRange, this.spawnLayerMask))
					{
						if (this.spawnTable != null)
						{
							Object.Instantiate<GameObject>(this.spawnTable.GetRandomSpawnObject().GetRandomPrefab(), raycastHit.point + raycastHit.normal * 0.05f, Quaternion.LookRotation(raycastHit.normal), base.transform.parent);
							break;
						}
						Object.Instantiate<GameObject>(this.spawnObjects[Random.Range(0, this.spawnObjects.Count)], raycastHit.point + raycastHit.normal * 0.05f, Quaternion.LookRotation(raycastHit.normal), base.transform.parent);
						break;
					}
					else if (j == 5)
					{
						if (this.spawnTable != null)
						{
							Object.Instantiate<GameObject>(this.spawnTable.GetRandomSpawnObject().GetRandomPrefab(), base.transform.position + Random.insideUnitSphere * this.spawnRange, base.transform.rotation, base.transform.parent);
							break;
						}
						Object.Instantiate<GameObject>(this.spawnObjects[Random.Range(0, this.spawnObjects.Count)], base.transform.position + Random.insideUnitSphere * this.spawnRange, base.transform.rotation, base.transform.parent);
						break;
					}
					else
					{
						j++;
					}
				}
			}
			else if (this.spawnTable != null)
			{
				Object.Instantiate<GameObject>(this.spawnTable.GetRandomSpawnObject().GetRandomPrefab(), base.transform.position + Random.insideUnitSphere * this.spawnRange, base.transform.rotation, base.transform.parent);
			}
			else
			{
				Object.Instantiate<GameObject>(this.spawnObjects[Random.Range(0, this.spawnObjects.Count)], base.transform.position + Random.insideUnitSphere * this.spawnRange, base.transform.rotation, base.transform.parent);
			}
		}
		base.gameObject.SetActive(false);
		if (this.useSeed)
		{
			Random.state = state;
		}
	}

	// Token: 0x06000B0E RID: 2830 RVA: 0x00047D40 File Offset: 0x00045F40
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			Draw.SphereOutline(base.transform.position, this.playerVicinity, new Color(1f, 0.561f, 0f, 0.25f));
			Draw.SphereOutline(base.transform.position, this.spawnRange, Color.red);
			return;
		}
		Draw.SphereOutline(base.transform.position, this.playerVicinity, new Color(1f, 0.561f, 0f, 0.003f));
	}

	// Token: 0x06000B0F RID: 2831 RVA: 0x00047DDE File Offset: 0x00045FDE
	public bool CanSave()
	{
		return this.saveState;
	}

	// Token: 0x06000B10 RID: 2832 RVA: 0x00047DE6 File Offset: 0x00045FE6
	public string GetSaveID()
	{
		return this.saveStateID;
	}

	// Token: 0x06000B11 RID: 2833 RVA: 0x00047DF0 File Offset: 0x00045FF0
	public SaveableInfo GetSaveInfo()
	{
		return new SaveableInfo
		{
			id = this.saveStateID,
			data = base.gameObject.activeSelf.ToString()
		};
	}

	// Token: 0x06000B12 RID: 2834 RVA: 0x00047E27 File Offset: 0x00046027
	public void SetSaveInfo(SaveableInfo info)
	{
		if (!bool.Parse(info.data))
		{
			base.gameObject.SetActive(false);
		}
	}

	// Token: 0x04000C0F RID: 3087
	public float playerVicinity = 10f;

	// Token: 0x04000C10 RID: 3088
	public float spawnRange = 1f;

	// Token: 0x04000C11 RID: 3089
	public SpawnTable spawnTable;

	// Token: 0x04000C12 RID: 3090
	public List<GameObject> spawnObjects;

	// Token: 0x04000C13 RID: 3091
	public int minSpawnAmount = 1;

	// Token: 0x04000C14 RID: 3092
	public int maxSpawnAmount = 5;

	// Token: 0x04000C15 RID: 3093
	public bool useRays;

	// Token: 0x04000C16 RID: 3094
	public LayerMask spawnLayerMask;

	// Token: 0x04000C17 RID: 3095
	public bool spawnOnStart;

	// Token: 0x04000C18 RID: 3096
	public bool saveState;

	// Token: 0x04000C19 RID: 3097
	public string saveStateID = "";

	// Token: 0x04000C1A RID: 3098
	public bool useSeed;

	// Token: 0x04000C1B RID: 3099
	public int seed;
}
