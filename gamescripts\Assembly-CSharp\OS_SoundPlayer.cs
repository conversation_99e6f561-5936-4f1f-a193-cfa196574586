﻿using System;
using UnityEngine;

// Token: 0x02000100 RID: 256
public class OS_SoundPlayer : MonoBehaviour
{
	// Token: 0x060007D7 RID: 2007 RVA: 0x0003A96F File Offset: 0x00038B6F
	private void OnEnable()
	{
		if (this.playOnAwake)
		{
			this.Play();
		}
	}

	// Token: 0x060007D8 RID: 2008 RVA: 0x0003A97F File Offset: 0x00038B7F
	public void Play()
	{
		if (OS_Manager.soundPlayer == null)
		{
			return;
		}
		OS_Manager.soundPlayer.PlaySound("os:" + this.soundName);
	}

	// Token: 0x04000952 RID: 2386
	public string soundName;

	// Token: 0x04000953 RID: 2387
	public bool loop;

	// Token: 0x04000954 RID: 2388
	public bool playOnAwake;
}
