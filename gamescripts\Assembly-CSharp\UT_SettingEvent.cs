﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200018F RID: 399
public class UT_SettingEvent : MonoBehaviour
{
	// Token: 0x06000AD1 RID: 2769 RVA: 0x00046B1D File Offset: 0x00044D1D
	private void Start()
	{
		if (bool.Parse(SettingsManager.GetSetting(this.settingName)))
		{
			this.activeEvent.Invoke();
			return;
		}
		this.inactiveEvent.Invoke();
	}

	// Token: 0x04000BD0 RID: 3024
	public string settingName;

	// Token: 0x04000BD1 RID: 3025
	public UnityEvent activeEvent;

	// Token: 0x04000BD2 RID: 3026
	public UnityEvent inactiveEvent;
}
