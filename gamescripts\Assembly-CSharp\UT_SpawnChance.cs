﻿using System;
using UnityEngine;

// Token: 0x02000196 RID: 406
public class UT_SpawnChance : MonoBehaviour
{
	// Token: 0x06000AEC RID: 2796 RVA: 0x00047220 File Offset: 0x00045420
	private void Start()
	{
		float num = 1f;
		if (this.useAscent)
		{
			float num2 = CL_GameManager.gMan.GetPlayerAscent() - this.ascentMin;
			num2 = Mathf.Clamp01(num2 / (this.ascentMax - this.ascentMin));
			num *= this.ascentSpawnModifier.Evaluate(num2);
		}
		if (this.useDifficulty)
		{
			float num3 = Mathf.Clamp01((CL_GameManager.currentDifficulty - this.difficultyMin) / (this.difficultyMax - this.difficultyMin));
			num *= num3;
		}
		num *= this.spawnSettings.GetEffectiveSpawnChance();
		this.chanceDebug = num;
		if (Random.value > this.chance * num)
		{
			base.gameObject.SetActive(false);
		}
	}

	// Token: 0x04000BE9 RID: 3049
	[Range(0f, 1f)]
	public float chance = 1f;

	// Token: 0x04000BEA RID: 3050
	public bool useDifficulty;

	// Token: 0x04000BEB RID: 3051
	public float difficultyMin;

	// Token: 0x04000BEC RID: 3052
	public float difficultyMax = 1f;

	// Token: 0x04000BED RID: 3053
	public AnimationCurve difficultySpawnModifier;

	// Token: 0x04000BEE RID: 3054
	public bool useAscent;

	// Token: 0x04000BEF RID: 3055
	public float ascentMin;

	// Token: 0x04000BF0 RID: 3056
	public float ascentMax = 1f;

	// Token: 0x04000BF1 RID: 3057
	public AnimationCurve ascentSpawnModifier;

	// Token: 0x04000BF2 RID: 3058
	public SpawnTable.SpawnSettings spawnSettings;

	// Token: 0x04000BF3 RID: 3059
	private float chanceDebug;
}
