﻿using System;
using UnityEngine;

// Token: 0x020000D4 RID: 212
public class UT_LerpBetween : MonoBehaviour
{
	// Token: 0x060006C7 RID: 1735 RVA: 0x00035F00 File Offset: 0x00034100
	private void Update()
	{
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		base.transform.position = Vector3.Lerp(this.o1.position, this.o2.position, this.lerp);
	}

	// Token: 0x04000848 RID: 2120
	public Transform o1;

	// Token: 0x04000849 RID: 2121
	public Transform o2;

	// Token: 0x0400084A RID: 2122
	public float lerp = 0.5f;
}
