﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000065 RID: 101
[Serializable]
public class SessionEventModule_LocatorSpawner : SessionEventModule
{
	// Token: 0x060003C9 RID: 969 RVA: 0x0002328D File Offset: 0x0002148D
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		this.Activate();
	}

	// Token: 0x060003CA RID: 970 RVA: 0x0002329C File Offset: 0x0002149C
	public override void Activate()
	{
		this.Spawn();
		base.Activate();
	}

	// Token: 0x060003CB RID: 971 RVA: 0x000232AA File Offset: 0x000214AA
	public override void SendMessage(string m)
	{
		base.SendMessage(m);
		if (m == "spawn")
		{
			this.Spawn();
		}
	}

	// Token: 0x060003CC RID: 972 RVA: 0x000232C8 File Offset: 0x000214C8
	public void Spawn()
	{
		Vector3 vector = ENT_Player.playerObject.transform.position + this.checkPlayerPositionOffset;
		UT_Locator.LocatorGroup locatorGroup = UT_Locator.GetLocatorGroup(this.locatorID);
		if (locatorGroup == null)
		{
			return;
		}
		List<UT_Locator> closestLocators = locatorGroup.GetClosestLocators(vector, this.locatorAmount, false);
		if (closestLocators == null || closestLocators.Count == 0)
		{
			return;
		}
		foreach (UT_Locator ut_Locator in closestLocators)
		{
			if (Vector3.Distance(ut_Locator.transform.position, ENT_Player.playerObject.transform.position) >= this.minimumDistanceToPlayer)
			{
				int num = Mathf.FloorToInt((float)Random.Range(this.spawnMin, this.spawnMax));
				for (int i = 0; i < num; i++)
				{
					Object.Instantiate<GameObject>(this.spawnList[Random.Range(0, this.spawnList.Count)], ut_Locator.transform.position, ut_Locator.transform.rotation, ut_Locator.transform);
				}
			}
		}
	}

	// Token: 0x04000525 RID: 1317
	public string locatorID;

	// Token: 0x04000526 RID: 1318
	public List<GameObject> spawnList;

	// Token: 0x04000527 RID: 1319
	public int spawnMin;

	// Token: 0x04000528 RID: 1320
	public int spawnMax;

	// Token: 0x04000529 RID: 1321
	public Vector3 checkPlayerPositionOffset;

	// Token: 0x0400052A RID: 1322
	public int locatorAmount = 1;

	// Token: 0x0400052B RID: 1323
	public float minimumDistanceToPlayer = 5f;
}
