﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000021 RID: 33
public class DEN_Buddy : MonoBehaviour
{
	// Token: 0x06000150 RID: 336 RVA: 0x0000AA4C File Offset: 0x00008C4C
	private void Start()
	{
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		this.drone = base.GetComponent<DEN_Drone>();
		this.barkTime = this.barkFrequency * 0.5f + Random.Range(0f, this.barkFrequency);
		this.eventTimer = this.eventFrequency * 0.5f + Random.Range(0f, this.eventFrequency);
	}

	// Token: 0x06000151 RID: 337 RVA: 0x0000AAB8 File Offset: 0x00008CB8
	private void Update()
	{
		if (this.drone.dead && !this.hasDied)
		{
			this.hasDied = true;
			CL_GameManager.SetGameFlag("buddy-i1", false, "", false);
			if (this.currentEvent != null)
			{
				this.currentEvent = null;
				base.StopAllCoroutines();
			}
			return;
		}
		if (this.drone.dead)
		{
			return;
		}
		if (this.currentEvent == null)
		{
			if (this.canSeeTarget || this.hangingOut)
			{
				this.barkTime -= Time.deltaTime;
				if (this.barkTime <= 0f && this.hasFoundPlayer)
				{
					this.barkTime = this.barkFrequency * 0.5f + Random.Range(0f, this.barkFrequency);
					this.clipHandler.PlaySound("buddy:bark");
				}
			}
			if (this.drone.GetTarget().isVisible)
			{
				if (!this.canSeeTarget)
				{
					this.canSeeTarget = true;
					this.SightTarget();
				}
				this.lastSightedTimer = 0f;
			}
			else
			{
				if (this.canSeeTarget)
				{
					this.LoseTarget();
				}
				this.lastSightedTimer += Time.deltaTime;
			}
		}
		this.RunEvents();
		this.Hangout();
	}

	// Token: 0x06000152 RID: 338 RVA: 0x0000ABEC File Offset: 0x00008DEC
	private void Hangout()
	{
		if (!this.hangingOut)
		{
			return;
		}
		float num = Vector3.Distance(base.transform.position, this.hangoutPos);
		if (num > 18f)
		{
			this.hangingOut = false;
			this.drone.lookForTarget = true;
			return;
		}
		if (num > 10f)
		{
			this.drone.targetPosition = this.hangoutPos + Random.insideUnitSphere;
		}
	}

	// Token: 0x06000153 RID: 339 RVA: 0x0000AC58 File Offset: 0x00008E58
	private void SightTarget()
	{
		if (!this.hasFoundPlayer)
		{
			this.hasFoundPlayer = true;
			this.clipHandler.PlaySound("buddy:foundplayer");
			return;
		}
		if (this.drone.GetTarget().lastSightedTimer > 5f)
		{
			this.clipHandler.PlaySound("buddy:relocateplayer");
		}
	}

	// Token: 0x06000154 RID: 340 RVA: 0x0000ACAC File Offset: 0x00008EAC
	private void LoseTarget()
	{
		if (this.lastSightedTimer > 5f)
		{
			this.canSeeTarget = false;
			this.clipHandler.PlaySound("buddy:lostplayer");
		}
	}

	// Token: 0x06000155 RID: 341 RVA: 0x0000ACD4 File Offset: 0x00008ED4
	private void RunEvents()
	{
		if (this.currentEvent == null && this.canSeeTarget)
		{
			this.eventTimer -= Time.deltaTime;
			if (this.eventTimer <= 0f)
			{
				DEN_Buddy.BuddyEvent buddyEvent = this.buddyEvents[Random.Range(0, this.buddyEvents.Count)];
				if (Random.value < buddyEvent.chance)
				{
					this.PlayEvent(buddyEvent);
					return;
				}
				this.eventTimer = 1f;
			}
		}
	}

	// Token: 0x06000156 RID: 342 RVA: 0x0000AD4D File Offset: 0x00008F4D
	public void StopEvents()
	{
		base.StopAllCoroutines();
		this.currentEvent = null;
	}

	// Token: 0x06000157 RID: 343 RVA: 0x0000AD5C File Offset: 0x00008F5C
	public void PlayEvent(DEN_Buddy.BuddyEvent bE)
	{
		this.currentEvent = bE;
		this.currentEvent.module.Initialize(this);
		this.eventTimer = this.eventFrequency * 0.5f + Random.Range(0f, this.eventFrequency);
	}

	// Token: 0x06000158 RID: 344 RVA: 0x0000AD99 File Offset: 0x00008F99
	public void ClearEvent()
	{
		this.currentEvent = null;
	}

	// Token: 0x06000159 RID: 345 RVA: 0x0000ADA2 File Offset: 0x00008FA2
	public void PlaySound(AudioClip c)
	{
		this.clipHandler.GetGroup("buddy").GetSet("event").PlaySound(1f, c);
	}

	// Token: 0x0600015A RID: 346 RVA: 0x0000ADC9 File Offset: 0x00008FC9
	public void SetTarget(Transform t)
	{
		this.drone.target = t;
		this.drone.targetPosition = t.position;
	}

	// Token: 0x0600015B RID: 347 RVA: 0x0000ADE8 File Offset: 0x00008FE8
	public void SetLookForTarget(bool b)
	{
		this.drone.lookForTarget = b;
	}

	// Token: 0x0600015C RID: 348 RVA: 0x0000ADF6 File Offset: 0x00008FF6
	public void SetHangoutSpot(Vector3 pos)
	{
		this.hangingOut = true;
		this.hangoutPos = pos;
	}

	// Token: 0x0600015D RID: 349 RVA: 0x0000AE06 File Offset: 0x00009006
	public void SetHangoutState(bool b)
	{
		this.hangingOut = b;
		if (this.hangingOut)
		{
			this.drone.targetPosition = this.hangoutPos;
			this.drone.lookForTarget = false;
		}
	}

	// Token: 0x0600015E RID: 350 RVA: 0x0000AE34 File Offset: 0x00009034
	public bool IsHanging()
	{
		return this.hangingOut;
	}

	// Token: 0x0400011E RID: 286
	private UT_AudioClipHandler clipHandler;

	// Token: 0x0400011F RID: 287
	private DEN_Drone drone;

	// Token: 0x04000120 RID: 288
	public float barkFrequency;

	// Token: 0x04000121 RID: 289
	private float barkTime;

	// Token: 0x04000122 RID: 290
	public float eventFrequency = 60f;

	// Token: 0x04000123 RID: 291
	private float eventTimer;

	// Token: 0x04000124 RID: 292
	private bool canSeeTarget;

	// Token: 0x04000125 RID: 293
	private float lastSightedTimer;

	// Token: 0x04000126 RID: 294
	private bool hasFoundPlayer;

	// Token: 0x04000127 RID: 295
	public List<DEN_Buddy.BuddyEvent> buddyEvents;

	// Token: 0x04000128 RID: 296
	private DEN_Buddy.BuddyEvent currentEvent;

	// Token: 0x04000129 RID: 297
	private bool hasDied;

	// Token: 0x0400012A RID: 298
	private bool hangingOut;

	// Token: 0x0400012B RID: 299
	private Vector3 hangoutPos;

	// Token: 0x0400012C RID: 300
	private Vector3 hangoutWanderTarget;

	// Token: 0x0200020C RID: 524
	[Serializable]
	public class BuddyEvent
	{
		// Token: 0x04000DDC RID: 3548
		public string id;

		// Token: 0x04000DDD RID: 3549
		public float chance = 1f;

		// Token: 0x04000DDE RID: 3550
		[SerializeReference]
		public BuddyModule module;
	}
}
