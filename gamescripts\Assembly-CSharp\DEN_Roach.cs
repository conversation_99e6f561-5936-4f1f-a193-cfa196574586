﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000038 RID: 56
public class DEN_Roach : Denizen
{
	// Token: 0x06000216 RID: 534 RVA: 0x00011620 File Offset: 0x0000F820
	public override void Start()
	{
		this.startPos = base.transform.position;
		this.velocity = Vector3.zero;
		this.targetVelocity = Vector3.zero;
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		base.GetComponent<AudioSource>().Play();
		base.GetComponent<AudioSource>().time = Random.value * base.GetComponent<AudioSource>().clip.length;
		this.hissTimer = Random.value * 5f;
	}

	// Token: 0x06000217 RID: 535 RVA: 0x000116A0 File Offset: 0x0000F8A0
	private new void Update()
	{
		if (base.IsTickFrame())
		{
			if (Vector3.Distance(Camera.main.transform.position, base.transform.position) > 30f)
			{
				this.tooFarFromPlayer = true;
			}
			else
			{
				this.tooFarFromPlayer = false;
			}
			this.sighted = Physics.OverlapSphere(base.transform.position, 1.5f, this.sight.sightMask);
		}
		if (CL_GameManager.gMan.isPaused || !this.active || this.tooFarFromPlayer)
		{
			return;
		}
		this.AI();
		if (this.dropTime >= 0f)
		{
			this.dropTime -= Time.deltaTime;
		}
	}

	// Token: 0x06000218 RID: 536 RVA: 0x00011758 File Offset: 0x0000F958
	private void OnEnable()
	{
		this.dropTime = 2f;
		this.startPos = base.transform.position;
	}

	// Token: 0x06000219 RID: 537 RVA: 0x00011776 File Offset: 0x0000F976
	private void OnDisable()
	{
	}

	// Token: 0x0600021A RID: 538 RVA: 0x00011778 File Offset: 0x0000F978
	private void OnDestroy()
	{
	}

	// Token: 0x0600021B RID: 539 RVA: 0x0001177C File Offset: 0x0000F97C
	private void AI()
	{
		if (Physics.Raycast(base.transform.position + base.transform.forward * this.floorOffset, base.transform.up, out this.hit, 0.03f, this.worldMask))
		{
			base.transform.position = this.hit.point + this.hit.normal * this.floorOffset;
			base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(this.hit.normal, base.transform.up), Time.deltaTime * 40f);
			this.velocity = base.transform.up;
			this.onFloor = true;
		}
		else if (Physics.Raycast(base.transform.position + base.transform.forward * this.floorOffset, -base.transform.forward, out this.hit, 0.15f, this.worldMask))
		{
			base.transform.position = this.hit.point + this.hit.normal * this.floorOffset;
			base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(this.hit.normal, base.transform.up + this.velocity.normalized), Time.deltaTime * 40f);
			this.onFloor = true;
		}
		else if (Physics.Raycast(base.transform.position + base.transform.up * this.floorOffset, -base.transform.forward + -base.transform.up, out this.hit, 0.02f, this.worldMask))
		{
			base.transform.position = this.hit.point + this.hit.normal * this.floorOffset;
			base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(this.hit.normal, base.transform.up), Time.deltaTime * 40f);
			this.velocity = base.transform.up;
			this.onFloor = true;
		}
		else if (this.onFloor)
		{
			this.targetVelocity = -this.targetVelocity;
			this.velocity = -this.velocity;
			this.onFloor = false;
			this.wanderTime = 0.5f;
		}
		else if (Vector3.Distance(this.startPos, base.transform.position) > 10f)
		{
			base.gameObject.SetActive(false);
		}
		else if (Physics.Raycast(base.transform.position, Vector3.down, out this.hit, Time.deltaTime * 8f, this.worldMask))
		{
			base.transform.position = this.hit.point + this.hit.normal * this.floorOffset;
			base.transform.rotation = Quaternion.LookRotation(this.hit.normal, base.transform.up);
		}
		else
		{
			base.transform.position += Vector3.down * Time.deltaTime * 8f;
		}
		base.transform.position += this.velocity * Time.deltaTime;
		this.velocity = Vector3.Lerp(this.velocity, this.targetVelocity, Time.deltaTime * 5f);
		bool debug = CL_UIManager.debug;
		if (this.wanderTime <= 0f)
		{
			this.Wander();
			this.wanderTime = Random.Range(0f, 3f);
		}
		else
		{
			this.wanderTime -= Time.deltaTime;
		}
		this.hissTimer -= Time.deltaTime;
		if (this.hissTimer <= 0f)
		{
			this.hissTimer = Random.value * 20f;
			this.clipHandler.PlaySound("roach:hiss");
		}
		if (this.sighted != null && this.sighted.Length != 0)
		{
			this.flockPositions.Clear();
			for (int i = 0; i < this.sighted.Length; i++)
			{
				if (this.sighted != null && !(this.sighted[i] == null))
				{
					ObjectTagger component = this.sighted[i].GetComponent<ObjectTagger>();
					if (component != null)
					{
						if (component.HasTagInList(this.fearTags))
						{
							if (CL_UIManager.debug)
							{
								CL_DebugView.draw.SphereOutline(base.transform.position, 0.45f, Color.yellow);
							}
							if (!ENT_Player.playerObject.HasPerk("Perk_PheromoneGlands"))
							{
								this.targetVelocity = (base.transform.position - this.sighted[i].transform.position).normalized * this.runSpeed;
								if (Vector3.Distance(this.sighted[i].ClosestPoint(base.transform.position), base.transform.position) < 0.45f && this.dropTime <= 0f)
								{
									if (CL_UIManager.debug)
									{
										CL_DebugView.draw.SphereOutline(base.transform.position, 0.45f, Color.red);
									}
									this.Kill("");
									return;
								}
							}
						}
						else if (component.HasTagInList(this.flockTags))
						{
							this.flockPositions.Add(this.sighted[i].transform.position);
						}
					}
				}
			}
			return;
		}
		this.sightedFear = false;
	}

	// Token: 0x0600021C RID: 540 RVA: 0x00011DE0 File Offset: 0x0000FFE0
	private void Wander()
	{
		if (Random.value > 0.7f)
		{
			this.state = DEN_Roach.RoachState.idle;
			this.targetVelocity = Vector3.zero;
			return;
		}
		this.state = DEN_Roach.RoachState.wandering;
		Vector2 insideUnitCircle = Random.insideUnitCircle;
		this.targetVelocity = (base.transform.up * insideUnitCircle.x + base.transform.right * insideUnitCircle.y + this.GetFlockVector()) * this.speed;
	}

	// Token: 0x0600021D RID: 541 RVA: 0x00011E68 File Offset: 0x00010068
	private Vector3 GetFlockVector()
	{
		if (!this.flock)
		{
			return Vector3.zero;
		}
		Vector3 vector = Vector3.zero;
		foreach (Vector3 vector2 in this.flockPositions)
		{
			vector += (vector2 - base.transform.position) * 0.15f;
		}
		return Vector3.ClampMagnitude(vector, 1f);
	}

	// Token: 0x0600021E RID: 542 RVA: 0x00011EF8 File Offset: 0x000100F8
	public override void Kill(string type = "")
	{
		StatManager.sessionStats.UpdateStatistic("roach-killed", 1, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Add, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
		Object.Instantiate<GameObject>(this.deathEffect, base.transform.position, Quaternion.identity);
		Object.Destroy(base.gameObject);
	}

	// Token: 0x0600021F RID: 543 RVA: 0x00011F45 File Offset: 0x00010145
	public override bool Damage(float amount, string type)
	{
		this.Kill("");
		return true;
	}

	// Token: 0x04000293 RID: 659
	public bool active = true;

	// Token: 0x04000294 RID: 660
	public LayerMask worldMask;

	// Token: 0x04000295 RID: 661
	public float floorOffset = 0.01f;

	// Token: 0x04000296 RID: 662
	private RaycastHit hit;

	// Token: 0x04000297 RID: 663
	public string[] flockTags;

	// Token: 0x04000298 RID: 664
	public string[] fearTags;

	// Token: 0x04000299 RID: 665
	private Vector3 velocity;

	// Token: 0x0400029A RID: 666
	private Vector3 targetVelocity;

	// Token: 0x0400029B RID: 667
	private float wanderTime;

	// Token: 0x0400029C RID: 668
	public float speed = 0.5f;

	// Token: 0x0400029D RID: 669
	private bool onFloor;

	// Token: 0x0400029E RID: 670
	public GameObject deathEffect;

	// Token: 0x0400029F RID: 671
	private bool sightedFear;

	// Token: 0x040002A0 RID: 672
	private float hissTimer = 1f;

	// Token: 0x040002A1 RID: 673
	public DEN_Roach.RoachState state;

	// Token: 0x040002A2 RID: 674
	public bool flock = true;

	// Token: 0x040002A3 RID: 675
	public bool fearPlayer = true;

	// Token: 0x040002A4 RID: 676
	public float runSpeed = 2f;

	// Token: 0x040002A5 RID: 677
	private float dropTime = 2f;

	// Token: 0x040002A6 RID: 678
	private Vector3 startPos;

	// Token: 0x040002A7 RID: 679
	private List<Vector3> flockPositions = new List<Vector3>();

	// Token: 0x040002A8 RID: 680
	private bool tooFarFromPlayer;

	// Token: 0x040002A9 RID: 681
	private Collider[] sighted;

	// Token: 0x02000221 RID: 545
	public enum RoachState
	{
		// Token: 0x04000E52 RID: 3666
		idle,
		// Token: 0x04000E53 RID: 3667
		hungry,
		// Token: 0x04000E54 RID: 3668
		wandering
	}
}
