﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;

// Token: 0x020000F1 RID: 241
public class ContextMenu : MonoBeh<PERSON>our, IPointerClickHandler, IEventSystemHandler
{
	// Token: 0x06000764 RID: 1892 RVA: 0x0003894B File Offset: 0x00036B4B
	public void OnPointerClick(PointerEventData eventData)
	{
		if (eventData.button == PointerEventData.InputButton.Right)
		{
			this.ShowMessage();
		}
	}

	// Token: 0x06000765 RID: 1893 RVA: 0x0003895C File Offset: 0x00036B5C
	private void ShowMessage()
	{
		ContextMenu_Manager.OnClick("", OS_Manager.mouseRealPosition, this.options);
	}

	// Token: 0x040008E2 RID: 2274
	public List<global::ContextMenu.ContextOption> options;

	// Token: 0x0200029B RID: 667
	[Serializable]
	public class ContextOption
	{
		// Token: 0x040010C6 RID: 4294
		public string text;

		// Token: 0x040010C7 RID: 4295
		public string data;

		// Token: 0x040010C8 RID: 4296
		public UnityEvent clickEvent;
	}
}
