﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000187 RID: 391
public class UT_RoachCounter : MonoBehaviour
{
	// Token: 0x06000AB7 RID: 2743 RVA: 0x00046754 File Offset: 0x00044954
	private void Update()
	{
		this.RollCounters();
	}

	// Token: 0x06000AB8 RID: 2744 RVA: 0x0004675C File Offset: 0x0004495C
	private void RollCounters()
	{
		if (this.lastRoaches != CL_GameManager.roaches)
		{
			this.digits = UT_RoachCounter.GetDigits(CL_GameManager.roaches);
			this.lastRoaches = CL_GameManager.roaches;
		}
		if (this.digits == null)
		{
			this.digits = new int[3];
		}
		for (int i = 0; i < this.counters.Length; i++)
		{
			Transform transform = this.counters[i];
			int num = 0;
			if (this.digits.Length > i)
			{
				num = this.digits[i] * -36;
			}
			transform.localRotation = Quaternion.Lerp(transform.localRotation, Quaternion.Euler((float)num, 0f, 0f), Time.deltaTime * 8f);
		}
	}

	// Token: 0x06000AB9 RID: 2745 RVA: 0x00046808 File Offset: 0x00044A08
	public static int[] GetDigits(int number)
	{
		List<int> list = new List<int>();
		while (number > 0)
		{
			list.Add(number % 10);
			number /= 10;
		}
		return list.ToArray();
	}

	// Token: 0x04000BBA RID: 3002
	public Transform[] counters;

	// Token: 0x04000BBB RID: 3003
	private int lastRoaches;

	// Token: 0x04000BBC RID: 3004
	private int[] digits;
}
