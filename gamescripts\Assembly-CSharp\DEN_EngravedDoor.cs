﻿using System;
using System.Collections.Generic;
using DarkMachine.AI;
using UnityEngine;

// Token: 0x02000025 RID: 37
public class DEN_EngravedDoor : MonoBehaviour
{
	// Token: 0x0600016A RID: 362 RVA: 0x0000AECC File Offset: 0x000090CC
	private void Awake()
	{
		this.doorID = DEN_EngravedDoor.doorIDCounter;
		DEN_EngravedDoor.doorIDCounter++;
		this.player = CL_GameManager.gMan.localPlayer;
		this.aud = base.GetComponent<AudioSource>();
		this.aud.volume = 0f;
	}

	// Token: 0x0600016B RID: 363 RVA: 0x0000AF1C File Offset: 0x0000911C
	private void OnEnable()
	{
		this.AddDebuffToPlayer(ref this.proximityDebuff, this.proximityEffects);
		this.AddDebuffToPlayer(ref this.lookDebuff, this.lookEffects);
	}

	// Token: 0x0600016C RID: 364 RVA: 0x0000AF42 File Offset: 0x00009142
	private void OnDisable()
	{
		this.player.curBuffs.RemoveBuffContainer(this.proximityDebuff.id);
		this.player.curBuffs.RemoveBuffContainer(this.lookDebuff.id);
	}

	// Token: 0x0600016D RID: 365 RVA: 0x0000AF7C File Offset: 0x0000917C
	private void Update()
	{
		float num = Vector3.Distance(this.player.transform.position, this.lookTarget.transform.position);
		this.proximityBlend = 1f - Mathf.Clamp01((num + 4f) / this.vision.sightDistance);
		if (num > this.vision.sightDistance * 1.5f)
		{
			this.lookBlend = Mathf.Lerp(this.lookBlend, 0f, Time.deltaTime * 1f);
			this.DataLerp();
			return;
		}
		this.canSeeTarget = this.vision.CanSeeTarget(this.player.transform);
		this.RotatePlayerCamTowardsLookTarget();
		this.LookCheck();
		float num2 = this.doorSwingCurve.Evaluate(1f - Mathf.Clamp(num / this.doorSwingDist, 0f, 1f));
		num2 *= this.doorSwingAmount;
		this.door.localRotation = Quaternion.Euler(0f, num2, 0f);
		if (num < 1f)
		{
			this.player.Kill("engraveddoor");
		}
		if (this.canSeeTarget)
		{
			this.headerTimer -= Time.deltaTime;
			if (this.headerTimer <= 0f)
			{
				string text = "<color=red>" + this.headerStrings[Random.Range(0, this.headerStrings.Count)] + "</color>";
				CL_UIManager.instance.tipHeader.ShowText(text);
				this.speakSource.clip = this.speakClips[Random.Range(0, this.speakClips.Count)];
				this.speakSource.Play();
				this.headerTimer = Random.Range(this.headerFrequency, this.headerFrequency * 2f);
			}
		}
	}

	// Token: 0x0600016E RID: 366 RVA: 0x0000B154 File Offset: 0x00009354
	public void RotatePlayerCamTowardsLookTarget()
	{
		if (!this.canSeeTarget)
		{
			return;
		}
		Vector3 normalized = (this.lookTarget.transform.position - (Camera.main.transform.forward + Camera.main.transform.position)).normalized;
		Vector3 vector = (Camera.main.transform.forward - normalized).normalized;
		vector = Camera.main.transform.InverseTransformVector(vector);
		Vector3 vector2 = new Vector3(vector.y, -vector.x, 0f).normalized * this.lookForceCurve.Evaluate(Mathf.Sin(Time.time * this.lookFreq) * 0.5f + 0.5f);
		float num = (InputManager.IsGamepad() ? 0.5f : 1f);
		ENT_Player.playerObject.AddRotationVelocity(vector2 * this.lookForce * Time.deltaTime * num);
		this.player.AddForce((this.lookTarget.transform.position - this.player.transform.position).normalized * this.pullForce * Time.deltaTime);
	}

	// Token: 0x0600016F RID: 367 RVA: 0x0000B2B4 File Offset: 0x000094B4
	public void LookCheck()
	{
		float num = 0f;
		if (this.canSeeTarget && ENT_Player.playerObject.camRoot.CanSeeTarget(this.lookTarget, 70f))
		{
			if (this.damageTime <= 0f)
			{
				ENT_Player.playerObject.Damage(this.damage, "engraveddoor");
				this.damageTime = 0.5f;
			}
			this.damageTime -= Time.deltaTime;
			num = 1f;
		}
		else
		{
			this.damageTime = 1f;
		}
		this.lookBlend = Mathf.Lerp(this.lookBlend, num, Time.deltaTime);
		this.lookDebuff.SetMultiplier(this.lookBlend);
		this.aud.volume = Mathf.Lerp(0f, this.audioVolume, this.lookBlend);
		ENT_Player.playerObject.camRoot.ShakeCamera(this.lookCamShake * this.lookBlend * (Mathf.Sin(Time.time * this.lookFreq) + 1f) * Time.deltaTime);
		this.DataLerp();
	}

	// Token: 0x06000170 RID: 368 RVA: 0x0000B3C8 File Offset: 0x000095C8
	private void DataLerp()
	{
		FXManager.FXZoneStore fxzoneStore = new FXManager.FXZoneStore();
		fxzoneStore.blend = this.lookBlend;
		fxzoneStore.priority = 15;
		fxzoneStore.data = this.lookAtFX;
		FXManager.fxZones.Enqueue(fxzoneStore, 15);
		FXManager.FXZoneStore fxzoneStore2 = new FXManager.FXZoneStore();
		fxzoneStore2.blend = this.proximityBlend;
		fxzoneStore2.priority = 14;
		fxzoneStore2.data = this.proximityFX;
		FXManager.fxZones.Enqueue(fxzoneStore2, 14);
	}

	// Token: 0x06000171 RID: 369 RVA: 0x0000B43C File Offset: 0x0000963C
	private void AddDebuffToPlayer(ref BuffContainer debuff, BuffContainer effects)
	{
		debuff = new BuffContainer();
		debuff.id = effects.id + this.doorID.ToString();
		debuff.buffs = new List<BuffContainer.Buff>();
		debuff.buffs.AddRange(effects.buffs);
		debuff.loseOverTime = false;
		debuff.SetMultiplier(0f);
		if (!this.player.curBuffs.HasBuffContainer(effects.id))
		{
			this.player.curBuffs.AddBuff(debuff);
		}
	}

	// Token: 0x04000132 RID: 306
	public Transform lookTarget;

	// Token: 0x04000133 RID: 307
	public float lookForce;

	// Token: 0x04000134 RID: 308
	public float lookFreq = 3f;

	// Token: 0x04000135 RID: 309
	public float lookCamShake = 0.03f;

	// Token: 0x04000136 RID: 310
	public AnimationCurve lookForceCurve;

	// Token: 0x04000137 RID: 311
	public float pullForce = 1f;

	// Token: 0x04000138 RID: 312
	public float damage;

	// Token: 0x04000139 RID: 313
	private float damageTime;

	// Token: 0x0400013A RID: 314
	public FXManager.FXData lookAtFX;

	// Token: 0x0400013B RID: 315
	public FXManager.FXData proximityFX;

	// Token: 0x0400013C RID: 316
	private float lookBlend;

	// Token: 0x0400013D RID: 317
	private float proximityBlend;

	// Token: 0x0400013E RID: 318
	private BuffContainer proximityDebuff;

	// Token: 0x0400013F RID: 319
	public BuffContainer proximityEffects;

	// Token: 0x04000140 RID: 320
	private BuffContainer lookDebuff;

	// Token: 0x04000141 RID: 321
	public BuffContainer lookEffects;

	// Token: 0x04000142 RID: 322
	private ENT_Player player;

	// Token: 0x04000143 RID: 323
	private AudioSource aud;

	// Token: 0x04000144 RID: 324
	public float audioVolume = 1f;

	// Token: 0x04000145 RID: 325
	public AISightComponent vision;

	// Token: 0x04000146 RID: 326
	private bool canSeeTarget;

	// Token: 0x04000147 RID: 327
	public Transform door;

	// Token: 0x04000148 RID: 328
	public float doorSwingAmount = 90f;

	// Token: 0x04000149 RID: 329
	public float doorSwingDist = 2f;

	// Token: 0x0400014A RID: 330
	public AnimationCurve doorSwingCurve;

	// Token: 0x0400014B RID: 331
	public List<string> headerStrings;

	// Token: 0x0400014C RID: 332
	public float headerFrequency;

	// Token: 0x0400014D RID: 333
	private float headerTimer;

	// Token: 0x0400014E RID: 334
	public AudioSource speakSource;

	// Token: 0x0400014F RID: 335
	public List<AudioClip> speakClips;

	// Token: 0x04000150 RID: 336
	private static int doorIDCounter;

	// Token: 0x04000151 RID: 337
	private int doorID;
}
