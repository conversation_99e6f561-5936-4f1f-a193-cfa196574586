﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000012 RID: 18
public class App_TrainingSectorControls_Toggle : MonoBehaviour
{
	// Token: 0x0600006A RID: 106 RVA: 0x00005290 File Offset: 0x00003490
	private void Awake()
	{
		this.toggle = base.GetComponent<Toggle>();
		this.toggle.onValueChanged.AddListener(delegate
		{
			this.SendState();
		});
		this.app.AddToggle(this);
		this.group = base.gameObject.AddComponent<CanvasGroup>();
		this.text = base.GetComponentInChildren<TMP_Text>();
	}

	// Token: 0x0600006B RID: 107 RVA: 0x000052EE File Offset: 0x000034EE
	public void SendState()
	{
		OS_Manager.soundPlayer.PlaySound(this.toggleAudio);
		this.app.ToggleSetting(this.id, this.toggle.isOn);
	}

	// Token: 0x0600006C RID: 108 RVA: 0x0000531C File Offset: 0x0000351C
	public Toggle GetToggle()
	{
		return this.toggle;
	}

	// Token: 0x0600006D RID: 109 RVA: 0x00005324 File Offset: 0x00003524
	public void SetInteractable(bool b)
	{
		this.toggle.interactable = b;
		this.text.color = (b ? Color.white : Color.grey);
	}

	// Token: 0x0400006F RID: 111
	public string id;

	// Token: 0x04000070 RID: 112
	private Toggle toggle;

	// Token: 0x04000071 RID: 113
	public string toggleAudio = "os:click";

	// Token: 0x04000072 RID: 114
	public App_TrainingSectorControls app;

	// Token: 0x04000073 RID: 115
	private CanvasGroup group;

	// Token: 0x04000074 RID: 116
	private TMP_Text text;
}
