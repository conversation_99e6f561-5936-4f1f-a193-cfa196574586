﻿using System;
using UnityEngine;

namespace DitzelGames.FastIK
{
	// Token: 0x020001DF RID: 479
	public class FastIKFabric : MonoBehaviour
	{
		// Token: 0x06000C49 RID: 3145 RVA: 0x0004D8B9 File Offset: 0x0004BAB9
		private void Awake()
		{
			this.Init();
		}

		// Token: 0x06000C4A RID: 3146 RVA: 0x0004D8C4 File Offset: 0x0004BAC4
		private void Init()
		{
			this.Bones = new Transform[this.ChainLength + 1];
			this.Positions = new Vector3[this.ChainLength + 1];
			this.BonesLength = new float[this.ChainLength];
			this.StartDirectionSucc = new Vector3[this.ChainLength + 1];
			this.StartRotationBone = new Quaternion[this.ChainLength + 1];
			this.Root = base.transform;
			for (int i = 0; i <= this.ChainLength; i++)
			{
				if (this.Root == null)
				{
					throw new UnityException("The chain value is longer than the ancestor chain!");
				}
				this.Root = this.Root.parent;
			}
			if (this.Target == null)
			{
				this.Target = new GameObject(base.gameObject.name + " Target").transform;
				this.SetPositionRootSpace(this.Target, this.GetPositionRootSpace(base.transform));
			}
			this.StartRotationTarget = this.GetRotationRootSpace(this.Target);
			Transform transform = base.transform;
			this.CompleteLength = 0f;
			for (int j = this.Bones.Length - 1; j >= 0; j--)
			{
				this.Bones[j] = transform;
				this.StartRotationBone[j] = this.GetRotationRootSpace(transform);
				if (j == this.Bones.Length - 1)
				{
					this.StartDirectionSucc[j] = this.GetPositionRootSpace(this.Target) - this.GetPositionRootSpace(transform);
				}
				else
				{
					this.StartDirectionSucc[j] = this.GetPositionRootSpace(this.Bones[j + 1]) - this.GetPositionRootSpace(transform);
					this.BonesLength[j] = this.StartDirectionSucc[j].magnitude;
					this.CompleteLength += this.BonesLength[j];
				}
				transform = transform.parent;
			}
		}

		// Token: 0x06000C4B RID: 3147 RVA: 0x0004DAAB File Offset: 0x0004BCAB
		private void LateUpdate()
		{
			this.ResolveIK();
		}

		// Token: 0x06000C4C RID: 3148 RVA: 0x0004DAB4 File Offset: 0x0004BCB4
		private void ResolveIK()
		{
			if (this.Target == null)
			{
				return;
			}
			if (this.BonesLength.Length != this.ChainLength)
			{
				this.Init();
			}
			for (int i = 0; i < this.Bones.Length; i++)
			{
				this.Positions[i] = this.GetPositionRootSpace(this.Bones[i]);
			}
			Vector3 positionRootSpace = this.GetPositionRootSpace(this.Target);
			Quaternion rotationRootSpace = this.GetRotationRootSpace(this.Target);
			if ((positionRootSpace - this.GetPositionRootSpace(this.Bones[0])).sqrMagnitude >= this.CompleteLength * this.CompleteLength)
			{
				Vector3 normalized = (positionRootSpace - this.Positions[0]).normalized;
				for (int j = 1; j < this.Positions.Length; j++)
				{
					this.Positions[j] = this.Positions[j - 1] + normalized * this.BonesLength[j - 1];
				}
			}
			else
			{
				for (int k = 0; k < this.Positions.Length - 1; k++)
				{
					this.Positions[k + 1] = Vector3.Lerp(this.Positions[k + 1], this.Positions[k] + this.StartDirectionSucc[k], this.SnapBackStrength);
				}
				for (int l = 0; l < this.Iterations; l++)
				{
					for (int m = this.Positions.Length - 1; m > 0; m--)
					{
						if (m == this.Positions.Length - 1)
						{
							this.Positions[m] = positionRootSpace;
						}
						else
						{
							this.Positions[m] = this.Positions[m + 1] + (this.Positions[m] - this.Positions[m + 1]).normalized * this.BonesLength[m];
						}
					}
					for (int n = 1; n < this.Positions.Length; n++)
					{
						this.Positions[n] = this.Positions[n - 1] + (this.Positions[n] - this.Positions[n - 1]).normalized * this.BonesLength[n - 1];
					}
					if ((this.Positions[this.Positions.Length - 1] - positionRootSpace).sqrMagnitude < this.Delta * this.Delta)
					{
						break;
					}
				}
			}
			if (this.Pole != null)
			{
				Vector3 positionRootSpace2 = this.GetPositionRootSpace(this.Pole);
				for (int num = 1; num < this.Positions.Length - 1; num++)
				{
					Plane plane = new Plane(this.Positions[num + 1] - this.Positions[num - 1], this.Positions[num - 1]);
					Vector3 vector = plane.ClosestPointOnPlane(positionRootSpace2);
					float num2 = Vector3.SignedAngle(plane.ClosestPointOnPlane(this.Positions[num]) - this.Positions[num - 1], vector - this.Positions[num - 1], plane.normal);
					this.Positions[num] = Quaternion.AngleAxis(num2, plane.normal) * (this.Positions[num] - this.Positions[num - 1]) + this.Positions[num - 1];
				}
			}
			for (int num3 = 0; num3 < this.Positions.Length; num3++)
			{
				if (num3 <= 1 && !this.affectRotation)
				{
					this.SetPositionRootSpace(this.Bones[num3], this.Positions[num3]);
				}
				else
				{
					if (num3 == this.Positions.Length - 1)
					{
						this.SetRotationRootSpace(this.Bones[num3], Quaternion.Inverse(rotationRootSpace) * this.StartRotationTarget * Quaternion.Inverse(this.StartRotationBone[num3]));
					}
					else
					{
						this.SetRotationRootSpace(this.Bones[num3], Quaternion.FromToRotation(this.StartDirectionSucc[num3], this.Positions[num3 + 1] - this.Positions[num3]) * Quaternion.Inverse(this.StartRotationBone[num3]));
					}
					this.SetPositionRootSpace(this.Bones[num3], this.Positions[num3]);
				}
			}
		}

		// Token: 0x06000C4D RID: 3149 RVA: 0x0004DFA0 File Offset: 0x0004C1A0
		private Vector3 GetPositionRootSpace(Transform current)
		{
			if (this.Root == null)
			{
				return current.position;
			}
			return Quaternion.Inverse(this.Root.rotation) * (current.position - this.Root.position);
		}

		// Token: 0x06000C4E RID: 3150 RVA: 0x0004DFF0 File Offset: 0x0004C1F0
		private void SetPositionRootSpace(Transform current, Vector3 position)
		{
			if (this.Root == null)
			{
				current.position = Vector3.Lerp(current.position, position, this.blend);
				return;
			}
			current.position = Vector3.Lerp(current.position, this.Root.rotation * position + this.Root.position, this.blend);
		}

		// Token: 0x06000C4F RID: 3151 RVA: 0x0004E05C File Offset: 0x0004C25C
		private Quaternion GetRotationRootSpace(Transform current)
		{
			if (this.Root == null)
			{
				return current.rotation;
			}
			return Quaternion.Inverse(current.rotation) * this.Root.rotation;
		}

		// Token: 0x06000C50 RID: 3152 RVA: 0x0004E090 File Offset: 0x0004C290
		private void SetRotationRootSpace(Transform current, Quaternion rotation)
		{
			if (this.Root == null)
			{
				current.rotation = Quaternion.Lerp(current.rotation, rotation, this.blend);
				return;
			}
			current.rotation = Quaternion.Lerp(current.rotation, this.Root.rotation * rotation, this.blend);
		}

		// Token: 0x06000C51 RID: 3153 RVA: 0x0004E0EC File Offset: 0x0004C2EC
		public void ResetTransform()
		{
			for (int i = this.Bones.Length - 1; i >= 0; i--)
			{
				this.Bones[i].rotation = this.StartRotationBone[i];
			}
		}

		// Token: 0x06000C52 RID: 3154 RVA: 0x0004E127 File Offset: 0x0004C327
		private void OnDrawGizmos()
		{
		}

		// Token: 0x04000D14 RID: 3348
		public int ChainLength = 2;

		// Token: 0x04000D15 RID: 3349
		public Transform Target;

		// Token: 0x04000D16 RID: 3350
		public Transform Pole;

		// Token: 0x04000D17 RID: 3351
		public bool affectRotation = true;

		// Token: 0x04000D18 RID: 3352
		[Header("Solver Parameters")]
		public int Iterations = 10;

		// Token: 0x04000D19 RID: 3353
		public float Delta = 0.001f;

		// Token: 0x04000D1A RID: 3354
		[Range(0f, 1f)]
		public float SnapBackStrength = 1f;

		// Token: 0x04000D1B RID: 3355
		[Range(0f, 1f)]
		public float blend = 1f;

		// Token: 0x04000D1C RID: 3356
		protected float[] BonesLength;

		// Token: 0x04000D1D RID: 3357
		protected float CompleteLength;

		// Token: 0x04000D1E RID: 3358
		protected Transform[] Bones;

		// Token: 0x04000D1F RID: 3359
		protected Vector3[] Positions;

		// Token: 0x04000D20 RID: 3360
		protected Vector3[] StartDirectionSucc;

		// Token: 0x04000D21 RID: 3361
		protected Quaternion[] StartRotationBone;

		// Token: 0x04000D22 RID: 3362
		protected Quaternion StartRotationTarget;

		// Token: 0x04000D23 RID: 3363
		protected Transform Root;
	}
}
