﻿using System;
using UnityEngine;

// Token: 0x020000DF RID: 223
public class UT_Spin : Mono<PERSON><PERSON><PERSON><PERSON>, Flippable
{
	// Token: 0x060006EF RID: 1775 RVA: 0x000364B8 File Offset: 0x000346B8
	private void Start()
	{
		if (this.local)
		{
			this.initialRotation = base.transform.localRotation;
		}
		else
		{
			this.initialRotation = base.transform.rotation;
		}
		if (this.spinning)
		{
			this.curRotate = this.rotate * this.multiplier;
		}
	}

	// Token: 0x060006F0 RID: 1776 RVA: 0x00036510 File Offset: 0x00034710
	private void Update()
	{
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		if (!this.spinning)
		{
			this.curRotate = Vector3.Lerp(this.curRotate, Vector3.zero, Time.deltaTime * this.acceleration);
		}
		else
		{
			this.curRotate = Vector3.Lerp(this.curRotate, this.rotate * this.multiplier, Time.deltaTime * this.acceleration);
		}
		if (this.local)
		{
			base.transform.localRotation *= Quaternion.Euler(this.curRotate * Time.deltaTime * (float)this.dir);
		}
		else
		{
			base.transform.rotation *= Quaternion.Euler(this.curRotate * Time.deltaTime * (float)this.dir);
		}
		if (this.audioSource != null)
		{
			this.audioSource.pitch = this.curRotate.magnitude / this.rotate.magnitude * this.audioPitch;
			this.audioSource.volume = this.curRotate.magnitude / this.rotate.magnitude * this.audioVolume;
		}
	}

	// Token: 0x060006F1 RID: 1777 RVA: 0x0003665C File Offset: 0x0003485C
	public void SetSpin(Vector3 s)
	{
		this.rotate = s;
	}

	// Token: 0x060006F2 RID: 1778 RVA: 0x00036665 File Offset: 0x00034865
	public void SetSpinning(bool b)
	{
		this.spinning = b;
		if (this.audioSource)
		{
			if (this.spinning)
			{
				this.audioSource.Play();
				return;
			}
			this.audioSource.Stop();
		}
	}

	// Token: 0x060006F3 RID: 1779 RVA: 0x0003669A File Offset: 0x0003489A
	public void ToggleSpin()
	{
		this.SetSpinning(!this.spinning);
	}

	// Token: 0x060006F4 RID: 1780 RVA: 0x000366AB File Offset: 0x000348AB
	public void Revert()
	{
		if (this.local)
		{
			base.transform.localRotation = this.initialRotation;
			return;
		}
		base.transform.rotation = this.initialRotation;
	}

	// Token: 0x060006F5 RID: 1781 RVA: 0x000366D8 File Offset: 0x000348D8
	public void SetMultiplier(float f)
	{
		this.multiplier = f;
	}

	// Token: 0x060006F6 RID: 1782 RVA: 0x000366E1 File Offset: 0x000348E1
	public void Reverse()
	{
		this.multiplier = -this.multiplier;
	}

	// Token: 0x060006F7 RID: 1783 RVA: 0x000366F0 File Offset: 0x000348F0
	public void OnFlip(bool flipped)
	{
		if (flipped)
		{
			this.dir = -1;
		}
	}

	// Token: 0x0400086D RID: 2157
	public bool local;

	// Token: 0x0400086E RID: 2158
	public Vector3 rotate;

	// Token: 0x0400086F RID: 2159
	public bool spinning = true;

	// Token: 0x04000870 RID: 2160
	private Vector3 curRotate = Vector3.zero;

	// Token: 0x04000871 RID: 2161
	private int dir = 1;

	// Token: 0x04000872 RID: 2162
	public float acceleration = 1f;

	// Token: 0x04000873 RID: 2163
	private Quaternion initialRotation;

	// Token: 0x04000874 RID: 2164
	public float multiplier = 1f;

	// Token: 0x04000875 RID: 2165
	[Header("Audio")]
	public AudioSource audioSource;

	// Token: 0x04000876 RID: 2166
	public float audioVolume = 1f;

	// Token: 0x04000877 RID: 2167
	public float audioPitch = 1f;
}
