﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200004F RID: 79
public class ENV_WarpFissure : MonoBehaviour, Blinkable
{
	// Token: 0x06000349 RID: 841 RVA: 0x00021119 File Offset: 0x0001F319
	private void Start()
	{
		this.fissureMaterial = base.GetComponent<MeshRenderer>().material;
	}

	// Token: 0x0600034A RID: 842 RVA: 0x0002112C File Offset: 0x0001F32C
	private void Update()
	{
		this.curFissureOpen = Mathf.Lerp(this.curFissureOpen, this.fissureOpen, Time.deltaTime);
		this.fissureMaterial.SetFloat("_Depth", this.curFissureOpen * 13f);
		this.fissureMaterial.SetFloat("_Bright", this.curFissureOpen * 23f);
		if (this.peelTime > 0f && this.peeled)
		{
			this.peelTime -= Time.deltaTime;
			return;
		}
		if (this.peelTime <= 0f && this.peeled)
		{
			this.OnBlinkEnd();
		}
	}

	// Token: 0x0600034B RID: 843 RVA: 0x000211D0 File Offset: 0x0001F3D0
	public void SetFissureOpenAmount(float f)
	{
		this.fissureOpen = f;
	}

	// Token: 0x0600034C RID: 844 RVA: 0x000211D9 File Offset: 0x0001F3D9
	public void PeekFissure()
	{
		if (this.fissureState == ENV_WarpFissure.FissureState.none)
		{
			this.fissureState = ENV_WarpFissure.FissureState.peeked;
			this.fissureOpen = 0.06f;
			this.clipHandler.PlaySound("fissure:peek");
			this.loopEffects.SetVolume(0.3f);
		}
	}

	// Token: 0x0600034D RID: 845 RVA: 0x00021218 File Offset: 0x0001F418
	public void OpenFissure()
	{
		if (this.fissureState == ENV_WarpFissure.FissureState.peeked || this.fissureState == ENV_WarpFissure.FissureState.none)
		{
			if (this.CheckCheated())
			{
				return;
			}
			this.fissureState = ENV_WarpFissure.FissureState.open;
			this.fissureOpen = 1f;
			this.clipHandler.PlaySound("fissure:open");
			this.loopEffects.SetVolume(0.7f);
		}
	}

	// Token: 0x0600034E RID: 846 RVA: 0x00021274 File Offset: 0x0001F474
	public void OnBlinkStart()
	{
		this.peelTime = 0.5f;
		if (this.fissureOpen > 0.1f && !this.peeled)
		{
			this.fissureState = ENV_WarpFissure.FissureState.peered;
			this.fissureOpen = 3f;
			this.clipHandler.PlaySound("fissure:peel");
			this.peeled = true;
			this.loopEffects.SetVolume(0.8f);
		}
	}

	// Token: 0x0600034F RID: 847 RVA: 0x000212DA File Offset: 0x0001F4DA
	public void OnBlinkEnd()
	{
		if (this.fissureOpen > 0.1f && this.peeled)
		{
			this.fissureState = ENV_WarpFissure.FissureState.open;
			this.fissureOpen = 1f;
			this.peeled = false;
			this.loopEffects.SetVolume(0.6f);
		}
	}

	// Token: 0x06000350 RID: 848 RVA: 0x0002131A File Offset: 0x0001F51A
	public void OnBlinkTeleport()
	{
		if (this.fissureOpen > 0.1f)
		{
			this.fissureState = ENV_WarpFissure.FissureState.open;
			this.clipHandler.PlaySound("fissure:use");
			CL_UIManager.instance.FlashVignette("whiteout");
			this.onBlinkTeleport.Invoke();
		}
	}

	// Token: 0x06000351 RID: 849 RVA: 0x0002135A File Offset: 0x0001F55A
	public bool CheckCheated()
	{
		if (CommandConsole.hasCheated)
		{
			CL_UIManager.instance.tipHeader.ShowText("Huh? looks like you cheated or something?");
			return true;
		}
		return false;
	}

	// Token: 0x04000488 RID: 1160
	private Material fissureMaterial;

	// Token: 0x04000489 RID: 1161
	public float fissureOpen;

	// Token: 0x0400048A RID: 1162
	private float curFissureOpen;

	// Token: 0x0400048B RID: 1163
	public UnityEvent onBlinkTeleport;

	// Token: 0x0400048C RID: 1164
	public UT_AudioClipHandler clipHandler;

	// Token: 0x0400048D RID: 1165
	private bool peeled;

	// Token: 0x0400048E RID: 1166
	private float peelTime;

	// Token: 0x0400048F RID: 1167
	public UT_AudioEffects loopEffects;

	// Token: 0x04000490 RID: 1168
	private ENV_WarpFissure.FissureState fissureState;

	// Token: 0x0200023D RID: 573
	public enum FissureState
	{
		// Token: 0x04000EFA RID: 3834
		none,
		// Token: 0x04000EFB RID: 3835
		peeked,
		// Token: 0x04000EFC RID: 3836
		open,
		// Token: 0x04000EFD RID: 3837
		peered
	}
}
