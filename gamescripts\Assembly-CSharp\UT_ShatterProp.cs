﻿using System;
using UnityEngine;

// Token: 0x02000190 RID: 400
public class UT_ShatterProp : MonoBehaviour
{
	// Token: 0x06000AD3 RID: 2771 RVA: 0x00046B50 File Offset: 0x00044D50
	private void Start()
	{
		this.childRigidbodies = base.GetComponentsInChildren<Rigidbody>();
		foreach (Rigidbody rigidbody in this.childRigidbodies)
		{
			Vector3 normalized = (rigidbody.transform.position - base.transform.position).normalized;
			rigidbody.AddForce(normalized * this.shatterStrength);
		}
	}

	// Token: 0x06000AD4 RID: 2772 RVA: 0x00046BB8 File Offset: 0x00044DB8
	private void Update()
	{
		this.curLifetime += Time.deltaTime;
		Rigidbody[] array = this.childRigidbodies;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].transform.localScale = Vector3.Lerp(Vector3.one, Vector3.one * 0.1f, this.curLifetime / this.lifeTime);
		}
		if (this.curLifetime > this.lifeTime)
		{
			Object.Destroy(base.gameObject);
		}
	}

	// Token: 0x04000BD3 RID: 3027
	public float shatterStrength = 1f;

	// Token: 0x04000BD4 RID: 3028
	public float lifeTime = 5f;

	// Token: 0x04000BD5 RID: 3029
	private float curLifetime;

	// Token: 0x04000BD6 RID: 3030
	private Rigidbody[] childRigidbodies;
}
