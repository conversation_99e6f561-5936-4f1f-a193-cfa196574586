﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000051 RID: 81
[CreateAssetMenu(fileName = "New Radio Station", menuName = "White Knuckle/Radio/Radio Station", order = 0)]
public class ENV_RadioStation : ScriptableObject
{
	// Token: 0x06000358 RID: 856 RVA: 0x00021458 File Offset: 0x0001F658
	public ENV_RadioStation.RadioTrack GetRandomTrack()
	{
		if (this.tracks == null || this.tracks.Count == 0)
		{
			Debug.LogWarning("Radio List is empty!");
			return null;
		}
		float num = 0f;
		foreach (ENV_RadioStation.RadioTrack radioTrack in this.tracks)
		{
			num += radioTrack.spawnSettings.GetEffectiveSpawnChance();
		}
		float num2 = Random.Range(0f, num);
		float num3 = 0f;
		foreach (ENV_RadioStation.RadioTrack radioTrack2 in this.tracks)
		{
			num3 += radioTrack2.spawnSettings.GetEffectiveSpawnChance();
			if (num2 <= num3)
			{
				return radioTrack2;
			}
		}
		return null;
	}

	// Token: 0x0400049B RID: 1179
	public string stationName = "jackFM";

	// Token: 0x0400049C RID: 1180
	public float stationFrequency = 93.7f;

	// Token: 0x0400049D RID: 1181
	public List<ENV_RadioStation.RadioTrack> tracks;

	// Token: 0x0200023E RID: 574
	[Serializable]
	public class RadioTrack
	{
		// Token: 0x04000EFE RID: 3838
		public string trackName;

		// Token: 0x04000EFF RID: 3839
		public AudioClip trackClip;

		// Token: 0x04000F00 RID: 3840
		public SpawnTable.SpawnSettings spawnSettings;
	}
}
