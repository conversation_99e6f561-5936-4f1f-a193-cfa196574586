﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Steamworks;
using Steamworks.Data;
using TMPro;
using UnityEngine;

// Token: 0x0200009F RID: 159
public class Leaderboard_Panel : MonoBehaviour
{
	// Token: 0x0600052D RID: 1325 RVA: 0x0002BB0A File Offset: 0x00029D0A
	private void Awake()
	{
		this.CreateScoreObjects();
	}

	// Token: 0x0600052E RID: 1326 RVA: 0x0002BB12 File Offset: 0x00029D12
	private void OnEnable()
	{
		if (this.refreshOnEnable)
		{
			this.Refresh();
		}
	}

	// Token: 0x0600052F RID: 1327 RVA: 0x0002BB24 File Offset: 0x00029D24
	public void Refresh()
	{
		for (int i = 0; i < this.scoreObjects.Count; i++)
		{
			this.scoreObjects[i].gameObject.SetActive(false);
		}
		if (CL_GameManager.gamemode.scoreLeaderboardScore)
		{
			this.scoreType = Leaderboard_Panel.ScoreType.score;
		}
		else
		{
			if (!CL_GameManager.gamemode.scoreLeaderboardTime)
			{
				base.gameObject.SetActive(false);
				return;
			}
			this.scoreType = Leaderboard_Panel.ScoreType.time;
		}
		Leaderboard_Panel.ScoreType scoreType = this.scoreType;
		if (scoreType != Leaderboard_Panel.ScoreType.score)
		{
			if (scoreType == Leaderboard_Panel.ScoreType.time)
			{
				this.scoreTypeText.text = "Time";
			}
		}
		else
		{
			this.scoreTypeText.text = "Score";
		}
		this.UpdateScore();
	}

	// Token: 0x06000530 RID: 1328 RVA: 0x0002BBCC File Offset: 0x00029DCC
	public void SwitchScore()
	{
		Leaderboard_Panel.ScoreType scoreType = this.scoreType + 1;
		this.scoreType = scoreType;
		this.scoreType = scoreType % (Leaderboard_Panel.ScoreType)2;
		if (!CL_GameManager.gamemode.scoreLeaderboardTime)
		{
			this.scoreType = Leaderboard_Panel.ScoreType.score;
		}
		else if (!CL_GameManager.gamemode.scoreLeaderboardScore)
		{
			this.scoreType = Leaderboard_Panel.ScoreType.time;
		}
		scoreType = this.scoreType;
		if (scoreType != Leaderboard_Panel.ScoreType.score)
		{
			if (scoreType == Leaderboard_Panel.ScoreType.time)
			{
				this.scoreTypeText.text = "Time";
			}
		}
		else
		{
			this.scoreTypeText.text = "Score";
		}
		this.UpdateScore();
	}

	// Token: 0x06000531 RID: 1329 RVA: 0x0002BC54 File Offset: 0x00029E54
	public void SwitchType()
	{
		Leaderboard_Panel.LeaderboardType leaderboardType = this.defaultType + 1;
		this.defaultType = leaderboardType;
		this.defaultType = leaderboardType % (Leaderboard_Panel.LeaderboardType)3;
		Debug.Log(this.defaultType);
		switch (this.defaultType)
		{
		case Leaderboard_Panel.LeaderboardType.top:
			this.title.text = "Leaderboard - Top";
			break;
		case Leaderboard_Panel.LeaderboardType.friendsOnly:
			this.title.text = "Leaderboard - Friends";
			break;
		case Leaderboard_Panel.LeaderboardType.surrounding:
			this.title.text = "Leaderboard - Global";
			break;
		}
		this.UpdateScore();
	}

	// Token: 0x06000532 RID: 1330 RVA: 0x0002BCE0 File Offset: 0x00029EE0
	private void CreateScoreObjects()
	{
		this.scoreObjects = new List<Leaderboard_Score>();
		for (int i = 0; i < this.scoresToPull; i++)
		{
			Leaderboard_Score leaderboard_Score = Object.Instantiate<Leaderboard_Score>(this.scorePrefab, this.scoreRoot);
			this.scoreObjects.Add(leaderboard_Score);
		}
	}

	// Token: 0x06000533 RID: 1331 RVA: 0x0002BD28 File Offset: 0x00029F28
	public async Task UpdateScore()
	{
		for (int j = 0; j < this.scoreObjects.Count; j++)
		{
			this.scoreObjects[j].gameObject.SetActive(false);
		}
		M_Gamemode gamemode = CL_GameManager.gamemode;
		string text = "-score";
		if (this.scoreType == Leaderboard_Panel.ScoreType.time)
		{
			text = "-time";
		}
		string text2 = "";
		if (SettingsManager.settings.g_hard && gamemode.scoreLeaderboardHard)
		{
			text2 += "-hard";
		}
		if (SettingsManager.settings.g_competitive && gamemode.scoreLeaderboardIronKnuckle)
		{
			text2 += "-ik";
		}
		Leaderboard? leaderboard2 = await SteamUserStats.FindLeaderboardAsync(CL_GameManager.gamemode.steamLeaderboardName + text2 + text);
		Leaderboard? leaderboard = leaderboard2;
		LeaderboardEntry[] scores = null;
		if (this.defaultType == Leaderboard_Panel.LeaderboardType.top)
		{
			scores = await leaderboard.Value.GetScoresAsync(this.scoresToPull, 1);
		}
		else if (this.defaultType == Leaderboard_Panel.LeaderboardType.friendsOnly)
		{
			scores = await leaderboard.Value.GetScoresFromFriendsAsync();
		}
		else if (this.defaultType == Leaderboard_Panel.LeaderboardType.surrounding)
		{
			scores = await leaderboard.Value.GetScoresAroundUserAsync(-this.scoresToPull / 2, this.scoresToPull / 2);
			if (scores == null)
			{
				scores = await leaderboard.Value.GetScoresAsync(this.scoresToPull, 1);
			}
		}
		for (int i = 0; i < this.scoreObjects.Count; i++)
		{
			if (i >= scores.Length)
			{
				this.scoreObjects[i].gameObject.SetActive(false);
			}
			else
			{
				this.scoreObjects[i].gameObject.SetActive(true);
				this.scoreObjects[i].positionText.text = string.Format("<mspace=''>{0}</mspace>", scores[i].GlobalRank);
				this.scoreObjects[i].nameText.text = scores[i].User.Name ?? "";
				if (this.scoreType == Leaderboard_Panel.ScoreType.time)
				{
					TimeSpan timeSpan = TimeSpan.FromSeconds((double)scores[i].Score);
					if (gamemode.timeMeasure == M_Gamemode.TimeScoreMeasure.milliseconds)
					{
						timeSpan = TimeSpan.FromSeconds((double)((float)scores[i].Score / 100f));
						this.scoreObjects[i].scoreText.text = string.Format("{0:mm\\:ss\\:ff}", timeSpan);
					}
					else
					{
						this.scoreObjects[i].scoreText.text = string.Format("{0:hh\\:mm\\:ss}", timeSpan);
					}
				}
				else
				{
					this.scoreObjects[i].scoreText.text = string.Format("{0}", scores[i].Score);
				}
				Friend friend = new Friend(scores[i].User.Id);
				Texture2D texture2D = SteamManager.ConvertSteamIcon((await friend.GetMediumAvatarAsync()).Value);
				this.scoreObjects[i].profile.texture = texture2D;
				if (scores[i].User.IsMe)
				{
					this.scoreObjects[i].positionText.color = this.isMeColor;
					this.scoreObjects[i].nameText.color = this.isMeColor;
					this.scoreObjects[i].scoreText.color = this.isMeColor;
				}
				else
				{
					this.scoreObjects[i].positionText.color = global::UnityEngine.Color.white;
					this.scoreObjects[i].nameText.color = global::UnityEngine.Color.white;
					this.scoreObjects[i].scoreText.color = global::UnityEngine.Color.white;
				}
			}
		}
	}

	// Token: 0x040006B2 RID: 1714
	public bool refreshOnEnable = true;

	// Token: 0x040006B3 RID: 1715
	public Transform scoreRoot;

	// Token: 0x040006B4 RID: 1716
	public Leaderboard_Score scorePrefab;

	// Token: 0x040006B5 RID: 1717
	public TMP_Text title;

	// Token: 0x040006B6 RID: 1718
	public TMP_Text scoreTypeText;

	// Token: 0x040006B7 RID: 1719
	public Leaderboard_Panel.LeaderboardType defaultType;

	// Token: 0x040006B8 RID: 1720
	public Leaderboard_Panel.ScoreType scoreType;

	// Token: 0x040006B9 RID: 1721
	public int scoresToPull = 10;

	// Token: 0x040006BA RID: 1722
	private List<Leaderboard_Score> scoreObjects;

	// Token: 0x040006BB RID: 1723
	public global::UnityEngine.Color isMeColor = global::UnityEngine.Color.red;

	// Token: 0x0200025A RID: 602
	public enum LeaderboardType
	{
		// Token: 0x04000F79 RID: 3961
		top,
		// Token: 0x04000F7A RID: 3962
		friendsOnly,
		// Token: 0x04000F7B RID: 3963
		surrounding
	}

	// Token: 0x0200025B RID: 603
	public enum ScoreType
	{
		// Token: 0x04000F7D RID: 3965
		score,
		// Token: 0x04000F7E RID: 3966
		time
	}
}
