﻿using System;
using System.Collections;
using Drawing;
using UnityEngine;

// Token: 0x02000119 RID: 281
public class UT_SPT_Spawner : MonoBehaviourGizmos
{
	// Token: 0x0600086A RID: 2154 RVA: 0x0003CA84 File Offset: 0x0003AC84
	private void Start()
	{
		if (this.spawnOnStart)
		{
			this.Spawn();
		}
	}

	// Token: 0x0600086B RID: 2155 RVA: 0x0003CA94 File Offset: 0x0003AC94
	public void Spawn()
	{
		if (this.spawnTable == null)
		{
			Debug.LogWarning("No spawn table assigned to spawner.");
			return;
		}
		int num = Random.Range(this.minSpawnAmount, this.maxSpawnAmount + 1);
		for (int i = 0; i < num; i++)
		{
			Vector3 vector = Random.insideUnitSphere * this.randomOffset;
			SpawnTable.SpawnTableAsset randomSpawnObject = this.spawnTable.GetRandomSpawnObject();
			if (randomSpawnObject == null || randomSpawnObject.prefabs == null)
			{
				Debug.LogWarning("No valid object chosen or prefab missing in the table.");
				return;
			}
			Quaternion quaternion = Quaternion.identity;
			if (this.randomRotation)
			{
				quaternion = Quaternion.Euler(Random.value * 20f, Random.value * 180f, Random.value * 20f) * base.transform.rotation;
			}
			GameObject gameObject = Object.Instantiate<GameObject>(randomSpawnObject.GetRandomPrefab(), base.transform.position + vector, quaternion);
			if (this.setParentToCurrentLevel && WorldLoader.initialized)
			{
				gameObject.transform.parent = WorldLoader.instance.GetCurrentLevel().level.transform;
			}
			if (this.scaleUp)
			{
				base.StartCoroutine(this.SpawnScale(gameObject.transform));
			}
		}
	}

	// Token: 0x0600086C RID: 2156 RVA: 0x0003CBC8 File Offset: 0x0003ADC8
	public bool TryGetSpawnPosition(GameObject prefab, out Vector3 spawnPos, out Bounds spawnBounds, bool dryRun = false)
	{
		spawnPos = base.transform.position;
		spawnBounds = default(Bounds);
		if (prefab == null)
		{
			return false;
		}
		MeshFilter[] componentsInChildren = prefab.GetComponentsInChildren<MeshFilter>();
		if (componentsInChildren == null || componentsInChildren.Length == 0)
		{
			Draw.Cross(base.transform.position, 0.5f, Color.yellow);
			spawnBounds = new Bounds(base.transform.position, Vector3.zero);
			spawnPos = this.AdjustForBottom(base.transform.position, spawnBounds.extents);
			return dryRun || !this.IsOverlapping(spawnPos, spawnBounds.extents);
		}
		spawnBounds = componentsInChildren[0].sharedMesh.bounds;
		for (int i = 1; i < componentsInChildren.Length; i++)
		{
			spawnBounds.Encapsulate(componentsInChildren[i].sharedMesh.bounds);
		}
		Draw.WireBox(base.transform.position, spawnBounds.extents, Color.yellow);
		Vector3 extents = spawnBounds.extents;
		spawnBounds.size.Scale(prefab.transform.localScale);
		Vector3 position = base.transform.position;
		if (dryRun)
		{
			spawnPos = this.AdjustForBottom(position, extents);
			return true;
		}
		Vector3 vector = this.AdjustForBottom(position, extents);
		if (!this.IsOverlapping(vector, extents))
		{
			spawnPos = vector;
			return true;
		}
		for (int j = 0; j < this.maxAttempts; j++)
		{
			Vector3 vector2 = Random.insideUnitSphere * this.searchRadius;
			Vector3 vector3 = position + vector2;
			vector3 = this.AdjustForBottom(vector3, extents);
			if (!this.IsOverlapping(vector3, extents))
			{
				spawnPos = vector3;
				return true;
			}
		}
		return false;
	}

	// Token: 0x0600086D RID: 2157 RVA: 0x0003CD88 File Offset: 0x0003AF88
	private IEnumerator SpawnScale(Transform targetObject)
	{
		Vector3 scaleTarget = targetObject.localScale;
		targetObject.localScale = scaleTarget * 0.1f;
		while (Vector3.Distance(targetObject.localScale, scaleTarget) > 0.01f)
		{
			targetObject.localScale = Vector3.Lerp(targetObject.localScale, scaleTarget, Time.deltaTime * 5f);
			yield return 0;
		}
		targetObject.localScale = scaleTarget;
		yield break;
	}

	// Token: 0x0600086E RID: 2158 RVA: 0x0003CD97 File Offset: 0x0003AF97
	private Vector3 AdjustForBottom(Vector3 candidatePos, Vector3 halfExtents)
	{
		if (this.placeBottomAtSpawner)
		{
			candidatePos.y = base.transform.position.y + halfExtents.y;
		}
		return candidatePos;
	}

	// Token: 0x0600086F RID: 2159 RVA: 0x0003CDC0 File Offset: 0x0003AFC0
	private bool IsOverlapping(Vector3 position, Vector3 halfExtents)
	{
		return Physics.OverlapBox(position, halfExtents, Quaternion.identity).Length != 0;
	}

	// Token: 0x06000870 RID: 2160 RVA: 0x0003CDD2 File Offset: 0x0003AFD2
	public override void DrawGizmos()
	{
		if (!this.debugDraw || this.spawnTable == null)
		{
			return;
		}
		this.DrawDebugCube();
	}

	// Token: 0x06000871 RID: 2161 RVA: 0x0003CDF1 File Offset: 0x0003AFF1
	private void DrawDebugCube()
	{
	}

	// Token: 0x040009C5 RID: 2501
	public bool spawnOnStart = true;

	// Token: 0x040009C6 RID: 2502
	[Header("Table and Spawning Settings")]
	public SpawnTable spawnTable;

	// Token: 0x040009C7 RID: 2503
	public bool scaleUp = true;

	// Token: 0x040009C8 RID: 2504
	public bool forceSpawnIfNoSpaceFound;

	// Token: 0x040009C9 RID: 2505
	public float searchRadius = 5f;

	// Token: 0x040009CA RID: 2506
	public int maxAttempts = 10;

	// Token: 0x040009CB RID: 2507
	[Range(0f, 5f)]
	public int minSpawnAmount = 1;

	// Token: 0x040009CC RID: 2508
	[Range(0f, 5f)]
	public int maxSpawnAmount = 1;

	// Token: 0x040009CD RID: 2509
	public float randomOffset;

	// Token: 0x040009CE RID: 2510
	[Header("Placement Options")]
	public bool placeBottomAtSpawner;

	// Token: 0x040009CF RID: 2511
	public bool setParentToCurrentLevel = true;

	// Token: 0x040009D0 RID: 2512
	[Header("Debug Options")]
	public bool debugDraw;

	// Token: 0x040009D1 RID: 2513
	public bool randomRotation;
}
