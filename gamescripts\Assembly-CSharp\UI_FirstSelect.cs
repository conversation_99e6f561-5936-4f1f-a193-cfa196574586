﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000127 RID: 295
public class UI_FirstSelect : MonoBehaviour
{
	// Token: 0x060008B9 RID: 2233 RVA: 0x0003DD23 File Offset: 0x0003BF23
	private void OnEnable()
	{
		this.button = base.GetComponent<Button>();
		if (this.gamepadOnly)
		{
			InputManager.IsGamepad();
			return;
		}
	}

	// Token: 0x060008BA RID: 2234 RVA: 0x0003DD40 File Offset: 0x0003BF40
	private void Update()
	{
		if (!this.button.IsInteractable())
		{
			return;
		}
		if (this.gamepadOnly && !InputManager.IsGamepad())
		{
			return;
		}
		if (InputManager.GetVector("Navigate").Down)
		{
			if (EventSystem.current.currentSelectedGameObject == null)
			{
				this.button.Select();
				return;
			}
			if (!EventSystem.current.currentSelectedGameObject.activeInHierarchy || !EventSystem.current.currentSelectedGameObject.GetComponent<Selectable>().IsInteractable())
			{
				this.button.Select();
			}
		}
	}

	// Token: 0x04000A06 RID: 2566
	private Button button;

	// Token: 0x04000A07 RID: 2567
	public bool gamepadOnly;
}
