﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sirenix.Utilities;
using Steamworks;
using Steamworks.Data;
using UnityEngine;

// Token: 0x020000A7 RID: 167
[CreateAssetMenu(fileName = "New Gamemode", menuName = "White Knuckle/Gamemode")]
public class M_Gamemode : ScriptableObject, WorldGenerator
{
	// Token: 0x06000555 RID: 1365 RVA: 0x0002C474 File Offset: 0x0002A674
	public void Initialize(string[] args)
	{
		this.hasSavedPlayerInfo = false;
		this.savedPlayerAscent = 0f;
		GameObject gameObject = new GameObject(this.gamemodeName + "-root");
		if (ENT_Player.GetPlayer() == null)
		{
			return;
		}
		foreach (GameObject gameObject2 in this.gamemodeObjects)
		{
			Object.Instantiate<GameObject>(gameObject2, gameObject.transform);
		}
		List<Item> list = new List<Item>();
		foreach (M_Gamemode.SpawnItem spawnItem in this.startItems)
		{
			Item clone = CL_AssetManager.GetAssetGameObject(spawnItem.itemid, "").GetComponent<Item_Object>().itemData.GetClone();
			clone.bagPosition = new Vector3(spawnItem.position.x, spawnItem.position.y, 1f).normalized;
			clone.bagRotation = Quaternion.LookRotation(-ENT_Player.GetPlayer().transform.forward + Vector3.up * 0.5f) * Quaternion.LookRotation(clone.upDirection);
			list.Add(clone);
		}
		if (list.Count > 0)
		{
			ENT_Player.GetInventory().LoadItemsIntoBag(list);
		}
		if (this.playlistLevels == null)
		{
			this.playlistLevels = new List<M_Level>();
		}
		if (args.Length != 0)
		{
			this.playlistLevels = new List<M_Level>();
		}
		for (int i = 0; i < args.Length; i++)
		{
			string text = args[i];
			M_Level levelAsset = CL_AssetManager.GetLevelAsset(text, "");
			if (levelAsset != null)
			{
				string text2 = "Loaded Level Into List: ";
				M_Level m_Level = levelAsset;
				CommandConsole.Log(text2 + ((m_Level != null) ? m_Level.ToString() : null), false);
				this.playlistLevels.Add(levelAsset);
				if (i == args.Length - 1)
				{
					this.playlistLevels.Add(CL_AssetManager.GetLevelAsset("MX_Level_Null", ""));
				}
			}
			else
			{
				CommandConsole.Log("Error: Level does not exist - " + text, false);
			}
		}
		if (this.gamemodeModule == null)
		{
			this.gamemodeModule = new GamemodeModule_Standard
			{
				winScoreMultiplier = 5f
			};
		}
		this.gamemodeModule.Initialize(this);
	}

	// Token: 0x06000556 RID: 1366 RVA: 0x0002C6E8 File Offset: 0x0002A8E8
	public void Remove()
	{
	}

	// Token: 0x06000557 RID: 1367 RVA: 0x0002C6EA File Offset: 0x0002A8EA
	public void Reset()
	{
	}

	// Token: 0x06000558 RID: 1368 RVA: 0x0002C6EC File Offset: 0x0002A8EC
	public virtual async Task Finish(float time, bool hasFinished = false)
	{
		this.gamemodeModule.OnFinish(hasFinished);
		if ((this.allowCheatedScores || !CommandConsole.hasCheated) && (this.allowCheatedScores || CL_GameManager.AreAchievementsAllowed()) && !WorldLoader.customSeed && CL_GameManager.gMan.allowScores && this.allowLeaderboardScoring)
		{
			float score = this.GetPlayerScore(hasFinished);
			Debug.Log(this.GetPlayerScore(hasFinished));
			float leaderboardTime = time;
			M_Gamemode.TimeScoreMeasure timeScoreMeasure = this.timeMeasure;
			if (timeScoreMeasure != M_Gamemode.TimeScoreMeasure.milliseconds)
			{
				if (timeScoreMeasure == M_Gamemode.TimeScoreMeasure.minutes)
				{
					leaderboardTime /= 10f;
				}
			}
			else
			{
				leaderboardTime *= 100f;
			}
			if (this.steamLeaderboardName != "")
			{
				string gametypeAffix = "";
				if (this.scoreLeaderboardHard && CL_GameManager.IsHardmode())
				{
					gametypeAffix += "-hard";
				}
				if (this.scoreLeaderboardIronKnuckle && SettingsManager.settings.g_competitive)
				{
					gametypeAffix += "-ik";
				}
				if (this.scoreLeaderboardScore)
				{
					Leaderboard? leaderboard = await SteamUserStats.FindLeaderboardAsync(this.steamLeaderboardName + gametypeAffix + "-score");
					if (leaderboard != null)
					{
						leaderboard.Value.SubmitScoreAsync((int)score, null);
					}
				}
				if (this.scoreLeaderboardTime && hasFinished)
				{
					Leaderboard? leaderboard2 = await SteamUserStats.FindLeaderboardAsync(this.steamLeaderboardName + gametypeAffix + "-time");
					if (leaderboard2 != null)
					{
						leaderboard2.Value.SubmitScoreAsync((int)leaderboardTime, null);
					}
				}
				gametypeAffix = null;
			}
		}
	}

	// Token: 0x06000559 RID: 1369 RVA: 0x0002C73F File Offset: 0x0002A93F
	internal void SavePlayerAscent(float playerAscent)
	{
		this.savedPlayerAscent = playerAscent;
		this.hasSavedPlayerInfo = true;
	}

	// Token: 0x0600055A RID: 1370 RVA: 0x0002C74F File Offset: 0x0002A94F
	internal bool HasSavedPlayerInfo()
	{
		return this.hasSavedPlayerInfo;
	}

	// Token: 0x0600055B RID: 1371 RVA: 0x0002C757 File Offset: 0x0002A957
	internal float GetSavedPlayerAscent()
	{
		return this.savedPlayerAscent;
	}

	// Token: 0x0600055C RID: 1372 RVA: 0x0002C75F File Offset: 0x0002A95F
	public string GetRoachBankID()
	{
		if (CL_GameManager.IsHardmode())
		{
			return this.roachBankID + "-hard";
		}
		return this.roachBankID;
	}

	// Token: 0x0600055D RID: 1373 RVA: 0x0002C77F File Offset: 0x0002A97F
	public float GetPlayerScore(bool hasFinished = false)
	{
		return this.gamemodeModule.GetScore(hasFinished);
	}

	// Token: 0x0600055E RID: 1374 RVA: 0x0002C790 File Offset: 0x0002A990
	public List<M_Level> GetGenerationList(M_Region lastRegion = null, M_Region regionToGenerate = null, M_Subregion startAfterSubregion = null)
	{
		List<M_Level> list = new List<M_Level>();
		if (this.modeType == M_Gamemode.GameType.single)
		{
			list = new List<M_Level>();
			list.Add(this.playlistLevels[0]);
			this.FinalizeLevelList(ref list);
			return list;
		}
		if (this.modeType == M_Gamemode.GameType.playlist)
		{
			list.AddRange(this.playlistLevels);
			this.FinalizeLevelList(ref list);
			return list;
		}
		if (regionToGenerate != null)
		{
			List<M_Level> regionLevels = this.GetRegionLevels(regionToGenerate, lastRegion, startAfterSubregion);
			list.AddRange(regionLevels);
			lastRegion = regionToGenerate;
			if (list.Count > this.levelsToGenerate)
			{
				list.SetLength(this.levelsToGenerate);
				this.FinalizeLevelList(ref list);
				return list;
			}
		}
		List<M_Region> list2 = new List<M_Region>();
		if (lastRegion == null || this.regions.Count == 1 || lastRegion == this.regions[this.regions.Count - 1])
		{
			list2.AddRange(this.regions);
		}
		else
		{
			bool flag = false;
			for (int i = 0; i < this.regions.Count; i++)
			{
				if (flag)
				{
					list2.Add(this.regions[i]);
				}
				else if (!flag && this.regions[i] == lastRegion)
				{
					flag = true;
				}
				if (i == this.regions.Count - 1)
				{
					list2.AddRange(this.regions);
				}
			}
		}
		while (list.Count < this.levelsToGenerate)
		{
			for (int j = 0; j < list2.Count; j++)
			{
				M_Region m_Region = list2[j];
				if (list2.Count <= 1 || !(m_Region == lastRegion))
				{
					List<M_Level> regionLevels2 = this.GetRegionLevels(m_Region, lastRegion, startAfterSubregion);
					list.AddRange(regionLevels2);
					lastRegion = m_Region;
					if (list.Count > this.levelsToGenerate)
					{
						this.FinalizeLevelList(ref list);
						return list;
					}
					if (j == list2.Count - 1)
					{
						list2 = this.regions;
					}
				}
			}
		}
		this.FinalizeLevelList(ref list);
		return list;
	}

	// Token: 0x0600055F RID: 1375 RVA: 0x0002C978 File Offset: 0x0002AB78
	private void FinalizeLevelList(ref List<M_Level> levelGenerationList)
	{
		if (this.modeType == M_Gamemode.GameType.single)
		{
			levelGenerationList[levelGenerationList.Count - 1].pauseGeneration = true;
			return;
		}
		levelGenerationList[levelGenerationList.Count - 1].pauseGeneration = false;
	}

	// Token: 0x06000560 RID: 1376 RVA: 0x0002C9B0 File Offset: 0x0002ABB0
	private List<M_Level> GetRegionLevels(M_Region region, M_Region lastRegion = null, M_Subregion lastSubregion = null)
	{
		List<M_Level> levels = region.GetLevels(lastRegion, lastSubregion);
		foreach (M_Level m_Level in levels)
		{
			if (!(m_Level == null))
			{
				m_Level.region = region;
			}
		}
		for (int i = levels.Count - 1; i >= 0; i--)
		{
			if (levels[i] == null)
			{
				levels.RemoveAt(i);
			}
		}
		return levels;
	}

	// Token: 0x06000561 RID: 1377 RVA: 0x0002CA3C File Offset: 0x0002AC3C
	public string GetGamemodeName(bool includeOptions = true)
	{
		string text = this.gamemodeName;
		if (includeOptions)
		{
			if (this.allowHardmode && CL_GameManager.IsHardmode())
			{
				text += "-Hardmode";
			}
			if (this.allowIronKnuckle && SettingsManager.settings.g_competitive)
			{
				text += "-Iron";
			}
		}
		return text;
	}

	// Token: 0x06000562 RID: 1378 RVA: 0x0002CA8F File Offset: 0x0002AC8F
	public bool HasWonGamemode()
	{
		return this.GetGamemodeSaveData() != null && StatManager.GetStatisticInt(this.GetGamemodeSaveData().stats, "wins") > 0;
	}

	// Token: 0x06000563 RID: 1379 RVA: 0x0002CAB3 File Offset: 0x0002ACB3
	public StatManager.SaveData.GameModeData GetGamemodeSaveData()
	{
		return StatManager.saveData.GetGameMode(this.gamemodeName);
	}

	// Token: 0x06000564 RID: 1380 RVA: 0x0002CAC5 File Offset: 0x0002ACC5
	public bool HasTag(string s)
	{
		return this.modeTags.Contains(s);
	}

	// Token: 0x06000565 RID: 1381 RVA: 0x0002CAD4 File Offset: 0x0002ACD4
	public bool HasAnyTag(List<string> strings)
	{
		foreach (string text in strings)
		{
			if (this.HasTag(text))
			{
				return true;
			}
		}
		return false;
	}

	// Token: 0x040006D2 RID: 1746
	public string gamemodeName;

	// Token: 0x040006D3 RID: 1747
	public bool useCustomCapsuleName;

	// Token: 0x040006D4 RID: 1748
	public string capsuleName;

	// Token: 0x040006D5 RID: 1749
	[SerializeReference]
	public GamemodeModule gamemodeModule;

	// Token: 0x040006D6 RID: 1750
	public M_Gamemode.GameType modeType;

	// Token: 0x040006D7 RID: 1751
	public string introText;

	// Token: 0x040006D8 RID: 1752
	[TextArea(8, 10)]
	public string modeDescription;

	// Token: 0x040006D9 RID: 1753
	[Header("Mode Customization")]
	public List<GameObject> gamemodeObjects;

	// Token: 0x040006DA RID: 1754
	public List<M_Gamemode.SpawnItem> startItems;

	// Token: 0x040006DB RID: 1755
	public bool timeGoal;

	// Token: 0x040006DC RID: 1756
	public bool allowAchievements = true;

	// Token: 0x040006DD RID: 1757
	public bool allowHeightAchievements = true;

	// Token: 0x040006DE RID: 1758
	private List<GameObject> spawnedObjects;

	// Token: 0x040006DF RID: 1759
	public string gamemodeScene = "Game-Main";

	// Token: 0x040006E0 RID: 1760
	[Space(5f)]
	public bool isEndless;

	// Token: 0x040006E1 RID: 1761
	public bool hasRevives = true;

	// Token: 0x040006E2 RID: 1762
	public bool hasPerks = true;

	// Token: 0x040006E3 RID: 1763
	public bool baseGamemode = true;

	// Token: 0x040006E4 RID: 1764
	[Space(5f)]
	public bool allowCheats = true;

	// Token: 0x040006E5 RID: 1765
	public bool allowCheatedScores;

	// Token: 0x040006E6 RID: 1766
	public bool saveRoaches = true;

	// Token: 0x040006E7 RID: 1767
	[Header("Gamemode Flags")]
	public string roachBankID = "standard-bank";

	// Token: 0x040006E8 RID: 1768
	public bool showBankAmount = true;

	// Token: 0x040006E9 RID: 1769
	public List<string> modeTags;

	// Token: 0x040006EA RID: 1770
	public List<M_Level> playlistLevels;

	// Token: 0x040006EB RID: 1771
	public List<M_Region> regions;

	// Token: 0x040006EC RID: 1772
	public int levelsToGenerate = 4;

	// Token: 0x040006ED RID: 1773
	public bool allowHardmode = true;

	// Token: 0x040006EE RID: 1774
	public bool allowIronKnuckle = true;

	// Token: 0x040006EF RID: 1775
	public float gooSpeedMult = 1f;

	// Token: 0x040006F0 RID: 1776
	public float gooSpeedIncreaseMult = 1f;

	// Token: 0x040006F1 RID: 1777
	public Sprite capsuleArt;

	// Token: 0x040006F2 RID: 1778
	public Sprite screenArt;

	// Token: 0x040006F3 RID: 1779
	public global::UnityEngine.Color gamemodeColor;

	// Token: 0x040006F4 RID: 1780
	public UI_GamemodeScreen_Panel gamemodePanel;

	// Token: 0x040006F5 RID: 1781
	public string unlockAchievement;

	// Token: 0x040006F6 RID: 1782
	public string unlockHint;

	// Token: 0x040006F7 RID: 1783
	public bool hiddenUntilUnlocked;

	// Token: 0x040006F8 RID: 1784
	public bool devOnly;

	// Token: 0x040006F9 RID: 1785
	public UI_ScoreScreen winScreen;

	// Token: 0x040006FA RID: 1786
	public UI_ScoreScreen loseScreen;

	// Token: 0x040006FB RID: 1787
	public bool allowLeaderboardScoring = true;

	// Token: 0x040006FC RID: 1788
	public string steamLeaderboardName;

	// Token: 0x040006FD RID: 1789
	public bool scoreLeaderboardTime = true;

	// Token: 0x040006FE RID: 1790
	public M_Gamemode.TimeScoreMeasure timeMeasure = M_Gamemode.TimeScoreMeasure.seconds;

	// Token: 0x040006FF RID: 1791
	public bool scoreLeaderboardScore = true;

	// Token: 0x04000700 RID: 1792
	public bool scoreLeaderboardHard = true;

	// Token: 0x04000701 RID: 1793
	public bool scoreLeaderboardIronKnuckle = true;

	// Token: 0x04000702 RID: 1794
	private bool hasSavedPlayerInfo;

	// Token: 0x04000703 RID: 1795
	private float savedPlayerAscent;

	// Token: 0x02000262 RID: 610
	[Serializable]
	public class SpawnItem
	{
		// Token: 0x04000F9D RID: 3997
		public string itemid;

		// Token: 0x04000F9E RID: 3998
		public Vector2 position;
	}

	// Token: 0x02000263 RID: 611
	public enum GameType
	{
		// Token: 0x04000FA0 RID: 4000
		standard,
		// Token: 0x04000FA1 RID: 4001
		playlist,
		// Token: 0x04000FA2 RID: 4002
		shuffledPlaylist,
		// Token: 0x04000FA3 RID: 4003
		endlessPlaylist,
		// Token: 0x04000FA4 RID: 4004
		single
	}

	// Token: 0x02000264 RID: 612
	public enum TimeScoreMeasure
	{
		// Token: 0x04000FA6 RID: 4006
		milliseconds,
		// Token: 0x04000FA7 RID: 4007
		seconds,
		// Token: 0x04000FA8 RID: 4008
		minutes,
		// Token: 0x04000FA9 RID: 4009
		hours
	}
}
