﻿using System;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x0200016B RID: 363
public class UT_ForceZone : UT_TriggerBase
{
	// Token: 0x06000A1D RID: 2589 RVA: 0x00043AB7 File Offset: 0x00041CB7
	internal override void Start()
	{
		this.curForce = this.force;
		base.Start();
	}

	// Token: 0x06000A1E RID: 2590 RVA: 0x00043ACC File Offset: 0x00041CCC
	internal override void FixedUpdate()
	{
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		if (!this.active)
		{
			return;
		}
		bool flag = false;
		this.curForce = Vector3.Lerp(this.curForce, this.force * this.forceMult, this.forceLerpAcceleration * Time.fixedDeltaTime);
		foreach (GameEntity gameEntity in this.gameEntities)
		{
			if (gameEntity == null)
			{
				this.gameEntities.Remove(gameEntity);
				return;
			}
			if (gameEntity.GetTagger().HasTagInList(this.triggerTags))
			{
				float num = Vector3.Distance(gameEntity.transform.position, base.transform.position);
				float num2 = 1f;
				if (this.fadeByDistance)
				{
					num2 = 1f - num / this.distanceFadeAmount;
					num2 = this.fadeCurve.Evaluate(num2);
				}
				if (gameEntity.GetTagger().HasTag("Player"))
				{
					gameEntity.AddForce(base.transform.TransformVector(this.curForce) * Time.fixedDeltaTime * num2);
					if (this.preventPlayerGrab)
					{
						gameEntity.GetComponent<ENT_Player>().DropHang(this.preventPlayerGrabCooldown);
					}
					flag = true;
				}
				else if (gameEntity.GetTagger().HasTag("Entity") || gameEntity.GetTagger().HasTag("Prop"))
				{
					gameEntity.AddForce(base.transform.TransformVector(this.curForce) * Time.fixedDeltaTime * 30f);
				}
			}
		}
		if (flag && this.audioSource)
		{
			this.audioSource.volume = Mathf.Lerp(this.audioSource.volume, this.audioVolume * this.forceMult, Time.deltaTime);
			if (!this.audioSource.isPlaying)
			{
				this.audioSource.Play();
			}
		}
		else if (this.audioSource)
		{
			this.audioSource.volume = Mathf.Lerp(this.audioSource.volume, 0f, Time.deltaTime);
			if (this.audioSource.volume == 0f && this.audioSource.isPlaying)
			{
				this.audioSource.Pause();
			}
		}
		base.FixedUpdate();
	}

	// Token: 0x06000A1F RID: 2591 RVA: 0x00043D4C File Offset: 0x00041F4C
	private void OnTriggerEnter(Collider other)
	{
		ObjectTagger objectTagger;
		if (ObjectTagger.TagCheck(other.gameObject, this.triggerTags, out objectTagger))
		{
			GameEntity component = other.GetComponent<GameEntity>();
			if (component != null && !this.gameEntities.Contains(component))
			{
				this.gameEntities.Add(component);
				Debug.Log("Added entity: " + component.name);
				this.triggerObjects.Add(other.gameObject);
			}
		}
	}

	// Token: 0x06000A20 RID: 2592 RVA: 0x00043DC0 File Offset: 0x00041FC0
	private void OnTriggerExit(Collider other)
	{
		GameEntity component = other.GetComponent<GameEntity>();
		if (this.gameEntities.Contains(component))
		{
			this.RemoveIndexFromTrigger(this.gameEntities.IndexOf(component));
		}
	}

	// Token: 0x06000A21 RID: 2593 RVA: 0x00043DF4 File Offset: 0x00041FF4
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			Draw.Arrow(base.transform.position, base.transform.position + base.transform.TransformDirection(this.force), Vector3.up, 0.1f, new Color(1f, 0.92f, 0.016f, 1f));
			if (this.fadeByDistance)
			{
				Draw.Cross(base.transform.position - base.transform.TransformDirection(this.force.normalized) * this.distanceFadeAmount, Color.yellow);
				return;
			}
		}
		else
		{
			Draw.Arrow(base.transform.position, base.transform.position + base.transform.TransformDirection(this.force), Vector3.up, 0.1f, new Color(1f, 0.92f, 0.016f, 0.1f));
		}
	}

	// Token: 0x06000A22 RID: 2594 RVA: 0x00043F1E File Offset: 0x0004211E
	public void SetForce(Vector3 f)
	{
		this.force = f;
	}

	// Token: 0x06000A23 RID: 2595 RVA: 0x00043F27 File Offset: 0x00042127
	public void MultForce(float m)
	{
		this.forceMult = m;
	}

	// Token: 0x06000A24 RID: 2596 RVA: 0x00043F30 File Offset: 0x00042130
	internal override void RemoveIndexFromTrigger(int i)
	{
		Debug.Log("Removed entity: " + this.gameEntities[i].name);
		base.RemoveIndexFromTrigger(i);
		this.gameEntities.RemoveAt(i);
	}

	// Token: 0x04000B3B RID: 2875
	public Vector3 force;

	// Token: 0x04000B3C RID: 2876
	private Vector3 curForce;

	// Token: 0x04000B3D RID: 2877
	public bool active = true;

	// Token: 0x04000B3E RID: 2878
	public List<GameEntity> gameEntities = new List<GameEntity>();

	// Token: 0x04000B3F RID: 2879
	public bool fadeByDistance;

	// Token: 0x04000B40 RID: 2880
	public float distanceFadeAmount;

	// Token: 0x04000B41 RID: 2881
	public AnimationCurve fadeCurve;

	// Token: 0x04000B42 RID: 2882
	private float forceMult = 1f;

	// Token: 0x04000B43 RID: 2883
	public float forceLerpAcceleration = 1f;

	// Token: 0x04000B44 RID: 2884
	public bool preventPlayerGrab;

	// Token: 0x04000B45 RID: 2885
	public float preventPlayerGrabCooldown;

	// Token: 0x04000B46 RID: 2886
	public bool useAudio;

	// Token: 0x04000B47 RID: 2887
	public AudioSource audioSource;

	// Token: 0x04000B48 RID: 2888
	public float audioVolume;
}
