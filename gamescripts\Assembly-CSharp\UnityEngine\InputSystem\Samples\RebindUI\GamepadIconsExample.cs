﻿using System;
using UnityEngine.Events;
using UnityEngine.UI;

namespace UnityEngine.InputSystem.Samples.RebindUI
{
	// Token: 0x020001DD RID: 477
	public class GamepadIconsExample : MonoBehaviour
	{
		// Token: 0x06000C27 RID: 3111 RVA: 0x0004CFDC File Offset: 0x0004B1DC
		protected void OnEnable()
		{
			foreach (RebindActionUI rebindActionUI in base.transform.GetComponentsInChildren<RebindActionUI>())
			{
				rebindActionUI.updateBindingUIEvent.AddListener(new UnityAction<RebindActionUI, string, string, string>(this.OnUpdateBindingDisplay));
				rebindActionUI.UpdateBindingDisplay();
			}
		}

		// Token: 0x06000C28 RID: 3112 RVA: 0x0004D024 File Offset: 0x0004B224
		protected void OnUpdateBindingDisplay(RebindActionUI component, string bindingDisplayString, string deviceLayoutName, string controlPath)
		{
			if (string.IsNullOrEmpty(deviceLayoutName) || string.IsNullOrEmpty(controlPath))
			{
				return;
			}
			Sprite sprite = null;
			if (InputSystem.IsFirstLayoutBasedOnSecond(deviceLayoutName, "DualShockGamepad"))
			{
				sprite = this.ps4.GetSprite(controlPath);
			}
			else if (InputSystem.IsFirstLayoutBasedOnSecond(deviceLayoutName, "Gamepad"))
			{
				sprite = this.xbox.GetSprite(controlPath);
			}
			Text bindingText = component.bindingText;
			Image component2 = bindingText.transform.parent.Find("ActionBindingIcon").GetComponent<Image>();
			if (sprite != null)
			{
				bindingText.gameObject.SetActive(false);
				component2.sprite = sprite;
				component2.gameObject.SetActive(true);
				return;
			}
			bindingText.gameObject.SetActive(true);
			component2.gameObject.SetActive(false);
		}

		// Token: 0x04000D04 RID: 3332
		public GamepadIconsExample.GamepadIcons xbox;

		// Token: 0x04000D05 RID: 3333
		public GamepadIconsExample.GamepadIcons ps4;

		// Token: 0x020002F0 RID: 752
		[Serializable]
		public struct GamepadIcons
		{
			// Token: 0x06000F7A RID: 3962 RVA: 0x0005B67C File Offset: 0x0005987C
			public Sprite GetSprite(string controlPath)
			{
				uint num = <PrivateImplementationDetails>.ComputeStringHash(controlPath);
				if (num <= 2126255620U)
				{
					if (num <= 996955572U)
					{
						if (num <= 527024624U)
						{
							if (num != 297952813U)
							{
								if (num == 527024624U)
								{
									if (controlPath == "leftStick")
									{
										return this.leftStick;
									}
								}
							}
							else if (controlPath == "select")
							{
								return this.selectButton;
							}
						}
						else if (num != 881114153U)
						{
							if (num == 996955572U)
							{
								if (controlPath == "dpad/up")
								{
									return this.dpadUp;
								}
							}
						}
						else if (controlPath == "dpad/right")
						{
							return this.dpadRight;
						}
					}
					else if (num <= 1697318111U)
					{
						if (num != 1411432780U)
						{
							if (num == 1697318111U)
							{
								if (controlPath == "start")
								{
									return this.startButton;
								}
							}
						}
						else if (controlPath == "leftTrigger")
						{
							return this.leftTrigger;
						}
					}
					else if (num != 2106031334U)
					{
						if (num != 2113007854U)
						{
							if (num == 2126255620U)
							{
								if (controlPath == "dpad")
								{
									return this.dpad;
								}
							}
						}
						else if (controlPath == "rightStickPress")
						{
							return this.rightStickPress;
						}
					}
					else if (controlPath == "buttonSouth")
					{
						return this.buttonSouth;
					}
				}
				else if (num <= 2991474140U)
				{
					if (num <= 2427478531U)
					{
						if (num != 2139186104U)
						{
							if (num == 2427478531U)
							{
								if (controlPath == "rightTrigger")
								{
									return this.rightTrigger;
								}
							}
						}
						else if (controlPath == "buttonEast")
						{
							return this.buttonEast;
						}
					}
					else if (num != 2803379527U)
					{
						if (num != 2866927184U)
						{
							if (num == 2991474140U)
							{
								if (controlPath == "dpad/left")
								{
									return this.dpadLeft;
								}
							}
						}
						else if (controlPath == "buttonNorth")
						{
							return this.buttonNorth;
						}
					}
					else if (controlPath == "rightStick")
					{
						return this.rightStick;
					}
				}
				else if (num <= 3792611837U)
				{
					if (num != 3616964664U)
					{
						if (num == 3792611837U)
						{
							if (controlPath == "rightShoulder")
							{
								return this.rightShoulder;
							}
						}
					}
					else if (controlPath == "leftShoulder")
					{
						return this.leftShoulder;
					}
				}
				else if (num != 4104197953U)
				{
					if (num != 4167788306U)
					{
						if (num == 4285979527U)
						{
							if (controlPath == "leftStickPress")
							{
								return this.leftStickPress;
							}
						}
					}
					else if (controlPath == "buttonWest")
					{
						return this.buttonWest;
					}
				}
				else if (controlPath == "dpad/down")
				{
					return this.dpadDown;
				}
				return null;
			}

			// Token: 0x0400129C RID: 4764
			public Sprite buttonSouth;

			// Token: 0x0400129D RID: 4765
			public Sprite buttonNorth;

			// Token: 0x0400129E RID: 4766
			public Sprite buttonEast;

			// Token: 0x0400129F RID: 4767
			public Sprite buttonWest;

			// Token: 0x040012A0 RID: 4768
			public Sprite startButton;

			// Token: 0x040012A1 RID: 4769
			public Sprite selectButton;

			// Token: 0x040012A2 RID: 4770
			public Sprite leftTrigger;

			// Token: 0x040012A3 RID: 4771
			public Sprite rightTrigger;

			// Token: 0x040012A4 RID: 4772
			public Sprite leftShoulder;

			// Token: 0x040012A5 RID: 4773
			public Sprite rightShoulder;

			// Token: 0x040012A6 RID: 4774
			public Sprite dpad;

			// Token: 0x040012A7 RID: 4775
			public Sprite dpadUp;

			// Token: 0x040012A8 RID: 4776
			public Sprite dpadDown;

			// Token: 0x040012A9 RID: 4777
			public Sprite dpadLeft;

			// Token: 0x040012AA RID: 4778
			public Sprite dpadRight;

			// Token: 0x040012AB RID: 4779
			public Sprite leftStick;

			// Token: 0x040012AC RID: 4780
			public Sprite rightStick;

			// Token: 0x040012AD RID: 4781
			public Sprite leftStickPress;

			// Token: 0x040012AE RID: 4782
			public Sprite rightStickPress;
		}
	}
}
