﻿using System;
using System.Collections;
using UnityEngine;

// Token: 0x020000D6 RID: 214
public class UT_ObjectSnapper : MonoBehaviour
{
	// Token: 0x060006CB RID: 1739 RVA: 0x00035F8B File Offset: 0x0003418B
	private void Update()
	{
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		this.DetectAndSnapObject();
	}

	// Token: 0x060006CC RID: 1740 RVA: 0x00035FA0 File Offset: 0x000341A0
	private void DetectAndSnapObject()
	{
		this.colliders = Physics.OverlapSphere(base.transform.position, this.detectionRadius);
		this.objectToSnap = null;
		foreach (Collider collider in this.colliders)
		{
			if (collider.CompareTag(this.targetTag))
			{
				this.objectToSnap = collider.gameObject;
				break;
			}
		}
		if (this.objectToSnap != null)
		{
			this.objectProp = this.objectToSnap.GetComponent<CL_Prop>();
			this.objectRigidbody = this.objectToSnap.GetComponent<Rigidbody>();
			if (this.objectProp != null && this.objectRigidbody != null)
			{
				base.StartCoroutine(this.SnapObject());
			}
		}
	}

	// Token: 0x060006CD RID: 1741 RVA: 0x0003605E File Offset: 0x0003425E
	private IEnumerator SnapObject()
	{
		this.objectProp.enabled = false;
		this.objectRigidbody.isKinematic = true;
		while (Vector3.Distance(this.objectToSnap.transform.position, this.snapPoint.position) > 0.01f)
		{
			this.objectToSnap.transform.position = Vector3.Lerp(this.objectToSnap.transform.position, this.snapPoint.position, Time.deltaTime * this.snapSpeed);
			this.objectToSnap.transform.rotation = Quaternion.Lerp(this.objectToSnap.transform.rotation, this.snapPoint.rotation, Time.deltaTime * this.snapSpeed);
			yield return null;
		}
		this.objectToSnap.transform.position = this.snapPoint.position;
		this.objectToSnap.transform.rotation = this.snapPoint.rotation;
		yield break;
	}

	// Token: 0x0400084C RID: 2124
	[SerializeField]
	private string targetTag = "Draggable";

	// Token: 0x0400084D RID: 2125
	[SerializeField]
	private Transform snapPoint;

	// Token: 0x0400084E RID: 2126
	[SerializeField]
	private float detectionRadius = 1f;

	// Token: 0x0400084F RID: 2127
	[SerializeField]
	private float snapSpeed = 5f;

	// Token: 0x04000850 RID: 2128
	private Collider[] colliders;

	// Token: 0x04000851 RID: 2129
	private GameObject objectToSnap;

	// Token: 0x04000852 RID: 2130
	private Rigidbody objectRigidbody;

	// Token: 0x04000853 RID: 2131
	private CL_Prop objectProp;
}
