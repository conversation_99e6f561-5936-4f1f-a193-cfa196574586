﻿using System;
using UnityEngine;

// Token: 0x020000D3 RID: 211
public class UT_InstantDeath : MonoBehaviour
{
	// Token: 0x060006C5 RID: 1733 RVA: 0x00035E94 File Offset: 0x00034094
	private void OnTriggerEnter(Collider other)
	{
		foreach (string text in this.triggerTags)
		{
			if (other.tag == text)
			{
				GameEntity component = other.GetComponent<GameEntity>();
				if (component != null)
				{
					component.Kill(this.type);
				}
			}
		}
	}

	// Token: 0x04000846 RID: 2118
	public string[] triggerTags = new string[] { "Player" };

	// Token: 0x04000847 RID: 2119
	public string type;
}
