﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

// Token: 0x02000008 RID: 8
public class LocatorManager : MonoBehaviour
{
	// Token: 0x0600002D RID: 45 RVA: 0x00003F9E File Offset: 0x0000219E
	private void Awake()
	{
		LocatorManager.instance = this;
	}

	// Token: 0x0600002E RID: 46 RVA: 0x00003FA6 File Offset: 0x000021A6
	private void OnEnable()
	{
		base.StartCoroutine(this.GridUpdateLoop());
	}

	// Token: 0x0600002F RID: 47 RVA: 0x00003FB8 File Offset: 0x000021B8
	public List<UT_Locator> GetClosestLocatorsFast(Vector3 pos, int count, bool onlyActive = true)
	{
		if (count <= 0)
		{
			return new List<UT_Locator>();
		}
		List<UT_Locator> list = new List<UT_Locator>(count);
		Vector3Int vector3Int = this.WorldToCell(pos);
		int num = 0;
		while (num <= this.maxShells && list.Count < count)
		{
			foreach (Vector3Int vector3Int2 in LocatorManager.Shell(vector3Int, num))
			{
				List<UT_Locator> list2;
				if (this._grid.TryGetValue(vector3Int2, out list2))
				{
					foreach (UT_Locator ut_Locator in list2)
					{
						if (!(ut_Locator == null) && (!onlyActive || ut_Locator.gameObject.activeInHierarchy))
						{
							list.Add(ut_Locator);
						}
					}
				}
			}
			num++;
		}
		if (list.Count < count)
		{
			list.AddRange(this.GetClosestLocatorsFullScan(pos, count, onlyActive));
			list = (from l in list.Distinct<UT_Locator>()
				orderby (l.transform.position - pos).sqrMagnitude
				select l).Take(count).ToList<UT_Locator>();
		}
		else
		{
			list = list.OrderBy((UT_Locator l) => (l.transform.position - pos).sqrMagnitude).Take(count).ToList<UT_Locator>();
		}
		return list;
	}

	// Token: 0x06000030 RID: 48 RVA: 0x00004120 File Offset: 0x00002320
	public List<UT_Locator> GetClosestLocatorsFullScan(Vector3 pos, int count, bool onlyActive = true)
	{
		return (from l in this.locators
			where l != null && (!onlyActive || l.gameObject.activeInHierarchy)
			orderby (l.transform.position - pos).sqrMagnitude
			select l).Take(count).ToList<UT_Locator>();
	}

	// Token: 0x06000031 RID: 49 RVA: 0x00004174 File Offset: 0x00002374
	private IEnumerator GridUpdateLoop()
	{
		for (;;)
		{
			int num = 0;
			while (num < this.batchPerFrame && this.locators.Count > 0)
			{
				if (this._indexCursor >= this.locators.Count)
				{
					this._indexCursor = 0;
				}
				UT_Locator ut_Locator = this.locators[this._indexCursor];
				this._indexCursor++;
				num++;
				if (!(ut_Locator == null))
				{
					Vector3Int vector3Int = this.WorldToCell(ut_Locator.transform.position);
					Vector3Int vector3Int2;
					if (this._locToCell.TryGetValue(ut_Locator, out vector3Int2))
					{
						if (vector3Int == vector3Int2)
						{
							continue;
						}
						this.RemoveFromCell(vector3Int2, ut_Locator);
					}
					this.AddToCell(vector3Int, ut_Locator);
					this._locToCell[ut_Locator] = vector3Int;
				}
			}
			yield return null;
		}
		yield break;
	}

	// Token: 0x06000032 RID: 50 RVA: 0x00004183 File Offset: 0x00002383
	private Vector3Int WorldToCell(Vector3 p)
	{
		return new Vector3Int(Mathf.FloorToInt(p.x / this.cellSize), Mathf.FloorToInt(p.y / this.cellSize), Mathf.FloorToInt(p.z / this.cellSize));
	}

	// Token: 0x06000033 RID: 51 RVA: 0x000041C0 File Offset: 0x000023C0
	private void AddToCell(Vector3Int cell, UT_Locator loc)
	{
		List<UT_Locator> list;
		if (!this._grid.TryGetValue(cell, out list))
		{
			list = (this._grid[cell] = new List<UT_Locator>(4));
		}
		list.Add(loc);
	}

	// Token: 0x06000034 RID: 52 RVA: 0x000041F8 File Offset: 0x000023F8
	private void RemoveFromCell(Vector3Int cell, UT_Locator loc)
	{
		List<UT_Locator> list;
		if (this._grid.TryGetValue(cell, out list))
		{
			list.Remove(loc);
			if (list.Count == 0)
			{
				this._grid.Remove(cell);
			}
		}
	}

	// Token: 0x06000035 RID: 53 RVA: 0x00004232 File Offset: 0x00002432
	private static IEnumerable<Vector3Int> Shell(Vector3Int center, int r)
	{
		if (r == 0)
		{
			yield return center;
			yield break;
		}
		int num;
		for (int x = -r; x <= r; x = num)
		{
			for (int y = -r; y <= r; y = num)
			{
				for (int z = -r; z <= r; z = num)
				{
					if (Mathf.Abs(x) == r || Mathf.Abs(y) == r || Mathf.Abs(z) == r)
					{
						yield return center + new Vector3Int(x, y, z);
					}
					num = z + 1;
				}
				num = y + 1;
			}
			num = x + 1;
		}
		yield break;
	}

	// Token: 0x04000041 RID: 65
	internal List<UT_Locator> locators = new List<UT_Locator>();

	// Token: 0x04000042 RID: 66
	[Header("Tuning")]
	[SerializeField]
	private float cellSize = 10f;

	// Token: 0x04000043 RID: 67
	[SerializeField]
	private int batchPerFrame = 50;

	// Token: 0x04000044 RID: 68
	[SerializeField]
	private int maxShells = 3;

	// Token: 0x04000045 RID: 69
	private readonly Dictionary<Vector3Int, List<UT_Locator>> _grid = new Dictionary<Vector3Int, List<UT_Locator>>();

	// Token: 0x04000046 RID: 70
	private readonly Dictionary<UT_Locator, Vector3Int> _locToCell = new Dictionary<UT_Locator, Vector3Int>();

	// Token: 0x04000047 RID: 71
	private int _indexCursor;

	// Token: 0x04000048 RID: 72
	public static LocatorManager instance;
}
