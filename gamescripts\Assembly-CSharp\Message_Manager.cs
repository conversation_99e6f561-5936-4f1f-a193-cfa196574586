﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000155 RID: 341
public class Message_Manager : MonoBehaviour
{
	// Token: 0x060009B5 RID: 2485 RVA: 0x000424A9 File Offset: 0x000406A9
	public void HideBlocker()
	{
		this.blocker.gameObject.SetActive(false);
		this.showingMessage = false;
	}

	// Token: 0x060009B6 RID: 2486 RVA: 0x000424C3 File Offset: 0x000406C3
	public void ShowBlocker()
	{
		this.blocker.gameObject.SetActive(true);
		this.showingMessage = true;
	}

	// Token: 0x060009B7 RID: 2487 RVA: 0x000424E0 File Offset: 0x000406E0
	public Message CreateMessage(Message_Manager.Message_Packet packet)
	{
		foreach (Message_Manager.MessageType messageType in this.messageTypes)
		{
			if (messageType.name == packet.type)
			{
				Message message = Object.Instantiate<Message>(messageType.messageAsset, packet.screenPos, Quaternion.identity, this.messageParent);
				message.transform.localPosition = packet.screenPos;
				message.Initialize(packet);
				message.exitFunction = (Action)Delegate.Combine(message.exitFunction, new Action(this.HideBlocker));
				this.ShowBlocker();
				return message;
			}
		}
		Message message2 = Object.Instantiate<Message>(this.messageTypes[0].messageAsset, packet.screenPos, Quaternion.identity, this.messageParent);
		message2.transform.localPosition = packet.screenPos;
		message2.Initialize(packet);
		message2.exitFunction = (Action)Delegate.Combine(message2.exitFunction, new Action(this.HideBlocker));
		this.ShowBlocker();
		return message2;
	}

	// Token: 0x060009B8 RID: 2488 RVA: 0x00042624 File Offset: 0x00040824
	public bool IsShowingMessage()
	{
		return this.showingMessage;
	}

	// Token: 0x04000AEF RID: 2799
	public Transform messageParent;

	// Token: 0x04000AF0 RID: 2800
	public Transform blocker;

	// Token: 0x04000AF1 RID: 2801
	public List<Message_Manager.MessageType> messageTypes;

	// Token: 0x04000AF2 RID: 2802
	public bool showingMessage;

	// Token: 0x020002D3 RID: 723
	[Serializable]
	public class MessageType
	{
		// Token: 0x04001219 RID: 4633
		public string name;

		// Token: 0x0400121A RID: 4634
		public Message messageAsset;
	}

	// Token: 0x020002D4 RID: 724
	public class Message_Packet
	{
		// Token: 0x0400121B RID: 4635
		public string type;

		// Token: 0x0400121C RID: 4636
		public string message;

		// Token: 0x0400121D RID: 4637
		public string closeText;

		// Token: 0x0400121E RID: 4638
		public string aText;

		// Token: 0x0400121F RID: 4639
		public Action closeFunction;

		// Token: 0x04001220 RID: 4640
		public Action optionAFunction;

		// Token: 0x04001221 RID: 4641
		public Vector2 screenPos;

		// Token: 0x04001222 RID: 4642
		public List<string> data;
	}
}
