﻿using System;
using System.Collections;
using Drawing;
using UnityEngine;

namespace DarkMachine.AI
{
	// Token: 0x020001D5 RID: 469
	public class AIC_Teeth_Chase : AIStateComponent
	{
		// Token: 0x1700000A RID: 10
		// (get) Token: 0x06000BDD RID: 3037 RVA: 0x0004B3AB File Offset: 0x000495AB
		public override string name
		{
			get
			{
				return "chase";
			}
		}

		// Token: 0x06000BDE RID: 3038 RVA: 0x0004B3B2 File Offset: 0x000495B2
		public override void Initialize(AIGameEntity e, AIStateVariable[] variables, string[] args = null)
		{
			base.Initialize(e, variables, args);
			this.teeth = e.GetComponent<DEN_Teeth>();
		}

		// Token: 0x06000BDF RID: 3039 RVA: 0x0004B3CC File Offset: 0x000495CC
		public override void Enter(AIGameEntity e, string[] args = null)
		{
			base.Enter(e, args);
			Debug.Log("Entered chase state for " + e.name);
			e.clipHandler.PlaySound("teeth:startChase");
			this.currentFear = 0f;
			this.teeth.locked = false;
			this.teeth.curBarkTime = Random.Range(this.teeth.barkTime / 2f, this.teeth.barkTime * 2f);
		}

		// Token: 0x06000BE0 RID: 3040 RVA: 0x0004B450 File Offset: 0x00049650
		public override void Execute(AIGameEntity e, string[] args = null)
		{
			base.Execute(e, args);
			if (this.hasKilledPlayer)
			{
				return;
			}
			if (e.target == null)
			{
				e.ChangeState("wander");
				return;
			}
			this.teeth.anim.SetBool("afraid", false);
			this.teeth.curBarkTime -= Time.deltaTime;
			this.timeSinceSeenLight += Time.deltaTime;
			if (this.teeth.curBarkTime <= 0f)
			{
				this.teeth.curBarkTime = Random.Range(this.teeth.barkTime / 2f, this.teeth.barkTime * 2f);
				e.clipHandler.PlaySound("teeth:bark");
			}
			this.teeth.SetCohesionEffect(this.currentFear * 0.1f + this.teeth.aggression * 0.1f);
			this.teeth.SetBurnEffect(this.currentFear - 1f);
			if (this.fearCheckTick <= 0f)
			{
				float highestEffectorFearRating = CL_Lamp.GetHighestEffectorFearRating(this.teeth.geoRoot.position);
				if (highestEffectorFearRating > 0f)
				{
					this.currentFear += highestEffectorFearRating / this.teeth.aggression;
					if (highestEffectorFearRating > 0.5f)
					{
						if (this.timeSinceSeenLight > 4f)
						{
							this.stunTime = 2f;
							e.clipHandler.PlaySound("teeth:reacttolight");
							CL_CameraControl.ShakeAtPosition(e.transform.position, 20f, 0.18f);
						}
						this.timeSinceSeenLight = 0f;
					}
					if (this.currentFear > this.maxFear)
					{
						Debug.Log("Feared!");
						e.clipHandler.PlaySound("teeth:bark");
						this.currentFear = 0f;
						this.teeth.speedMult = 2.3f / this.teeth.aggression;
						this.teeth.locked = false;
						this.teeth.anim.SetBool("afraid", false);
						e.ChangeState("retreat");
						return;
					}
				}
				else
				{
					this.currentFear = Mathf.Max(this.currentFear - 1f, 0f);
				}
				this.fearCheckTick = 1f;
			}
			else
			{
				this.fearCheckTick -= Time.deltaTime;
			}
			if (this.stunTime > 0f)
			{
				this.stunTime -= Time.deltaTime;
				this.teeth.anim.SetBool("afraid", true);
				this.teeth.locked = true;
				this.teeth.speedMult = 0f;
				return;
			}
			this.teeth.locked = false;
			this.teeth.anim.SetBool("afraid", false);
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.Label2D(e.transform.position + Vector3.up * 0.6f, "Fear: " + this.currentFear.ToString(), 8f, LabelAlignment.Center, Color.white);
			}
			this.teeth.speedMult = 1f - this.currentFear / this.maxFear * 0.25f;
			bool flag = e.sight.CanSeeTarget(e.target);
			if (flag)
			{
				if (this.timeSinceLastSawTarget > 4f)
				{
					e.clipHandler.PlaySound("teeth:locateTarget");
				}
				this.timeSinceLastSawTarget = 0f;
				if (e.targetPosition.y < this.teeth.transform.position.y - 5f)
				{
					this.teeth.speedMult *= 2f;
				}
				else if (e.targetPosition.y > this.teeth.transform.position.y + 15f)
				{
					this.teeth.speedMult *= 2f;
				}
			}
			else
			{
				this.timeSinceLastSawTarget += Time.deltaTime;
			}
			if (!flag && !e.sight.CanRemember())
			{
				e.clipHandler.PlaySound("teeth:endChase");
				this.teeth.charging = false;
				e.ChangeState("wander");
				return;
			}
			if (!flag && e.sight.CanRemember())
			{
				e.targetPosition = e.sight.GetLastSeenPosition();
				if (CL_UIManager.debug)
				{
					CL_DebugView.draw.Cross(e.sight.GetLastSeenPosition(), 0.3f, new Color(0f, 1f, 0f, 1f));
					CL_DebugView.draw.Label2D(e.sight.GetLastSeenPosition() + Vector3.up * 0.3f, "Investigate Target", 8f, LabelAlignment.Center, Color.white);
				}
				this.teeth.charging = false;
				return;
			}
			e.targetPosition = e.target.position;
			float num = Vector3.Distance(e.target.position, e.transform.position);
			if (num < 10f)
			{
				if (num < 3f)
				{
					this.teeth.locked = true;
					this.teeth.charging = true;
				}
				else
				{
					this.teeth.locked = false;
					this.teeth.speedMult = 1f;
					this.teeth.charging = false;
				}
				GameEntity component = e.target.GetComponent<GameEntity>();
				if (num < 3.75f)
				{
					if (!this.canAttackTarget)
					{
						this.canAttackTarget = true;
						this.attackTime = 1f;
					}
					if (component != null && this.attackTime <= 0f)
					{
						this.teeth.anim.SetTrigger("attack");
						float num2 = this.teeth.attackDamage;
						if (SettingsManager.settings.g_hard)
						{
							num2 *= 2f;
						}
						if (component.Damage(num2, "teeth"))
						{
							this.hasKilledPlayer = true;
							e.StartCoroutine(this.KillPlayerAnimation());
						}
						this.attackTime = 0.5f;
						component.AddForce(-(e.transform.position - component.transform.position).normalized * 0.5f);
					}
					component.AddForce((e.transform.position - component.transform.position).normalized * Time.deltaTime * 0.3f);
				}
				if (component != null)
				{
				}
			}
			else
			{
				this.canAttackTarget = false;
			}
			if (this.attackTime > 0f)
			{
				this.attackTime -= Time.deltaTime;
			}
		}

		// Token: 0x06000BE1 RID: 3041 RVA: 0x0004BB4A File Offset: 0x00049D4A
		private IEnumerator KillPlayerAnimation()
		{
			this.teeth.clipHandler.PlaySound("teeth:killplayer");
			this.teeth.speedMult = 0f;
			bool flag = 0f < Vector3.Dot((this.teeth.transform.position - ENT_Player.playerObject.cam.transform.position).normalized, ENT_Player.playerObject.cam.transform.forward);
			this.teeth.anim.SetBool("playerfacing", flag);
			this.teeth.anim.SetTrigger("killplayer");
			ENT_Player.playerObject.DropEverything();
			ENT_Player.playerObject.Lock();
			CL_GameManager.gMan.lockPlayer = true;
			this.teeth.locked = true;
			Camera playerCam = ENT_Player.playerObject.cam;
			ENT_Player.playerObject.DropEverything();
			float anim = 0f;
			this.teeth.screenHandEffect.emission.rateOverTimeMultiplier = 15f;
			Vector3 camStartPos = playerCam.transform.position;
			Quaternion camStartRot = playerCam.transform.rotation;
			while (anim < 1f)
			{
				ENT_Player.playerObject.Lock();
				anim += Time.deltaTime / 2f;
				playerCam.transform.position = Vector3.Lerp(camStartPos, this.teeth.cameraTakeover.position, anim);
				playerCam.transform.rotation = Quaternion.Lerp(camStartRot, this.teeth.cameraTakeover.rotation, anim);
				yield return null;
			}
			anim = 0f;
			CL_CameraControl.Shake(0.1f);
			while (anim < 2.8f)
			{
				ENT_Player.playerObject.Lock();
				anim += Time.deltaTime;
				playerCam.transform.position = this.teeth.cameraTakeover.position;
				playerCam.transform.rotation = this.teeth.cameraTakeover.rotation;
				yield return null;
			}
			this.teeth.clipHandler.PlaySound("teeth:killchomp");
			ENT_Player.playerObject.Kill("teeth");
			yield break;
		}

		// Token: 0x06000BE2 RID: 3042 RVA: 0x0004BB59 File Offset: 0x00049D59
		public override void Exit(AIGameEntity e, string[] args = null)
		{
			base.Exit(e, args);
		}

		// Token: 0x04000CE3 RID: 3299
		public DEN_Teeth teeth;

		// Token: 0x04000CE4 RID: 3300
		private float attackTime = 1f;

		// Token: 0x04000CE5 RID: 3301
		private bool canAttackTarget;

		// Token: 0x04000CE6 RID: 3302
		private float timeSinceLastSawTarget;

		// Token: 0x04000CE7 RID: 3303
		private float fearCheckTick;

		// Token: 0x04000CE8 RID: 3304
		private float currentFear;

		// Token: 0x04000CE9 RID: 3305
		private float maxFear = 4.5f;

		// Token: 0x04000CEA RID: 3306
		private bool hasKilledPlayer;

		// Token: 0x04000CEB RID: 3307
		private float stunTime;

		// Token: 0x04000CEC RID: 3308
		private float timeSinceSeenLight;
	}
}
