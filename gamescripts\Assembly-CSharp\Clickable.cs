﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000151 RID: 337
public interface Clickable
{
	// Token: 0x0600099E RID: 2462 RVA: 0x000421F4 File Offset: 0x000403F4
	void Interact(ENT_Player p, string s)
	{
	}

	// Token: 0x0600099F RID: 2463 RVA: 0x000421F6 File Offset: 0x000403F6
	void Interact(ENT_Player p, List<string> s)
	{
		this.Interact(p, s[0]);
	}

	// Token: 0x060009A0 RID: 2464 RVA: 0x00042206 File Offset: 0x00040406
	void Interact(ENT_Player p, ENT_Player.Hand hand)
	{
	}

	// Token: 0x060009A1 RID: 2465 RVA: 0x00042208 File Offset: 0x00040408
	void StartInteract(ENT_Player p, string s)
	{
	}

	// Token: 0x060009A2 RID: 2466 RVA: 0x0004220A File Offset: 0x0004040A
	void StopInteract(ENT_Player p, string s)
	{
	}

	// Token: 0x060009A3 RID: 2467 RVA: 0x0004220C File Offset: 0x0004040C
	void StopInteract(ENT_Player p, ENT_Player.Hand hand, string s)
	{
	}

	// Token: 0x060009A4 RID: 2468 RVA: 0x0004220E File Offset: 0x0004040E
	bool CanInteract(ENT_Player p, ENT_Player.Hand hand)
	{
		return true;
	}

	// Token: 0x060009A5 RID: 2469 RVA: 0x00042211 File Offset: 0x00040411
	ObjectTagger GetTagger()
	{
		return null;
	}

	// Token: 0x060009A6 RID: 2470 RVA: 0x00042214 File Offset: 0x00040414
	GameObject GetGameObject()
	{
		return null;
	}
}
