﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020000FA RID: 250
public class OS_FloppyReader : MonoBehaviour, Clickable
{
	// Token: 0x0600079C RID: 1948 RVA: 0x00039BB6 File Offset: 0x00037DB6
	public void Interact()
	{
		this.activeEvent.Invoke();
		this.hasBeenPressed = true;
	}

	// Token: 0x0600079D RID: 1949 RVA: 0x00039BCA File Offset: 0x00037DCA
	public void Interact(ENT_Player p, string s = "")
	{
		this.activeEvent.Invoke();
		this.hasBeenPressed = true;
	}

	// Token: 0x0600079E RID: 1950 RVA: 0x00039BDE File Offset: 0x00037DDE
	public void InsertFloppy(ENT_Player p, Item floppy)
	{
		this.Interact();
		base.StartCoroutine(this.InsertAnimation(floppy));
	}

	// Token: 0x0600079F RID: 1951 RVA: 0x00039BF4 File Offset: 0x00037DF4
	private IEnumerator InsertAnimation(Item floppy)
	{
		floppy.GetDropObject().GetComponent<Rigidbody>().isKinematic = true;
		floppy.GetDropObject().canPickup = false;
		floppy.GetDropObject().transform.position = base.transform.position;
		floppy.GetDropObject().transform.rotation = base.transform.rotation;
		yield return new WaitForSeconds(0.5f);
		AudioManager.PlaySound(this.insertStart, base.transform.position, this.audioVolume, 1f, 1f, false, 1f, null);
		float insertAmount = 0f;
		while (insertAmount < 1f)
		{
			insertAmount += Time.deltaTime;
			floppy.GetDropObject().transform.position += floppy.GetDropObject().transform.forward * Time.deltaTime * 0.2f;
			yield return null;
		}
		this.computerInterface.GetComputer().LoadFloppy(floppy);
		AudioManager.PlaySound(this.insertFinish, base.transform.position, this.audioVolume, 1f, 1f, false, 1f, null);
		Object.Destroy(floppy.GetDropObject().gameObject);
		yield break;
	}

	// Token: 0x060007A0 RID: 1952 RVA: 0x00039C0A File Offset: 0x00037E0A
	public void Reset()
	{
		this.hasBeenPressed = false;
	}

	// Token: 0x060007A1 RID: 1953 RVA: 0x00039C13 File Offset: 0x00037E13
	public bool CanInteract(ENT_Player p, ENT_Player.Hand hand)
	{
		return this.active && (hand.interactState == ENT_Player.InteractType.item && hand.inventoryHand.currentItem.interactType == Item.InteractType.usable);
	}

	// Token: 0x060007A2 RID: 1954 RVA: 0x00039C3E File Offset: 0x00037E3E
	public GameObject GetGameObject()
	{
		return base.gameObject;
	}

	// Token: 0x060007A3 RID: 1955 RVA: 0x00039C46 File Offset: 0x00037E46
	public ObjectTagger GetTagger()
	{
		return base.gameObject.GetComponent<ObjectTagger>();
	}

	// Token: 0x060007A4 RID: 1956 RVA: 0x00039C53 File Offset: 0x00037E53
	public void SetInteractable(bool b)
	{
		this.active = b;
	}

	// Token: 0x04000910 RID: 2320
	public bool active = true;

	// Token: 0x04000911 RID: 2321
	public UnityEvent activeEvent;

	// Token: 0x04000912 RID: 2322
	public OS_Computer_Interface computerInterface;

	// Token: 0x04000913 RID: 2323
	[Range(0f, 1f)]
	public float audioVolume = 1f;

	// Token: 0x04000914 RID: 2324
	public AudioClip insertStart;

	// Token: 0x04000915 RID: 2325
	public AudioClip insertFinish;

	// Token: 0x04000916 RID: 2326
	[HideInInspector]
	public bool hasBeenPressed;
}
