﻿using System;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using Drawing;
using UnityEngine;

// Token: 0x0200012C RID: 300
public class UI_LerpOpen : MonoBehaviourGizmos
{
	// Token: 0x060008DD RID: 2269 RVA: 0x0003E6DC File Offset: 0x0003C8DC
	private void Awake()
	{
		this.targetSize = base.transform.localScale;
		this.targetPosition = base.transform.localPosition;
		this.group = base.GetComponent<CanvasGroup>();
		this.rootPositon = base.transform.localPosition;
		this.rootScale = base.transform.localScale;
		if (this.startHidden)
		{
			if (this.group != null)
			{
				this.group.interactable = false;
			}
			if (this.affectScale)
			{
				base.transform.localScale = this.startScale;
			}
			if (this.affectPosition)
			{
				base.transform.localPosition = this.targetPosition + this.startPosition;
			}
			this.state = false;
			return;
		}
		this.state = true;
	}

	// Token: 0x060008DE RID: 2270 RVA: 0x0003E7A8 File Offset: 0x0003C9A8
	private void OnEnable()
	{
		if (this.startHidden)
		{
			if (this.group != null)
			{
				this.group.interactable = false;
			}
			if (this.affectScale)
			{
				base.transform.localScale = this.startScale;
			}
			if (this.affectPosition)
			{
				base.transform.localPosition = this.targetPosition + this.startPosition;
				return;
			}
		}
		else
		{
			this.Show();
		}
	}

	// Token: 0x060008DF RID: 2271 RVA: 0x0003E81B File Offset: 0x0003CA1B
	private void OnDisable()
	{
		if (this.startHidden)
		{
			this.state = false;
		}
		DOTween.Complete(base.transform, false);
	}

	// Token: 0x060008E0 RID: 2272 RVA: 0x0003E83C File Offset: 0x0003CA3C
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			Draw.Cross(base.transform.parent.TransformPoint(base.transform.localPosition + this.startPosition), 20f, new Color(1f, 0.561f, 0f, 0.25f));
		}
	}

	// Token: 0x060008E1 RID: 2273 RVA: 0x0003E8A0 File Offset: 0x0003CAA0
	public void Hide()
	{
		if (!this.state)
		{
			return;
		}
		this.state = false;
		DOTween.Complete(base.transform, false);
		if (this.affectScale)
		{
			base.transform.localScale = this.targetSize;
		}
		if (this.affectPosition)
		{
			base.transform.localPosition = this.targetPosition;
		}
		if (this.affectScale)
		{
			base.transform.DOScale(this.startScale, this.scaleLerpTime).SetUpdate(true);
		}
		if (this.affectPosition)
		{
			if (!this.hideOpposite)
			{
				base.transform.DOLocalMove(this.targetPosition + this.startPosition, this.positionLerpTime, false).SetUpdate(true).SetEase(this.easeOut, this.easeOutAmplitude, 0f);
			}
			else
			{
				base.transform.DOLocalMove(this.targetPosition - this.startPosition, this.positionLerpTime, false).SetUpdate(true).SetEase(this.easeOut, this.easeOutAmplitude, 0f);
			}
		}
		if (this.group != null)
		{
			this.group.interactable = false;
		}
	}

	// Token: 0x060008E2 RID: 2274 RVA: 0x0003E9D4 File Offset: 0x0003CBD4
	public void Show()
	{
		if (this.state)
		{
			return;
		}
		if (!base.gameObject.activeSelf)
		{
			base.gameObject.SetActive(true);
		}
		this.state = true;
		DOTween.Complete(base.transform, false);
		if (this.affectScale)
		{
			base.transform.localScale = this.startScale;
		}
		if (this.affectPosition)
		{
			base.transform.localPosition = this.targetPosition + this.startPosition;
		}
		if (this.affectScale)
		{
			base.transform.DOScale(this.targetSize, this.scaleLerpTime).SetUpdate(true).SetEase(this.easeIn);
		}
		if (this.affectPosition)
		{
			base.transform.DOLocalMove(this.targetPosition, this.positionLerpTime, false).SetUpdate(true).SetEase(this.easeIn, this.easeInAmplitude, 0f);
		}
		if (this.group != null)
		{
			this.group.interactable = true;
		}
	}

	// Token: 0x04000A27 RID: 2599
	private Vector3 targetSize;

	// Token: 0x04000A28 RID: 2600
	private Vector3 targetPosition;

	// Token: 0x04000A29 RID: 2601
	public Vector3 startPosition = Vector3.zero;

	// Token: 0x04000A2A RID: 2602
	public Vector3 startScale = Vector3.zero;

	// Token: 0x04000A2B RID: 2603
	private Vector3 rootPositon = Vector3.zero;

	// Token: 0x04000A2C RID: 2604
	private Vector3 rootScale = Vector3.zero;

	// Token: 0x04000A2D RID: 2605
	public float positionLerpTime = 1f;

	// Token: 0x04000A2E RID: 2606
	public float scaleLerpTime = 1f;

	// Token: 0x04000A2F RID: 2607
	private CanvasGroup group;

	// Token: 0x04000A30 RID: 2608
	public bool startHidden;

	// Token: 0x04000A31 RID: 2609
	public bool affectScale = true;

	// Token: 0x04000A32 RID: 2610
	public bool affectPosition = true;

	// Token: 0x04000A33 RID: 2611
	public bool hideOpposite;

	// Token: 0x04000A34 RID: 2612
	public Ease easeIn = Ease.Linear;

	// Token: 0x04000A35 RID: 2613
	public Ease easeOut = Ease.Linear;

	// Token: 0x04000A36 RID: 2614
	public float easeInAmplitude = 1f;

	// Token: 0x04000A37 RID: 2615
	public float easeOutAmplitude = 1f;

	// Token: 0x04000A38 RID: 2616
	private bool state;
}
