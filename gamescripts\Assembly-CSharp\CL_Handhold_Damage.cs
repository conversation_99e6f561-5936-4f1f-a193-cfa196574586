﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000085 RID: 133
public class CL_Handhold_Damage : CL_Handhold
{
	// Token: 0x06000485 RID: 1157 RVA: 0x00027A44 File Offset: 0x00025C44
	private void Start()
	{
		this.localRotation = base.transform.localRotation;
	}

	// Token: 0x06000486 RID: 1158 RVA: 0x00027A58 File Offset: 0x00025C58
	internal override void Update()
	{
		base.Update();
		if (base.GetHolding())
		{
			if (this.hands.Count > 0)
			{
				this.damageTime -= Time.deltaTime;
				bool flag = false;
				if (this.damageTime <= 0f)
				{
					this.damageTime = this.damageRate;
					flag = true;
				}
				if (!this.useTotalHoldTime)
				{
					for (int i = 0; i < this.hands.Count; i++)
					{
						if (this.handHoldTime.ContainsKey(this.hands[i]))
						{
							float num = this.handHoldTime[this.hands[i]];
							float num2 = this.maxHoldTime;
							Dictionary<ENT_Player.Hand, float> dictionary = this.handHoldTime;
							ENT_Player.Hand hand = this.hands[i];
							dictionary[hand] += Time.deltaTime;
							if (this.handHoldTime[this.hands[i]] > this.maxHoldTime)
							{
								this.hands[i].ShakeHand(this.handShakeAmount * Time.deltaTime);
								CL_CameraControl.Shake(0.0005f);
							}
							if (this.handHoldTime[this.hands[i]] > this.maxHoldTime && flag)
							{
								if (this.damageSound != null)
								{
									AudioManager.PlaySound(this.damageSound, this.hands[0].handModel.position, 1f, 1f, 1f, false, 1f, null);
								}
								if (this.dropOnDamage)
								{
									for (int j = this.hands.Count - 1; j >= 0; j--)
									{
										this.hands[j].AddHandWait(this.dropGripWait);
										this.hands[j].DropHand(false);
									}
								}
								this.damageEvent.Invoke();
								if (flag)
								{
									ENT_Player.playerObject.Damage(this.damage, "handhold");
									for (int k = 0; k < this.hands.Count; k++)
									{
										this.hands[i].AddGripStrength(this.handStrainDamage);
									}
								}
								return;
							}
						}
					}
					return;
				}
				this.totalHoldTime += Time.deltaTime;
				for (int l = 0; l < this.hands.Count; l++)
				{
					float num3 = this.handHoldTime[this.hands[l]];
					float num4 = this.maxHoldTime;
					Dictionary<ENT_Player.Hand, float> dictionary = this.handHoldTime;
					ENT_Player.Hand hand = this.hands[l];
					dictionary[hand] += Time.deltaTime;
				}
				if (this.totalHoldTime > this.maxHoldTime)
				{
					for (int m = 0; m < this.hands.Count; m++)
					{
						this.hands[m].ShakeHand(this.handShakeAmount * Time.deltaTime);
					}
					CL_CameraControl.Shake(0.0005f);
					if (flag)
					{
						ENT_Player.playerObject.Damage(this.damage, "handhold");
						for (int n = 0; n < this.hands.Count; n++)
						{
							this.hands[n].AddGripStrength(this.handStrainDamage);
						}
					}
				}
				if (this.totalHoldTime > this.maxHoldTime && flag)
				{
					if (this.damageSound != null)
					{
						AudioManager.PlaySound(this.damageSound, this.hands[0].handModel.position, 1f, 1f, 1f, false, 1f, null);
					}
					if (this.dropOnDamage)
					{
						for (int num5 = this.hands.Count - 1; num5 >= 0; num5--)
						{
							this.hands[num5].AddHandWait(this.dropGripWait);
							this.hands[num5].DropHand(false);
						}
					}
					this.damageEvent.Invoke();
					return;
				}
			}
		}
		else if (this.useTotalHoldTime && this.drainTotalHoldTime)
		{
			this.totalHoldTime = Mathf.Max(this.totalHoldTime - Time.deltaTime, 0f);
		}
	}

	// Token: 0x06000487 RID: 1159 RVA: 0x00027E9C File Offset: 0x0002609C
	public override void Interact(ENT_Player p, ENT_Player.Hand hand)
	{
		if (!this.handHoldTime.ContainsKey(hand))
		{
			this.handHoldTime.Add(hand, 0f);
		}
		base.Interact(p, hand);
		Vector3 holdPosition = hand.GetHoldPosition();
		hand.SetHoldPosition(holdPosition);
	}

	// Token: 0x06000488 RID: 1160 RVA: 0x00027EDE File Offset: 0x000260DE
	public override void StopInteract(ENT_Player p, ENT_Player.Hand dropHand, string s = "")
	{
		if (this.handHoldTime.ContainsKey(dropHand))
		{
			this.handHoldTime.Remove(dropHand);
		}
		base.StopInteract(p, dropHand, s);
	}

	// Token: 0x040005FB RID: 1531
	public float maxHoldTime;

	// Token: 0x040005FC RID: 1532
	public float damage = 1f;

	// Token: 0x040005FD RID: 1533
	public string damageType = "handhold-sharp";

	// Token: 0x040005FE RID: 1534
	public float handStrainDamage = 1f;

	// Token: 0x040005FF RID: 1535
	public float damageRate = 1f;

	// Token: 0x04000600 RID: 1536
	private float damageTime;

	// Token: 0x04000601 RID: 1537
	public bool dropOnDamage;

	// Token: 0x04000602 RID: 1538
	public float dropGripWait = 0.3f;

	// Token: 0x04000603 RID: 1539
	public bool useTotalHoldTime;

	// Token: 0x04000604 RID: 1540
	public bool drainTotalHoldTime;

	// Token: 0x04000605 RID: 1541
	public float handShakeAmount = 0.1f;

	// Token: 0x04000606 RID: 1542
	private float holdShakeTimer;

	// Token: 0x04000607 RID: 1543
	[Space]
	[Header("Audio")]
	public AudioClip damageSound;

	// Token: 0x04000608 RID: 1544
	public UnityEvent damageEvent;

	// Token: 0x04000609 RID: 1545
	private Dictionary<ENT_Player.Hand, float> handHoldTime = new Dictionary<ENT_Player.Hand, float>();

	// Token: 0x0400060A RID: 1546
	private float totalHoldTime;

	// Token: 0x0400060B RID: 1547
	private Quaternion localRotation;
}
