﻿using System;
using System.Collections;
using System.Collections.Generic;
using Steamworks;
using Steamworks.Data;
using UnityEngine;

// Token: 0x020000B2 RID: 178
public class CL_AchievementManager : MonoBehaviour
{
	// Token: 0x060005CF RID: 1487 RVA: 0x00030950 File Offset: 0x0002EB50
	private void Awake()
	{
		CL_AchievementManager.instance = this;
		this.achievements = new List<CL_AchievementManager.GameAchievement>();
		foreach (AchievementCollection achievementCollection in this.achievementCollections)
		{
			AchievementCollection achievementCollection2 = Object.Instantiate<AchievementCollection>(achievementCollection);
			this.achievements.AddRange(achievementCollection2.achievements);
		}
		CL_AchievementManager.LoadAchievements(StatManager.saveData.flags);
		base.StartCoroutine(this.WaitForSteamManagerToLoad());
	}

	// Token: 0x060005D0 RID: 1488 RVA: 0x000309E0 File Offset: 0x0002EBE0
	private void OnDestroy()
	{
		CL_AchievementManager.SaveAchievements();
	}

	// Token: 0x060005D1 RID: 1489 RVA: 0x000309E7 File Offset: 0x0002EBE7
	private void Update()
	{
		if (this.slowUpdateTime < 0f)
		{
			this.CheckAchievement();
			this.slowUpdateTime = 1f;
		}
		this.slowUpdateTime -= Time.unscaledDeltaTime;
	}

	// Token: 0x060005D2 RID: 1490 RVA: 0x00030A1C File Offset: 0x0002EC1C
	private void CheckAchievement()
	{
		if (SettingsManager.settings.unlockAll)
		{
			foreach (CL_AchievementManager.GameAchievement gameAchievement in this.achievements)
			{
				gameAchievement.SetState(true, true);
			}
			return;
		}
		StatManager.SaveData saveData = StatManager.saveData;
		if (CL_GameManager.gMan && !CL_GameManager.isDead())
		{
			if (StatManager.GetStatisticInt(StatManager.sessionStats, "pitons-used") + StatManager.GetStatisticInt(StatManager.saveData.gameStats, "pitons-used") >= 100)
			{
				CL_AchievementManager.SetAchievementValue("ACH_PITONIST", true);
			}
			if (StatManager.GetStatisticInt(StatManager.sessionStats, "navitems-used") + StatManager.GetStatisticInt(StatManager.saveData.gameStats, "navitems-used") >= 100)
			{
				CL_AchievementManager.SetAchievementValue("ACH_PATHFINDER", true);
			}
			if (StatManager.GetStatisticInt(StatManager.sessionStats, "grubs-used") + StatManager.GetStatisticInt(StatManager.saveData.gameStats, "grubs-used") >= 50)
			{
				CL_AchievementManager.SetAchievementValue("ACH_50GRUBSKILLED", true);
			}
			if (StatManager.GetStatisticInt(StatManager.sessionStats, "roach-killed") + StatManager.GetStatisticInt(StatManager.saveData.gameStats, "roach-killed") >= 100)
			{
				CL_AchievementManager.SetAchievementValue("ACH_ROACHSMASH", true);
			}
			if (StatManager.GetTotalStatisticInt("grubs-used") > 0)
			{
				CL_AchievementManager.SetAchievementValue("ACH_CRIMINAL", true);
			}
			if (CL_GameManager.GetCurrentGamemode() != null && !CL_GameManager.GetCurrentGamemode().HasTag("blockroachachievements"))
			{
				if (CL_GameManager.roaches >= 50)
				{
					CL_AchievementManager.SetAchievementValue("ACH_50ROACHES", true);
				}
				if (CL_GameManager.roaches >= 100)
				{
					CL_AchievementManager.SetAchievementValue("ACH_100ROACHES", true);
				}
				if (CL_GameManager.roaches >= 500)
				{
					CL_AchievementManager.SetAchievementValue("ACH_500ROACHES", true);
				}
			}
			if (StatManager.GetStatisticInt(StatManager.sessionStats, "rebar-used") + StatManager.GetStatisticInt(StatManager.saveData.gameStats, "rebar-used") >= 100)
			{
				CL_AchievementManager.SetAchievementValue("ACH_RODGOD", true);
			}
			if (StatManager.GetTotalStatisticInt("flaregun-jump") >= 1)
			{
				CL_AchievementManager.SetAchievementValue("ACH_FLAREJUMP", true);
			}
		}
	}

	// Token: 0x060005D3 RID: 1491 RVA: 0x00030C28 File Offset: 0x0002EE28
	public static void SaveAchievements()
	{
		if (CL_AchievementManager.instance == null)
		{
			return;
		}
		foreach (CL_AchievementManager.GameAchievement gameAchievement in CL_AchievementManager.instance.achievements)
		{
			StatManager.saveData.SaveFlag(gameAchievement.id, gameAchievement.flagged, "");
		}
	}

	// Token: 0x060005D4 RID: 1492 RVA: 0x00030CA4 File Offset: 0x0002EEA4
	public static void LoadAchievements(List<StatManager.SaveData.SaveFlags> flags)
	{
		if (CL_AchievementManager.instance == null)
		{
			return;
		}
		if (CL_AchievementManager.instance.achievementDictionary == null)
		{
			CL_AchievementManager.instance.achievementDictionary = new Dictionary<string, CL_AchievementManager.GameAchievement>();
			foreach (CL_AchievementManager.GameAchievement gameAchievement in CL_AchievementManager.instance.achievements)
			{
				if (!CL_AchievementManager.instance.achievementDictionary.ContainsKey(gameAchievement.id))
				{
					CL_AchievementManager.instance.achievementDictionary.Add(gameAchievement.id, gameAchievement);
				}
				CL_AchievementManager.steamAchievements = new Dictionary<string, Achievement>();
				if (SteamUserStats.StatsRecieved)
				{
					foreach (Achievement achievement in SteamUserStats.Achievements)
					{
						CL_AchievementManager.steamAchievements.Add(achievement.Name, achievement);
					}
				}
				if (gameAchievement.steamAchievementID != "" && CL_AchievementManager.steamAchievements.ContainsKey(gameAchievement.steamAchievementID))
				{
					gameAchievement.steamHasAchieved = CL_AchievementManager.steamAchievements[gameAchievement.steamAchievementID].State;
				}
			}
		}
		if (flags != null)
		{
			foreach (StatManager.SaveData.SaveFlags saveFlags in flags)
			{
				if (CL_AchievementManager.instance.achievementDictionary.ContainsKey(saveFlags.name))
				{
					CL_AchievementManager.instance.achievementDictionary[saveFlags.name].flagged = saveFlags.value;
				}
			}
		}
	}

	// Token: 0x060005D5 RID: 1493 RVA: 0x00030E64 File Offset: 0x0002F064
	public static bool GetAchievementValue(string name)
	{
		if (CL_AchievementManager.instance == null)
		{
			return false;
		}
		if (SettingsManager.settings.unlockAll)
		{
			return true;
		}
		if (!CL_AchievementManager.instance.achievementDictionary.ContainsKey(name))
		{
			CommandConsole.LogError("Tried finding achievement: " + name + ". Achievement not found.");
			return false;
		}
		return CL_AchievementManager.instance.achievementDictionary[name].flagged;
	}

	// Token: 0x060005D6 RID: 1494 RVA: 0x00030ED0 File Offset: 0x0002F0D0
	public static void SetAchievementValue(string name, bool b)
	{
		if (CL_AchievementManager.instance == null)
		{
			return;
		}
		if (CL_GameManager.gamemode != null && !CL_GameManager.AreAchievementsAllowed())
		{
			return;
		}
		if (CL_AchievementManager.instance.achievementDictionary.ContainsKey(name))
		{
			if (!CL_AchievementManager.instance.achievementDictionary[name].CheckTags(CL_GameManager.GetBaseGamemode()) && CL_AchievementManager.instance.achievementDictionary[name].CheckTags(CL_GameManager.GetCurrentGamemode()))
			{
				return;
			}
			if (CL_AchievementManager.instance.achievementDictionary[name].flagged == b)
			{
				if (b)
				{
					CL_AchievementManager.instance.achievementDictionary[name].GetSteamAchievement();
				}
				return;
			}
			CL_AchievementManager.instance.achievementDictionary[name].SetState(b, false);
		}
	}

	// Token: 0x060005D7 RID: 1495 RVA: 0x00030F96 File Offset: 0x0002F196
	public IEnumerator WaitForSteamManagerToLoad()
	{
		while (!SteamManager.initialized)
		{
			yield return new WaitForSecondsRealtime(5.2f);
		}
		CL_AchievementManager.steamAchievements = new Dictionary<string, Achievement>();
		if (SteamUserStats.StatsRecieved)
		{
			foreach (Achievement achievement in SteamUserStats.Achievements)
			{
				CL_AchievementManager.steamAchievements.Add(achievement.Name, achievement);
			}
		}
		using (List<CL_AchievementManager.GameAchievement>.Enumerator enumerator2 = CL_AchievementManager.instance.achievements.GetEnumerator())
		{
			while (enumerator2.MoveNext())
			{
				CL_AchievementManager.GameAchievement gameAchievement = enumerator2.Current;
				if (gameAchievement.steamAchievementID != "" && CL_AchievementManager.steamAchievements.ContainsKey(gameAchievement.steamAchievementID))
				{
					gameAchievement.steamHasAchieved = CL_AchievementManager.steamAchievements[gameAchievement.steamAchievementID].State;
				}
			}
			yield break;
		}
		yield break;
	}

	// Token: 0x060005D8 RID: 1496 RVA: 0x00030F9E File Offset: 0x0002F19E
	public static void ShowAchievement(string announceText)
	{
		if (CL_AchievementManager.instance == null || CL_AchievementManager.textInstance == null)
		{
			return;
		}
		CL_AchievementManager.textInstance.ShowText(announceText);
	}

	// Token: 0x060005D9 RID: 1497 RVA: 0x00030FC8 File Offset: 0x0002F1C8
	public static void ResetAchievements()
	{
		foreach (CL_AchievementManager.GameAchievement gameAchievement in CL_AchievementManager.instance.achievements)
		{
			gameAchievement.flagged = false;
		}
		CL_AchievementManager.SaveAchievements();
	}

	// Token: 0x060005DA RID: 1498 RVA: 0x00031024 File Offset: 0x0002F224
	public void ResetSteamAchievements()
	{
		SteamUserStats.ResetAll(true);
	}

	// Token: 0x060005DB RID: 1499 RVA: 0x00031030 File Offset: 0x0002F230
	public static int GetCalculatedXP()
	{
		int num = 0;
		foreach (CL_AchievementManager.GameAchievement gameAchievement in CL_AchievementManager.instance.achievements)
		{
			Debug.Log(string.Concat(new string[]
			{
				"Checking Achievement: ",
				gameAchievement.name,
				" :: ",
				gameAchievement.flagged.ToString(),
				" :: ",
				gameAchievement.xp.ToString()
			}));
			if (gameAchievement.flagged)
			{
				num += gameAchievement.xp;
			}
		}
		return num;
	}

	// Token: 0x04000784 RID: 1924
	internal List<CL_AchievementManager.GameAchievement> achievements;

	// Token: 0x04000785 RID: 1925
	public List<AchievementCollection> achievementCollections;

	// Token: 0x04000786 RID: 1926
	private Dictionary<string, CL_AchievementManager.GameAchievement> achievementDictionary;

	// Token: 0x04000787 RID: 1927
	public static CL_AchievementManager instance;

	// Token: 0x04000788 RID: 1928
	public static UI_AchievementText textInstance;

	// Token: 0x04000789 RID: 1929
	private float slowUpdateTime;

	// Token: 0x0400078A RID: 1930
	private static Dictionary<string, Achievement> steamAchievements;

	// Token: 0x02000276 RID: 630
	[Serializable]
	public class GameAchievement
	{
		// Token: 0x06000DE9 RID: 3561 RVA: 0x000555A8 File Offset: 0x000537A8
		public void SetState(bool b, bool skipSteam = false)
		{
			if (SettingsManager.settings.unlockAll)
			{
				return;
			}
			if (!this.flagged && b)
			{
				if (!skipSteam)
				{
					this.GetSteamAchievement();
				}
				if (this.announce)
				{
					CL_AchievementManager.ShowAchievement("<size=30>" + this.name + "</size>\n<color=grey>" + this.announceText);
				}
				this.flagged = b;
				CL_ProgressionManager.AddExperience(this.xp);
				CL_ProgressionManager.UpdateUnlocks(true);
			}
			Debug.Log("Getting Achievement " + this.name);
			this.flagged = b;
		}

		// Token: 0x06000DEA RID: 3562 RVA: 0x00055634 File Offset: 0x00053834
		public void GetSteamAchievement()
		{
			if (!SteamClient.IsValid || !SteamClient.IsLoggedOn)
			{
				return;
			}
			if (this.steamAchievementID != "" && !CommandConsole.hasCheated)
			{
				Achievement achievement = new Achievement(this.steamAchievementID);
				achievement.Trigger(true);
			}
		}

		// Token: 0x06000DEB RID: 3563 RVA: 0x00055680 File Offset: 0x00053880
		internal bool CheckTags(M_Gamemode mode)
		{
			if (this.modeTagWhitelist != null && this.modeTagWhitelist.Count > 0)
			{
				if (mode == null)
				{
					return false;
				}
				if (!mode.HasAnyTag(this.modeTagWhitelist))
				{
					return false;
				}
			}
			if (this.modeTagBlacklist != null && this.modeTagBlacklist.Count > 0)
			{
				if (mode == null)
				{
					return true;
				}
				if (mode.HasAnyTag(this.modeTagBlacklist))
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x04001012 RID: 4114
		public string id;

		// Token: 0x04001013 RID: 4115
		public string name;

		// Token: 0x04001014 RID: 4116
		public bool flagged;

		// Token: 0x04001015 RID: 4117
		public bool announce = true;

		// Token: 0x04001016 RID: 4118
		public string announceText;

		// Token: 0x04001017 RID: 4119
		public int xp = 10;

		// Token: 0x04001018 RID: 4120
		[Header("Steam Achievements")]
		public string steamAchievementID;

		// Token: 0x04001019 RID: 4121
		public bool steamHasAchieved;

		// Token: 0x0400101A RID: 4122
		public List<string> modeTagWhitelist;

		// Token: 0x0400101B RID: 4123
		public List<string> modeTagBlacklist;
	}
}
