﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x0200016C RID: 364
public class UT_Framerate : MonoBehaviour
{
	// Token: 0x06000A26 RID: 2598 RVA: 0x00043F95 File Offset: 0x00042195
	private void Start()
	{
		this.counter = base.GetComponent<TMP_Text>();
	}

	// Token: 0x06000A27 RID: 2599 RVA: 0x00043FA4 File Offset: 0x000421A4
	private void Update()
	{
		this.updateTime -= Time.deltaTime;
		if (this.updateTime > 0f)
		{
			return;
		}
		this.updateTime = 0.25f;
		if (Input.GetKeyDown(KeyCode.Tab))
		{
			this.hidden = !this.hidden;
		}
		if (this.hidden)
		{
			this.counter.text = "";
			return;
		}
		float num = (float)Mathf.RoundToInt(1f / Time.unscaledDeltaTime);
		this.counter.text = num.ToString() + " FPS";
	}

	// Token: 0x04000B49 RID: 2889
	private TMP_Text counter;

	// Token: 0x04000B4A RID: 2890
	public bool hidden = true;

	// Token: 0x04000B4B RID: 2891
	private float updateTime;
}
