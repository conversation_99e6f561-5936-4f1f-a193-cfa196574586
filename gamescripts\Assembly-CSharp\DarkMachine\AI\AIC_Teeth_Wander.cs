﻿using System;
using Drawing;
using UnityEngine;

namespace DarkMachine.AI
{
	// Token: 0x020001D4 RID: 468
	public class AIC_Teeth_Wander : AIStateComponent
	{
		// Token: 0x17000009 RID: 9
		// (get) Token: 0x06000BD9 RID: 3033 RVA: 0x0004B022 File Offset: 0x00049222
		public override string name
		{
			get
			{
				return "wander";
			}
		}

		// Token: 0x06000BDA RID: 3034 RVA: 0x0004B02C File Offset: 0x0004922C
		public override void Enter(AIGameEntity e, string[] args)
		{
			base.Enter(e, args);
			this.timeSinceWanderStarted = 0f;
			if (this.teeth == null)
			{
				this.teeth = e.GetComponent<DEN_Teeth>();
			}
			this.teeth.locked = false;
			this.teeth.curBarkTime = Random.Range(this.teeth.barkTime / 2f, this.teeth.barkTime * 2f);
			this.stuckDetection = 0f;
			Debug.Log("Entered wander state for " + e.name);
		}

		// Token: 0x06000BDB RID: 3035 RVA: 0x0004B0C4 File Offset: 0x000492C4
		public override void Execute(AIGameEntity e, string[] args = null)
		{
			base.Execute(e, args);
			this.timeSinceWanderStarted += Time.deltaTime;
			float num = Vector3.Distance(this.lastWanderPosition, e.transform.position);
			this.teeth.curBarkTime -= Time.deltaTime;
			if (this.teeth.curBarkTime <= 0f)
			{
				this.teeth.curBarkTime = Random.Range(this.teeth.barkTime / 2f, this.teeth.barkTime * 2f);
				e.clipHandler.PlaySound("teeth:bark");
			}
			float num2 = Vector3.Distance(e.target.position, e.transform.position);
			if (num < 0.3f * Time.deltaTime && num2 > 15f)
			{
				this.stuckDetection += Time.deltaTime;
				if (this.stuckDetection > 1f)
				{
					e.ChangeState("burrow");
					return;
				}
			}
			else
			{
				this.stuckDetection = 0f;
			}
			this.lastWanderPosition = e.transform.position;
			if (this.currentTimer <= 0f)
			{
				this.isWaiting = !this.isWaiting;
				this.currentTimer = (this.isWaiting ? this.wanderWaitTime : this.wanderTime);
				this.wanderTarget = Vector3.Lerp(e.transform.position, e.target.position, 0.5f) + Random.insideUnitSphere * this.wanderDistance;
				e.targetPosition = (this.isWaiting ? Vector3.Lerp(e.transform.position, e.target.position, 0.5f) : this.wanderTarget);
			}
			this.currentTimer -= Time.deltaTime;
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.Cross(this.wanderTarget, 0.3f, new Color(0f, 1f, 0f, 1f));
				CL_DebugView.draw.Label2D(this.wanderTarget + Vector3.up * 0.3f, "Wandertarget", 8f, LabelAlignment.Center, Color.white);
			}
			if (e.sight.CanSeeTarget(e.target))
			{
				e.ChangeState("chase");
			}
			if (num2 > 15f && this.timeSinceWanderStarted > 15f)
			{
				e.ChangeState("burrow");
				return;
			}
			if (num2 > this.burrowDistance)
			{
				e.ChangeState("burrow");
			}
		}

		// Token: 0x04000CD8 RID: 3288
		public float wanderTime = 5f;

		// Token: 0x04000CD9 RID: 3289
		public float wanderWaitTime = 2f;

		// Token: 0x04000CDA RID: 3290
		public float wanderDistance = 5f;

		// Token: 0x04000CDB RID: 3291
		private bool isWaiting;

		// Token: 0x04000CDC RID: 3292
		private float currentTimer = 1f;

		// Token: 0x04000CDD RID: 3293
		private Vector3 wanderTarget;

		// Token: 0x04000CDE RID: 3294
		public float burrowDistance = 50f;

		// Token: 0x04000CDF RID: 3295
		private float timeSinceWanderStarted;

		// Token: 0x04000CE0 RID: 3296
		public DEN_Teeth teeth;

		// Token: 0x04000CE1 RID: 3297
		private Vector3 lastWanderPosition;

		// Token: 0x04000CE2 RID: 3298
		private float stuckDetection;
	}
}
