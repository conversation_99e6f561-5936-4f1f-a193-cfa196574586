﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x0200011C RID: 284
public class UI_AutoScrollToSelected : MonoBehaviour
{
	// Token: 0x0600087E RID: 2174 RVA: 0x0003D0DC File Offset: 0x0003B2DC
	private void Awake()
	{
		this.scrollRect = base.GetComponent<ScrollRect>();
	}

	// Token: 0x0600087F RID: 2175 RVA: 0x0003D0EC File Offset: 0x0003B2EC
	private void LateUpdate()
	{
		if (this.onlyScrollOnController && !InputManager.IsGamepad())
		{
			return;
		}
		GameObject currentSelectedGameObject = EventSystem.current.currentSelectedGameObject;
		if (currentSelectedGameObject == null)
		{
			return;
		}
		foreach (Transform transform in this.ignoreList)
		{
			if (currentSelectedGameObject.transform.IsChildOf(transform))
			{
				return;
			}
		}
		if (!currentSelectedGameObject.transform.IsChildOf(this.scrollRect.content))
		{
			return;
		}
		RectTransform component = currentSelectedGameObject.GetComponent<RectTransform>();
		if (component == null)
		{
			return;
		}
		Vector2 vector = this.CalculateScrollOffset(this.scrollRect, component);
		if (this.scrollRect.vertical)
		{
			float num = this.scrollRect.content.anchoredPosition.y + vector.y;
			this.scrollRect.content.anchoredPosition = new Vector2(this.scrollRect.content.anchoredPosition.x, num);
		}
		if (this.scrollRect.horizontal)
		{
			float num2 = this.scrollRect.content.anchoredPosition.x + vector.x;
			this.scrollRect.content.anchoredPosition = new Vector2(num2, this.scrollRect.content.anchoredPosition.y);
		}
	}

	// Token: 0x06000880 RID: 2176 RVA: 0x0003D258 File Offset: 0x0003B458
	private Vector2 CalculateScrollOffset(ScrollRect scrollRect, RectTransform target)
	{
		Vector2 zero = Vector2.zero;
		RectTransform content = scrollRect.content;
		RectTransform viewport = scrollRect.viewport;
		Vector3[] array = new Vector3[4];
		content.GetWorldCorners(array);
		Vector3[] array2 = new Vector3[4];
		viewport.GetWorldCorners(array2);
		Vector3[] array3 = new Vector3[4];
		target.GetWorldCorners(array3);
		if (scrollRect.vertical)
		{
			float y = array2[1].y;
			float y2 = array2[0].y;
			float y3 = array3[1].y;
			float y4 = array3[0].y;
			if (y3 > y)
			{
				zero.y = y - y3;
			}
			else if (y4 < y2)
			{
				zero.y = y2 - y4;
			}
		}
		if (scrollRect.horizontal)
		{
			float x = array2[0].x;
			float x2 = array2[2].x;
			float x3 = array3[0].x;
			float x4 = array3[2].x;
			if (x3 < x)
			{
				zero.x = x - x3;
			}
			else if (x4 > x2)
			{
				zero.x = x2 - x4;
			}
		}
		return zero;
	}

	// Token: 0x040009D7 RID: 2519
	private ScrollRect scrollRect;

	// Token: 0x040009D8 RID: 2520
	public List<Transform> ignoreList;

	// Token: 0x040009D9 RID: 2521
	public bool onlyScrollOnController;
}
