﻿using System;
using UnityEngine;

// Token: 0x02000162 RID: 354
public class UT_CompetitiveModeEnable : MonoBehaviour
{
	// Token: 0x06000A00 RID: 2560 RVA: 0x00043534 File Offset: 0x00041734
	private void Awake()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.CheckStatus));
	}

	// Token: 0x06000A01 RID: 2561 RVA: 0x00043556 File Offset: 0x00041756
	private void OnDestroy()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Remove(SettingsManager.SettingsRefreshUpdate, new Action(this.CheckStatus));
	}

	// Token: 0x06000A02 RID: 2562 RVA: 0x00043578 File Offset: 0x00041778
	private void OnEnable()
	{
		this.CheckStatus();
	}

	// Token: 0x06000A03 RID: 2563 RVA: 0x00043580 File Offset: 0x00041780
	public void CheckStatus()
	{
		base.gameObject.SetActive(SettingsManager.settings.g_competitive ? (!this.disable) : this.disable);
	}

	// Token: 0x04000B1D RID: 2845
	public bool disable;
}
