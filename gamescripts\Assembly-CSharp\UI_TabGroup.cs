﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000140 RID: 320
public class UI_TabGroup : MonoBehaviour
{
	// Token: 0x0600093F RID: 2367 RVA: 0x0003FF10 File Offset: 0x0003E110
	private void Start()
	{
		this.group = base.GetComponent<CanvasGroup>();
		using (List<UI_TabGroup.Tab>.Enumerator enumerator = this.tabs.GetEnumerator())
		{
			while (enumerator.MoveNext())
			{
				UI_TabGroup.Tab g = enumerator.Current;
				if (g.button != null)
				{
					g.button.onClick.AddListener(delegate
					{
						this.SelectTab(g.name);
					});
					if (g.onlyDev)
					{
						g.button.gameObject.SetActive(false);
					}
				}
			}
		}
		this.SelectTab(this.tabs[0].name, true);
	}

	// Token: 0x06000940 RID: 2368 RVA: 0x0003FFEC File Offset: 0x0003E1EC
	private void OnEnable()
	{
		base.StartCoroutine(this.<OnEnable>g__FrameWait|18_0());
	}

	// Token: 0x06000941 RID: 2369 RVA: 0x0003FFFB File Offset: 0x0003E1FB
	private void OnDisable()
	{
		this.active = false;
	}

	// Token: 0x06000942 RID: 2370 RVA: 0x00040004 File Offset: 0x0003E204
	public void SelectTab(string tabName)
	{
		this.SelectTab(tabName, false);
	}

	// Token: 0x06000943 RID: 2371 RVA: 0x00040010 File Offset: 0x0003E210
	public void SelectTab(string tabName, bool skipAudio)
	{
		for (int i = 0; i < this.tabs.Count; i++)
		{
			UI_TabGroup.Tab tab = this.tabs[i];
			if (tab.name == tabName)
			{
				tab.button.image.color = this.openColor;
				if (this.changeTextColor)
				{
					tab.buttonText.color = this.textOpenColor;
				}
				tab.tabObject.SetActive(true);
				this.currentTab = i;
				if (tab.firstSelect != null && EventSystem.current.currentSelectedGameObject != tab.firstSelect)
				{
					tab.firstSelect.Select();
				}
			}
			else
			{
				tab.button.image.color = this.closeColor;
				if (this.changeTextColor)
				{
					tab.buttonText.color = this.textCloseColor;
				}
				tab.tabObject.SetActive(false);
			}
		}
		if (this.switchTabSound != null && !skipAudio)
		{
			AudioManager.PlayUISound(this.switchTabSound, this.switchTabSoundVolume, 1f);
		}
	}

	// Token: 0x06000944 RID: 2372 RVA: 0x0004012C File Offset: 0x0003E32C
	private void Update()
	{
		if (!this.active)
		{
			return;
		}
		if (this.parentCanvasGroup != null && !this.parentCanvasGroup.interactable)
		{
			return;
		}
		if (this.disableIfGamepad)
		{
			if (InputManager.instance != null && InputManager.IsGamepad())
			{
				this.group.interactable = false;
			}
			else
			{
				this.group.interactable = true;
			}
		}
		if (this.tabLeftButton != "" && this.tabRightButton != "")
		{
			if (InputManager.GetButton(this.tabLeftButton).Down)
			{
				this.SwitchTab(-1);
				return;
			}
			if (InputManager.GetButton(this.tabRightButton).Down)
			{
				this.SwitchTab(1);
			}
		}
	}

	// Token: 0x06000945 RID: 2373 RVA: 0x000401EC File Offset: 0x0003E3EC
	private void SwitchTab(int x = 1)
	{
		this.currentTab += x;
		if (this.currentTab >= this.tabs.Count)
		{
			this.currentTab = 0;
		}
		if (this.currentTab < 0)
		{
			this.currentTab = this.tabs.Count - 1;
		}
		if (!this.tabs[this.currentTab].button.gameObject.activeSelf)
		{
			this.SwitchTab(x);
			return;
		}
		for (int i = 0; i < this.tabs.Count; i++)
		{
			UI_TabGroup.Tab tab = this.tabs[i];
			if (tab.name == this.tabs[this.currentTab].name)
			{
				tab.button.Select();
				tab.button.onClick.Invoke();
			}
		}
	}

	// Token: 0x06000947 RID: 2375 RVA: 0x000402DB File Offset: 0x0003E4DB
	[CompilerGenerated]
	private IEnumerator <OnEnable>g__FrameWait|18_0()
	{
		yield return new WaitForSecondsRealtime(0.1f);
		this.active = true;
		yield break;
	}

	// Token: 0x04000A84 RID: 2692
	private bool active;

	// Token: 0x04000A85 RID: 2693
	public Color openColor;

	// Token: 0x04000A86 RID: 2694
	public Color closeColor;

	// Token: 0x04000A87 RID: 2695
	public bool changeTextColor;

	// Token: 0x04000A88 RID: 2696
	public Color textOpenColor;

	// Token: 0x04000A89 RID: 2697
	public Color textCloseColor;

	// Token: 0x04000A8A RID: 2698
	public List<UI_TabGroup.Tab> tabs;

	// Token: 0x04000A8B RID: 2699
	public string tabLeftButton;

	// Token: 0x04000A8C RID: 2700
	public string tabRightButton;

	// Token: 0x04000A8D RID: 2701
	private int currentTab;

	// Token: 0x04000A8E RID: 2702
	public bool disableIfGamepad;

	// Token: 0x04000A8F RID: 2703
	public bool autoSelectOnEnable;

	// Token: 0x04000A90 RID: 2704
	private CanvasGroup group;

	// Token: 0x04000A91 RID: 2705
	public CanvasGroup parentCanvasGroup;

	// Token: 0x04000A92 RID: 2706
	public AudioClip switchTabSound;

	// Token: 0x04000A93 RID: 2707
	public float switchTabSoundVolume = 0.5f;

	// Token: 0x020002C4 RID: 708
	[Serializable]
	public class Tab
	{
		// Token: 0x040011C0 RID: 4544
		public string name;

		// Token: 0x040011C1 RID: 4545
		public Button button;

		// Token: 0x040011C2 RID: 4546
		public GameObject tabObject;

		// Token: 0x040011C3 RID: 4547
		public Selectable firstSelect;

		// Token: 0x040011C4 RID: 4548
		public TMP_Text buttonText;

		// Token: 0x040011C5 RID: 4549
		public bool onlyDev;
	}
}
