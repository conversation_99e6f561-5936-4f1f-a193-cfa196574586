﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000FB RID: 251
public class OS_Folder : OS_Window
{
	// Token: 0x060007A6 RID: 1958 RVA: 0x00039C76 File Offset: 0x00037E76
	public void CreateFiles(List<OS_Filesystem.FileInfo> files)
	{
		this.os.GetFilesystem().CreateFiles(files, this);
	}

	// Token: 0x060007A7 RID: 1959 RVA: 0x00039C8A File Offset: 0x00037E8A
	public void AddFile(OS_Filesystem.FileInfo newFile)
	{
		if (!this.subFiles.Contains(newFile))
		{
			this.subFiles.Add(newFile);
		}
		if (this.file != null)
		{
			this.file.fileInfo.children = this.subFiles;
		}
	}

	// Token: 0x060007A8 RID: 1960 RVA: 0x00039CCC File Offset: 0x00037ECC
	public void RemoveFile(OS_Filesystem.FileInfo newFile)
	{
		if (this.subFiles.Contains(newFile))
		{
			this.subFiles.Remove(newFile);
		}
		if (this.file != null)
		{
			this.file.fileInfo.children = this.subFiles;
		}
	}

	// Token: 0x060007A9 RID: 1961 RVA: 0x00039D18 File Offset: 0x00037F18
	public override void CloseApp()
	{
		OS_Manager.soundPlayer.PlaySound("os:folder-close");
		this.closeEvent.Invoke();
		this.file.CloseFile();
		bool flag = true;
		if (this.subFiles.Count > 0)
		{
			using (List<OS_Filesystem.FileInfo>.Enumerator enumerator = this.subFiles.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					if (enumerator.Current.fileObject.open)
					{
						flag = false;
						break;
					}
				}
			}
		}
		if (!flag)
		{
			base.gameObject.SetActive(false);
			return;
		}
		this.os.CloseAppWindow(this, this.id);
	}

	// Token: 0x060007AA RID: 1962 RVA: 0x00039DCC File Offset: 0x00037FCC
	public void SortFiles()
	{
		List<Transform> list = new List<Transform>();
		foreach (object obj in this.contents.transform)
		{
			Transform transform = (Transform)obj;
			list.Add(transform);
		}
		list.Sort((Transform a, Transform b) => a.name.CompareTo(b.name));
		for (int i = 0; i < list.Count; i++)
		{
			list[i].SetSiblingIndex(i);
		}
		if (this.gridSorter != null)
		{
			base.StartCoroutine(this.Sorter());
		}
	}

	// Token: 0x060007AB RID: 1963 RVA: 0x00039E94 File Offset: 0x00038094
	private IEnumerator Sorter()
	{
		this.gridSorter.enabled = true;
		yield return null;
		this.gridSorter.enabled = false;
		yield break;
	}

	// Token: 0x060007AC RID: 1964 RVA: 0x00039EA3 File Offset: 0x000380A3
	public void NewFolder()
	{
		this.NewFolder(OS_Manager.mouseRealPosition, "New Folder", null, true);
	}

	// Token: 0x060007AD RID: 1965 RVA: 0x00039EB8 File Offset: 0x000380B8
	public OS_File NewFolder(Vector2 pos, string folderName = "New Folder", OS_Filesystem.IconType icon = null, bool createAtWorldPosition = true)
	{
		OS_Filesystem.FileInfo fileInfo = new OS_Filesystem.FileInfo();
		fileInfo.type = OS_Filesystem.FileInfo.fileType.folder;
		fileInfo.name = folderName;
		fileInfo.location = this.location;
		fileInfo.useCustomFiletype = false;
		if (icon != null)
		{
			fileInfo.icon = icon.icon;
			fileInfo.openIcon = icon.openIcon;
		}
		if (this.os == null)
		{
			this.os = OS_Manager.activeComputer;
		}
		if (fileInfo.targetWindow != null)
		{
			fileInfo.targetWindow.Initialize(this.os);
		}
		this.contents.GetComponent<RectTransform>();
		fileInfo.position = pos;
		fileInfo.useWorldPosition = createAtWorldPosition;
		return this.os.GetFilesystem().CreateFile(fileInfo, this);
	}

	// Token: 0x060007AE RID: 1966 RVA: 0x00039F6C File Offset: 0x0003816C
	public override void SelectFirst()
	{
		if (this.subFiles[0].fileObject.interactable)
		{
			this.subFiles[0].fileObject.Select();
		}
	}

	// Token: 0x04000917 RID: 2327
	public GameObject contents;

	// Token: 0x04000918 RID: 2328
	public List<OS_Filesystem.FileInfo> subFiles = new List<OS_Filesystem.FileInfo>();

	// Token: 0x04000919 RID: 2329
	public string location = "Desktop/";

	// Token: 0x0400091A RID: 2330
	public GridLayoutGroup gridSorter;

	// Token: 0x0400091B RID: 2331
	public bool locked;
}
