﻿using System;
using UnityEngine;

// Token: 0x02000191 RID: 401
public class UT_ShimmerHold : MonoBehaviour
{
	// Token: 0x06000AD6 RID: 2774 RVA: 0x00046C56 File Offset: 0x00044E56
	private void Start()
	{
		this.holdRenderer = base.GetComponent<Renderer>();
		this.holdRenderer.material = FXManager.GetSharedHandholdMaterial(this.holdRenderer.material);
	}

	// Token: 0x04000BD7 RID: 3031
	private Renderer holdRenderer;
}
