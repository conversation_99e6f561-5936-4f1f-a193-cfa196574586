﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020000A5 RID: 165
[Serializable]
public class GamemodeModule_Training : GamemodeModule
{
	// Token: 0x0600054D RID: 1357 RVA: 0x0002C236 File Offset: 0x0002A436
	public static GamemodeModule_Training.SectorSettings GetSectorSettings()
	{
		if (GamemodeModule_Training.sectorSettings == null)
		{
			GamemodeModule_Training.sectorSettings = new GamemodeModule_Training.SectorSettings();
		}
		return GamemodeModule_Training.sectorSettings;
	}

	// Token: 0x0600054E RID: 1358 RVA: 0x0002C250 File Offset: 0x0002A450
	public static void UpdateSectorFlags()
	{
		CL_GameManager.SetGameFlag("race", GamemodeModule_Training.sectorSettings.race, "", false);
		CL_GameManager.SetGameFlag("zoneClimbing", GamemodeModule_Training.sectorSettings.zoneClimbing, "", false);
		CL_GameManager.SetGameFlag("zoneItems", GamemodeModule_Training.sectorSettings.zoneItems, "", false);
		CL_GameManager.SetGameFlag("zoneMastery", GamemodeModule_Training.sectorSettings.zoneMastery, "", false);
		CL_GameManager.SetGameFlag("hazardFlood", GamemodeModule_Training.sectorSettings.hazardFlood, "", false);
		CL_GameManager.SetGameFlag("hazardBarnacles", GamemodeModule_Training.sectorSettings.hazardBarnacles, "", false);
		CL_GameManager.SetGameFlag("hazardDrones", GamemodeModule_Training.sectorSettings.hazardDrones, "", false);
		CL_GameManager.SetGameFlag("hazardTurrets", GamemodeModule_Training.sectorSettings.hazardTurrets, "", false);
	}

	// Token: 0x0600054F RID: 1359 RVA: 0x0002C330 File Offset: 0x0002A530
	public override void Initialize(M_Gamemode gamemode)
	{
		base.Initialize(gamemode);
		if (GamemodeModule_Training.sectorSettings == null)
		{
			GamemodeModule_Training.sectorSettings = new GamemodeModule_Training.SectorSettings();
		}
		this.roomScores = new List<GamemodeModule_Training.RoomScore>();
		StatManager.SaveData.GameModeData gamemodeSaveData = gamemode.GetGamemodeSaveData();
		if (gamemodeSaveData != null)
		{
			Debug.Log((string)StatManager.GetStatisticValue(gamemodeSaveData.stats, "roomScoreData"));
		}
		GamemodeModule_Training.UpdateSectorFlags();
	}

	// Token: 0x06000550 RID: 1360 RVA: 0x0002C38C File Offset: 0x0002A58C
	public override void RunEvent(string e)
	{
		base.RunEvent(e);
		if (e == "start" && GamemodeModule_Training.sectorSettings.hazardFlood)
		{
			DEN_DeathFloor den_DeathFloor = Object.Instantiate<DEN_DeathFloor>(this.floodAsset, new Vector3(0f, -50f, 0f), Quaternion.identity);
			float num = Mathf.Max(this.floodSpeedMult, 0.05f);
			den_DeathFloor.speed *= GamemodeModule_Training.sectorSettings.floodSpeed * num;
			den_DeathFloor.speedIncreaseRate *= GamemodeModule_Training.sectorSettings.floodSpeed * num;
		}
	}

	// Token: 0x06000551 RID: 1361 RVA: 0x0002C41F File Offset: 0x0002A61F
	public override void OnFinish(bool hasFinished)
	{
	}

	// Token: 0x06000552 RID: 1362 RVA: 0x0002C424 File Offset: 0x0002A624
	public override float GetScore(bool hasFinished = false)
	{
		float num = CL_GameManager.gMan.GetPlayerAscent() * CL_GameManager.gMan.GetPlayerAscentRate();
		if (hasFinished)
		{
			num *= this.winScoreMultiplier;
		}
		return num;
	}

	// Token: 0x040006CD RID: 1741
	private List<GamemodeModule_Training.RoomScore> roomScores;

	// Token: 0x040006CE RID: 1742
	public static GamemodeModule_Training.SectorSettings sectorSettings;

	// Token: 0x040006CF RID: 1743
	public DEN_DeathFloor floodAsset;

	// Token: 0x040006D0 RID: 1744
	public float floodSpeedMult = 2f;

	// Token: 0x040006D1 RID: 1745
	public float winScoreMultiplier = 5f;

	// Token: 0x02000260 RID: 608
	public class SectorSettings
	{
		// Token: 0x04000F92 RID: 3986
		public bool race;

		// Token: 0x04000F93 RID: 3987
		public bool zoneClimbing = true;

		// Token: 0x04000F94 RID: 3988
		public bool zoneItems = true;

		// Token: 0x04000F95 RID: 3989
		public bool zoneMastery = true;

		// Token: 0x04000F96 RID: 3990
		public bool hazardFlood;

		// Token: 0x04000F97 RID: 3991
		public bool hazardBarnacles;

		// Token: 0x04000F98 RID: 3992
		public bool hazardDrones;

		// Token: 0x04000F99 RID: 3993
		public bool hazardTurrets;

		// Token: 0x04000F9A RID: 3994
		public float floodSpeed = 0.1f;
	}

	// Token: 0x02000261 RID: 609
	public class RoomScore
	{
		// Token: 0x04000F9B RID: 3995
		public string id;

		// Token: 0x04000F9C RID: 3996
		public float timeToBeat;
	}
}
