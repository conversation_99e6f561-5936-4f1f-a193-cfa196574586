﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using Unity.VisualScripting;
using UnityEngine;

// Token: 0x020000AE RID: 174
[RequireComponent(typeof(HandholdManager))]
public class WorldLoader : MonoBehaviour
{
	// Token: 0x060005A5 RID: 1445 RVA: 0x0002E7A8 File Offset: 0x0002C9A8
	private void Awake()
	{
		WorldLoader.instance = this;
		if (WorldLoader.presetSeed != 0)
		{
			Debug.Log("Using custom seed: " + WorldLoader.presetSeed.ToString());
			WorldLoader.customSeed = true;
		}
	}

	// Token: 0x060005A6 RID: 1446 RVA: 0x0002E7D8 File Offset: 0x0002C9D8
	public void Initialize()
	{
		this.handholdManager = base.GetComponent<HandholdManager>();
		this.branches = new List<WorldLoader.BranchInfo>();
		this.currentBranch = new WorldLoader.BranchInfo();
		this.currentBranch.id = "branch-root";
		this.currentBranch.levelTracker = new List<WorldLoader.LevelInfo>();
		this.currentBranch.children = new List<WorldLoader.BranchInfo>();
		this.branches.Add(this.currentBranch);
		if (!this.active || !base.gameObject.activeSelf)
		{
			return;
		}
		base.StopAllCoroutines();
		if (WorldLoader.presetSeed != 0)
		{
			Debug.Log("Using custom seed: " + WorldLoader.presetSeed.ToString());
			this.seed = WorldLoader.presetSeed;
			WorldLoader.customSeed = true;
			WorldLoader.presetSeed = 0;
		}
		else
		{
			WorldLoader.customSeed = false;
			if (this.seed == 0 || this.seed == -1)
			{
				this.seed = Random.Range(0, 10000000);
			}
		}
		if (!CL_SaveManager.loadSessionSaveOnLoad)
		{
			base.StartCoroutine(this.GenerateLevels(this.GetCurrentBranch(), null));
			if (this.currentBranch.levelTracker != null && this.currentBranch.levelTracker.Count > 0)
			{
				this.currentBranch.currentLevel = this.currentBranch.levelTracker[0];
				if (this.currentBranch.currentLevel.level.spawnPosition != null)
				{
					ENT_Player.playerObject.Teleport(this.currentBranch.currentLevel.level.spawnPosition.position, this.currentBranch.currentLevel.level.spawnPosition.rotation, false);
				}
			}
			else
			{
				WorldLoader.isLoaded = true;
			}
		}
		WorldLoader.initialized = true;
		CommandConsole.AddCommand("teleportplayertolevel", new Action<string[]>(this.TeleportPlayerToTargetLevel), true);
		CommandConsole.AddCommand("listloadedlevels", new Action<string[]>(this.ListLoadedLevels), true);
		CommandConsole.AddCommand("getgenerationseed", new Action<string[]>(this.GetSeed), true);
		CommandConsole.AddCommand("setgenerationseed", new Action<string[]>(WorldLoader.ReloadWithSeed), true);
		this.startingSeed = this.seed;
	}

	// Token: 0x060005A7 RID: 1447 RVA: 0x0002E9F4 File Offset: 0x0002CBF4
	public WorldLoader.BranchInfo GetNewBranch(string id)
	{
		WorldLoader.BranchInfo branchInfo = new WorldLoader.BranchInfo();
		branchInfo.id = id;
		branchInfo.levelTracker = new List<WorldLoader.LevelInfo>();
		branchInfo.children = new List<WorldLoader.BranchInfo>();
		this.branches.Add(branchInfo);
		return branchInfo;
	}

	// Token: 0x060005A8 RID: 1448 RVA: 0x0002EA34 File Offset: 0x0002CC34
	private void Update()
	{
		if (this.currentBranch.levelTracker == null || this.currentBranch.levelTracker.Count == 0)
		{
			return;
		}
		if (WorldLoader.isLoaded)
		{
			this.LoadUnload(CL_GameManager.gMan.localPlayer.transform.position.y, false);
			if (FXManager.fxMan != null && this.currentBranch != null && this.currentBranch.currentLevel.level != null && this.currentBranch.currentLevel.level.region != null)
			{
				float num = 0.1f;
				FXManager.defaultData.ditherLevels = Mathf.Lerp(FXManager.defaultData.ditherLevels, (float)this.currentBranch.currentLevel.level.region.ditherLevels, Time.fixedDeltaTime * num);
				FXManager.defaultData.fog = Color.Lerp(FXManager.defaultData.fog, this.currentBranch.currentLevel.level.region.fogColor, Time.fixedDeltaTime * num);
				if (this.currentBranch.currentLevel != null && this.currentBranch.currentLevel.level.subRegion != null)
				{
					this.currentBranch.currentLevel.level.subRegion.Update();
				}
			}
			return;
		}
	}

	// Token: 0x060005A9 RID: 1449 RVA: 0x0002EBA0 File Offset: 0x0002CDA0
	private void OnDestroy()
	{
		if (WorldLoader.instance == this)
		{
			WorldLoader.initialized = false;
			WorldLoader.instance = null;
		}
	}

	// Token: 0x060005AA RID: 1450 RVA: 0x0002EBBB File Offset: 0x0002CDBB
	private IEnumerator GenerateLevels(WorldLoader.BranchInfo branch, WorldLoader.GenerationParameters genParams = null)
	{
		WorldLoader.isLoaded = false;
		int startingLevel = branch.levelTracker.Count;
		int worldPreloadStartID = branch.levelTracker.Count;
		if (genParams == null)
		{
			genParams = new WorldLoader.GenerationParameters();
		}
		if (genParams.forceResetGeneratorToGamemode)
		{
			genParams.generator = CL_GameManager.gamemode;
		}
		if (genParams.generator == null)
		{
			if (this.currentGenerator == null)
			{
				genParams.generator = CL_GameManager.gamemode;
			}
			else
			{
				genParams.generator = this.currentGenerator;
			}
		}
		this.currentGenerator = genParams.generator;
		Debug.Log("Generation Seed: " + this.seed.ToString());
		if (this.seed != -1)
		{
			this.seed += genParams.seedOffset;
			Random.InitState(this.seed);
		}
		this.loading = true;
		CL_GameManager.SetLoading(true);
		List<M_Level> levelGenerationList = new List<M_Level>();
		List<M_Level> list = null;
		if (genParams.preloadLevels != null && genParams.generationType == WorldLoader.GenerationParameters.GenerationType.generateUntilFound)
		{
			Debug.Log("Preload levels!!");
			bool flag = false;
			Debug.Log(CL_GameManager.gamemode);
			int num = 0;
			while (!flag)
			{
				num++;
				List<M_Level> generationList = genParams.generator.GetGenerationList(null, null, null);
				for (int j = 0; j < generationList.Count; j++)
				{
					if (generationList[j].name == genParams.preloadLevels[0].name)
					{
						flag = true;
						if (genParams.preloadLevels.Count > 1)
						{
							generationList.InsertRange(j, genParams.preloadLevels.GetRange(1, genParams.preloadLevels.Count - 1));
						}
						list = generationList;
						worldPreloadStartID += j;
						Debug.Log(string.Format("Preload ID : {0} I: {1} Start ID: {2}", worldPreloadStartID, j, startingLevel));
						break;
					}
				}
				if (flag)
				{
					break;
				}
				if (num > 1000)
				{
					Debug.LogError("ERROR: COULD NOT FIND LEVEL " + genParams.preloadLevels[0].name + " IN GENERATION STEP. EXITING GAME.");
					CL_GameManager.gMan.LoadScene("Main-Menu", false);
					yield break;
				}
			}
		}
		else if (genParams.preloadLevels != null && genParams.generationType == WorldLoader.GenerationParameters.GenerationType.replace)
		{
			list = genParams.preloadLevels;
		}
		else
		{
			list = genParams.generator.GetGenerationList(genParams.lastRegion, genParams.startRegion, null);
		}
		levelGenerationList.AddRange(list);
		Vector3 positionOffset = Vector3.up * genParams.generationHeightOffset;
		int num2;
		for (int i = 0; i < levelGenerationList.Count; i = num2 + 1)
		{
			M_Level m_Level = Object.Instantiate<M_Level>(levelGenerationList[i], Vector3.zero, Quaternion.identity, base.transform);
			m_Level.OnSpawn();
			if (i > 0)
			{
				if (Random.value > 0.5f && m_Level.canFlip)
				{
					m_Level.transform.localScale = new Vector3(-1f, 1f, 1f);
					m_Level.SetFlipped(true);
				}
				else
				{
					m_Level.SetFlipped(false);
				}
			}
			if (branch.levelTracker.Count > 0 || branch.parentLevel != null)
			{
				Transform transform;
				if (branch.levelTracker.Count > 0)
				{
					transform = branch.levelTracker[i - 1 + startingLevel].level.GetLevelExit();
				}
				else
				{
					WorldLoader.LevelInfo parentLevel = branch.parentLevel;
					transform = branch.originTransform;
				}
				this.levelExitPosition = transform.position;
				Quaternion quaternion = transform.rotation * Quaternion.Inverse(m_Level.GetLevelEntrance().rotation);
				m_Level.transform.rotation = quaternion * m_Level.transform.rotation;
				m_Level.transform.position += transform.position - m_Level.GetLevelEntrance().position;
			}
			else
			{
				this.levelExitPosition = positionOffset;
				m_Level.transform.position = positionOffset;
			}
			this.handholdManager.LoadHandholds(m_Level.gameObject);
			WorldLoader.LevelInfo levelInfo = new WorldLoader.LevelInfo();
			levelInfo.level = m_Level;
			levelInfo.connectedLevels = new List<WorldLoader.LevelInfo>();
			levelInfo.branch = branch;
			if (branch.levelTracker.Count > 0 || branch.parentLevel != null)
			{
				if (branch.levelTracker.Count > 0)
				{
					levelInfo.connectedLevels.Add(branch.levelTracker[i - 1 + startingLevel]);
					branch.levelTracker[i - 1 + startingLevel].connectedLevels.Add(levelInfo);
				}
				else
				{
					levelInfo.connectedLevels.Add(branch.parentLevel);
					branch.parentLevel.connectedLevels.Add(levelInfo);
				}
			}
			levelInfo.lowBounds = levelInfo.level.GetLevelEntrance().transform.position.y - 1f;
			levelInfo.highBounds = levelInfo.level.GetLevelExit().transform.position.y + 1f;
			branch.levelTracker.Add(levelInfo);
			if (branch.levelTracker[i] != branch.currentLevel)
			{
				branch.levelTracker[i].level.gameObject.SetActive(false);
			}
			if (branch.levelTracker[i].stopLevel || m_Level.DoesPauseGeneration())
			{
				break;
			}
			if (Mathf.Repeat((float)i, 2f) == 0f)
			{
				yield return null;
			}
			num2 = i;
		}
		yield return null;
		if (genParams.endType == WorldLoader.GenerationParameters.ReachEndType.stop)
		{
			branch.levelTracker[branch.levelTracker.Count - 1].stopLevel = true;
		}
		if (genParams.setPlayerPositionToStartLevel)
		{
			if (branch.levelTracker[0].level.spawnPosition != null)
			{
				ENT_Player.playerObject.Teleport(branch.levelTracker[0].level.spawnPosition.position, branch.levelTracker[0].level.spawnPosition.rotation, false);
			}
			else
			{
				ENT_Player.playerObject.Teleport(branch.levelTracker[0].level.transform.position, Quaternion.identity, false);
			}
		}
		CL_GameManager.SetLoading(true);
		for (int k = 0; k < branch.levelTracker.Count; k++)
		{
			if (branch.levelTracker[k] != branch.currentLevel)
			{
				branch.levelTracker[k].level.gameObject.SetActive(true);
			}
			branch.levelTracker[k].level.Initialize(this.seed + k * 999);
			if (branch.levelTracker[k] != branch.currentLevel)
			{
				branch.levelTracker[k].level.gameObject.SetActive(false);
			}
			if (branch.levelTracker[k].level.DoesPauseGeneration())
			{
				branch.levelTracker[k].level.pauseGeneration = false;
				branch.levelTracker[k].stopLevel = true;
			}
		}
		branch.currentLevel = branch.levelTracker[0];
		this.loading = false;
		WorldLoader.LevelInfo closestLevelToPlayer = WorldLoader.GetClosestLevelToPosition(ENT_Player.playerObject.transform.position);
		int closestLevelToPlayerIndex = branch.levelTracker.IndexOf(closestLevelToPlayer);
		yield return new WaitForSecondsRealtime(0.1f);
		if (genParams.levelData != null)
		{
			for (int l = 0; l < genParams.levelData.Count; l++)
			{
				Debug.Log("Attempting to load " + genParams.levelData[l].levelName + " is it equal to " + branch.levelTracker[worldPreloadStartID + l].level.levelName);
				if (genParams.levelData[l].levelName == branch.levelTracker[worldPreloadStartID + l].level.levelName)
				{
					M_Level.SaveData.LoadDataIntoLevel(genParams.levelData[l], branch.levelTracker[worldPreloadStartID + l].level, genParams.setPlayerPositionToTargetLevel, genParams.loadSavedEntities);
				}
			}
		}
		if (!CL_GameManager.gMan.IsReviving() && WorldLoader.isLoaded && closestLevelToPlayerIndex > 10)
		{
			this.UnloadLevelRange(0, closestLevelToPlayerIndex - 10);
		}
		closestLevelToPlayer.level.gameObject.SetActive(true);
		yield return new WaitForSecondsRealtime(0.1f);
		CL_GameManager.SetLoading(false);
		WorldLoader.isLoaded = true;
		yield break;
	}

	// Token: 0x060005AB RID: 1451 RVA: 0x0002EBD8 File Offset: 0x0002CDD8
	private void UnloadLevelRange(int start, int end)
	{
		for (int i = start; i < end; i++)
		{
			this.currentBranch.levelTracker[i].DestroyLevelObject();
		}
		this.currentBranch.levelTracker.RemoveRange(start, end);
	}

	// Token: 0x060005AC RID: 1452 RVA: 0x0002EC1C File Offset: 0x0002CE1C
	public void UnloadPreviousLevels(int offset = 1)
	{
		int num = this.currentBranch.levelTracker.IndexOf(this.currentBranch.currentLevel) - offset;
		if (num >= 0)
		{
			this.UnloadLevelRange(0, num);
		}
	}

	// Token: 0x060005AD RID: 1453 RVA: 0x0002EC54 File Offset: 0x0002CE54
	private void UnloadLevelsAfterTargetLevel(M_Level level)
	{
		WorldLoader.LevelInfo levelInfoFromLevel = this.GetLevelInfoFromLevel(level);
		if (levelInfoFromLevel == null)
		{
			return;
		}
		int num = this.currentBranch.levelTracker.IndexOf(levelInfoFromLevel);
		for (int i = this.currentBranch.levelTracker.Count - 1; i > num; i--)
		{
			this.currentBranch.levelTracker[i].DestroyLevelObject();
			this.currentBranch.levelTracker.RemoveAt(i);
		}
	}

	// Token: 0x060005AE RID: 1454 RVA: 0x0002ECC4 File Offset: 0x0002CEC4
	private void UnloadAllLevels()
	{
		for (int i = this.branches.Count - 1; i >= 0; i--)
		{
			WorldLoader.BranchInfo branchInfo = this.branches[i];
			for (int j = branchInfo.levelTracker.Count - 1; j >= 0; j--)
			{
				branchInfo.levelTracker[j].DestroyLevelObject();
				branchInfo.levelTracker.RemoveAt(j);
			}
			branchInfo.levelTracker.Clear();
		}
		if (this.currentBranch != null)
		{
			for (int k = this.currentBranch.levelTracker.Count - 1; k >= 0; k--)
			{
				this.currentBranch.levelTracker[k].DestroyLevelObject();
			}
			this.currentBranch.levelTracker.Clear();
		}
		this.branches.Clear();
		this.currentBranch = this.GetNewBranch("branch-root");
	}

	// Token: 0x060005AF RID: 1455 RVA: 0x0002EDA0 File Offset: 0x0002CFA0
	public void GenerateBranchFromLevel(M_Level level, Transform startTransform, WorldGenerator generator, M_GenerationBranch branch)
	{
		WorldLoader.BranchInfo branchInfo = new WorldLoader.BranchInfo();
		branchInfo.id = branch.id;
		branchInfo.generationBranch = branch;
		branchInfo.parent = this.currentBranch;
		branchInfo.parentLevel = this.GetLevelInfoFromLevel(level);
		branchInfo.originTransform = startTransform;
		this.branches.Add(branchInfo);
		if (this.currentBranch.children == null)
		{
			this.currentBranch.children = new List<WorldLoader.BranchInfo>();
		}
		this.currentBranch.children.Add(branchInfo);
		WorldLoader.GenerationParameters generationParameters = new WorldLoader.GenerationParameters
		{
			lastRegion = level.region,
			startRegion = null,
			generator = generator
		};
		base.StartCoroutine(this.GenerateLevels(branchInfo, generationParameters));
	}

	// Token: 0x060005B0 RID: 1456 RVA: 0x0002EE54 File Offset: 0x0002D054
	public void ChangeBranch(WorldLoader.BranchInfo branch)
	{
		foreach (WorldLoader.LevelInfo levelInfo in this.currentBranch.levelTracker)
		{
			if (levelInfo != this.currentBranch.currentLevel)
			{
				levelInfo.level.gameObject.SetActive(false);
			}
		}
		if (this.currentBranch.parentLevel != null)
		{
			this.currentBranch.parentLevel.level.gameObject.SetActive(false);
		}
		this.currentBranch = branch;
		if (branch.currentLevel == null)
		{
			branch.currentLevel = branch.levelTracker[0];
		}
	}

	// Token: 0x060005B1 RID: 1457 RVA: 0x0002EF10 File Offset: 0x0002D110
	public void ChangeBranch(string id)
	{
		foreach (WorldLoader.BranchInfo branchInfo in this.branches)
		{
			if (branchInfo.id == id)
			{
				this.ChangeBranch(branchInfo);
				break;
			}
		}
	}

	// Token: 0x060005B2 RID: 1458 RVA: 0x0002EF74 File Offset: 0x0002D174
	public void RestartGenerationFromLevelList(WorldLoader.GenerationParameters generationParameters = null)
	{
		this.UnloadAllLevels();
		base.StartCoroutine(this.GenerateLevels(this.GetCurrentBranch(), generationParameters));
	}

	// Token: 0x060005B3 RID: 1459 RVA: 0x0002EF90 File Offset: 0x0002D190
	public void RestartGenerationFromLevel(M_Level level, M_Region region, M_Subregion subregion, List<M_Level.SaveData> levelData = null)
	{
		WorldLoader.GenerationParameters generationParameters = new WorldLoader.GenerationParameters
		{
			levelData = levelData,
			preloadLevels = new List<M_Level> { level },
			lastRegion = region,
			startRegion = region
		};
		this.RestartGenerationFromLevelList(generationParameters);
	}

	// Token: 0x060005B4 RID: 1460 RVA: 0x0002EFD4 File Offset: 0x0002D1D4
	private void LoadUnload(float playerHeight, bool force = false)
	{
		WorldLoader.<>c__DisplayClass47_0 CS$<>8__locals1;
		CS$<>8__locals1.<>4__this = this;
		ref WorldLoader.LevelInfo ptr = ref this.GetCurrentBranch().currentLevel;
		if (this.loading || this.currentBranch.levelTracker.Count == 0)
		{
			return;
		}
		if (force)
		{
			ptr = this.currentBranch.levelTracker[0];
		}
		if (this.currentBranch != null)
		{
			DebugMenu.UpdateDebugText("current-branch", "<color=red>Current Branch: " + this.currentBranch.id);
		}
		else
		{
			DebugMenu.UpdateDebugText("current-branch", "<color=red>Current Branch: N/A");
		}
		if (ptr != null && ptr.level != null)
		{
			if (ptr.level.region != null && ptr.level.subRegion != null)
			{
				DebugMenu.UpdateDebugText("current-level", string.Concat(new string[]
				{
					"<color=red>Current Level: ",
					ptr.level.name,
					" : ",
					ptr.level.region.name,
					"/",
					ptr.level.subRegion.name
				}));
			}
			else
			{
				DebugMenu.UpdateDebugText("current-level", "<color=red>Current Level: " + ptr.level.name + " : inter_region");
			}
		}
		if (CL_GameManager.gMan.GetPlayerCorrectedHeight() > this.heightTarget)
		{
			this.heightTarget += 50f;
			CL_GameManager.gMan.uiMan.ascentHeader.ShowText("ASCENDED " + Mathf.RoundToInt(CL_GameManager.gMan.GetPlayerCorrectedHeight()).ToString() + " METERS");
		}
		if (ENT_Player.GetPlayer() == null)
		{
			return;
		}
		WorldLoader.LevelInfo closestLevelToPosition = WorldLoader.GetClosestLevelToPosition(ENT_Player.GetPlayer().transform.position);
		CS$<>8__locals1.firstTimeEnteringCurrentLevel = false;
		if (ptr != closestLevelToPosition)
		{
			if (ptr != null && ptr.level != null)
			{
				ptr.level.OnExit();
			}
			if (closestLevelToPosition != null && closestLevelToPosition.level != null)
			{
				Debug.Log(ptr.level.name + " :: " + this.currentBranch.currentLevel.level.name);
				Debug.Log(ptr.level.name + " :: " + this.currentBranch.id);
				Debug.Log(ptr.branch.id + " :: " + closestLevelToPosition.level.name);
				if (ptr.branch != null && ptr.branch != closestLevelToPosition.branch)
				{
					this.ChangeBranch(closestLevelToPosition.branch);
				}
				ptr = closestLevelToPosition;
				ptr.level.CycleColliders();
				if (!ptr.level.HasEntered())
				{
					CS$<>8__locals1.firstTimeEnteringCurrentLevel = true;
				}
				ptr.level.OnEnter();
			}
			CL_GameManager.SetSafe(false);
		}
		if (this.currentBranch.parentLevel != null)
		{
			this.<LoadUnload>g__CheckLevelLoad|47_0(this.currentBranch.parentLevel, ptr, this.currentBranch.levelTracker[0], this.currentBranch, false, ref CS$<>8__locals1);
		}
		for (int i = 0; i < this.currentBranch.levelTracker.Count; i++)
		{
			WorldLoader.LevelInfo levelInfo = this.currentBranch.levelTracker[i];
			WorldLoader.LevelInfo levelInfo2 = null;
			if (this.currentBranch.levelTracker.Count > i + 1)
			{
				levelInfo2 = this.currentBranch.levelTracker[i + 1];
			}
			if (!this.<LoadUnload>g__CheckLevelLoad|47_0(levelInfo, ptr, levelInfo2, this.currentBranch, i == this.currentBranch.levelTracker.Count - 1, ref CS$<>8__locals1))
			{
				return;
			}
		}
	}

	// Token: 0x060005B5 RID: 1461 RVA: 0x0002F3AC File Offset: 0x0002D5AC
	private M_Level GetRandomLevel(int i = 0)
	{
		M_Level m_Level = this.levelOrdering[Random.Range(0, this.levelOrdering.Count)];
		if (i == 0)
		{
			m_Level = this.introSegments[Random.Range(0, this.introSegments.Count)];
		}
		this.levelOrdering.Remove(m_Level);
		return m_Level;
	}

	// Token: 0x060005B6 RID: 1462 RVA: 0x0002F404 File Offset: 0x0002D604
	private void RefillLevelList()
	{
		this.levelOrdering = new List<M_Level>();
		foreach (M_Level m_Level in this.levels)
		{
			this.levelOrdering.Add(m_Level);
		}
	}

	// Token: 0x060005B7 RID: 1463 RVA: 0x0002F468 File Offset: 0x0002D668
	public static WorldLoader.LevelInfo GetClosestLevelToPosition(Vector3 position)
	{
		if (WorldLoader.instance == null || WorldLoader.instance.GetCurrentBranch().levelTracker == null || WorldLoader.instance.GetCurrentBranch().levelTracker.Count == 0)
		{
			return null;
		}
		WorldLoader.LevelInfo levelInfo = WorldLoader.instance.GetCurrentBranch().levelTracker[0];
		if (WorldLoader.instance.GetCurrentBranch().currentLevel != null && WorldLoader.instance.GetCurrentBranch().levelTracker.Contains(WorldLoader.instance.GetCurrentBranch().currentLevel))
		{
			levelInfo = WorldLoader.instance.GetCurrentBranch().currentLevel;
		}
		Queue<WorldLoader.LevelInfo> queue = new Queue<WorldLoader.LevelInfo>();
		HashSet<WorldLoader.LevelInfo> hashSet = new HashSet<WorldLoader.LevelInfo>();
		queue.Enqueue(levelInfo);
		hashSet.Add(levelInfo);
		while (queue.Count > 0)
		{
			WorldLoader.LevelInfo levelInfo2 = queue.Dequeue();
			if (levelInfo2.level.GetBounds().Contains(position))
			{
				return levelInfo2;
			}
			foreach (WorldLoader.LevelInfo levelInfo3 in levelInfo2.connectedLevels)
			{
				if (levelInfo3 != null && !hashSet.Contains(levelInfo3) && levelInfo2.branch == WorldLoader.instance.GetCurrentBranch())
				{
					hashSet.Add(levelInfo3);
					queue.Enqueue(levelInfo3);
				}
			}
		}
		float num = float.PositiveInfinity;
		WorldLoader.LevelInfo levelInfo4 = null;
		foreach (WorldLoader.LevelInfo levelInfo5 in WorldLoader.instance.GetCurrentBranch().levelTracker)
		{
			float num2 = levelInfo5.level.GetBounds().SqrDistance(position);
			if (num2 < num)
			{
				num = num2;
				levelInfo4 = levelInfo5;
			}
		}
		return levelInfo4;
	}

	// Token: 0x060005B8 RID: 1464 RVA: 0x0002F640 File Offset: 0x0002D840
	public static WorldLoader.LevelInfo GetCurrentLevelFromBounds()
	{
		if (WorldLoader.instance != null)
		{
			return WorldLoader.instance.GetCurrentBranch().currentLevel;
		}
		return null;
	}

	// Token: 0x060005B9 RID: 1465 RVA: 0x0002F660 File Offset: 0x0002D860
	public void TeleportPlayerToTargetLevel(string[] args)
	{
		foreach (WorldLoader.LevelInfo levelInfo in this.GetCurrentBranch().levelTracker)
		{
			if (levelInfo.level.name.ToLower().Replace("(clone)", "").Contains(args[0].ToLower()))
			{
				if (levelInfo.level.spawnPosition != null)
				{
					ENT_Player.playerObject.Teleport(levelInfo.level.spawnPosition.position);
				}
				else
				{
					ENT_Player.playerObject.Teleport(levelInfo.level.GetLevelEntrance().position + Vector3.up * 5f);
				}
				if (DEN_DeathFloor.instance != null && DEN_DeathFloor.instance.transform.position.y > ENT_Player.playerObject.transform.position.y)
				{
					DEN_DeathFloor.instance.Teleport(ENT_Player.playerObject.transform.position - Vector3.up * 20f);
				}
				break;
			}
		}
	}

	// Token: 0x060005BA RID: 1466 RVA: 0x0002F7B8 File Offset: 0x0002D9B8
	public void ListLoadedLevels(string[] args)
	{
		foreach (WorldLoader.LevelInfo levelInfo in this.GetCurrentBranch().levelTracker)
		{
			CommandConsole.Log(levelInfo.level.name.ToLower().Replace("(clone)", "") + " : " + levelInfo.level.transform.position.ToString(), false);
		}
	}

	// Token: 0x060005BB RID: 1467 RVA: 0x0002F858 File Offset: 0x0002DA58
	public void GetSeed(string[] args)
	{
		CommandConsole.Log(this.seed.ToString() ?? "", false);
	}

	// Token: 0x060005BC RID: 1468 RVA: 0x0002F874 File Offset: 0x0002DA74
	public static void ReloadWithSeed(string[] args)
	{
		WorldLoader.presetSeed = int.Parse(args[0]);
		CL_GameManager.gMan.RestartScene();
	}

	// Token: 0x060005BD RID: 1469 RVA: 0x0002F88D File Offset: 0x0002DA8D
	public static void SetPresetSeed(string s)
	{
		WorldLoader.presetSeed = int.Parse(s);
	}

	// Token: 0x060005BE RID: 1470 RVA: 0x0002F89C File Offset: 0x0002DA9C
	public static Transform GetCurrentLevelParentRoot()
	{
		if (WorldLoader.initialized && WorldLoader.instance != null && WorldLoader.instance.GetCurrentBranch().currentLevel != null)
		{
			return WorldLoader.instance.GetCurrentBranch().currentLevel.level.GetParentRoot();
		}
		return null;
	}

	// Token: 0x060005BF RID: 1471 RVA: 0x0002F8E9 File Offset: 0x0002DAE9
	internal static void IncrementSeed(int v)
	{
		WorldLoader.instance.seed += v;
	}

	// Token: 0x060005C0 RID: 1472 RVA: 0x0002F900 File Offset: 0x0002DB00
	internal void SetCurrentLevel(M_Level level)
	{
		foreach (WorldLoader.LevelInfo levelInfo in this.GetCurrentBranch().levelTracker)
		{
			if (levelInfo.level == level)
			{
				this.GetCurrentBranch().currentLevel = levelInfo;
				break;
			}
		}
	}

	// Token: 0x060005C1 RID: 1473 RVA: 0x0002F970 File Offset: 0x0002DB70
	private WorldLoader.LevelInfo GetLevelInfoFromLevel(M_Level level)
	{
		foreach (WorldLoader.LevelInfo levelInfo in this.GetCurrentBranch().levelTracker)
		{
			if (levelInfo.level == level)
			{
				return levelInfo;
			}
		}
		return null;
	}

	// Token: 0x060005C2 RID: 1474 RVA: 0x0002F9D8 File Offset: 0x0002DBD8
	public void ClearCurrentGenerator()
	{
		this.currentGenerator = null;
	}

	// Token: 0x060005C3 RID: 1475 RVA: 0x0002F9E1 File Offset: 0x0002DBE1
	public WorldLoader.BranchInfo GetCurrentBranch()
	{
		return this.currentBranch;
	}

	// Token: 0x060005C4 RID: 1476 RVA: 0x0002F9E9 File Offset: 0x0002DBE9
	public WorldLoader.LevelInfo GetCurrentLevel()
	{
		return this.currentBranch.currentLevel;
	}

	// Token: 0x060005C5 RID: 1477 RVA: 0x0002F9F6 File Offset: 0x0002DBF6
	public void ResumeGeneration()
	{
		this.currentBranch.currentLevel.stopLevel = false;
	}

	// Token: 0x060005C6 RID: 1478 RVA: 0x0002FA0C File Offset: 0x0002DC0C
	internal void SetOffset(float amount)
	{
		foreach (WorldLoader.BranchInfo branchInfo in this.branches)
		{
			foreach (WorldLoader.LevelInfo levelInfo in branchInfo.levelTracker)
			{
				levelInfo.level.transform.position += Vector3.up * amount;
				levelInfo.level.ResetBounds();
			}
		}
	}

	// Token: 0x060005C8 RID: 1480 RVA: 0x0002FAFC File Offset: 0x0002DCFC
	[CompilerGenerated]
	private bool <LoadUnload>g__CheckLevelLoad|47_0(WorldLoader.LevelInfo info, WorldLoader.LevelInfo currentLevel, WorldLoader.LevelInfo nextLevel, WorldLoader.BranchInfo currentBranch, bool lastLevelCheck, ref WorldLoader.<>c__DisplayClass47_0 A_6)
	{
		bool loaded = info.loaded;
		if (info == currentLevel)
		{
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.WireBox(info.level.GetBounds(), Color.green);
			}
			info.level.gameObject.SetActive(true);
			if (info.level.subRegion != null && info.level.region != null)
			{
				if ((info.level.region != this.currentRegion || info.level.subRegion != this.currentSubregion) && A_6.firstTimeEnteringCurrentLevel && info.level.showIntroText)
				{
					if (info.level.introText != "")
					{
						CL_GameManager.gMan.uiMan.header.ShowText(info.level.introText);
					}
					else if (info.level.subRegion.showIntroText)
					{
						if (CL_GameManager.IsHardmode())
						{
							CL_GameManager.gMan.uiMan.header.ShowText("<size=40><color=red>" + info.level.region.introText + "</size></color>\n<color=grey>" + info.level.subRegion.introText);
						}
						else
						{
							CL_GameManager.gMan.uiMan.header.ShowText("<size=40>" + info.level.region.introText + "</size>\n<color=grey>" + info.level.subRegion.introText);
						}
					}
				}
				this.currentRegion = info.level.region;
				this.currentSubregion = info.level.subRegion;
			}
			if (!info.level.HasEntered())
			{
				info.level.OnEnter();
			}
			if (info.connectedLevels != null)
			{
				foreach (WorldLoader.LevelInfo levelInfo in info.connectedLevels)
				{
					if (levelInfo != null && !(levelInfo.level == null) && !levelInfo.level.gameObject.activeSelf)
					{
						levelInfo.level.gameObject.SetActive(true);
					}
				}
			}
			info.loaded = true;
			info.adjacent = false;
			if (lastLevelCheck && !info.level.DoesPauseGeneration() && !info.stopLevel)
			{
				WorldLoader.GenerationParameters generationParameters = new WorldLoader.GenerationParameters();
				generationParameters.lastRegion = currentBranch.levelTracker[currentBranch.levelTracker.Count - 1].level.region;
				generationParameters.seedOffset = 1;
				if (info.level.transform.position.y > 10000f)
				{
					CL_GameManager.gMan.SetWorldOffset(-info.level.transform.position.y + 500f);
				}
				this.UnloadPreviousLevels(5);
				base.StartCoroutine(this.GenerateLevels(this.GetCurrentBranch(), generationParameters));
				info.connectedLevels.Add(nextLevel);
				foreach (WorldLoader.LevelInfo levelInfo2 in info.connectedLevels)
				{
					if (levelInfo2 != null && !levelInfo2.level.gameObject.activeSelf)
					{
						levelInfo2.level.gameObject.SetActive(true);
					}
				}
				if (!loaded && info.loaded)
				{
					info.level.OnLevelActivate();
				}
				return false;
			}
		}
		else if (currentLevel.connectedLevels != null && currentLevel.connectedLevels.Count > 0 && !currentLevel.connectedLevels.Contains(info))
		{
			info.adjacent = false;
			bool flag = false;
			foreach (WorldLoader.LevelInfo levelInfo3 in info.connectedLevels)
			{
				if (levelInfo3 != null && levelInfo3.level.loadAdjacent && levelInfo3.loaded && levelInfo3.adjacent)
				{
					info.loaded = true;
					flag = true;
					info.level.gameObject.SetActive(true);
					if (CL_UIManager.debug)
					{
						CL_DebugView.draw.WireBox(info.level.GetBounds(), Color.magenta);
						break;
					}
					break;
				}
			}
			if (!flag)
			{
				info.level.gameObject.SetActive(false);
				if (CL_UIManager.debug)
				{
					CL_DebugView.draw.WireBox(info.level.GetBounds(), Color.red);
				}
				info.loaded = false;
			}
		}
		else
		{
			info.loaded = true;
			info.adjacent = true;
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.WireBox(info.level.GetBounds(), Color.yellow);
			}
		}
		if (!loaded && info.loaded)
		{
			info.level.OnLevelActivate();
		}
		return true;
	}

	// Token: 0x04000759 RID: 1881
	public bool active = true;

	// Token: 0x0400075A RID: 1882
	public List<M_Level> introSegments;

	// Token: 0x0400075B RID: 1883
	public List<M_Level> levels;

	// Token: 0x0400075C RID: 1884
	[HideInInspector]
	public List<WorldLoader.BranchInfo> branchTracker;

	// Token: 0x0400075D RID: 1885
	[HideInInspector]
	public WorldLoader.BranchInfo currentBranch;

	// Token: 0x0400075E RID: 1886
	[HideInInspector]
	private List<WorldLoader.BranchInfo> branches;

	// Token: 0x0400075F RID: 1887
	private Vector3 levelExitPosition;

	// Token: 0x04000760 RID: 1888
	private HandholdManager handholdManager;

	// Token: 0x04000761 RID: 1889
	private float curLevelMinBounds;

	// Token: 0x04000762 RID: 1890
	private float curLevelMaxBounds;

	// Token: 0x04000763 RID: 1891
	private List<M_Level> previousLevels = new List<M_Level>();

	// Token: 0x04000764 RID: 1892
	private List<M_Level> levelOrdering = new List<M_Level>();

	// Token: 0x04000765 RID: 1893
	public List<M_Region> regions;

	// Token: 0x04000766 RID: 1894
	private float heightTarget = 50f;

	// Token: 0x04000767 RID: 1895
	private M_Region currentRegion;

	// Token: 0x04000768 RID: 1896
	private M_Subregion currentSubregion;

	// Token: 0x04000769 RID: 1897
	public static WorldLoader instance;

	// Token: 0x0400076A RID: 1898
	private bool loading;

	// Token: 0x0400076B RID: 1899
	public M_Level loadTestlevel;

	// Token: 0x0400076C RID: 1900
	public M_Region loadTestRegion;

	// Token: 0x0400076D RID: 1901
	public M_Subregion loadTestSubregion;

	// Token: 0x0400076E RID: 1902
	public int seed;

	// Token: 0x0400076F RID: 1903
	public int startingSeed;

	// Token: 0x04000770 RID: 1904
	private static int presetSeed;

	// Token: 0x04000771 RID: 1905
	public static bool customSeed;

	// Token: 0x04000772 RID: 1906
	private ENT_Player.SaveData playerSave = new ENT_Player.SaveData();

	// Token: 0x04000773 RID: 1907
	public static bool initialized;

	// Token: 0x04000774 RID: 1908
	public static bool isLoaded;

	// Token: 0x04000775 RID: 1909
	private WorldGenerator currentGenerator;

	// Token: 0x02000270 RID: 624
	[Serializable]
	public class LevelInfo
	{
		// Token: 0x06000DD9 RID: 3545 RVA: 0x000549B4 File Offset: 0x00052BB4
		public void DestroyLevelObject()
		{
			Object.Destroy(this.level.gameObject);
		}

		// Token: 0x04000FE2 RID: 4066
		public M_Level level;

		// Token: 0x04000FE3 RID: 4067
		[DoNotSerialize]
		public WorldLoader.BranchInfo branch;

		// Token: 0x04000FE4 RID: 4068
		public float lowBounds;

		// Token: 0x04000FE5 RID: 4069
		public float highBounds;

		// Token: 0x04000FE6 RID: 4070
		[HideInInspector]
		[DoNotSerialize]
		public List<WorldLoader.LevelInfo> connectedLevels;

		// Token: 0x04000FE7 RID: 4071
		public bool loaded;

		// Token: 0x04000FE8 RID: 4072
		public bool adjacent;

		// Token: 0x04000FE9 RID: 4073
		public bool stopLevel;
	}

	// Token: 0x02000271 RID: 625
	[Serializable]
	public class BranchInfo
	{
		// Token: 0x04000FEA RID: 4074
		public string id;

		// Token: 0x04000FEB RID: 4075
		[DoNotSerialize]
		public M_GenerationBranch generationBranch;

		// Token: 0x04000FEC RID: 4076
		[HideInInspector]
		[DoNotSerialize]
		public WorldLoader.BranchInfo parent;

		// Token: 0x04000FED RID: 4077
		[HideInInspector]
		[DoNotSerialize]
		public WorldLoader.LevelInfo parentLevel;

		// Token: 0x04000FEE RID: 4078
		[HideInInspector]
		public List<WorldLoader.BranchInfo> children;

		// Token: 0x04000FEF RID: 4079
		public Transform originTransform;

		// Token: 0x04000FF0 RID: 4080
		[HideInInspector]
		public List<WorldLoader.LevelInfo> levelTracker = new List<WorldLoader.LevelInfo>();

		// Token: 0x04000FF1 RID: 4081
		[HideInInspector]
		public WorldLoader.LevelInfo currentLevel;

		// Token: 0x04000FF2 RID: 4082
		private int currentLevelId;
	}

	// Token: 0x02000272 RID: 626
	[Serializable]
	public class GenerationParameters
	{
		// Token: 0x04000FF3 RID: 4083
		public WorldLoader.GenerationParameters.GenerationType generationType;

		// Token: 0x04000FF4 RID: 4084
		public WorldLoader.GenerationParameters.ReachEndType endType;

		// Token: 0x04000FF5 RID: 4085
		public bool forceResetGeneratorToGamemode;

		// Token: 0x04000FF6 RID: 4086
		public WorldGenerator generator;

		// Token: 0x04000FF7 RID: 4087
		public float generationHeightOffset;

		// Token: 0x04000FF8 RID: 4088
		public M_Region lastRegion;

		// Token: 0x04000FF9 RID: 4089
		public M_Region startRegion;

		// Token: 0x04000FFA RID: 4090
		public List<M_Level> preloadLevels;

		// Token: 0x04000FFB RID: 4091
		public M_Level targetLevel;

		// Token: 0x04000FFC RID: 4092
		public bool removeLevelsBeforeTarget;

		// Token: 0x04000FFD RID: 4093
		public bool setPlayerPositionToTargetLevel = true;

		// Token: 0x04000FFE RID: 4094
		public bool setPlayerPositionToStartLevel;

		// Token: 0x04000FFF RID: 4095
		public List<M_Level.SaveData> levelData;

		// Token: 0x04001000 RID: 4096
		public bool loadSavedEntities;

		// Token: 0x04001001 RID: 4097
		public int seedOffset;

		// Token: 0x02000309 RID: 777
		public enum GenerationType
		{
			// Token: 0x040012F0 RID: 4848
			standard,
			// Token: 0x040012F1 RID: 4849
			continued,
			// Token: 0x040012F2 RID: 4850
			replace,
			// Token: 0x040012F3 RID: 4851
			generateUntilFound
		}

		// Token: 0x0200030A RID: 778
		public enum ReachEndType
		{
			// Token: 0x040012F5 RID: 4853
			restart,
			// Token: 0x040012F6 RID: 4854
			stop
		}
	}
}
