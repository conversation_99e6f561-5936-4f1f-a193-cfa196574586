﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200005A RID: 90
[CreateAssetMenu(fileName = "New Event", menuName = "White Knuckle/Events/Base Event")]
public class SessionEvent : ScriptableObject
{
	// Token: 0x06000393 RID: 915 RVA: 0x000228EC File Offset: 0x00020AEC
	public virtual void StartEvent(M_Level curLevel = null, M_Subregion curSubregion = null, M_Region curRegion = null)
	{
		this.startLevel = curLevel;
		this.startSubregion = curSubregion;
		this.startRegion = curRegion;
		foreach (SessionEventModule sessionEventModule in this.modules)
		{
			sessionEventModule.Initialize(this);
			sessionEventModule.OnModuleStart();
		}
	}

	// Token: 0x06000394 RID: 916 RVA: 0x00022958 File Offset: 0x00020B58
	public virtual void EndEvent()
	{
	}

	// Token: 0x06000395 RID: 917 RVA: 0x0002295C File Offset: 0x00020B5C
	public virtual void StopEvent()
	{
		foreach (SessionEventModule sessionEventModule in this.modules)
		{
			sessionEventModule.OnModuleDestroy();
		}
		CL_EventManager.RemoveEvent(this.id);
	}

	// Token: 0x06000396 RID: 918 RVA: 0x000229B8 File Offset: 0x00020BB8
	public virtual void UpdateEvent()
	{
		for (int i = 0; i < this.modules.Count; i++)
		{
			this.modules[i].Update();
		}
	}

	// Token: 0x06000397 RID: 919 RVA: 0x000229EC File Offset: 0x00020BEC
	public virtual void LateUpdateEvent()
	{
		for (int i = 0; i < this.modules.Count; i++)
		{
			this.modules[i].LateUpdate();
		}
	}

	// Token: 0x06000398 RID: 920 RVA: 0x00022A20 File Offset: 0x00020C20
	public virtual void AddModule(SessionEventModule m)
	{
		m.Initialize(this);
		m.OnModuleStart();
		this.modules.Add(m);
	}

	// Token: 0x06000399 RID: 921 RVA: 0x00022A3B File Offset: 0x00020C3B
	public virtual void EnterRegion(M_Region r)
	{
	}

	// Token: 0x0600039A RID: 922 RVA: 0x00022A3D File Offset: 0x00020C3D
	public virtual void LeaveRegion(M_Region r)
	{
	}

	// Token: 0x0600039B RID: 923 RVA: 0x00022A3F File Offset: 0x00020C3F
	public virtual void EnterSubregion(M_Subregion sR)
	{
	}

	// Token: 0x0600039C RID: 924 RVA: 0x00022A41 File Offset: 0x00020C41
	public virtual void LeaveSubregion(M_Subregion sR)
	{
	}

	// Token: 0x0600039D RID: 925 RVA: 0x00022A43 File Offset: 0x00020C43
	public virtual void EnterLevel(M_Level l)
	{
	}

	// Token: 0x0600039E RID: 926 RVA: 0x00022A45 File Offset: 0x00020C45
	public virtual void LeaveLevel(M_Level l)
	{
	}

	// Token: 0x0600039F RID: 927 RVA: 0x00022A48 File Offset: 0x00020C48
	public virtual void SendMessageToModule(string moduleID, string message)
	{
		for (int i = 0; i < this.modules.Count; i++)
		{
			if (this.modules[i].name == moduleID)
			{
				this.modules[i].SendMessage(message);
			}
		}
	}

	// Token: 0x040004F5 RID: 1269
	public string id;

	// Token: 0x040004F6 RID: 1270
	public SessionEvent.EventStart startCheck;

	// Token: 0x040004F7 RID: 1271
	public SpawnTable.SpawnSettings spawnSettings;

	// Token: 0x040004F8 RID: 1272
	[SerializeReference]
	public List<SessionEventModule> modules;

	// Token: 0x040004F9 RID: 1273
	internal M_Level startLevel;

	// Token: 0x040004FA RID: 1274
	internal M_Subregion startSubregion;

	// Token: 0x040004FB RID: 1275
	internal M_Region startRegion;

	// Token: 0x040004FC RID: 1276
	public bool showInList = true;

	// Token: 0x040004FD RID: 1277
	public bool playOnce;

	// Token: 0x02000246 RID: 582
	public enum EventStart
	{
		// Token: 0x04000F24 RID: 3876
		startOfRegion,
		// Token: 0x04000F25 RID: 3877
		startOfSubregion,
		// Token: 0x04000F26 RID: 3878
		startOfLevel,
		// Token: 0x04000F27 RID: 3879
		checkEverySecond
	}
}
