﻿using System;
using Drawing;
using UnityEngine;

// Token: 0x02000171 RID: 369
public class UT_GravityZone : MonoBehaviourGizmos
{
	// Token: 0x06000A57 RID: 2647 RVA: 0x000445A0 File Offset: 0x000427A0
	private void Update()
	{
		if (ENT_Player.playerObject == null)
		{
			return;
		}
		if (this.player == null)
		{
			this.player = ENT_Player.playerObject;
		}
		float num = Vector3.Distance(base.transform.position, this.player.transform.position);
		if (!this.playerInField && num < this.radius)
		{
			this.playerInField = true;
		}
		else if (this.playerInField && num > this.radius)
		{
			this.player.SetGravityMult(1f);
			this.playerInField = false;
		}
		if (this.playerInField)
		{
			this.player.SetGravityMult(Mathf.Lerp(this.gravity, 1f, this.falloff.Evaluate(num / this.radius)));
		}
	}

	// Token: 0x06000A58 RID: 2648 RVA: 0x00044670 File Offset: 0x00042870
	public override void DrawGizmos()
	{
		Draw.SphereOutline(base.transform.position, 0.2f, Color.blue);
		if (GizmoContext.InSelection(this))
		{
			Draw.WireSphere(base.transform.position, this.radius, Color.cyan);
			return;
		}
		Draw.WireSphere(base.transform.position, this.radius, Color.cyan * new Color(1f, 1f, 1f, 0.05f));
	}

	// Token: 0x04000B5B RID: 2907
	public float gravity;

	// Token: 0x04000B5C RID: 2908
	public float radius = 10f;

	// Token: 0x04000B5D RID: 2909
	public AnimationCurve falloff;

	// Token: 0x04000B5E RID: 2910
	private ENT_Player player;

	// Token: 0x04000B5F RID: 2911
	private bool playerInField;
}
