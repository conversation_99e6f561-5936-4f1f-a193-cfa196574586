﻿using System;

// Token: 0x020000A1 RID: 161
[Serializable]
public class GamemodeModule
{
	// Token: 0x06000536 RID: 1334 RVA: 0x0002BD95 File Offset: 0x00029F95
	public virtual void Initialize(M_Gamemode m_Gamemode)
	{
		this.curGamemode = m_Gamemode;
	}

	// Token: 0x06000537 RID: 1335 RVA: 0x0002BD9E File Offset: 0x00029F9E
	public virtual void OnFinish(bool hasFinished)
	{
	}

	// Token: 0x06000538 RID: 1336 RVA: 0x0002BDA0 File Offset: 0x00029FA0
	public virtual float GetScore(bool hasFinished = false)
	{
		return 0f;
	}

	// Token: 0x06000539 RID: 1337 RVA: 0x0002BDA7 File Offset: 0x00029FA7
	public virtual void RunEvent(string e)
	{
	}

	// Token: 0x040006C1 RID: 1729
	internal M_Gamemode curGamemode;
}
