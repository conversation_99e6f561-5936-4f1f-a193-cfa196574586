﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Audio;

// Token: 0x020000C6 RID: 198
public class UT_AudioClipHandler : MonoBehaviour
{
	// Token: 0x06000679 RID: 1657 RVA: 0x00034940 File Offset: 0x00032B40
	public void Initialize()
	{
		if (!this.initialized)
		{
			this.groupDictionary = new Dictionary<string, UT_AudioClipHandler.AudioGroup>();
			foreach (UT_AudioClipHandler.AudioGroup audioGroup in this.groups)
			{
				this.groupDictionary.Add(audioGroup.name, audioGroup);
				audioGroup.Initialize(this);
			}
			this.aud = base.GetComponent<AudioSource>();
			this.initialized = true;
		}
	}

	// Token: 0x0600067A RID: 1658 RVA: 0x000349CC File Offset: 0x00032BCC
	private void Start()
	{
		this.Initialize();
	}

	// Token: 0x0600067B RID: 1659 RVA: 0x000349D4 File Offset: 0x00032BD4
	public UT_AudioClipHandler.AudioGroup GetGroup(string s)
	{
		if (this.groupDictionary == null)
		{
			return null;
		}
		return this.groupDictionary[s];
	}

	// Token: 0x0600067C RID: 1660 RVA: 0x000349EC File Offset: 0x00032BEC
	public void StopSound()
	{
		this.aud.Stop();
	}

	// Token: 0x0600067D RID: 1661 RVA: 0x000349FC File Offset: 0x00032BFC
	public void StopSound(string sound)
	{
		string[] array = sound.Split(':', StringSplitOptions.None);
		this.GetGroup(array[0]).StopAudioSet(array[1]);
	}

	// Token: 0x0600067E RID: 1662 RVA: 0x00034A24 File Offset: 0x00032C24
	public void PlaySound(string sound)
	{
		if (!this.HasSound(sound))
		{
			return;
		}
		if (!sound.Contains(':'))
		{
			return;
		}
		string[] array = sound.Split(':', StringSplitOptions.None);
		if (this.groupDictionary.ContainsKey(array[0]))
		{
			this.GetGroup(array[0]).PlayAudioSet(array[1]);
		}
	}

	// Token: 0x0600067F RID: 1663 RVA: 0x00034A74 File Offset: 0x00032C74
	public void PlaySound(string sound, Vector3 pos)
	{
		if (!this.HasSound(sound))
		{
			return;
		}
		string[] array = sound.Split(':', StringSplitOptions.None);
		this.GetGroup(array[0]).PlayAudioSetAtPoint(array[1], pos);
	}

	// Token: 0x06000680 RID: 1664 RVA: 0x00034AA8 File Offset: 0x00032CA8
	public void FadeSound(string sound, float volume, float delta, bool autoStop = true)
	{
		if (!this.HasSound(sound))
		{
			return;
		}
		string[] array = sound.Split(':', StringSplitOptions.None);
		this.GetGroup(array[0]).GetSet(array[1]).FadeSound(volume, delta, autoStop);
	}

	// Token: 0x06000681 RID: 1665 RVA: 0x00034AE4 File Offset: 0x00032CE4
	public void FadeIn(string sound, float delta, bool autoStop = true)
	{
		if (!this.HasSound(sound))
		{
			return;
		}
		string[] array = sound.Split(':', StringSplitOptions.None);
		this.GetGroup(array[0]).GetSet(array[1]).FadeIn(delta, autoStop);
	}

	// Token: 0x06000682 RID: 1666 RVA: 0x00034B20 File Offset: 0x00032D20
	public bool HasSound(string sound)
	{
		if (this.groupDictionary == null)
		{
			return false;
		}
		if (!sound.Contains(':'))
		{
			return false;
		}
		string[] array = sound.Split(':', StringSplitOptions.None);
		return this.GetGroup(array[0]) != null && this.GetGroup(array[0]).GetSet(array[1]) != null;
	}

	// Token: 0x040007ED RID: 2029
	public float globalVolume = 1f;

	// Token: 0x040007EE RID: 2030
	private AudioSource aud;

	// Token: 0x040007EF RID: 2031
	public List<UT_AudioClipHandler.AudioGroup> groups;

	// Token: 0x040007F0 RID: 2032
	private Dictionary<string, UT_AudioClipHandler.AudioGroup> groupDictionary;

	// Token: 0x040007F1 RID: 2033
	private bool initialized;

	// Token: 0x0200028C RID: 652
	[Serializable]
	public class AudioGroup
	{
		// Token: 0x06000E3E RID: 3646 RVA: 0x0005675C File Offset: 0x0005495C
		public void Initialize(UT_AudioClipHandler h)
		{
			this.audioDictionary = new Dictionary<string, UT_AudioClipHandler.AudioSet>();
			this.handler = h;
			foreach (UT_AudioClipHandler.AudioSet audioSet in this.audioSets)
			{
				this.audioDictionary.Add(audioSet.name, audioSet);
				audioSet.transform = this.handler.transform;
			}
		}

		// Token: 0x06000E3F RID: 3647 RVA: 0x000567E0 File Offset: 0x000549E0
		public void PlayAudioSet(string n)
		{
			if (this.audioDictionary == null)
			{
				this.Initialize(this.handler);
			}
			if (!this.audioDictionary.ContainsKey(n))
			{
				Debug.LogWarning("Missing Audio Clip Set " + n);
				return;
			}
			this.audioDictionary[n].Play(1f, null);
		}

		// Token: 0x06000E40 RID: 3648 RVA: 0x00056838 File Offset: 0x00054A38
		public void StopAudioSet(string n)
		{
			if (this.audioDictionary == null)
			{
				this.Initialize(this.handler);
			}
			if (!this.audioDictionary.ContainsKey(n))
			{
				Debug.LogWarning("Missing Audio Clip Set " + n);
				return;
			}
			this.audioDictionary[n].StopSound();
		}

		// Token: 0x06000E41 RID: 3649 RVA: 0x00056889 File Offset: 0x00054A89
		public void PlayAudioSetAtPoint(string n, Vector3 pos)
		{
			if (this.audioDictionary == null)
			{
				this.Initialize(this.handler);
			}
			this.audioDictionary[n].PlayAtPoint(1f, pos, null);
		}

		// Token: 0x06000E42 RID: 3650 RVA: 0x000568B7 File Offset: 0x00054AB7
		public UT_AudioClipHandler.AudioSet GetSet(string n)
		{
			if (!this.audioDictionary.ContainsKey(n))
			{
				return null;
			}
			return this.audioDictionary[n];
		}

		// Token: 0x04001079 RID: 4217
		public string name;

		// Token: 0x0400107A RID: 4218
		public List<UT_AudioClipHandler.AudioSet> audioSets;

		// Token: 0x0400107B RID: 4219
		private Dictionary<string, UT_AudioClipHandler.AudioSet> audioDictionary;

		// Token: 0x0400107C RID: 4220
		private UT_AudioClipHandler handler;
	}

	// Token: 0x0200028D RID: 653
	[Serializable]
	public class AudioSet
	{
		// Token: 0x06000E44 RID: 3652 RVA: 0x000568E0 File Offset: 0x00054AE0
		public AudioClip GetClip()
		{
			if (this.orderStyle == UT_AudioClipHandler.AudioSet.AudioOrdering.random)
			{
				return this.clips[Random.Range(0, this.clips.Count)];
			}
			if (this.orderStyle == UT_AudioClipHandler.AudioSet.AudioOrdering.sequential)
			{
				this.sequenceCounter++;
				if (this.sequenceCounter >= this.clips.Count)
				{
					this.sequenceCounter = 0;
				}
				return this.clips[this.sequenceCounter];
			}
			if (this.orderStyle == UT_AudioClipHandler.AudioSet.AudioOrdering.reverse)
			{
				this.sequenceCounter--;
				if (this.sequenceCounter < 0)
				{
					this.sequenceCounter = this.clips.Count - 1;
				}
				return this.clips[this.sequenceCounter];
			}
			return this.clips[0];
		}

		// Token: 0x06000E45 RID: 3653 RVA: 0x000569A6 File Offset: 0x00054BA6
		public float GetPitch()
		{
			return Random.Range(this.minPitch, this.maxPitch);
		}

		// Token: 0x06000E46 RID: 3654 RVA: 0x000569B9 File Offset: 0x00054BB9
		public float GetVolume()
		{
			return this.maxVolume;
		}

		// Token: 0x06000E47 RID: 3655 RVA: 0x000569C1 File Offset: 0x00054BC1
		public AudioClip GetClip(int i)
		{
			return this.clips[i];
		}

		// Token: 0x06000E48 RID: 3656 RVA: 0x000569CF File Offset: 0x00054BCF
		public List<AudioClip> GetClips()
		{
			return this.clips;
		}

		// Token: 0x06000E49 RID: 3657 RVA: 0x000569D8 File Offset: 0x00054BD8
		public void Play(float globalVolume, AudioSource src = null)
		{
			if (src == null && this.aud == null)
			{
				AudioManager.PlaySound(this.GetClip(), this.transform.position, Random.Range(this.minVolume, this.maxVolume) * globalVolume, Random.Range(this.minPitch, this.maxPitch), this.spatialBlend, false, this.reverbZoneMix, this.useCustomMixerGroup ? this.customMixerGroup : null);
				return;
			}
			AudioSource audioSource = this.aud;
			if (src != null)
			{
				audioSource = src;
			}
			if (this.preventInterrupt && audioSource.isPlaying)
			{
				return;
			}
			audioSource.clip = this.clips[Random.Range(0, this.clips.Count)];
			audioSource.volume = Random.Range(this.minVolume, this.maxVolume) * globalVolume;
			audioSource.pitch = Random.Range(this.minPitch, this.maxPitch);
			audioSource.loop = this.loop;
			audioSource.Play();
			if (this.useSubtitles)
			{
				CL_UIManager.ShowSubtitle(CL_LocalizationManager.currentLocalization.GetAnnouncementLine(audioSource.clip.name));
			}
		}

		// Token: 0x06000E4A RID: 3658 RVA: 0x00056B00 File Offset: 0x00054D00
		public void Play(float globalVolume, Vector3 pos)
		{
			AudioManager.PlaySound(this.GetClip(), pos, Random.Range(this.minVolume, this.maxVolume) * globalVolume, Random.Range(this.minPitch, this.maxPitch), this.spatialBlend, false, this.reverbZoneMix, this.useCustomMixerGroup ? this.customMixerGroup : null);
		}

		// Token: 0x06000E4B RID: 3659 RVA: 0x00056B5C File Offset: 0x00054D5C
		public void PlaySound(float globalVolume, AudioClip clip)
		{
			if (clip == null)
			{
				this.aud.Stop();
				return;
			}
			if (this.aud != null)
			{
				AudioSource audioSource = this.aud;
				audioSource.clip = clip;
				audioSource.volume = Random.Range(this.minVolume, this.maxVolume) * globalVolume;
				audioSource.pitch = Random.Range(this.minPitch, this.maxPitch);
				audioSource.spatialBlend = this.spatialBlend;
				audioSource.loop = this.loop;
				audioSource.Play();
			}
			else
			{
				AudioManager.PlaySound(clip, this.transform.position, Random.Range(this.minVolume, this.maxVolume) * globalVolume, Random.Range(this.minPitch, this.maxPitch), this.spatialBlend, false, this.reverbZoneMix, this.useCustomMixerGroup ? this.customMixerGroup : null);
			}
			if (this.useSubtitles)
			{
				CL_UIManager.ShowSubtitle(CL_LocalizationManager.currentLocalization.GetAnnouncementLine(clip.name));
			}
		}

		// Token: 0x06000E4C RID: 3660 RVA: 0x00056C58 File Offset: 0x00054E58
		public void PlayAtPoint(float globalVolume, Vector3 pos, AudioSource src = null)
		{
			AudioManager.PlaySound(this.GetClip(), pos, Random.Range(this.minVolume, this.maxVolume) * globalVolume, Random.Range(this.minPitch, this.maxPitch), 1f, false, this.reverbZoneMix, null);
		}

		// Token: 0x06000E4D RID: 3661 RVA: 0x00056C97 File Offset: 0x00054E97
		public void StopSound()
		{
			this.aud.Stop();
		}

		// Token: 0x06000E4E RID: 3662 RVA: 0x00056CA4 File Offset: 0x00054EA4
		public void SetAudiosource(AudioSource a)
		{
			this.aud = a;
		}

		// Token: 0x06000E4F RID: 3663 RVA: 0x00056CAD File Offset: 0x00054EAD
		public void SetVolume(float v)
		{
			this.aud.volume = v;
		}

		// Token: 0x06000E50 RID: 3664 RVA: 0x00056CBB File Offset: 0x00054EBB
		public float GetCurrentVolume()
		{
			return this.aud.volume;
		}

		// Token: 0x06000E51 RID: 3665 RVA: 0x00056CC8 File Offset: 0x00054EC8
		public void FadeSound(float target, float delta, bool autoStop = true)
		{
			if (this.aud == null)
			{
				Debug.LogError("Audio Clip " + this.name + " Is missing an audio source, for fading.");
				return;
			}
			this.aud.clip = this.clips[0];
			if (autoStop)
			{
				if (this.aud.volume == 0f && target == 0f)
				{
					this.aud.Stop();
				}
				else if (!this.aud.isPlaying)
				{
					this.aud.Play();
				}
			}
			this.aud.volume = Mathf.Lerp(this.aud.volume, target, delta);
		}

		// Token: 0x06000E52 RID: 3666 RVA: 0x00056D74 File Offset: 0x00054F74
		public void FadeIn(float delta, bool autoStop = true)
		{
			if (this.aud == null)
			{
				Debug.LogError("Audio Clip " + this.name + " Is missing an audio source, for fading.");
				return;
			}
			this.aud.clip = this.clips[0];
			if (autoStop && !this.aud.isPlaying)
			{
				this.aud.Play();
			}
			this.aud.volume = Mathf.Lerp(this.aud.volume, this.maxVolume, delta);
		}

		// Token: 0x0400107D RID: 4221
		public string name;

		// Token: 0x0400107E RID: 4222
		public UT_AudioClipHandler.AudioSet.AudioOrdering orderStyle;

		// Token: 0x0400107F RID: 4223
		private int sequenceCounter;

		// Token: 0x04001080 RID: 4224
		public List<AudioClip> clips;

		// Token: 0x04001081 RID: 4225
		[Range(0f, 2f)]
		public float minVolume = 1f;

		// Token: 0x04001082 RID: 4226
		[Range(0f, 2f)]
		public float maxVolume = 1f;

		// Token: 0x04001083 RID: 4227
		[Range(0f, 2f)]
		public float minPitch = 1f;

		// Token: 0x04001084 RID: 4228
		[Range(0f, 2f)]
		public float maxPitch = 1f;

		// Token: 0x04001085 RID: 4229
		public bool loop;

		// Token: 0x04001086 RID: 4230
		public bool preventInterrupt;

		// Token: 0x04001087 RID: 4231
		[Range(0f, 1f)]
		public float spatialBlend = 1f;

		// Token: 0x04001088 RID: 4232
		[Range(0f, 1f)]
		public float reverbZoneMix = 1f;

		// Token: 0x04001089 RID: 4233
		public AudioSource aud;

		// Token: 0x0400108A RID: 4234
		[HideInInspector]
		public Transform transform;

		// Token: 0x0400108B RID: 4235
		public bool useSubtitles;

		// Token: 0x0400108C RID: 4236
		public bool useCustomMixerGroup;

		// Token: 0x0400108D RID: 4237
		public AudioMixerGroup customMixerGroup;

		// Token: 0x02000314 RID: 788
		public enum AudioOrdering
		{
			// Token: 0x0400131D RID: 4893
			random,
			// Token: 0x0400131E RID: 4894
			sequential,
			// Token: 0x0400131F RID: 4895
			reverse
		}
	}
}
