﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000082 RID: 130
public class CL_ToggleButton : CL_Button
{
	// Token: 0x06000466 RID: 1126 RVA: 0x00026E75 File Offset: 0x00025075
	public override void Interact()
	{
		base.Interact();
		this.Toggle();
	}

	// Token: 0x06000467 RID: 1127 RVA: 0x00026E83 File Offset: 0x00025083
	public override void Interact(ENT_Player p, string s = "")
	{
		base.Interact(p, s);
	}

	// Token: 0x06000468 RID: 1128 RVA: 0x00026E90 File Offset: 0x00025090
	private void Toggle()
	{
		this.state = !this.state;
		if (this.state)
		{
			this.onEvent.Invoke();
			AudioManager.PlaySound(this.onSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
		}
		else
		{
			this.offEvent.Invoke();
			AudioManager.PlaySound(this.offSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
		}
		this.ChooseMaterial();
	}

	// Token: 0x06000469 RID: 1129 RVA: 0x00026F2C File Offset: 0x0002512C
	internal override void ChooseMaterial()
	{
		if (!this.active || (this.cooldown && this.inactiveMaterialOnCooldown))
		{
			base.SetMaterial(this.inactiveMaterial);
			return;
		}
		if (!this.state)
		{
			base.SetMaterial(this.offMaterial);
			return;
		}
		base.SetMaterial(this.activeMaterial);
	}

	// Token: 0x0600046A RID: 1130 RVA: 0x00026F80 File Offset: 0x00025180
	public void SetState(bool b)
	{
		this.state = b;
		if (this.state)
		{
			this.onEvent.Invoke();
			AudioManager.PlaySound(this.onSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
		}
		else
		{
			this.offEvent.Invoke();
			AudioManager.PlaySound(this.offSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
		}
		this.ChooseMaterial();
	}

	// Token: 0x0600046B RID: 1131 RVA: 0x00027012 File Offset: 0x00025212
	public void SetStateWithoutEvent(bool b)
	{
		this.state = b;
		this.ChooseMaterial();
	}

	// Token: 0x040005CE RID: 1486
	public bool state;

	// Token: 0x040005CF RID: 1487
	public UnityEvent onEvent;

	// Token: 0x040005D0 RID: 1488
	public UnityEvent offEvent;

	// Token: 0x040005D1 RID: 1489
	[Header("Audio")]
	public AudioClip onSound;

	// Token: 0x040005D2 RID: 1490
	public AudioClip offSound;

	// Token: 0x040005D3 RID: 1491
	[Header("Materials")]
	public Material offMaterial;
}
