﻿using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000E8 RID: 232
public class App_PerkPage : MonoBehaviour
{
	// Token: 0x06000729 RID: 1833 RVA: 0x00037154 File Offset: 0x00035354
	private void Start()
	{
		this.window = base.GetComponent<OS_Window>();
		this.id = Guid.NewGuid().ToString();
		this.cards = new List<App_PerkPage_Card>();
		this.os = this.window.os;
		this.lastRoaches = CL_GameManager.roaches;
		this.GenerateCards();
		this.CheckIronKnuckle();
	}

	// Token: 0x0600072A RID: 1834 RVA: 0x000371B9 File Offset: 0x000353B9
	private void OnEnable()
	{
		this.GenerateIcons();
		this.CheckIronKnuckle();
	}

	// Token: 0x0600072B RID: 1835 RVA: 0x000371C8 File Offset: 0x000353C8
	private void CheckIronKnuckle()
	{
		if (SettingsManager.settings.g_competitive)
		{
			Message_Manager.Message_Packet message_Packet = new Message_Manager.Message_Packet();
			message_Packet.type = "default";
			message_Packet.closeText = "Quit";
			message_Packet.closeFunction = new Action(this.window.CloseApp);
			message_Packet.message = "You are playing in IRON KNUCKLE mode. No perks for you!";
			message_Packet.screenPos = new Vector2(0f, 0f);
			this.window.os.messageManager.CreateMessage(message_Packet);
		}
	}

	// Token: 0x0600072C RID: 1836 RVA: 0x0003724C File Offset: 0x0003544C
	private void Update()
	{
		if (!this.fullfilled && this.lastRoaches != CL_GameManager.roaches)
		{
			this.selectedCard = this.cards[0];
			for (int i = 0; i < this.cards.Count; i++)
			{
				App_PerkPage_Card app_PerkPage_Card = this.cards[i];
				if (i == 0)
				{
					app_PerkPage_Card.SelectCard();
				}
				else
				{
					app_PerkPage_Card.DeselectCard();
				}
				app_PerkPage_Card.UpdateCard();
			}
			this.lastRoaches = CL_GameManager.roaches;
		}
	}

	// Token: 0x0600072D RID: 1837 RVA: 0x000372C8 File Offset: 0x000354C8
	private void GenerateCards()
	{
		CL_GameManager.SessionFlag gameFlag = CL_GameManager.GetGameFlag("recieved-" + this.os.worldInterface.computerID + "-perk");
		if (gameFlag != null && gameFlag.state)
		{
			return;
		}
		if (this.os.worldInterface.GetSeed() != -1)
		{
			Random.InitState(this.os.worldInterface.GetSeed());
		}
		int num = Random.Range(this.minCards, this.maxCards + 1);
		List<Perk> list = new List<Perk>();
		list.AddRange(CL_AssetManager.GetFullCombinedAssetDatabase().perkAssets);
		for (int i = list.Count - 1; i >= 0; i--)
		{
			if (this.perkPageType == App_PerkPage.PerkPageType.regular)
			{
				if (list[i].spawnPool != Perk.PerkPool.standard)
				{
					list.RemoveAt(i);
				}
			}
			else if (this.perkPageType == App_PerkPage.PerkPageType.unstable && list[i].spawnPool != Perk.PerkPool.unstable)
			{
				list.RemoveAt(i);
			}
		}
		for (int j = 0; j < num; j++)
		{
			App_PerkPage_Card component = Object.Instantiate<App_PerkPage_Card>(this.cardAsset, this.cardParent.position, Quaternion.identity, this.cardParent).GetComponent<App_PerkPage_Card>();
			bool flag = false;
			Perk perk = null;
			int num2 = 0;
			while (!flag && num2 < 100)
			{
				num2++;
				perk = list[Random.Range(0, list.Count)];
				if (!perk.CanSpawn())
				{
					Debug.Log("Found perk which can't spawn." + perk.name);
				}
				else if (this.perkPageType == App_PerkPage.PerkPageType.unstable)
				{
					flag = true;
				}
				else if ((j < num - 1 && perk.cost == 0) || (j == num - 1 && perk.cost > 0))
				{
					flag = true;
				}
			}
			list.Remove(perk);
			perk = Object.Instantiate<Perk>(perk);
			int num3 = 0;
			if (CL_GameManager.gMan.GetPlayerAscent() > 2000f)
			{
				num3 = Mathf.RoundToInt((CL_GameManager.gMan.GetPlayerAscent() - 2000f) / 200f);
			}
			perk.cost += num3;
			perk.cost = Mathf.Min(perk.cost, 99);
			component.Initialize(this, perk);
			this.cards.Add(component);
		}
		this.SubmitCard(this.cards[0]);
	}

	// Token: 0x0600072E RID: 1838 RVA: 0x000374FC File Offset: 0x000356FC
	private void GenerateIcons()
	{
		foreach (object obj in this.iconParent.transform)
		{
			Object.Destroy(((Transform)obj).gameObject);
		}
		List<Perk> list = CL_GameManager.gMan.localPlayer.perks;
		if (list.Count == 0)
		{
			return;
		}
		for (int i = 0; i < list.Count; i++)
		{
			Image image = Object.Instantiate<Image>(this.iconAsset, this.iconParent.position, Quaternion.identity, this.iconParent);
			image.sprite = list[i].icon;
			image.GetComponent<Tooltip>().tip = list[i].title + "\n<color=\"grey\">" + list[i].description + "</color>";
			if (list[i].GetStackAmount() > 1)
			{
				image.GetComponentInChildren<TMP_Text>().text = string.Format("x{0}", list[i].GetStackAmount());
			}
		}
	}

	// Token: 0x0600072F RID: 1839 RVA: 0x0003762C File Offset: 0x0003582C
	public void SelectCard(App_PerkPage_Card selection)
	{
		foreach (App_PerkPage_Card app_PerkPage_Card in this.cards)
		{
			if (app_PerkPage_Card != selection)
			{
				if (app_PerkPage_Card != this.selectedCard)
				{
					app_PerkPage_Card.DeselectCard();
				}
			}
			else if (app_PerkPage_Card != this.selectedCard)
			{
				app_PerkPage_Card.SelectCard();
			}
			app_PerkPage_Card.UpdateCard();
		}
	}

	// Token: 0x06000730 RID: 1840 RVA: 0x000376B4 File Offset: 0x000358B4
	public void SubmitCard(App_PerkPage_Card selection)
	{
		foreach (App_PerkPage_Card app_PerkPage_Card in this.cards)
		{
			if (app_PerkPage_Card != selection)
			{
				app_PerkPage_Card.DeselectCard();
			}
			else
			{
				app_PerkPage_Card.SubmitCard();
				this.selectedCard = selection;
			}
			app_PerkPage_Card.UpdateCard();
		}
	}

	// Token: 0x06000731 RID: 1841 RVA: 0x00037724 File Offset: 0x00035924
	public void PurchaseSelected()
	{
		if (this.selectedCard == null || SettingsManager.settings.g_competitive)
		{
			return;
		}
		if (!this.selectedCard.CanAfford())
		{
			return;
		}
		this.fullfilled = true;
		foreach (App_PerkPage_Card app_PerkPage_Card in this.cards)
		{
			if (app_PerkPage_Card != this.selectedCard)
			{
				app_PerkPage_Card.DisableCard();
			}
			else
			{
				app_PerkPage_Card.BuyCard();
			}
			app_PerkPage_Card.LockCard();
			app_PerkPage_Card.UpdateCard();
		}
		this.purchaseButton.interactable = false;
		if (this.os.worldInterface.computerID != "")
		{
			CL_GameManager.SetGameFlag("recieved-" + this.os.worldInterface.computerID + "-perk", true, "", false);
		}
		if (this.perkPageType == App_PerkPage.PerkPageType.regular)
		{
			base.StartCoroutine(this.StandardPurchaseSequence());
			this.os.audioClipHandler.PlaySound("perk:buy");
			return;
		}
		if (this.perkPageType == App_PerkPage.PerkPageType.unstable)
		{
			base.StartCoroutine(this.UnstablePurchaseSequence());
		}
	}

	// Token: 0x06000732 RID: 1842 RVA: 0x0003785C File Offset: 0x00035A5C
	private IEnumerator StandardPurchaseSequence()
	{
		this.os.audioClipHandler.PlaySound("os:error");
		OS_Window purchaseWindow = this.os.CreateAppWindow(this.fulfillmentWindow, this.window.id + "-purchase");
		while (purchaseWindow != null)
		{
			yield return null;
		}
		this.os.cameraControl.EndTakeover();
		this.os.SetInteractable(false);
		while (this.os.cameraControl.isTakingOver)
		{
			yield return null;
		}
		ENT_Player.playerObject.Lock();
		ENT_Player.playerObject.UnlockCamera();
		ENT_Player.playerObject.hands[0].SetLocked(true);
		ENT_Player.playerObject.hands[1].SetLocked(false);
		this.os.canControl = false;
		this.os.worldInterface.lever.SetInteractable(true);
		ENT_Player.playerObject.SetInteractWhitelist(new List<string> { "Injection-Lever" });
		while (!this.os.worldInterface.systemData.leverPulled)
		{
			ENT_Player.playerObject.hands[0].MoveTo(this.os.worldInterface.handPositions[0].position);
			ENT_Player.playerObject.hands[0].SetSprite(ENT_Player.playerObject.hands[0].normalSprite);
			ENT_Player.playerObject.hands[0].handModel.transform.localScale = Vector3.one * 0.8f;
			yield return null;
		}
		Debug.Log(this.os.worldInterface.systemData.leverPulled);
		ENT_Player.playerObject.hands[1].SetLocked(true);
		float timer = 0f;
		while (timer < 0.5f)
		{
			ENT_Player.playerObject.hands[0].MoveTo(this.os.worldInterface.handPositions[0].position);
			ENT_Player.playerObject.hands[0].SetSprite(ENT_Player.playerObject.hands[0].normalSprite);
			ENT_Player.playerObject.hands[0].handModel.transform.localScale = Vector3.one * 0.8f;
			ENT_Player.playerObject.hands[1].MoveTo(this.os.worldInterface.lever.transform.GetChild(0).transform.position);
			ENT_Player.playerObject.hands[1].SetSprite(ENT_Player.playerObject.hands[0].grabSprite);
			ENT_Player.playerObject.hands[1].handModel.transform.localScale = Vector3.one * 0.8f;
			timer += Time.deltaTime;
			yield return null;
		}
		this.os.worldInterface.lever.SetInteractable(false);
		this.os.worldInterface.lever.SetState(false);
		ENT_Player.playerObject.StopInteractWhitelist();
		this.window.os.audioClipHandler.PlaySound("perk:inject");
		CL_GameManager.gMan.localPlayer.Damage(5f, "nonlethal");
		CL_GameManager.gMan.localPlayer.hands[0].SetGripStrength(0.5f);
		CL_GameManager.gMan.localPlayer.AddPerk(Object.Instantiate<Perk>(this.selectedCard.perk), 1);
		BuffContainer newBuff = new BuffContainer();
		newBuff.loseOverTime = true;
		newBuff.loseRate = 0.05f;
		newBuff.buffs = new List<BuffContainer.Buff>();
		newBuff.buffs.Add(new BuffContainer.Buff
		{
			id = "pilled",
			maxAmount = 0.5f
		});
		newBuff.SetMultiplier(1f);
		newBuff.Initialize();
		this.os.worldInterface.GetEventController().PlayEvent("injector-plunge");
		CL_UIManager.instance.SetVignette("bloodied", 1f);
		for (timer = 0f; timer < 0.5f; timer += Time.deltaTime)
		{
			ENT_Player.playerObject.hands[0].MoveTo(this.os.worldInterface.handPositions[0].position);
			ENT_Player.playerObject.hands[0].SetSprite(ENT_Player.playerObject.hands[0].openSprite);
			ENT_Player.playerObject.hands[0].handModel.transform.localScale = Vector3.one * 0.8f;
			ENT_Player.playerObject.hands[1].MoveTo(this.os.worldInterface.lever.transform.GetChild(0).transform.position);
			ENT_Player.playerObject.hands[1].SetSprite(ENT_Player.playerObject.hands[0].grabSprite);
			ENT_Player.playerObject.hands[1].handModel.transform.localScale = Vector3.one * 0.8f;
			yield return null;
		}
		timer = 0f;
		while (timer < 0.5f)
		{
			ENT_Player.playerObject.hands[0].MoveTo(this.os.worldInterface.handPositions[0].position);
			ENT_Player.playerObject.hands[0].SetSprite(ENT_Player.playerObject.hands[0].normalSprite);
			ENT_Player.playerObject.hands[0].handModel.transform.localScale = Vector3.one * 0.8f;
			ENT_Player.playerObject.hands[1].MoveTo(this.os.worldInterface.lever.transform.GetChild(0).transform.position);
			ENT_Player.playerObject.hands[1].SetSprite(ENT_Player.playerObject.hands[0].grabSprite);
			ENT_Player.playerObject.hands[1].handModel.transform.localScale = Vector3.one * 0.8f;
			timer += Time.deltaTime;
			yield return null;
		}
		ENT_Player.playerObject.hands[0].SetLocked(false);
		ENT_Player.playerObject.hands[1].SetLocked(false);
		CL_GameManager.gMan.localPlayer.curBuffs.AddBuff(newBuff);
		this.GenerateIcons();
		yield return new WaitForSeconds(0.5f);
		CL_UIManager.instance.SetVignetteTarget("bloodied", 0f);
		CL_AchievementManager.SetAchievementValue("ACH_GETPERK", true);
		this.os.Deactivate();
		yield break;
	}

	// Token: 0x06000733 RID: 1843 RVA: 0x0003786B File Offset: 0x00035A6B
	private IEnumerator UnstablePurchaseSequence()
	{
		this.os.SetInteractable(false);
		yield return new WaitForSeconds(0.1f);
		this.os.audioClipHandler.PlaySound("os:error");
		CL_AchievementManager.SetAchievementValue("ACH_GETUNSTABLEPERK", true);
		yield return new WaitForSeconds(0.1f);
		this.window.os.audioClipHandler.PlaySound("perk:unstable-inject");
		CL_GameManager.gMan.localPlayer.AddPerk(Object.Instantiate<Perk>(this.selectedCard.perk), 1);
		BuffContainer buffContainer = new BuffContainer();
		buffContainer.loseOverTime = true;
		buffContainer.loseRate = 0.05f;
		buffContainer.buffs = new List<BuffContainer.Buff>();
		buffContainer.buffs.Add(new BuffContainer.Buff
		{
			id = "pilled",
			maxAmount = 0.5f
		});
		buffContainer.SetMultiplier(1f);
		buffContainer.Initialize();
		this.os.worldInterface.GetEventController().PlayEvent("buy-perk");
		CL_UIManager.instance.SetVignette("bloodied", 1f);
		CL_GameManager.gMan.localPlayer.curBuffs.AddBuff(buffContainer);
		this.GenerateIcons();
		yield return new WaitForSeconds(0.5f);
		CL_UIManager.instance.SetVignetteTarget("bloodied", 0f);
		this.os.SetInteractable(true);
		this.os.Deactivate();
		yield break;
	}

	// Token: 0x0400089E RID: 2206
	public App_PerkPage.PerkPageType perkPageType;

	// Token: 0x0400089F RID: 2207
	public List<Perk> perks;

	// Token: 0x040008A0 RID: 2208
	public App_PerkPage_Card cardAsset;

	// Token: 0x040008A1 RID: 2209
	public Transform cardParent;

	// Token: 0x040008A2 RID: 2210
	public GameObject perksNotAvailableScreen;

	// Token: 0x040008A3 RID: 2211
	public Image iconAsset;

	// Token: 0x040008A4 RID: 2212
	public Transform iconParent;

	// Token: 0x040008A5 RID: 2213
	public int minCards = 1;

	// Token: 0x040008A6 RID: 2214
	public int maxCards = 3;

	// Token: 0x040008A7 RID: 2215
	private List<App_PerkPage_Card> cards;

	// Token: 0x040008A8 RID: 2216
	private App_PerkPage_Card selectedCard;

	// Token: 0x040008A9 RID: 2217
	public Button purchaseButton;

	// Token: 0x040008AA RID: 2218
	public OS_Window fulfillmentWindow;

	// Token: 0x040008AB RID: 2219
	private OS_Window window;

	// Token: 0x040008AC RID: 2220
	private string id;

	// Token: 0x040008AD RID: 2221
	private OS_Manager os;

	// Token: 0x040008AE RID: 2222
	private int lastRoaches;

	// Token: 0x040008AF RID: 2223
	private bool fullfilled;

	// Token: 0x02000295 RID: 661
	public enum PerkPageType
	{
		// Token: 0x040010AF RID: 4271
		regular,
		// Token: 0x040010B0 RID: 4272
		unstable
	}
}
