﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x0200012B RID: 299
public class UI_Gamemode_Button : <PERSON>o<PERSON>eh<PERSON>our, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler, ISelectHandler, IDeselectHandler, GamemodeHolder
{
	// Token: 0x060008D0 RID: 2256 RVA: 0x0003E354 File Offset: 0x0003C554
	private void Awake()
	{
		this.stats = base.GetComponentsInChildren<UT_StatText>();
		this.capsule = base.GetComponent<UI_CapsuleButton>();
		this.capsule.unlockAchievement = this.gamemode.unlockAchievement;
		this.unlockText.text = this.gamemode.unlockHint;
		if (this.gamemode.hiddenUntilUnlocked)
		{
			base.gameObject.SetActive(false);
		}
		if (this.showMedals)
		{
			int num = ChallengeMode.GetMedalRankFromGamemode(this.gamemode) - 1;
			if (this.gamemode.HasWonGamemode() && num >= 0)
			{
				Debug.Log(num);
				if (num >= this.medals.Count)
				{
					num = 0;
				}
				this.medalImage.sprite = this.medals[num];
				this.medalImage.gameObject.SetActive(true);
			}
		}
	}

	// Token: 0x060008D1 RID: 2257 RVA: 0x0003E428 File Offset: 0x0003C628
	private void OnEnable()
	{
		if (!this.initialized && UI_GamemodeScreen.instance)
		{
			this.gamemodeScreen = UI_GamemodeScreen.instance;
			this.Initialize();
		}
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.UpdateOnSettingsRefresh));
	}

	// Token: 0x060008D2 RID: 2258 RVA: 0x0003E47A File Offset: 0x0003C67A
	private void OnDisable()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Remove(SettingsManager.SettingsRefreshUpdate, new Action(this.UpdateOnSettingsRefresh));
	}

	// Token: 0x060008D3 RID: 2259 RVA: 0x0003E49C File Offset: 0x0003C69C
	private void UpdateOnSettingsRefresh()
	{
		if (this.runInProgressDisplay != null)
		{
			bool flag = CL_SaveManager.SessionFileExists(this.gamemode.gamemodeName, SettingsManager.settings.g_hard);
			if (SettingsManager.settings.g_competitive)
			{
				flag = false;
			}
			if (flag)
			{
				this.runInProgressDisplay.Show();
				this.title.color = new Color(1f, 0.52f, 0f);
			}
			else
			{
				this.runInProgressDisplay.Hide();
				this.title.color = Color.white;
			}
		}
		if (this.roachCounter != null)
		{
			int value = StatManager.saveData.GetRoachBankByID(this.gamemode.GetRoachBankID()).value;
			if (value <= 0 || !this.gamemode.showBankAmount)
			{
				this.roachCounterRoot.SetActive(false);
			}
			else
			{
				this.roachCounterRoot.SetActive(true);
			}
			this.roachCounter.text = value.ToString() ?? "";
		}
	}

	// Token: 0x060008D4 RID: 2260 RVA: 0x0003E599 File Offset: 0x0003C799
	private void Update()
	{
		if (!this.initialized && UI_GamemodeScreen.instance)
		{
			this.gamemodeScreen = UI_GamemodeScreen.instance;
			this.Initialize();
		}
	}

	// Token: 0x060008D5 RID: 2261 RVA: 0x0003E5C0 File Offset: 0x0003C7C0
	public void Initialize()
	{
		this.initialized = true;
		base.GetComponent<Button>().onClick.AddListener(delegate
		{
			this.gamemodeScreen.Initialize(this.gamemode);
		});
		base.GetComponent<Image>().sprite = this.gamemode.capsuleArt;
		if (this.gamemode.useCustomCapsuleName && (this.gamemode.capsuleName != null || this.gamemode.capsuleName != ""))
		{
			this.title.text = this.gamemode.capsuleName;
		}
		else
		{
			this.title.text = this.gamemode.gamemodeName;
		}
		UT_StatText[] array = this.stats;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].RefreshText(this.gamemode.gamemodeName);
		}
		this.UpdateOnSettingsRefresh();
	}

	// Token: 0x060008D6 RID: 2262 RVA: 0x0003E693 File Offset: 0x0003C893
	public void OnPointerEnter(PointerEventData eventData)
	{
		this.isHovering = true;
	}

	// Token: 0x060008D7 RID: 2263 RVA: 0x0003E69C File Offset: 0x0003C89C
	public void OnPointerExit(PointerEventData eventData)
	{
		this.isHovering = false;
	}

	// Token: 0x060008D8 RID: 2264 RVA: 0x0003E6A5 File Offset: 0x0003C8A5
	public void OnSelect(BaseEventData data)
	{
		this.isHovering = true;
	}

	// Token: 0x060008D9 RID: 2265 RVA: 0x0003E6AE File Offset: 0x0003C8AE
	public void OnDeselect(BaseEventData data)
	{
		this.isHovering = false;
	}

	// Token: 0x060008DA RID: 2266 RVA: 0x0003E6B7 File Offset: 0x0003C8B7
	public M_Gamemode GetGamemode()
	{
		return this.gamemode;
	}

	// Token: 0x04000A19 RID: 2585
	public M_Gamemode gamemode;

	// Token: 0x04000A1A RID: 2586
	private UI_GamemodeScreen gamemodeScreen;

	// Token: 0x04000A1B RID: 2587
	private UT_StatText[] stats;

	// Token: 0x04000A1C RID: 2588
	private bool initialized;

	// Token: 0x04000A1D RID: 2589
	public TMP_Text title;

	// Token: 0x04000A1E RID: 2590
	public UI_LerpOpen runInProgressDisplay;

	// Token: 0x04000A1F RID: 2591
	public TMP_Text roachCounter;

	// Token: 0x04000A20 RID: 2592
	public GameObject roachCounterRoot;

	// Token: 0x04000A21 RID: 2593
	public bool showMedals;

	// Token: 0x04000A22 RID: 2594
	public List<Sprite> medals;

	// Token: 0x04000A23 RID: 2595
	public Image medalImage;

	// Token: 0x04000A24 RID: 2596
	private UI_CapsuleButton capsule;

	// Token: 0x04000A25 RID: 2597
	public TMP_Text unlockText;

	// Token: 0x04000A26 RID: 2598
	private bool isHovering;
}
