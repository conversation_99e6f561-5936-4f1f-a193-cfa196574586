﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000045 RID: 69
[CreateAssetMenu(fileName = "Perk_New", menuName = "White Knuckle/Perk")]
public class Perk : ScriptableObject
{
	// Token: 0x06000311 RID: 785 RVA: 0x0001F990 File Offset: 0x0001DB90
	public void Initialize(ENT_Player p)
	{
		this.player = p;
		if (this.buff.id != "")
		{
			this.currentBuff = this.player.curBuffs.GetBuffContainer(this.buff.id);
		}
		if (this.currentBuff == null)
		{
			this.player.curBuffs.AddBuff(this.buff);
			this.currentBuff = this.player.curBuffs.GetBuffContainer(this.buff.id);
		}
		this.currentBuff.SetMultiplier(1f);
		this.stackAmount = 1;
		if (this.flags != null)
		{
			foreach (string text in this.flags)
			{
				CL_GameManager.SetGameFlag(text, true, "", false);
			}
		}
		for (int i = this.modules.Count - 1; i >= 0; i--)
		{
			this.modules[i].Initialize(this);
			this.modules[i].AddModule();
		}
	}

	// Token: 0x06000312 RID: 786 RVA: 0x0001FAC0 File Offset: 0x0001DCC0
	public void Update()
	{
		float num = 1f;
		if (this.stackAmount > 1)
		{
			for (int i = 1; i < Mathf.Min(this.stackAmount, this.stackMax); i++)
			{
				num += this.multiplierCurve.Evaluate((float)i / (float)this.stackMax);
			}
		}
		this.currentBuff.SetMultiplier(num);
		for (int j = this.modules.Count - 1; j >= 0; j--)
		{
			this.modules[j].Update();
		}
	}

	// Token: 0x06000313 RID: 787 RVA: 0x0001FB48 File Offset: 0x0001DD48
	public void AddStack(int amount = 1)
	{
		this.stackAmount += amount;
		foreach (PerkModule perkModule in this.modules)
		{
			perkModule.AddModule();
		}
	}

	// Token: 0x06000314 RID: 788 RVA: 0x0001FBA8 File Offset: 0x0001DDA8
	internal void RemoveStack(int amount = 1)
	{
		this.stackAmount -= amount;
	}

	// Token: 0x06000315 RID: 789 RVA: 0x0001FBB8 File Offset: 0x0001DDB8
	public int GetStackAmount()
	{
		return this.stackAmount;
	}

	// Token: 0x06000316 RID: 790 RVA: 0x0001FBC0 File Offset: 0x0001DDC0
	public bool CanSpawn()
	{
		Debug.Log("Checking Perk: " + base.name + " - " + this.unlockProgressionID);
		if (!this.spawnInEndless && CL_GameManager.gamemode.isEndless)
		{
			return false;
		}
		if (ENT_Player.GetPlayer().HasPerk(this.id))
		{
			if (!this.canStack)
			{
				Debug.Log("Blocking stack: Already has " + this.id);
				return false;
			}
			Perk perk = ENT_Player.GetPlayer().GetPerk(this.id);
			if (perk.stackMax <= perk.stackAmount)
			{
				Debug.Log("Blocking stack: Has enough " + this.id);
				return false;
			}
		}
		return (this.canStack || !ENT_Player.GetPlayer().HasPerk(this.id)) && (!(this.unlockProgressionID != "") || CL_ProgressionManager.HasProgressionUnlock(this.unlockProgressionID)) && CL_ProgressionManager.playerExperience >= this.unlockXP;
	}

	// Token: 0x06000317 RID: 791 RVA: 0x0001FCB7 File Offset: 0x0001DEB7
	public void RemoveFromPlayer()
	{
		Debug.Log("Removing Perk: " + this.id);
		this.player.RemovePerk(this, true);
		this.isActive = false;
	}

	// Token: 0x06000318 RID: 792 RVA: 0x0001FCE2 File Offset: 0x0001DEE2
	public void ClearPerkEffects()
	{
		this.player.curBuffs.RemoveBuffContainer(this.currentBuff.id);
	}

	// Token: 0x06000319 RID: 793 RVA: 0x0001FCFF File Offset: 0x0001DEFF
	public float GetBarValue()
	{
		return this.barValue;
	}

	// Token: 0x0600031A RID: 794 RVA: 0x0001FD07 File Offset: 0x0001DF07
	public void SetBarValue(float f)
	{
		this.barValue = f;
	}

	// Token: 0x0600031B RID: 795 RVA: 0x0001FD10 File Offset: 0x0001DF10
	public bool IsActive()
	{
		return this.isActive;
	}

	// Token: 0x0600031C RID: 796 RVA: 0x0001FD18 File Offset: 0x0001DF18
	internal void RemoveModule(PerkModule module)
	{
		this.modules.Remove(module);
	}

	// Token: 0x04000437 RID: 1079
	public string title;

	// Token: 0x04000438 RID: 1080
	public string id;

	// Token: 0x04000439 RID: 1081
	public string description;

	// Token: 0x0400043A RID: 1082
	[Space]
	public int cost;

	// Token: 0x0400043B RID: 1083
	[Space]
	public Perk.PerkPool spawnPool;

	// Token: 0x0400043C RID: 1084
	public bool spawnInEndless = true;

	// Token: 0x0400043D RID: 1085
	public bool canStack = true;

	// Token: 0x0400043E RID: 1086
	[Space]
	public int stackMax;

	// Token: 0x0400043F RID: 1087
	public AnimationCurve multiplierCurve;

	// Token: 0x04000440 RID: 1088
	[Space]
	public BuffContainer buff;

	// Token: 0x04000441 RID: 1089
	public List<string> flags;

	// Token: 0x04000442 RID: 1090
	[SerializeReference]
	public List<PerkModule> modules;

	// Token: 0x04000443 RID: 1091
	public Sprite icon;

	// Token: 0x04000444 RID: 1092
	public Sprite perkCard;

	// Token: 0x04000445 RID: 1093
	public Sprite perkFrame;

	// Token: 0x04000446 RID: 1094
	public int stackAmount;

	// Token: 0x04000447 RID: 1095
	private BuffContainer currentBuff;

	// Token: 0x04000448 RID: 1096
	private ENT_Player player;

	// Token: 0x04000449 RID: 1097
	public string unlockProgressionID;

	// Token: 0x0400044A RID: 1098
	public int unlockXP;

	// Token: 0x0400044B RID: 1099
	private float barValue = 1f;

	// Token: 0x0400044C RID: 1100
	private bool isActive = true;

	// Token: 0x0200023A RID: 570
	[Serializable]
	public class PerkSaveData
	{
		// Token: 0x06000D76 RID: 3446 RVA: 0x00052C3E File Offset: 0x00050E3E
		public static Perk.PerkSaveData GetPerkSaveData(Perk p)
		{
			return new Perk.PerkSaveData
			{
				id = p.id,
				stackAmount = p.GetStackAmount(),
				modules = p.modules
			};
		}

		// Token: 0x06000D77 RID: 3447 RVA: 0x00052C69 File Offset: 0x00050E69
		public static void SetPerkSaveData(Perk p, Perk.PerkSaveData data)
		{
			p.stackAmount = data.stackAmount;
			p.modules = data.modules;
		}

		// Token: 0x04000EF1 RID: 3825
		public string id;

		// Token: 0x04000EF2 RID: 3826
		public int stackAmount;

		// Token: 0x04000EF3 RID: 3827
		[SerializeReference]
		public List<PerkModule> modules;
	}

	// Token: 0x0200023B RID: 571
	public enum PerkPool
	{
		// Token: 0x04000EF5 RID: 3829
		standard,
		// Token: 0x04000EF6 RID: 3830
		unstable,
		// Token: 0x04000EF7 RID: 3831
		never
	}
}
