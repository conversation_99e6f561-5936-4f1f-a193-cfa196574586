﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200013A RID: 314
[RequireComponent(typeof(ScrollRect))]
public class UI_ScrollController : MonoBehaviour
{
	// Token: 0x06000918 RID: 2328 RVA: 0x0003F57C File Offset: 0x0003D77C
	private void Reset()
	{
		if (this.scrollRect == null)
		{
			this.scrollRect = base.GetComponent<ScrollRect>();
		}
	}

	// Token: 0x06000919 RID: 2329 RVA: 0x0003F598 File Offset: 0x0003D798
	public void ScrollToNextPage()
	{
		if (this.scrollRect == null)
		{
			return;
		}
		if (this.scrollRect.vertical)
		{
			this.ScrollNextPageVertical();
			return;
		}
		if (this.scrollRect.horizontal)
		{
			this.ScrollNextPageHorizontal();
		}
	}

	// Token: 0x0600091A RID: 2330 RVA: 0x0003F5D0 File Offset: 0x0003D7D0
	public void ScrollToPreviousPage()
	{
		if (this.scrollRect == null)
		{
			return;
		}
		if (this.scrollRect.vertical)
		{
			this.ScrollPreviousPageVertical();
			return;
		}
		if (this.scrollRect.horizontal)
		{
			this.ScrollPreviousPageHorizontal();
		}
	}

	// Token: 0x0600091B RID: 2331 RVA: 0x0003F608 File Offset: 0x0003D808
	private void ScrollNextPageVertical()
	{
		float height = this.scrollRect.content.rect.height;
		float num = this.scrollRect.viewport.rect.height / height;
		float num2 = this.scrollRect.verticalNormalizedPosition - num;
		num2 = Mathf.Clamp01(num2);
		this.ScrollToVerticalPosition(num2);
	}

	// Token: 0x0600091C RID: 2332 RVA: 0x0003F668 File Offset: 0x0003D868
	private void ScrollPreviousPageVertical()
	{
		float height = this.scrollRect.content.rect.height;
		float num = this.scrollRect.viewport.rect.height / height;
		float num2 = this.scrollRect.verticalNormalizedPosition + num;
		num2 = Mathf.Clamp01(num2);
		this.ScrollToVerticalPosition(num2);
	}

	// Token: 0x0600091D RID: 2333 RVA: 0x0003F6C8 File Offset: 0x0003D8C8
	private void ScrollNextPageHorizontal()
	{
		float width = this.scrollRect.content.rect.width;
		float num = this.scrollRect.viewport.rect.width / width;
		float num2 = this.scrollRect.horizontalNormalizedPosition + num;
		num2 = Mathf.Clamp01(num2);
		this.ScrollToHorizontalPosition(num2);
	}

	// Token: 0x0600091E RID: 2334 RVA: 0x0003F728 File Offset: 0x0003D928
	private void ScrollPreviousPageHorizontal()
	{
		float width = this.scrollRect.content.rect.width;
		float num = this.scrollRect.viewport.rect.width / width;
		float num2 = this.scrollRect.horizontalNormalizedPosition - num;
		num2 = Mathf.Clamp01(num2);
		this.ScrollToHorizontalPosition(num2);
	}

	// Token: 0x0600091F RID: 2335 RVA: 0x0003F785 File Offset: 0x0003D985
	private void ScrollToVerticalPosition(float targetPosition)
	{
		if (this.useSmoothScrolling)
		{
			if (this.scrollCoroutine != null)
			{
				base.StopCoroutine(this.scrollCoroutine);
			}
			this.scrollCoroutine = base.StartCoroutine(this.SmoothScrollVertical(targetPosition));
			return;
		}
		this.scrollRect.verticalNormalizedPosition = targetPosition;
	}

	// Token: 0x06000920 RID: 2336 RVA: 0x0003F7C3 File Offset: 0x0003D9C3
	private void ScrollToHorizontalPosition(float targetPosition)
	{
		if (this.useSmoothScrolling)
		{
			if (this.scrollCoroutine != null)
			{
				base.StopCoroutine(this.scrollCoroutine);
			}
			this.scrollCoroutine = base.StartCoroutine(this.SmoothScrollHorizontal(targetPosition));
			return;
		}
		this.scrollRect.horizontalNormalizedPosition = targetPosition;
	}

	// Token: 0x06000921 RID: 2337 RVA: 0x0003F801 File Offset: 0x0003DA01
	private IEnumerator SmoothScrollVertical(float targetPos)
	{
		float verticalNormalizedPosition = this.scrollRect.verticalNormalizedPosition;
		float elapsed = 0f;
		while (elapsed < 1f)
		{
			elapsed += Time.deltaTime;
			float num = Mathf.Lerp(this.scrollRect.horizontalNormalizedPosition, targetPos, Time.deltaTime * this.scrollSpeed);
			this.scrollRect.verticalNormalizedPosition = num;
			yield return null;
		}
		this.scrollRect.verticalNormalizedPosition = targetPos;
		this.scrollCoroutine = null;
		yield break;
	}

	// Token: 0x06000922 RID: 2338 RVA: 0x0003F817 File Offset: 0x0003DA17
	private IEnumerator SmoothScrollHorizontal(float targetPos)
	{
		float horizontalNormalizedPosition = this.scrollRect.horizontalNormalizedPosition;
		float elapsed = 0f;
		while (elapsed < 1f)
		{
			elapsed += Time.deltaTime;
			float num = Mathf.Lerp(this.scrollRect.horizontalNormalizedPosition, targetPos, Time.deltaTime * this.scrollSpeed);
			this.scrollRect.horizontalNormalizedPosition = num;
			yield return null;
		}
		this.scrollRect.horizontalNormalizedPosition = targetPos;
		this.scrollCoroutine = null;
		yield break;
	}

	// Token: 0x04000A67 RID: 2663
	[Header("Assign your ScrollRect here (or via GetComponent).")]
	[SerializeField]
	private ScrollRect scrollRect;

	// Token: 0x04000A68 RID: 2664
	[Header("Smooth Scrolling Options")]
	[Tooltip("Enable or disable smooth scrolling (lerping).")]
	public bool useSmoothScrolling;

	// Token: 0x04000A69 RID: 2665
	[Tooltip("Controls the speed of the smooth scroll (larger = faster).")]
	public float scrollSpeed = 5f;

	// Token: 0x04000A6A RID: 2666
	private Coroutine scrollCoroutine;
}
