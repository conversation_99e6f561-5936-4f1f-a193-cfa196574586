﻿using System;
using UnityEngine;

// Token: 0x0200009A RID: 154
[CreateAssetMenu(fileName = "New Item Interactor", menuName = "White Knuckle/Item/Interactor")]
public class Item_Interactor : ScriptableObject
{
	// Token: 0x06000519 RID: 1305 RVA: 0x0002B2F0 File Offset: 0x000294F0
	public virtual void Interact(ENT_Player.Hand curhand, ref bool interacting, string fireButton, ref InteractHit hit, out Sprite interactSprite, Collider hitCollider, ObjectTagger tagger, Clickable clickable, Item item = null)
	{
		if (tagger.HasTag(this.interactionTag))
		{
			interactSprite = this.interactionSprite;
			curhand.uiInteract.gameObject.SetActive(true);
			if (InputManager.GetButton(fireButton).Down && !interacting)
			{
				Item_Interactor.InteractionType interactionType = this.interactionType;
				if (interactionType != Item_Interactor.InteractionType.normal)
				{
					if (interactionType != Item_Interactor.InteractionType.destroyItem)
					{
						return;
					}
					if (item.data != null && item.data.Count > 0)
					{
						clickable.Interact(curhand.GetPlayer(), item.data);
					}
					else
					{
						clickable.Interact(curhand.GetPlayer(), this.interactData);
					}
					curhand.DestroyItem();
					return;
				}
				else
				{
					if (item.data != null && item.data.Count > 0)
					{
						clickable.Interact(curhand.GetPlayer(), item.data);
						return;
					}
					clickable.Interact(curhand.GetPlayer(), this.interactData);
					return;
				}
			}
		}
		else
		{
			interactSprite = null;
			curhand.uiInteract.gameObject.SetActive(false);
		}
	}

	// Token: 0x040006A1 RID: 1697
	public string interactorName;

	// Token: 0x040006A2 RID: 1698
	public string interactionTag;

	// Token: 0x040006A3 RID: 1699
	public Sprite interactionSprite;

	// Token: 0x040006A4 RID: 1700
	public string interactData = "";

	// Token: 0x040006A5 RID: 1701
	public Item_Interactor.InteractionType interactionType;

	// Token: 0x02000259 RID: 601
	public enum InteractionType
	{
		// Token: 0x04000F75 RID: 3957
		normal,
		// Token: 0x04000F76 RID: 3958
		destroyItem,
		// Token: 0x04000F77 RID: 3959
		swapItem
	}
}
