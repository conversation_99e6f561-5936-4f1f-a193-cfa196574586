﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000108 RID: 264
public class UI_Window : MonoBehaviour, IPointerClickHandler, IEventSystemHandler
{
	// Token: 0x06000801 RID: 2049 RVA: 0x0003B0B0 File Offset: 0x000392B0
	private void Awake()
	{
		this.image = base.GetComponent<Image>();
		if (this.titlebar == null)
		{
			this.titlebar = base.transform.Find("Title Bar").GetComponent<UI_Window_Title>();
		}
	}

	// Token: 0x06000802 RID: 2050 RVA: 0x0003B0E7 File Offset: 0x000392E7
	private void Update()
	{
	}

	// Token: 0x06000803 RID: 2051 RVA: 0x0003B0E9 File Offset: 0x000392E9
	public UI_Window_Title GetTitleBar()
	{
		if (this.titlebar == null)
		{
			this.titlebar = base.transform.Find("Title Bar").GetComponent<UI_Window_Title>();
		}
		return this.titlebar;
	}

	// Token: 0x06000804 RID: 2052 RVA: 0x0003B11A File Offset: 0x0003931A
	public void HideContents()
	{
		this.contents.SetActive(false);
		this.image.sprite = this.flickerSprite;
	}

	// Token: 0x06000805 RID: 2053 RVA: 0x0003B139 File Offset: 0x00039339
	public void ShowContents()
	{
		this.contents.SetActive(true);
		this.image.sprite = this.windowSprite;
	}

	// Token: 0x06000806 RID: 2054 RVA: 0x0003B158 File Offset: 0x00039358
	public void MoveToTop()
	{
		base.transform.SetAsLastSibling();
	}

	// Token: 0x06000807 RID: 2055 RVA: 0x0003B165 File Offset: 0x00039365
	public void OnMove()
	{
		this.moveEvent.Invoke();
	}

	// Token: 0x06000808 RID: 2056 RVA: 0x0003B172 File Offset: 0x00039372
	public void OnClick()
	{
		this.clickEvent.Invoke();
	}

	// Token: 0x06000809 RID: 2057 RVA: 0x0003B17F File Offset: 0x0003937F
	private IEnumerator FlickerShow()
	{
		this.image.sprite = this.flickerSprite;
		this.contents.SetActive(true);
		this.titlebar.gameObject.SetActive(false);
		yield return new WaitForSeconds(0.025f);
		this.titlebar.gameObject.SetActive(true);
		this.contents.SetActive(false);
		yield return new WaitForSeconds(0.025f);
		this.contents.SetActive(true);
		this.image.sprite = this.windowSprite;
		yield break;
	}

	// Token: 0x0600080A RID: 2058 RVA: 0x0003B18E File Offset: 0x0003938E
	public void Close()
	{
		this.closeEvent.Invoke();
		base.gameObject.SetActive(false);
	}

	// Token: 0x0600080B RID: 2059 RVA: 0x0003B1A7 File Offset: 0x000393A7
	public void OnPointerClick(PointerEventData eventData)
	{
		this.MoveToTop();
		this.OnClick();
	}

	// Token: 0x0400096D RID: 2413
	public GameObject contents;

	// Token: 0x0400096E RID: 2414
	private UI_Window_Title titlebar;

	// Token: 0x0400096F RID: 2415
	public Sprite windowSprite;

	// Token: 0x04000970 RID: 2416
	public Sprite flickerSprite;

	// Token: 0x04000971 RID: 2417
	private Image image;

	// Token: 0x04000972 RID: 2418
	public UnityEvent closeEvent;

	// Token: 0x04000973 RID: 2419
	public UnityEvent moveEvent;

	// Token: 0x04000974 RID: 2420
	public UnityEvent clickEvent;

	// Token: 0x04000975 RID: 2421
	public string dragEndSound;
}
