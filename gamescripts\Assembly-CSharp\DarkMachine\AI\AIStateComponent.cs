﻿using System;
using System.Reflection;
using UnityEngine;

namespace DarkMachine.AI
{
	// Token: 0x020001D2 RID: 466
	public class AIStateComponent
	{
		// Token: 0x17000008 RID: 8
		// (get) Token: 0x06000BCA RID: 3018 RVA: 0x0004AE37 File Offset: 0x00049037
		public virtual string name
		{
			get
			{
				return "null";
			}
		}

		// Token: 0x06000BCB RID: 3019 RVA: 0x0004AE40 File Offset: 0x00049040
		public virtual void Initialize(AIGameEntity e, AIStateVariable[] variables, string[] args = null)
		{
			foreach (AIStateVariable aistateVariable in variables)
			{
				this.SetValueById(aistateVariable.id, aistateVariable.data);
			}
		}

		// Token: 0x06000BCC RID: 3020 RVA: 0x0004AE74 File Offset: 0x00049074
		public void SetValueById(string id, string data)
		{
			FieldInfo field = base.GetType().GetField(id, BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);
			if (field == null)
			{
				return;
			}
			Type fieldType = field.FieldType;
			try
			{
				object obj = Convert.ChangeType(data, fieldType);
				field.SetValue(this, obj);
			}
			catch (Exception ex)
			{
				Debug.LogWarning("Failed to set field '" + id + "': " + ex.Message);
			}
		}

		// Token: 0x06000BCD RID: 3021 RVA: 0x0004AEE4 File Offset: 0x000490E4
		public virtual void Enter(AIGameEntity e, string[] args = null)
		{
		}

		// Token: 0x06000BCE RID: 3022 RVA: 0x0004AEE6 File Offset: 0x000490E6
		public virtual void Execute(AIGameEntity e, string[] args = null)
		{
		}

		// Token: 0x06000BCF RID: 3023 RVA: 0x0004AEE8 File Offset: 0x000490E8
		public virtual void Exit(AIGameEntity e, string[] args = null)
		{
		}

		// Token: 0x06000BD0 RID: 3024 RVA: 0x0004AEEA File Offset: 0x000490EA
		public virtual void SetTarget(Vector3 v)
		{
		}

		// Token: 0x06000BD1 RID: 3025 RVA: 0x0004AEEC File Offset: 0x000490EC
		public virtual void SetData(string[] args)
		{
		}
	}
}
