﻿using System;
using Drawing;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020001B0 RID: 432
public class UT_PlayerTriggerRadius : MonoBehaviourGizmos
{
	// Token: 0x06000B56 RID: 2902 RVA: 0x0004882C File Offset: 0x00046A2C
	private void FixedUpdate()
	{
		if (this.runOnce && this.hasRun)
		{
			return;
		}
		if (CL_GameManager.gMan == null)
		{
			return;
		}
		if (!(CL_GameManager.gMan.localPlayer != null))
		{
			return;
		}
		if (CL_GameManager.gMan.PlayerDistance(base.transform.position) > this.playerVicinity)
		{
			return;
		}
		this.RunEvent();
	}

	// Token: 0x06000B57 RID: 2903 RVA: 0x0004888F File Offset: 0x00046A8F
	private void RunEvent()
	{
		this.hasRun = true;
		this.enterEvent.Invoke();
	}

	// Token: 0x06000B58 RID: 2904 RVA: 0x000488A4 File Offset: 0x00046AA4
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			Draw.SphereOutline(base.transform.position, this.playerVicinity, new Color(1f, 0.561f, 0f, 0.25f));
			return;
		}
		Draw.SphereOutline(base.transform.position, this.playerVicinity, new Color(1f, 0.561f, 0f, 0.003f));
	}

	// Token: 0x04000C45 RID: 3141
	public float playerVicinity = 10f;

	// Token: 0x04000C46 RID: 3142
	public UnityEvent enterEvent;

	// Token: 0x04000C47 RID: 3143
	public bool runOnce = true;

	// Token: 0x04000C48 RID: 3144
	private bool hasRun;
}
