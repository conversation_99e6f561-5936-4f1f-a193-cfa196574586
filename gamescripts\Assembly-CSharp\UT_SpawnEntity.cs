﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020001C9 RID: 457
public class UT_SpawnEntity : MonoBehaviour
{
	// Token: 0x06000BA5 RID: 2981 RVA: 0x0004A4F6 File Offset: 0x000486F6
	private void Start()
	{
		if (this.spawnOnStart)
		{
			this.Spawn();
		}
	}

	// Token: 0x06000BA6 RID: 2982 RVA: 0x0004A508 File Offset: 0x00048708
	public void Spawn()
	{
		if (this.randomizeObjects != null && this.randomizeObjects.Count > 0)
		{
			this.SpawnCustom(this.randomizeObjects[Random.Range(0, this.randomizeObjects.Count)]);
			return;
		}
		this.SpawnCustom(this.spawnObject);
	}

	// Token: 0x06000BA7 RID: 2983 RVA: 0x0004A55C File Offset: 0x0004875C
	public void SpawnCustom(GameObject g)
	{
		Transform transform = base.transform;
		if (this.customParent)
		{
			transform = this.customParent;
		}
		Vector3 vector = Random.insideUnitSphere * this.randomizeOffset;
		Quaternion quaternion = Quaternion.Euler(Random.insideUnitSphere * this.randomizeRotation);
		GameObject gameObject = Object.Instantiate<GameObject>(g, base.transform.position + vector, base.transform.rotation * quaternion, transform);
		if (this.forceKinematicIfRigidbody)
		{
			Rigidbody component = gameObject.GetComponent<Rigidbody>();
			if (component != null)
			{
				component.isKinematic = true;
			}
		}
	}

	// Token: 0x06000BA8 RID: 2984 RVA: 0x0004A5F8 File Offset: 0x000487F8
	private void OnValidate()
	{
		if (this.spawnObject == null)
		{
			this.gizmoMesh = null;
			return;
		}
		if (this.spawnObject.GetComponentInChildren<MeshFilter>() != null)
		{
			this.gizmoMesh = this.spawnObject.GetComponentInChildren<MeshFilter>().sharedMesh;
			return;
		}
		SkinnedMeshRenderer componentInChildren = this.spawnObject.GetComponentInChildren<SkinnedMeshRenderer>();
		if (componentInChildren != null)
		{
			this.gizmoMesh = componentInChildren.sharedMesh;
			return;
		}
		this.gizmoMesh = null;
	}

	// Token: 0x06000BA9 RID: 2985 RVA: 0x0004A670 File Offset: 0x00048870
	private void OnDrawGizmos()
	{
		if (this.gizmoMesh == null)
		{
			this.gizmoMesh = Resources.GetBuiltinResource<Mesh>("Cube.fbx");
		}
		Gizmos.color = Color.yellow;
		Gizmos.DrawMesh(this.gizmoMesh, base.transform.position, base.transform.rotation);
		Gizmos.DrawWireSphere(base.transform.position, this.randomizeOffset);
	}

	// Token: 0x04000CB8 RID: 3256
	public bool spawnOnStart;

	// Token: 0x04000CB9 RID: 3257
	public GameObject spawnObject;

	// Token: 0x04000CBA RID: 3258
	public Transform customParent;

	// Token: 0x04000CBB RID: 3259
	private Mesh gizmoMesh;

	// Token: 0x04000CBC RID: 3260
	public bool forceKinematicIfRigidbody;

	// Token: 0x04000CBD RID: 3261
	[Header("Randomizer")]
	public float randomizeOffset;

	// Token: 0x04000CBE RID: 3262
	public float randomizeRotation;

	// Token: 0x04000CBF RID: 3263
	public List<GameObject> randomizeObjects;
}
