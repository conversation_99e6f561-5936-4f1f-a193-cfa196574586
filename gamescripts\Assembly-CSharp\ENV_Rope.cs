﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200004E RID: 78
public class ENV_Rope : MonoBehaviour
{
	// Token: 0x06000337 RID: 823 RVA: 0x000205A0 File Offset: 0x0001E7A0
	private void Start()
	{
		Vector3 position = base.transform.position;
		this.targetRigidbody = this.target.GetComponent<Rigidbody>();
		for (int i = 0; i < this.meshRoot.transform.childCount + 1; i++)
		{
			this.nodes.Add(new ENV_Rope_Node(position));
			if (i < this.meshRoot.transform.childCount)
			{
				this.nodes[i].mesh = this.meshRoot.transform.GetChild(i);
				this.nodes[i].mesh.GetComponent<CL_Handhold_Rope>().InitializeRope(this, this.nodes[i]);
			}
		}
		this.targetRigidbody.AddForce(Random.insideUnitSphere * 3f);
	}

	// Token: 0x06000338 RID: 824 RVA: 0x00020672 File Offset: 0x0001E872
	private void OnEnable()
	{
		DarkMachineFunctions.offsetAction = (Action<float>)Delegate.Combine(DarkMachineFunctions.offsetAction, new Action<float>(this.WorldOffset));
	}

	// Token: 0x06000339 RID: 825 RVA: 0x00020694 File Offset: 0x0001E894
	private void OnDisable()
	{
		DarkMachineFunctions.offsetAction = (Action<float>)Delegate.Remove(DarkMachineFunctions.offsetAction, new Action<float>(this.WorldOffset));
	}

	// Token: 0x0600033A RID: 826 RVA: 0x000206B8 File Offset: 0x0001E8B8
	private void FixedUpdate()
	{
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		this.nodes[0].position = base.transform.position;
		this.nodes[0].locked = false;
		if (this.target)
		{
			this.nodes[this.nodes.Count - 1].position = this.target.position;
			this.nodes[this.nodes.Count - 1].locked = false;
		}
		this.ApplyPhysics();
		this.SolveConstraints();
		this.RenderRope();
		if (this.targetRigidbody != null)
		{
			this.targetRigidbody.AddForce(Vector3.up * this.targetRigidbodyGravity, ForceMode.VelocityChange);
			if (this.swingAudio01 != null)
			{
				this.swingAudio01.volume = Mathf.Clamp01(this.targetRigidbody.velocity.magnitude * this.swingAudioVelocityMagnitude) * this.swingAudioVolume;
				this.swingAudio01.pitch = Mathf.Clamp(this.targetRigidbody.velocity.magnitude * this.swingAudioVelocityMagnitude, this.swingAudioPitchMin, this.swingAudioPitchMax);
			}
			if (this.swingAudio02 != null)
			{
				this.swingAudio02.volume = Mathf.Clamp01(this.targetRigidbody.velocity.magnitude * this.swingAudioVelocityMagnitude) * this.swingAudioVolume;
			}
		}
		if (!this.target)
		{
			return;
		}
		float num = Vector3.Distance(this.target.position, base.transform.position);
		float num2 = this.nodeDistance * (float)this.nodes.Count;
		if (num > num2)
		{
			Vector3 vector = (this.LimitDistanceFromTarget(this.target.position, base.transform.position, this.nodeDistance * (float)this.nodes.Count) - this.target.position) * this.tightness + (base.transform.position + Vector3.down * (this.nodeDistance * (float)this.nodes.Count) - this.target.position) * this.tightMult;
			float num3 = num2 + 1f;
			if (num > num3)
			{
				this.target.position = base.transform.position + (this.target.position - base.transform.position).normalized * num3;
			}
			this.targetRigidbody.AddForce(vector);
		}
	}

	// Token: 0x0600033B RID: 827 RVA: 0x00020984 File Offset: 0x0001EB84
	private void ApplyPhysics()
	{
		for (int i = 0; i < this.nodes.Count; i++)
		{
			ENV_Rope_Node env_Rope_Node = this.nodes[i];
			if (!env_Rope_Node.locked)
			{
				Vector3 position = env_Rope_Node.position;
				if (!env_Rope_Node.colliding)
				{
					float num = this.drag * env_Rope_Node.velocity.sqrMagnitude;
					Vector3 vector = -env_Rope_Node.velocity.normalized * num;
					env_Rope_Node.velocity += vector * Time.fixedDeltaTime;
					env_Rope_Node.position += env_Rope_Node.velocity * Time.fixedDeltaTime;
					env_Rope_Node.position += new Vector3(0f, this.gravity, 0f) * Time.fixedDeltaTime;
				}
				env_Rope_Node.previousPosition = position;
			}
		}
	}

	// Token: 0x0600033C RID: 828 RVA: 0x00020A78 File Offset: 0x0001EC78
	private void SolveConstraints()
	{
		for (int i = 0; i < this.constraintIterations; i++)
		{
			for (int j = 0; j < this.nodes.Count - 1; j++)
			{
				ENV_Rope_Node env_Rope_Node = this.nodes[j];
				ENV_Rope_Node env_Rope_Node2 = this.nodes[j + 1];
				float num = (env_Rope_Node.position - env_Rope_Node2.position).magnitude - this.nodeDistance;
				Vector3 vector = (env_Rope_Node.position - env_Rope_Node2.position).normalized * num * 0.5f;
				if (j != 0)
				{
					env_Rope_Node.position -= vector;
					env_Rope_Node.velocity -= vector;
				}
				else
				{
					env_Rope_Node.position = base.transform.position;
				}
				env_Rope_Node2.position += vector;
				env_Rope_Node2.velocity += vector;
				if (this.target && j == this.nodes.Count - 2)
				{
					env_Rope_Node2.position = this.target.position;
				}
			}
		}
	}

	// Token: 0x0600033D RID: 829 RVA: 0x00020BB8 File Offset: 0x0001EDB8
	private void RenderRope()
	{
		for (int i = 0; i < this.nodes.Count - 1; i++)
		{
			if (float.IsNaN(this.nodes[i].mesh.position.x) || float.IsNaN(this.nodes[i].mesh.position.y) || float.IsNaN(this.nodes[i].mesh.position.z))
			{
				this.nodes[i].mesh.position = base.transform.position;
			}
			Debug.DrawLine(this.nodes[i].position, this.nodes[i + 1].position, Color.red);
			this.nodes[i].mesh.position = this.nodes[i].position;
			this.nodes[i].mesh.rotation = Quaternion.LookRotation(this.nodes[i + 1].position - this.nodes[i].position, base.transform.up);
			this.nodes[i].mesh.localScale = new Vector3(1f, 1f, Vector3.Distance(this.nodes[i + 1].position, this.nodes[i].position));
		}
	}

	// Token: 0x0600033E RID: 830 RVA: 0x00020D5C File Offset: 0x0001EF5C
	private void HandleCollisions()
	{
		for (int i = 0; i < this.nodes.Count - 1; i++)
		{
			ENV_Rope_Node env_Rope_Node = this.nodes[i];
			ENV_Rope_Node env_Rope_Node2 = this.nodes[i + 1];
			if (Vector3.Distance(env_Rope_Node.position, env_Rope_Node2.position) >= 0.001f)
			{
				RaycastHit raycastHit;
				if (Physics.Linecast(env_Rope_Node.position, env_Rope_Node2.position, out raycastHit, this.layerMask))
				{
					Vector3 vector = raycastHit.point + raycastHit.normal * 0.1f;
					env_Rope_Node2.colliding = true;
					vector - env_Rope_Node2.position;
					env_Rope_Node2.position = vector;
				}
				else if (Physics.CheckSphere(env_Rope_Node2.position, 0.1f, this.layerMask))
				{
					env_Rope_Node2.colliding = true;
				}
				else
				{
					env_Rope_Node2.colliding = false;
				}
			}
		}
	}

	// Token: 0x0600033F RID: 831 RVA: 0x00020E4C File Offset: 0x0001F04C
	private Vector3 LimitDistanceFromTarget(Vector3 originPosition, Vector3 targetPosition, float maxDistance)
	{
		Vector3 vector = originPosition - targetPosition;
		if (vector.magnitude > maxDistance)
		{
			originPosition = targetPosition + vector.normalized * maxDistance;
		}
		return originPosition;
	}

	// Token: 0x06000340 RID: 832 RVA: 0x00020E84 File Offset: 0x0001F084
	public void AddVelocityToClosestNode(Vector3 position, Vector3 addedVelocity)
	{
		float num = float.MaxValue;
		ENV_Rope_Node env_Rope_Node = null;
		foreach (ENV_Rope_Node env_Rope_Node2 in this.nodes)
		{
			float num2 = Vector3.Distance(position, env_Rope_Node2.position);
			if (num2 < num)
			{
				num = num2;
				env_Rope_Node = env_Rope_Node2;
			}
		}
		if (env_Rope_Node != null)
		{
			Vector3 vector = env_Rope_Node.position - env_Rope_Node.previousPosition;
			vector += addedVelocity;
			env_Rope_Node.previousPosition = env_Rope_Node.position - vector;
		}
	}

	// Token: 0x06000341 RID: 833 RVA: 0x00020F24 File Offset: 0x0001F124
	public void DropRope()
	{
		this.SetCollision(false);
	}

	// Token: 0x06000342 RID: 834 RVA: 0x00020F2D File Offset: 0x0001F12D
	public void GrabRope()
	{
		this.SetCollision(true);
	}

	// Token: 0x06000343 RID: 835 RVA: 0x00020F36 File Offset: 0x0001F136
	public Rigidbody GetTargetRigidbody()
	{
		return this.targetRigidbody;
	}

	// Token: 0x06000344 RID: 836 RVA: 0x00020F40 File Offset: 0x0001F140
	public void SetCollision(bool b)
	{
		foreach (ENV_Rope_Node env_Rope_Node in this.nodes)
		{
			if (b)
			{
				if (env_Rope_Node.mesh)
				{
					env_Rope_Node.mesh.gameObject.layer = LayerMask.NameToLayer("Interactable");
				}
			}
			else if (env_Rope_Node.mesh)
			{
				env_Rope_Node.mesh.gameObject.layer = LayerMask.NameToLayer("Interactable No Collide");
			}
		}
	}

	// Token: 0x06000345 RID: 837 RVA: 0x00020FE0 File Offset: 0x0001F1E0
	public void AddVelocity(Vector3 v)
	{
		Vector3 vector = Vector3.ProjectOnPlane(v, base.transform.position - this.targetRigidbody.position);
		this.targetRigidbody.AddForce(vector);
	}

	// Token: 0x06000346 RID: 838 RVA: 0x0002101B File Offset: 0x0001F21B
	public Vector3 GetVelocity()
	{
		return this.targetRigidbody.velocity;
	}

	// Token: 0x06000347 RID: 839 RVA: 0x00021028 File Offset: 0x0001F228
	public void WorldOffset(float a)
	{
		for (int i = 0; i < this.nodes.Count - 1; i++)
		{
			this.nodes[i].position += Vector3.up * a;
			this.nodes[i].previousPosition += Vector3.up * a;
		}
	}

	// Token: 0x04000474 RID: 1140
	public List<ENV_Rope_Node> nodes = new List<ENV_Rope_Node>();

	// Token: 0x04000475 RID: 1141
	public float nodeDistance = 0.5f;

	// Token: 0x04000476 RID: 1142
	public float gravity = -9.81f;

	// Token: 0x04000477 RID: 1143
	public float drag = 0.99f;

	// Token: 0x04000478 RID: 1144
	public float tightness = 10f;

	// Token: 0x04000479 RID: 1145
	public float tightMult = 1f;

	// Token: 0x0400047A RID: 1146
	public int constraintIterations = 5;

	// Token: 0x0400047B RID: 1147
	public Transform target;

	// Token: 0x0400047C RID: 1148
	private Rigidbody targetRigidbody;

	// Token: 0x0400047D RID: 1149
	public float targetRigidbodyGravity = -9.81f;

	// Token: 0x0400047E RID: 1150
	public GameObject meshRoot;

	// Token: 0x0400047F RID: 1151
	public LayerMask layerMask;

	// Token: 0x04000480 RID: 1152
	public float grabVelocityTransfer = 20f;

	// Token: 0x04000481 RID: 1153
	public float grabVelocityMult = 5f;

	// Token: 0x04000482 RID: 1154
	[Header("Audio")]
	public AudioSource swingAudio01;

	// Token: 0x04000483 RID: 1155
	public AudioSource swingAudio02;

	// Token: 0x04000484 RID: 1156
	public float swingAudioVolume;

	// Token: 0x04000485 RID: 1157
	public float swingAudioVelocityMagnitude;

	// Token: 0x04000486 RID: 1158
	public float swingAudioPitchMin;

	// Token: 0x04000487 RID: 1159
	public float swingAudioPitchMax;
}
