﻿using System;

namespace EditorCools
{
	// Token: 0x020001E1 RID: 481
	[AttributeUsage(AttributeTargets.Method, Inherited = true, AllowMultiple = false)]
	public sealed class ButtonAttribute : Attribute
	{
		// Token: 0x06000C57 RID: 3159 RVA: 0x0004E218 File Offset: 0x0004C418
		public ButtonAttribute(string name = null, string row = null, float space = 0f)
		{
			this.Row = row;
			this.HasRow = !string.IsNullOrEmpty(this.Row);
			this.Name = name;
			this.Space = space;
		}

		// Token: 0x04000D27 RID: 3367
		public readonly string Name;

		// Token: 0x04000D28 RID: 3368
		public readonly string Row;

		// Token: 0x04000D29 RID: 3369
		public readonly float Space;

		// Token: 0x04000D2A RID: 3370
		public readonly bool HasRow;
	}
}
