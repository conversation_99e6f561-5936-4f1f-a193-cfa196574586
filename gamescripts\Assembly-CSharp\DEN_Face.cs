﻿using System;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x02000034 RID: 52
public class DEN_Face : Denizen
{
	// Token: 0x060001FA RID: 506 RVA: 0x00010701 File Offset: 0x0000E901
	public override void Start()
	{
		base.Start();
		CommandConsole.AddCommand("resetface", new Action<string[]>(this.ResetFace), true);
	}

	// Token: 0x060001FB RID: 507 RVA: 0x00010720 File Offset: 0x0000E920
	private new void Update()
	{
		base.transform.position += base.transform.forward * this.speed * Time.deltaTime;
		Quaternion quaternion = Quaternion.LookRotation((this.nodes[this.currentNode].position - base.transform.position).normalized + Vector3.up * Mathf.Sin(Time.time * 1f) * 0.15f);
		base.transform.rotation = Quaternion.Lerp(base.transform.rotation, quaternion, Time.deltaTime * this.turnSpeed);
		if (Vector3.Distance(base.transform.position, this.nodes[this.currentNode].position) < 3f)
		{
			this.currentNode++;
			if (this.currentNode >= this.nodes.Count)
			{
				this.currentNode = 0;
			}
		}
	}

	// Token: 0x060001FC RID: 508 RVA: 0x00010840 File Offset: 0x0000EA40
	public override void DrawGizmos()
	{
		for (int i = 0; i < this.nodes.Count; i++)
		{
			int num = i + 1;
			if (i + 1 >= this.nodes.Count)
			{
				num = 0;
			}
			Draw.Arrow(this.nodes[i].position, this.nodes[num].position, Color.red);
		}
	}

	// Token: 0x060001FD RID: 509 RVA: 0x000108B0 File Offset: 0x0000EAB0
	public void ResetFace(string[] args)
	{
		base.transform.position = this.nodes[0].position;
		base.transform.rotation = this.nodes[0].rotation;
		this.currentNode = 1;
	}

	// Token: 0x060001FE RID: 510 RVA: 0x000108FC File Offset: 0x0000EAFC
	private void UpdateSegments()
	{
	}

	// Token: 0x04000241 RID: 577
	public GameObject bodyPrefab;

	// Token: 0x04000242 RID: 578
	public float speed = 1f;

	// Token: 0x04000243 RID: 579
	public float turnSpeed = 1f;

	// Token: 0x04000244 RID: 580
	public List<Transform> nodes;

	// Token: 0x04000245 RID: 581
	private int currentNode;

	// Token: 0x04000246 RID: 582
	public List<DEN_Face.BodySegment> bodySegments;

	// Token: 0x0200021E RID: 542
	public class BodySegment
	{
		// Token: 0x04000E42 RID: 3650
		public int id;

		// Token: 0x04000E43 RID: 3651
		public GameObject segmentObject;

		// Token: 0x04000E44 RID: 3652
		public Transform position;
	}
}
