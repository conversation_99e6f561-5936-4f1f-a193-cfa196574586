﻿using System;

// Token: 0x02000069 RID: 105
[Serializable]
public class SessionEventModule_SetGameFlag : SessionEventModule
{
	// Token: 0x060003D8 RID: 984 RVA: 0x00023804 File Offset: 0x00021A04
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		CL_GameManager.SetGameFlag(this.gameFlagID, this.gameFlagState, this.data, this.saveFlag);
	}

	// Token: 0x04000533 RID: 1331
	public string gameFlagID;

	// Token: 0x04000534 RID: 1332
	public bool gameFlagState = true;

	// Token: 0x04000535 RID: 1333
	public string data;

	// Token: 0x04000536 RID: 1334
	public bool saveFlag;
}
