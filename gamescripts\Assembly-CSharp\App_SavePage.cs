﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000EB RID: 235
public class App_SavePage : MonoBehaviour
{
	// Token: 0x06000749 RID: 1865 RVA: 0x00037F9B File Offset: 0x0003619B
	private void Awake()
	{
		this.window = base.GetComponent<OS_Window>();
		OS_Window os_Window = this.window;
		os_Window.openAction = (Action)Delegate.Combine(os_Window.openAction, new Action(this.OnOpen));
	}

	// Token: 0x0600074A RID: 1866 RVA: 0x00037FD0 File Offset: 0x000361D0
	private void OnOpen()
	{
		Debug.Log("Opened!");
		this.ChangeMenu(this.menus[0].name);
		this.UpdateSaveText();
		this.CheckIronKnuckle();
	}

	// Token: 0x0600074B RID: 1867 RVA: 0x00038000 File Offset: 0x00036200
	private void CheckIronKnuckle()
	{
		if (SettingsManager.settings.g_competitive)
		{
			Message_Manager.Message_Packet message_Packet = new Message_Manager.Message_Packet();
			message_Packet.type = "default";
			message_Packet.closeText = "Quit";
			message_Packet.closeFunction = new Action(this.window.CloseApp);
			message_Packet.message = "You are playing in IRON KNUCKLE mode. Saves are not available.";
			message_Packet.screenPos = new Vector2(0f, 0f);
			this.window.os.messageManager.CreateMessage(message_Packet);
		}
	}

	// Token: 0x0600074C RID: 1868 RVA: 0x00038084 File Offset: 0x00036284
	private void UpdateSaveText()
	{
		List<CL_SaveManager.SaveState> allSaveStatesByType = CL_SaveManager.GetAllSaveStatesByType(CL_SaveManager.SaveState.SaveType.disk);
		string text = "SAVES | <mspace=5>";
		int num = 0;
		string text2 = "";
		if (allSaveStatesByType.Count > 0)
		{
			foreach (CL_SaveManager.SaveState saveState in allSaveStatesByType)
			{
				if (this.window.os.worldInterface != null)
				{
					if (saveState.id == this.window.os.worldInterface.computerID)
					{
						int amount = saveState.amount;
						text += string.Format("{0}:<color=red>{1}</color> ", saveState.id, saveState.amount);
					}
					else
					{
						text2 += string.Format("{0}:<color=red>{1}</color> ", saveState.id, saveState.amount);
						num += saveState.amount;
					}
				}
			}
			if (num > 0)
			{
				text += text2;
			}
		}
		else
		{
			text += "NO BACKUP DATA FOUND";
		}
		this.floppyText.text = text;
		if (this.diskCards.Count > 0)
		{
			for (int i = this.diskCards.Count - 1; i >= 0; i--)
			{
				Object.Destroy(this.diskCards[i].gameObject);
			}
			this.diskCards.Clear();
		}
		List<Item> disks = OS_Manager.activeComputer.GetDiskController().GetDisks();
		foreach (Item item in disks)
		{
			App_SavePage_DiskCard app_SavePage_DiskCard = Object.Instantiate<App_SavePage_DiskCard>(this.cardPrefab, this.diskCardParent);
			string text3 = "Untitled Disk";
			int num2 = 1;
			foreach (string text4 in item.data)
			{
				if (text4.Contains("name:"))
				{
					text3 = text4.Split(':', StringSplitOptions.None)[1];
				}
				else if (text4.Contains("capacity:"))
				{
					num2 = int.Parse(text4.Split(':', StringSplitOptions.None)[1]);
				}
			}
			app_SavePage_DiskCard.Initialize(text3, num2);
			this.diskCards.Add(app_SavePage_DiskCard);
		}
		if (disks.Count == 0 && this.currentMenu == "reformat")
		{
			this.ChangeMenu("nodisks");
			return;
		}
		if (disks.Count > 0 && this.currentMenu == "nodisks")
		{
			this.ChangeMenu("reformat");
		}
	}

	// Token: 0x0600074D RID: 1869 RVA: 0x0003834C File Offset: 0x0003654C
	public void AddSave(int capacity = 1)
	{
		CL_SaveManager.CreateOrUpdateSaveState(this.window.os.worldInterface.computerID, "disk", CL_SaveManager.SaveState.SaveType.disk, capacity, true);
		CL_SaveManager.instance.SaveSession(false, false, true, "diskbackup", true);
		this.UpdateSaveText();
	}

	// Token: 0x0600074E RID: 1870 RVA: 0x0003838B File Offset: 0x0003658B
	public void LoadSave()
	{
		this.window.os.Deactivate();
		CL_SaveManager.LoadSave(CL_SaveManager.GetSaveStateByID(this.window.os.worldInterface.computerID, CL_SaveManager.SaveState.SaveType.disk), null);
	}

	// Token: 0x0600074F RID: 1871 RVA: 0x000383C0 File Offset: 0x000365C0
	public void StartReformat()
	{
		App_SavePage.<>c__DisplayClass21_0 CS$<>8__locals1 = new App_SavePage.<>c__DisplayClass21_0();
		CS$<>8__locals1.<>4__this = this;
		if (this.busy || SettingsManager.settings.g_competitive)
		{
			return;
		}
		CS$<>8__locals1.disks = this.window.os.GetDiskController().GetDisks();
		Message_Manager.Message_Packet message_Packet = new Message_Manager.Message_Packet();
		message_Packet.type = "default";
		message_Packet.closeText = "Proceed";
		message_Packet.aText = "Cancel";
		message_Packet.closeFunction = new Action(CS$<>8__locals1.<StartReformat>g__Reformat|0);
		message_Packet.optionAFunction = new Action(App_SavePage.<StartReformat>g__Cancel|21_1);
		message_Packet.message = "This will erase all data on these disks. Proceed?";
		message_Packet.screenPos = new Vector2(0f, 0f);
		this.window.os.messageManager.CreateMessage(message_Packet);
	}

	// Token: 0x06000750 RID: 1872 RVA: 0x0003848C File Offset: 0x0003668C
	public void ChangeMenu(string menuName)
	{
		if (this.busy)
		{
			return;
		}
		foreach (App_SavePage.Menu menu in this.menus)
		{
			menu.windowObject.SetActive(menu.name == menuName);
			if (menu.name == menuName && menu.firstSelect != null)
			{
				menu.firstSelect.Select();
			}
		}
		this.currentMenu = menuName;
		this.UpdateSaveText();
		List<Item> disks = OS_Manager.activeComputer.GetDiskController().GetDisks();
		if (disks.Count == 0 && this.currentMenu == "reformat")
		{
			this.ChangeMenu("nodisks");
			return;
		}
		if (disks.Count > 0 && this.currentMenu == "nodisks")
		{
			this.ChangeMenu("reformat");
		}
	}

	// Token: 0x06000751 RID: 1873 RVA: 0x00038588 File Offset: 0x00036788
	private IEnumerator PurchaseSequence()
	{
		this.busy = true;
		yield return new WaitForSeconds(0.5f);
		this.window.os.audioClipHandler.PlaySound("os:error");
		OS_Window purchaseWindow = this.window.os.CreateAppWindow(this.fulfillmentWindow, this.window.id + "-purchase");
		while (purchaseWindow != null)
		{
			yield return null;
		}
		this.window.os.audioClipHandler.PlaySound("save:startscan");
		OS_Window scan = this.window.os.CreateAppWindow(this.scanWindow, this.window.id + "-purchase");
		CL_CameraControl.Shake(0.005f);
		CL_UIManager.instance.reviveGroup.gameObject.SetActive(true);
		AudioSource reviveAudio = CL_UIManager.instance.reviveGroup.GetComponent<AudioSource>();
		while ((double)CL_UIManager.instance.reviveGroup.alpha < 0.5)
		{
			CL_UIManager.instance.reviveGroup.alpha += Time.unscaledDeltaTime * 0.2f;
			reviveAudio.volume += Time.unscaledDeltaTime * 0.2f;
			yield return null;
		}
		yield return new WaitForSeconds(3.5f);
		CL_CameraControl.Shake(0.005f);
		OS_Window scanFinish = this.window.os.CreateAppWindow(this.scanFinishWindow, this.window.id + "-scanFinish");
		scan.QuitApp();
		CL_GameManager.gMan.localPlayer.health = 1f;
		BuffContainer buffContainer = new BuffContainer();
		buffContainer.loseOverTime = true;
		buffContainer.loseRate = 0.05f;
		buffContainer.buffs = new List<BuffContainer.Buff>();
		buffContainer.buffs.Add(new BuffContainer.Buff
		{
			id = "roided",
			maxAmount = 0.5f
		});
		buffContainer.buffs.Add(new BuffContainer.Buff
		{
			id = "pilled",
			maxAmount = 0.5f
		});
		buffContainer.SetMultiplier(1f);
		buffContainer.Initialize();
		CL_GameManager.gMan.localPlayer.curBuffs.AddBuff(buffContainer);
		while (CL_UIManager.instance.reviveGroup.alpha > 0f)
		{
			CL_UIManager.instance.reviveGroup.alpha -= Time.unscaledDeltaTime * 0.5f;
			reviveAudio.volume -= Time.unscaledDeltaTime * 0.5f;
			yield return null;
		}
		CL_UIManager.instance.reviveGroup.gameObject.SetActive(false);
		this.window.os.audioClipHandler.PlaySound("save:endscan");
		while (scanFinish != null)
		{
			yield return null;
		}
		CL_CameraControl.Shake(0.005f);
		this.window.os.audioClipHandler.PlaySound("os:error");
		this.UpdateSaveText();
		this.busy = false;
		yield return new WaitForSeconds(0.5f);
		CL_UIManager.instance.reviveGroup.gameObject.SetActive(false);
		this.ChangeMenu("main");
		yield break;
	}

	// Token: 0x06000753 RID: 1875 RVA: 0x000385B5 File Offset: 0x000367B5
	[CompilerGenerated]
	internal static void <StartReformat>g__Cancel|21_1()
	{
	}

	// Token: 0x040008C2 RID: 2242
	public Button purchaseButton;

	// Token: 0x040008C3 RID: 2243
	public TMP_Text floppyText;

	// Token: 0x040008C4 RID: 2244
	public Transform diskCardParent;

	// Token: 0x040008C5 RID: 2245
	public App_SavePage_DiskCard cardPrefab;

	// Token: 0x040008C6 RID: 2246
	private List<App_SavePage_DiskCard> diskCards = new List<App_SavePage_DiskCard>();

	// Token: 0x040008C7 RID: 2247
	public List<App_SavePage.Menu> menus;

	// Token: 0x040008C8 RID: 2248
	public OS_Window fulfillmentWindow;

	// Token: 0x040008C9 RID: 2249
	public OS_Window scanWindow;

	// Token: 0x040008CA RID: 2250
	public OS_Window scanFinishWindow;

	// Token: 0x040008CB RID: 2251
	public GameObject noDisksDetected;

	// Token: 0x040008CC RID: 2252
	public AudioClip finishMindscanSound;

	// Token: 0x040008CD RID: 2253
	private OS_Window window;

	// Token: 0x040008CE RID: 2254
	private bool busy;

	// Token: 0x040008CF RID: 2255
	private string currentMenu = "main";

	// Token: 0x02000298 RID: 664
	[Serializable]
	public class Menu
	{
		// Token: 0x040010BA RID: 4282
		public string name;

		// Token: 0x040010BB RID: 4283
		public GameObject windowObject;

		// Token: 0x040010BC RID: 4284
		public Selectable firstSelect;
	}
}
