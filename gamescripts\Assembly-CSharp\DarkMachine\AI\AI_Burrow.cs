﻿using System;
using Drawing;
using UnityEngine;

namespace DarkMachine.AI
{
	// Token: 0x020001D9 RID: 473
	public class AI_Burrow : MonoBehaviourGizmos
	{
		// Token: 0x06000BF1 RID: 3057 RVA: 0x0004BCEA File Offset: 0x00049EEA
		private void OnEnable()
		{
			if (AI_NavigationManager.burrowManager == null)
			{
				return;
			}
			AI_NavigationManager.burrowManager.AddBurrow(this);
		}

		// Token: 0x06000BF2 RID: 3058 RVA: 0x0004BCFF File Offset: 0x00049EFF
		private void OnDisable()
		{
			if (AI_NavigationManager.burrowManager == null)
			{
				return;
			}
			AI_NavigationManager.burrowManager.RemoveBurrow(this);
		}

		// Token: 0x06000BF3 RID: 3059 RVA: 0x0004BD14 File Offset: 0x00049F14
		private void Update()
		{
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.Label2D(base.transform.position, "AI Burrow", 12f, LabelAlignment.Center, Color.red);
				CL_DebugView.draw.WireHexagon(base.transform.position, Quaternion.LookRotation(base.transform.up, base.transform.forward), 1f, Color.red);
			}
		}

		// Token: 0x06000BF4 RID: 3060 RVA: 0x0004BD9C File Offset: 0x00049F9C
		public override void DrawGizmos()
		{
			Draw.Label2D(base.transform.position, "AI Burrow", 12f, LabelAlignment.Center, Color.red);
			if (GizmoContext.InSelection(this))
			{
				Draw.Arrow(base.transform.position, base.transform.position + base.transform.up, Color.white);
				Draw.Arrow(base.transform.position, base.transform.position + base.transform.forward, Color.grey);
				Draw.WireHexagon(base.transform.position, Quaternion.LookRotation(base.transform.up, base.transform.forward), 1f, Color.red);
				return;
			}
			Draw.Arrow(base.transform.position, base.transform.position + base.transform.up, new Color(1f, 1f, 1f, 0.3f));
			Draw.Arrow(base.transform.position, base.transform.position + base.transform.forward, Color.grey);
			Draw.WireHexagon(base.transform.position, Quaternion.LookRotation(base.transform.up, base.transform.forward), 1f, new Color(1f, 0f, 0f, 0.2f));
		}
	}
}
