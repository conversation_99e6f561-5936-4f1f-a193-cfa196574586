﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

// Token: 0x02000158 RID: 344
public static class RaycastUtilities
{
	// Token: 0x060009CA RID: 2506 RVA: 0x000429D4 File Offset: 0x00040BD4
	public static bool PointerIsOverUI(Vector2 screenPos, string layerName = "UI")
	{
		GameObject gameObject = RaycastUtilities.UIRaycast(RaycastUtilities.ScreenPosToPointerData(screenPos));
		return gameObject != null && gameObject.layer == LayerMask.NameToLayer(layerName);
	}

	// Token: 0x060009CB RID: 2507 RVA: 0x00042A08 File Offset: 0x00040C08
	public static bool PointerIsOverUI(Vector2 screenPos, string tag, string layerName = "UI")
	{
		GameObject gameObject = RaycastUtilities.UIRaycast(screenPos, tag, layerName);
		return gameObject != null && gameObject.layer == LayerMask.NameToLayer(layerName);
	}

	// Token: 0x060009CC RID: 2508 RVA: 0x00042A38 File Offset: 0x00040C38
	public static GameObject UIRaycast(PointerEventData pointerData)
	{
		List<RaycastResult> list = new List<RaycastResult>();
		EventSystem.current.RaycastAll(pointerData, list);
		if (list.Count >= 1)
		{
			return list[0].gameObject;
		}
		return null;
	}

	// Token: 0x060009CD RID: 2509 RVA: 0x00042A71 File Offset: 0x00040C71
	public static GameObject UIRaycast(Vector2 screenPos)
	{
		return RaycastUtilities.UIRaycast(RaycastUtilities.ScreenPosToPointerData(screenPos));
	}

	// Token: 0x060009CE RID: 2510 RVA: 0x00042A80 File Offset: 0x00040C80
	public static GameObject UIRaycast(Vector2 screenPos, string tag, string layerName)
	{
		PointerEventData pointerEventData = RaycastUtilities.ScreenPosToPointerData(screenPos);
		List<RaycastResult> list = new List<RaycastResult>();
		EventSystem.current.RaycastAll(pointerEventData, list);
		if (list.Count == 0)
		{
			return null;
		}
		while (list.Count > 0)
		{
			if (list[0].gameObject.tag == tag && list[0].gameObject.layer == LayerMask.NameToLayer(layerName))
			{
				return list[0].gameObject;
			}
			list.RemoveAt(0);
		}
		return null;
	}

	// Token: 0x060009CF RID: 2511 RVA: 0x00042B09 File Offset: 0x00040D09
	private static PointerEventData ScreenPosToPointerData(Vector2 screenPos)
	{
		return new PointerEventData(EventSystem.current)
		{
			position = screenPos
		};
	}
}
