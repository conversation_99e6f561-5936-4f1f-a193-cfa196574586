﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using UnityEngine;

// Token: 0x02000023 RID: 35
public class BuddyModule_RadioEvent : BuddyModule
{
	// Token: 0x06000163 RID: 355 RVA: 0x0000AE62 File Offset: 0x00009062
	public override void Initialize(DEN_Buddy b)
	{
		base.Initialize(b);
		this.buddy.StartCoroutine(this.<Initialize>g__RadioEventCoroutine|3_0());
	}

	// Token: 0x06000165 RID: 357 RVA: 0x0000AE85 File Offset: 0x00009085
	[CompilerGenerated]
	private IEnumerator <Initialize>g__RadioEventCoroutine|3_0()
	{
		AudioClip audioClip = this.startClips[Random.Range(0, this.startClips.Count)];
		this.buddy.PlaySound(audioClip);
		yield return new WaitForSeconds(audioClip.length + 0.5f);
		audioClip = this.station.GetRandomTrack().trackClip;
		this.buddy.PlaySound(audioClip);
		yield return new WaitForSeconds(audioClip.length + 1f);
		audioClip = this.endClips[Random.Range(0, this.endClips.Count)];
		this.buddy.PlaySound(audioClip);
		yield return new WaitForSeconds(audioClip.length + 1f);
		this.buddy.ClearEvent();
		yield break;
	}

	// Token: 0x0400012E RID: 302
	public ENV_RadioStation station;

	// Token: 0x0400012F RID: 303
	public List<AudioClip> startClips;

	// Token: 0x04000130 RID: 304
	public List<AudioClip> endClips;
}
