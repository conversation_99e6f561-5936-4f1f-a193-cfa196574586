﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020001CA RID: 458
[DefaultExecutionOrder(5)]
public class UT_SpawnEntityByID : MonoBehaviour
{
	// Token: 0x06000BAB RID: 2987 RVA: 0x0004A6DE File Offset: 0x000488DE
	private void Start()
	{
		if (this.spawnOnStart)
		{
			this.Spawn();
		}
	}

	// Token: 0x06000BAC RID: 2988 RVA: 0x0004A6EE File Offset: 0x000488EE
	public void Spawn()
	{
		this.SpawnCustom(this.idToSpawn);
	}

	// Token: 0x06000BAD RID: 2989 RVA: 0x0004A6FC File Offset: 0x000488FC
	public void SpawnCustom(string id)
	{
		GameObject assetGameObject = CL_AssetManager.GetAssetGameObject(id, this.customDatabase);
		Transform transform = base.transform;
		if (this.customParent)
		{
			transform = this.customParent;
		}
		GameObject gameObject = Object.Instantiate<GameObject>(assetGameObject, base.transform.position, base.transform.rotation, transform);
		if (this.forceKinematicIfRigidbody)
		{
			Rigidbody component = gameObject.GetComponent<Rigidbody>();
			if (component != null)
			{
				component.isKinematic = true;
			}
		}
		if (this.useCustomEntityData)
		{
			Item_Object component2 = gameObject.GetComponent<Item_Object>();
			if (component2 != null)
			{
				component2.itemData.data = this.entityData;
			}
		}
	}

	// Token: 0x06000BAE RID: 2990 RVA: 0x0004A798 File Offset: 0x00048998
	private void OnValidate()
	{
		this.objectToSpawn = CL_AssetManager.GetAssetGameObject(this.idToSpawn, this.customDatabase);
		if (this.objectToSpawn == null)
		{
			this.gizmoMesh = null;
			return;
		}
		if (this.objectToSpawn.GetComponentInChildren<MeshFilter>() != null)
		{
			this.gizmoMesh = this.objectToSpawn.GetComponentInChildren<MeshFilter>().sharedMesh;
			return;
		}
		SkinnedMeshRenderer componentInChildren = this.objectToSpawn.GetComponentInChildren<SkinnedMeshRenderer>();
		if (componentInChildren != null)
		{
			this.gizmoMesh = componentInChildren.sharedMesh;
			return;
		}
		this.gizmoMesh = null;
	}

	// Token: 0x06000BAF RID: 2991 RVA: 0x0004A828 File Offset: 0x00048A28
	private void OnDrawGizmos()
	{
		if (this.gizmoMesh == null)
		{
			this.gizmoMesh = Resources.GetBuiltinResource<Mesh>("Cube.fbx");
		}
		Gizmos.color = Color.yellow;
		Gizmos.DrawMesh(this.gizmoMesh, base.transform.position, base.transform.rotation);
	}

	// Token: 0x04000CC0 RID: 3264
	public bool spawnOnStart;

	// Token: 0x04000CC1 RID: 3265
	public string idToSpawn;

	// Token: 0x04000CC2 RID: 3266
	public string customDatabase;

	// Token: 0x04000CC3 RID: 3267
	public Transform customParent;

	// Token: 0x04000CC4 RID: 3268
	private GameObject objectToSpawn;

	// Token: 0x04000CC5 RID: 3269
	private Mesh gizmoMesh;

	// Token: 0x04000CC6 RID: 3270
	public bool forceKinematicIfRigidbody;

	// Token: 0x04000CC7 RID: 3271
	public bool useCustomEntityData;

	// Token: 0x04000CC8 RID: 3272
	[TextArea]
	public List<string> entityData;
}
