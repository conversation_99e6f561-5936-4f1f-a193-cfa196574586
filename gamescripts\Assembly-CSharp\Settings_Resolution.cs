﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

// Token: 0x02000113 RID: 275
public class Settings_Resolution : MonoBehaviour
{
	// Token: 0x0600084B RID: 2123 RVA: 0x0003C204 File Offset: 0x0003A404
	private void Start()
	{
		this.resolutions = Screen.resolutions;
		List<TMP_Dropdown.OptionData> list = new List<TMP_Dropdown.OptionData>();
		int num = -1;
		for (int i = 0; i < this.resolutions.Length; i++)
		{
			TMP_Dropdown.OptionData optionData = new TMP_Dropdown.OptionData();
			optionData.text = string.Concat(new string[]
			{
				this.resolutions[i].width.ToString(),
				"x",
				this.resolutions[i].height.ToString(),
				" ",
				this.resolutions[i].refreshRateRatio.value.ToString(),
				"hz"
			});
			if (this.resolutions[i].width == Screen.currentResolution.width && this.resolutions[i].height == Screen.currentResolution.height && this.resolutions[i].refreshRateRatio.value.ToString() == SettingsManager.settings.refreshRate)
			{
				num = i;
			}
			list.Add(optionData);
		}
		if (num == -1)
		{
			list.Add(new TMP_Dropdown.OptionData
			{
				text = string.Concat(new string[]
				{
					Screen.currentResolution.width.ToString(),
					"x",
					Screen.currentResolution.height.ToString(),
					" ",
					Screen.currentResolution.refreshRateRatio.value.ToString(),
					"hz"
				})
			});
			num = list.Count - 1;
		}
		this.dropdown.AddOptions(list);
		this.dropdown.value = num;
	}

	// Token: 0x0600084C RID: 2124 RVA: 0x0003C404 File Offset: 0x0003A604
	public void OnUpdateDropDown()
	{
		if (this.dropdown.value < this.resolutions.Length)
		{
			Resolution resolution = this.resolutions[this.dropdown.value];
			SettingsManager.SetSetting(new string[]
			{
				"screenResX",
				resolution.width.ToString()
			});
			SettingsManager.SetSetting(new string[]
			{
				"screenResY",
				resolution.height.ToString()
			});
			SettingsManager.SetSetting(new string[]
			{
				"refreshRate",
				resolution.refreshRateRatio.value.ToString()
			});
			SettingsManager.RefreshSettings("");
		}
	}

	// Token: 0x040009B0 RID: 2480
	public TMP_Dropdown dropdown;

	// Token: 0x040009B1 RID: 2481
	private Resolution[] resolutions;
}
