﻿using System;
using UnityEngine;
using UnityEngine.InputSystem;

// Token: 0x02000009 RID: 9
public class RebindSaveLoad : MonoBehaviour
{
	// Token: 0x06000037 RID: 55 RVA: 0x0000429C File Offset: 0x0000249C
	public void OnEnable()
	{
		string @string = PlayerPrefs.GetString("rebinds");
		if (!string.IsNullOrEmpty(@string))
		{
			this.actions.LoadBindingOverridesFromJson(@string, true);
		}
	}

	// Token: 0x06000038 RID: 56 RVA: 0x000042CC File Offset: 0x000024CC
	public void OnDisable()
	{
		string text = this.actions.SaveBindingOverridesAsJson();
		PlayerPrefs.SetString("rebinds", text);
	}

	// Token: 0x06000039 RID: 57 RVA: 0x000042F0 File Offset: 0x000024F0
	public void ResetKeyBindings()
	{
		this.actions.RemoveAllBindingOverrides();
	}

	// Token: 0x04000049 RID: 73
	public InputActionAsset actions;
}
