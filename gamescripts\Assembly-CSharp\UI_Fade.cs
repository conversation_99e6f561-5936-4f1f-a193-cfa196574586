﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000126 RID: 294
public class UI_Fade : MonoBehaviour
{
	// Token: 0x060008B3 RID: 2227 RVA: 0x0003DC80 File Offset: 0x0003BE80
	private void Start()
	{
		this.fadeImage = base.GetComponent<Image>();
		this.fadeColor = this.fadeImage.color;
		base.StartCoroutine(this.BeginFade());
	}

	// Token: 0x060008B4 RID: 2228 RVA: 0x0003DCAC File Offset: 0x0003BEAC
	private IEnumerator BeginFade()
	{
		if (this.startFade == UI_Fade.StartFade.fadeOut)
		{
			this.fadeImage.color = Color.clear;
		}
		this.fadeTarget = this.fadeImage.color;
		yield return new WaitForSeconds(this.startFadeTime);
		switch (this.startFade)
		{
		case UI_Fade.StartFade.none:
			this.fadeTarget = this.fadeImage.color;
			break;
		case UI_Fade.StartFade.fadeIn:
			this.fadeTarget = Color.clear;
			break;
		case UI_Fade.StartFade.fadeOut:
			this.fadeImage.color = Color.clear;
			this.fadeTarget = this.fadeColor;
			break;
		}
		yield break;
	}

	// Token: 0x060008B5 RID: 2229 RVA: 0x0003DCBB File Offset: 0x0003BEBB
	public void FadeIn()
	{
		this.fadeTarget = Color.clear;
	}

	// Token: 0x060008B6 RID: 2230 RVA: 0x0003DCC8 File Offset: 0x0003BEC8
	public void FadeOut()
	{
		this.fadeTarget = this.fadeColor;
	}

	// Token: 0x060008B7 RID: 2231 RVA: 0x0003DCD6 File Offset: 0x0003BED6
	private void Update()
	{
		this.fadeImage.color = Color.Lerp(this.fadeImage.color, this.fadeTarget, Time.deltaTime * this.fadeSpeed);
	}

	// Token: 0x04000A00 RID: 2560
	public UI_Fade.StartFade startFade;

	// Token: 0x04000A01 RID: 2561
	public float startFadeTime = 1f;

	// Token: 0x04000A02 RID: 2562
	public float fadeSpeed = 1f;

	// Token: 0x04000A03 RID: 2563
	private Image fadeImage;

	// Token: 0x04000A04 RID: 2564
	private Color fadeTarget;

	// Token: 0x04000A05 RID: 2565
	private Color fadeColor;

	// Token: 0x020002B7 RID: 695
	public enum StartFade
	{
		// Token: 0x0400118A RID: 4490
		none,
		// Token: 0x0400118B RID: 4491
		fadeIn,
		// Token: 0x0400118C RID: 4492
		fadeOut
	}
}
