﻿using System;
using UnityEngine;

// Token: 0x02000142 RID: 322
public class UI_UIScaleToSettings : MonoBehaviour
{
	// Token: 0x0600094F RID: 2383 RVA: 0x000405FB File Offset: 0x0003E7FB
	private void OnEnable()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.Refresh));
		this.Refresh();
	}

	// Token: 0x06000950 RID: 2384 RVA: 0x00040623 File Offset: 0x0003E823
	private void OnDisable()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Remove(SettingsManager.SettingsRefreshUpdate, new Action(this.Refresh));
	}

	// Token: 0x06000951 RID: 2385 RVA: 0x00040645 File Offset: 0x0003E845
	private void Refresh()
	{
		base.transform.localScale = Vector3.one * SettingsManager.settings.UIScale;
	}
}
