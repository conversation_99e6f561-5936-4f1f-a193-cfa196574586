﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x02000149 RID: 329
public class UI_RoachBankAmount : MonoBehaviour
{
	// Token: 0x06000970 RID: 2416 RVA: 0x00040C03 File Offset: 0x0003EE03
	private void Awake()
	{
		this.text = base.GetComponent<TMP_Text>();
	}

	// Token: 0x06000971 RID: 2417 RVA: 0x00040C11 File Offset: 0x0003EE11
	private void OnEnable()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.Refresh));
		this.Refresh();
	}

	// Token: 0x06000972 RID: 2418 RVA: 0x00040C39 File Offset: 0x0003EE39
	private void OnDisable()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Remove(SettingsManager.SettingsRefreshUpdate, new Action(this.Refresh));
	}

	// Token: 0x06000973 RID: 2419 RVA: 0x00040C5C File Offset: 0x0003EE5C
	public void Refresh()
	{
		if (SettingsManager.settings.g_competitive)
		{
			this.text.text = "--";
			return;
		}
		this.text.text = StatManager.saveData.GetRoachBankByID(SettingsManager.settings.g_hard ? (this.roachBankID + "-hard") : this.roachBankID).value.ToString();
	}

	// Token: 0x04000AB9 RID: 2745
	public string roachBankID;

	// Token: 0x04000ABA RID: 2746
	private TMP_Text text;
}
