﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020000FE RID: 254
public class OS_ResourceManager : MonoBehaviour
{
	// Token: 0x060007CC RID: 1996 RVA: 0x0003A798 File Offset: 0x00038998
	private void Start()
	{
		this.pictureFiles = new Dictionary<string, Sprite>();
		if (this.pictures != null)
		{
			foreach (Sprite sprite in this.pictures)
			{
				if (!(sprite == null))
				{
					this.pictureFiles.Add(sprite.name, sprite);
				}
			}
		}
		this.iconFiles = new Dictionary<string, Sprite>();
		foreach (Sprite sprite2 in this.icons)
		{
			this.iconFiles.Add(sprite2.name, sprite2);
		}
		this.soundFiles = new Dictionary<string, AudioClip>();
		foreach (AudioClip audioClip in this.sounds)
		{
			this.soundFiles.Add(audioClip.name, audioClip);
		}
	}

	// Token: 0x060007CD RID: 1997 RVA: 0x0003A8C8 File Offset: 0x00038AC8
	public Sprite GetPicture(string name)
	{
		return this.pictureFiles[name];
	}

	// Token: 0x060007CE RID: 1998 RVA: 0x0003A8D6 File Offset: 0x00038AD6
	public Sprite GetIcon(string name)
	{
		return this.iconFiles[name];
	}

	// Token: 0x060007CF RID: 1999 RVA: 0x0003A8E4 File Offset: 0x00038AE4
	public AudioClip GetSound(string name)
	{
		return this.soundFiles[name];
	}

	// Token: 0x04000943 RID: 2371
	public List<Sprite> pictures;

	// Token: 0x04000944 RID: 2372
	public Dictionary<string, Sprite> pictureFiles;

	// Token: 0x04000945 RID: 2373
	public List<Sprite> icons;

	// Token: 0x04000946 RID: 2374
	public Dictionary<string, Sprite> iconFiles;

	// Token: 0x04000947 RID: 2375
	public List<AudioClip> sounds;

	// Token: 0x04000948 RID: 2376
	public Dictionary<string, AudioClip> soundFiles;
}
