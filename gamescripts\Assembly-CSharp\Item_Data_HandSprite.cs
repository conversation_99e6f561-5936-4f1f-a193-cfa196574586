﻿using System;
using UnityEngine;

// Token: 0x02000095 RID: 149
public class Item_Data_HandSprite : Item_Data
{
	// Token: 0x0600050D RID: 1293 RVA: 0x0002B221 File Offset: 0x00029421
	public override string GetDataType()
	{
		return "handsprite";
	}

	// Token: 0x0600050E RID: 1294 RVA: 0x0002B228 File Offset: 0x00029428
	public override string GetSaveData()
	{
		return this.handSprite.name;
	}

	// Token: 0x0600050F RID: 1295 RVA: 0x0002B235 File Offset: 0x00029435
	public override void LoadSaveData(string s)
	{
		base.LoadSaveData(s);
		Debug.Log("Load Data: " + s);
		this.handSprite = CL_AssetManager.GetSpriteAsset(s, "");
	}

	// Token: 0x0400069D RID: 1693
	public Sprite handSprite;
}
