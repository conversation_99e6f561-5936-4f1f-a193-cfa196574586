﻿using System;

// Token: 0x02000046 RID: 70
[Serializable]
public class PerkModule
{
	// Token: 0x0600031E RID: 798 RVA: 0x0001FD4F File Offset: 0x0001DF4F
	public virtual void Initialize(Perk p)
	{
		this.perk = p;
	}

	// Token: 0x0600031F RID: 799 RVA: 0x0001FD58 File Offset: 0x0001DF58
	public virtual void AddModule()
	{
	}

	// Token: 0x06000320 RID: 800 RVA: 0x0001FD5A File Offset: 0x0001DF5A
	public virtual void Update()
	{
	}

	// Token: 0x0400044D RID: 1101
	public string name;

	// Token: 0x0400044E RID: 1102
	internal Perk perk;
}
