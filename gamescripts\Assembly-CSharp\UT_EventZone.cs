﻿using System;
using UnityEngine;

// Token: 0x020000CF RID: 207
public class UT_EventZone : MonoBehaviour
{
	// Token: 0x060006B1 RID: 1713 RVA: 0x00035A96 File Offset: 0x00033C96
	private void Start()
	{
		this.gMan = GameObject.Find("GameManager").GetComponent<CL_GameManager>();
		this.aud = base.GetComponent<AudioSource>();
	}

	// Token: 0x060006B2 RID: 1714 RVA: 0x00035ABC File Offset: 0x00033CBC
	private void OnTriggerEnter(Collider other)
	{
		if (this.hasRun || !this.active)
		{
			return;
		}
		foreach (string text in this.triggerTags)
		{
			if (other.tag == text)
			{
				if (this.enterScrawl != "")
				{
					this.gMan.uiMan.header.ShowText(this.enterScrawl);
				}
				if (this.enterEffect != null)
				{
					this.aud.clip = this.enterEffect;
					this.aud.Play();
				}
				this.enterEvent.Invoke(other.transform);
				if (this.triggerMessage != null && this.triggerMessage != "")
				{
					other.gameObject.SendMessage(this.triggerMessage);
				}
			}
		}
	}

	// Token: 0x060006B3 RID: 1715 RVA: 0x00035BA0 File Offset: 0x00033DA0
	private void OnTriggerExit(Collider other)
	{
		if (this.hasRun || !this.active)
		{
			return;
		}
		foreach (string text in this.triggerTags)
		{
			if (other.tag == text)
			{
				if (this.exitScrawl != "")
				{
					this.gMan.uiMan.header.ShowText(this.exitScrawl);
				}
				if (this.exitEffect != null)
				{
					this.aud.clip = this.exitEffect;
					this.aud.Play();
				}
				if (this.runOnce)
				{
					this.hasRun = true;
				}
				this.exitEvent.Invoke(other.transform);
			}
		}
	}

	// Token: 0x060006B4 RID: 1716 RVA: 0x00035C64 File Offset: 0x00033E64
	private void OnTriggerStay(Collider other)
	{
		if (this.hasRun || !this.active)
		{
			return;
		}
		foreach (string text in this.triggerTags)
		{
			if (other.tag == text)
			{
				this.stayEvent.Invoke(other.transform);
			}
		}
	}

	// Token: 0x060006B5 RID: 1717 RVA: 0x00035CBA File Offset: 0x00033EBA
	public void SetActive(bool b)
	{
		this.active = b;
	}

	// Token: 0x04000832 RID: 2098
	private CL_GameManager gMan;

	// Token: 0x04000833 RID: 2099
	private AudioSource aud;

	// Token: 0x04000834 RID: 2100
	public bool active = true;

	// Token: 0x04000835 RID: 2101
	public AudioClip enterEffect;

	// Token: 0x04000836 RID: 2102
	public AudioClip exitEffect;

	// Token: 0x04000837 RID: 2103
	public string enterScrawl;

	// Token: 0x04000838 RID: 2104
	public string exitScrawl;

	// Token: 0x04000839 RID: 2105
	public bool runOnce;

	// Token: 0x0400083A RID: 2106
	private bool hasRun;

	// Token: 0x0400083B RID: 2107
	public string[] triggerTags = new string[] { "Player" };

	// Token: 0x0400083C RID: 2108
	public string triggerMessage;

	// Token: 0x0400083D RID: 2109
	public TransformEvent enterEvent;

	// Token: 0x0400083E RID: 2110
	public TransformEvent exitEvent;

	// Token: 0x0400083F RID: 2111
	public TransformEvent stayEvent;

	// Token: 0x04000840 RID: 2112
	public bool pathfinderZone;
}
