﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000098 RID: 152
public class Item_Data_Text_Random : Item_Data_Text
{
	// Token: 0x06000515 RID: 1301 RVA: 0x0002B288 File Offset: 0x00029488
	public override string GetText()
	{
		if (this.selectedString == null || this.selectedString == "")
		{
			this.selectedString = this.strings[Random.Range(0, this.strings.Count)];
		}
		return this.selectedString;
	}

	// Token: 0x0400069E RID: 1694
	[TextArea(5, 10)]
	public List<string> strings;

	// Token: 0x0400069F RID: 1695
	[HideInInspector]
	public string selectedString;
}
