﻿using System;
using UnityEngine;

// Token: 0x0200004D RID: 77
[Serializable]
public class ENV_Rope_Node
{
	// Token: 0x06000336 RID: 822 RVA: 0x00020588 File Offset: 0x0001E788
	public ENV_Rope_Node(Vector3 initialPosition)
	{
		this.position = initialPosition;
		this.previousPosition = initialPosition;
	}

	// Token: 0x0400046E RID: 1134
	public Vector3 position;

	// Token: 0x0400046F RID: 1135
	public Vector3 previousPosition;

	// Token: 0x04000470 RID: 1136
	public Transform mesh;

	// Token: 0x04000471 RID: 1137
	public bool locked;

	// Token: 0x04000472 RID: 1138
	public bool colliding;

	// Token: 0x04000473 RID: 1139
	public Vector3 velocity;
}
