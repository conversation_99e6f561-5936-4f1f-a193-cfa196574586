﻿using System;
using Drawing;
using UnityEngine;

// Token: 0x0200017E RID: 382
public class UT_LookatPlayer : MonoBehaviourGizmos, Activatable
{
	// Token: 0x06000A89 RID: 2697 RVA: 0x00044EC5 File Offset: 0x000430C5
	private void Start()
	{
		this.forwardStart = base.transform.parent.InverseTransformDirection(base.transform.forward);
	}

	// Token: 0x06000A8A RID: 2698 RVA: 0x00044EE8 File Offset: 0x000430E8
	private void Update()
	{
		if (!this.active)
		{
			return;
		}
		if (ENT_Player.playerObject == null)
		{
			return;
		}
		Quaternion quaternion = Quaternion.LookRotation(ENT_Player.playerObject.transform.position - base.transform.position);
		if (!this.limitAngle)
		{
			base.transform.rotation = Quaternion.Lerp(base.transform.rotation, quaternion, Time.deltaTime * this.lookSpeed);
			return;
		}
		Vector3 vector = base.transform.parent.TransformDirection(this.forwardStart);
		if (Vector3.Angle((ENT_Player.playerObject.transform.position - base.transform.position).normalized, vector) < this.maxAngle)
		{
			base.transform.rotation = Quaternion.Lerp(base.transform.rotation, quaternion, Time.deltaTime * this.lookSpeed);
			return;
		}
		base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(vector), Time.deltaTime * this.lookSpeed);
	}

	// Token: 0x06000A8B RID: 2699 RVA: 0x00045008 File Offset: 0x00043208
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			float num = Mathf.Cos(this.maxAngle * 0.00872665f);
			float num2 = Mathf.Sin(this.maxAngle * 0.00872665f);
			Draw.Line(base.transform.position, base.transform.position + base.transform.forward * this.maxRange, Color.red);
			float num3 = 0.25f;
			Draw.DashedLine(base.transform.position, base.transform.position + (base.transform.forward * num + base.transform.up * num2).normalized * this.maxRange, num3, num3, Color.white);
			Draw.DashedLine(base.transform.position, base.transform.position + (base.transform.forward * num + -base.transform.up * num2).normalized * this.maxRange, num3, num3, Color.white);
			Draw.DashedLine(base.transform.position, base.transform.position + (base.transform.forward * num + -base.transform.right * num2).normalized * this.maxRange, num3, num3, Color.white);
			Draw.DashedLine(base.transform.position, base.transform.position + (base.transform.forward * num + base.transform.right * num2).normalized * this.maxRange, num3, num3, Color.white);
		}
	}

	// Token: 0x06000A8C RID: 2700 RVA: 0x0004523B File Offset: 0x0004343B
	public void Deactivate()
	{
		this.active = false;
	}

	// Token: 0x06000A8D RID: 2701 RVA: 0x00045244 File Offset: 0x00043444
	public void Activate()
	{
		this.active = true;
	}

	// Token: 0x06000A8E RID: 2702 RVA: 0x0004524D File Offset: 0x0004344D
	public void ToggleActivated()
	{
		this.active = !this.active;
	}

	// Token: 0x06000A8F RID: 2703 RVA: 0x0004525E File Offset: 0x0004345E
	public GameObject GetGameObject()
	{
		return base.gameObject;
	}

	// Token: 0x04000B8B RID: 2955
	public bool active = true;

	// Token: 0x04000B8C RID: 2956
	public float lookSpeed = 1f;

	// Token: 0x04000B8D RID: 2957
	public bool limitAngle;

	// Token: 0x04000B8E RID: 2958
	public float maxAngle = 90f;

	// Token: 0x04000B8F RID: 2959
	public float maxRange = 15f;

	// Token: 0x04000B90 RID: 2960
	private Vector3 forwardStart;
}
