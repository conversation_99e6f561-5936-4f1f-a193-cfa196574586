﻿using System;
using System.Collections;
using UnityEngine;

namespace DarkMachine.AI
{
	// Token: 0x020001D7 RID: 471
	public class AIC_Teeth_Burrow : AIStateComponent
	{
		// Token: 0x1700000C RID: 12
		// (get) Token: 0x06000BE6 RID: 3046 RVA: 0x0004BB90 File Offset: 0x00049D90
		public override string name
		{
			get
			{
				return "burrow";
			}
		}

		// Token: 0x06000BE7 RID: 3047 RVA: 0x0004BB97 File Offset: 0x00049D97
		public override void Initialize(AIGameEntity e, AIStateVariable[] variables, string[] args = null)
		{
			base.Initialize(e, variables, args);
			this.teeth = e.GetComponent<DEN_Teeth>();
		}

		// Token: 0x06000BE8 RID: 3048 RVA: 0x0004BBAE File Offset: 0x00049DAE
		public override void Enter(AIGameEntity e, string[] args = null)
		{
			base.Enter(e, args);
			e.StartCoroutine(this.BurrowAnimation(e));
		}

		// Token: 0x06000BE9 RID: 3049 RVA: 0x0004BBC6 File Offset: 0x00049DC6
		private IEnumerator BurrowAnimation(AIGameEntity e)
		{
			this.teeth.locked = true;
			this.teeth.anim.SetTrigger("burrowstart");
			this.teeth.clipHandler.PlaySound("teeth:burrowstart");
			this.teeth.handParticle.Stop();
			this.teeth.screenHandEffect.Stop();
			CL_CameraControl.ShakeAtPosition(this.teeth.transform.position, 0.2f, 15f);
			yield return new WaitForSeconds(5f);
			this.teeth.geoRoot.gameObject.SetActive(false);
			yield return new WaitForSeconds(4f);
			float closestBurrowDistance = float.PositiveInfinity;
			while (closestBurrowDistance > 15f)
			{
				if (AI_NavigationManager.burrowManager != null)
				{
					AI_Burrow closestBurrowToPosition = AI_NavigationManager.burrowManager.GetClosestBurrowToPosition(e.target.transform.position);
					float num = Vector3.Distance(e.target.transform.position, closestBurrowToPosition.transform.position);
					if (num < closestBurrowDistance)
					{
						closestBurrowDistance = num;
						if (num < 15f)
						{
							e.transform.position = closestBurrowToPosition.transform.position + -closestBurrowToPosition.transform.forward * 0.5f;
							this.teeth.geoRoot.transform.position = closestBurrowToPosition.transform.position + -closestBurrowToPosition.transform.forward * 0.5f;
							this.teeth.geoRoot.transform.rotation = closestBurrowToPosition.transform.rotation;
							this.teeth.fxZone.blend = 0f;
							yield return new WaitForSeconds(5f);
							break;
						}
					}
				}
				yield return new WaitForSeconds(1f);
			}
			this.teeth.geoRoot.gameObject.SetActive(true);
			this.teeth.anim.SetTrigger("burrowend");
			this.teeth.clipHandler.PlaySound("teeth:burrowend");
			CL_CameraControl.ShakeAtPosition(this.teeth.transform.position, 0.2f, 15f);
			yield return new WaitForSeconds(4f);
			this.teeth.handParticle.Play();
			this.teeth.screenHandEffect.Play();
			this.teeth.locked = false;
			e.ChangeState("wander");
			yield break;
		}

		// Token: 0x06000BEA RID: 3050 RVA: 0x0004BBDC File Offset: 0x00049DDC
		public override void Execute(AIGameEntity e, string[] args = null)
		{
			base.Execute(e, args);
		}

		// Token: 0x06000BEB RID: 3051 RVA: 0x0004BBE6 File Offset: 0x00049DE6
		public override void Exit(AIGameEntity e, string[] args = null)
		{
			base.Exit(e, args);
		}

		// Token: 0x04000CED RID: 3309
		private DEN_Teeth teeth;
	}
}
