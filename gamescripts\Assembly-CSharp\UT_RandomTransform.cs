﻿using System;
using UnityEngine;

// Token: 0x020001C7 RID: 455
public class UT_RandomTransform : MonoBehaviour
{
	// Token: 0x06000B9F RID: 2975 RVA: 0x0004A42F File Offset: 0x0004862F
	private void Start()
	{
		if (this.randomizeAtStart)
		{
			this.Randomize();
		}
	}

	// Token: 0x06000BA0 RID: 2976 RVA: 0x0004A440 File Offset: 0x00048640
	public void Randomize()
	{
		Vector3 vector = Random.insideUnitSphere * this.randomizeOffset;
		Quaternion quaternion = Quaternion.Euler(Random.insideUnitSphere * this.randomizeRotate);
		base.transform.position += vector;
		base.transform.rotation *= quaternion;
	}

	// Token: 0x04000CB3 RID: 3251
	public bool randomizeAtStart = true;

	// Token: 0x04000CB4 RID: 3252
	public float randomizeOffset = 1f;

	// Token: 0x04000CB5 RID: 3253
	public float randomizeRotate = 360f;
}
