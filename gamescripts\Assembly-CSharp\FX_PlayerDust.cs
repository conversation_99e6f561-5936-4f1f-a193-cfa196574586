﻿using System;
using UnityEngine;

// Token: 0x0200007C RID: 124
public class FX_PlayerDust : MonoBehaviour
{
	// Token: 0x06000422 RID: 1058 RVA: 0x00025A8A File Offset: 0x00023C8A
	private void OnEnable()
	{
		if (FX_PlayerDust.instance == null)
		{
			FX_PlayerDust.instance = this;
			this.particles = base.GetComponent<ParticleSystem>();
		}
	}

	// Token: 0x06000423 RID: 1059 RVA: 0x00025AAB File Offset: 0x00023CAB
	private void OnDisable()
	{
		if (FX_PlayerDust.instance == this)
		{
			FX_PlayerDust.instance = null;
		}
	}

	// Token: 0x06000424 RID: 1060 RVA: 0x00025AC0 File Offset: 0x00023CC0
	public static void SetDustAmount(float rate = 1f)
	{
		if (FX_PlayerDust.instance != null)
		{
			ParticleSystem.EmissionModule emission = FX_PlayerDust.instance.particles.emission;
			rate *= 100f;
			if (SettingsManager.settings.effectQuality == 1)
			{
				rate *= 0.5f;
				rate = Mathf.Clamp(rate, 0f, 100f);
			}
			else if (SettingsManager.settings.effectQuality == 0)
			{
				rate = 0f;
			}
			emission.rateOverTime = rate;
			emission.rateOverDistance = rate * 0.1f;
		}
	}

	// Token: 0x06000425 RID: 1061 RVA: 0x00025B50 File Offset: 0x00023D50
	public static void SetDustColor(Color c)
	{
		if (FX_PlayerDust.instance != null)
		{
			FX_PlayerDust.instance.particles.main.startColor = c;
		}
	}

	// Token: 0x06000426 RID: 1062 RVA: 0x00025B88 File Offset: 0x00023D88
	public static void SetDustWind(Vector3 w, float n)
	{
		if (FX_PlayerDust.instance != null)
		{
			ParticleSystem.VelocityOverLifetimeModule velocityOverLifetime = FX_PlayerDust.instance.particles.velocityOverLifetime;
			velocityOverLifetime.x = w.x;
			velocityOverLifetime.y = w.y;
			velocityOverLifetime.z = w.z;
			FX_PlayerDust.instance.particles.noise.strengthMultiplier = n * 0.1f;
		}
	}

	// Token: 0x04000576 RID: 1398
	private static FX_PlayerDust instance;

	// Token: 0x04000577 RID: 1399
	private ParticleSystem particles;
}
