﻿using System;
using UnityEngine;

// Token: 0x020001C5 RID: 453
public class UT_MovingGeometry : MonoBehaviour
{
	// Token: 0x06000B96 RID: 2966 RVA: 0x0004A19F File Offset: 0x0004839F
	private void Start()
	{
		this.currentParentWorldMatrix = base.transform.localToWorldMatrix;
		this.lastParentWorldMatrix = this.currentParentWorldMatrix;
	}

	// Token: 0x06000B97 RID: 2967 RVA: 0x0004A1BE File Offset: 0x000483BE
	private void Update()
	{
		this.lastParentWorldMatrix = this.currentParentWorldMatrix;
		this.currentParentWorldMatrix = base.transform.localToWorldMatrix;
	}

	// Token: 0x06000B98 RID: 2968 RVA: 0x0004A1E0 File Offset: 0x000483E0
	private Vector3 GetVelocityAtPosition(Vector3 p)
	{
		Vector3 vector = base.transform.InverseTransformPoint(p);
		Vector3 vector2 = this.lastParentWorldMatrix.MultiplyPoint3x4(vector);
		return (this.currentParentWorldMatrix.MultiplyPoint3x4(vector) - vector2) / Time.deltaTime;
	}

	// Token: 0x06000B99 RID: 2969 RVA: 0x0004A224 File Offset: 0x00048424
	private void OnCollisionEnter(Collision collision)
	{
		ContactPoint contact = collision.GetContact(0);
		Vector3 normal = contact.normal;
		Vector3 vector = this.GetVelocityAtPosition(contact.point);
		vector *= Mathf.Clamp01(Vector3.Dot(contact.impulse, contact.normal));
		Rigidbody component = contact.otherCollider.GetComponent<Rigidbody>();
		Debug.Log("Contact: " + contact.impulse.ToString());
		CL_DebugView.draw.Arrow(contact.point, contact.impulse + contact.point, Color.red * 50f);
		if (component != null)
		{
			component.AddForceAtPosition(vector * this.hitForceMult, contact.point);
			return;
		}
		GameEntity component2 = contact.otherCollider.GetComponent<GameEntity>();
		if (component2 != null)
		{
			component2.AddForceAtPosition(vector * this.hitForceMult, contact.point);
		}
	}

	// Token: 0x06000B9A RID: 2970 RVA: 0x0004A334 File Offset: 0x00048534
	public void OnContactCharacterController(ControllerColliderHit hit, GameEntity gE)
	{
		Vector3 normal = hit.normal;
		Vector3 vector = this.GetVelocityAtPosition(gE.transform.position);
		vector *= Mathf.Clamp01(Vector3.Dot(vector, hit.normal) - 0.3f);
		gE.AddForce(vector);
		string text = "Contact: ";
		Vector3 vector2 = vector;
		Debug.Log(text + vector2.ToString());
		CL_DebugView.draw.Arrow(gE.transform.position + Vector3.down, gE.transform.position + vector + Vector3.down, Color.red * 400f);
	}

	// Token: 0x04000CAC RID: 3244
	public float hitForceMult = 1f;

	// Token: 0x04000CAD RID: 3245
	private Matrix4x4 lastParentWorldMatrix;

	// Token: 0x04000CAE RID: 3246
	private Matrix4x4 currentParentWorldMatrix;
}
