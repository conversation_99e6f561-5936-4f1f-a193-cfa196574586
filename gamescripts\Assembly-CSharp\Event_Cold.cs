﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000058 RID: 88
public class Event_Cold : MonoBehaviour
{
	// Token: 0x06000388 RID: 904 RVA: 0x0002237C File Offset: 0x0002057C
	private void Start()
	{
		this.player = CL_GameManager.gMan.localPlayer;
		this.coldDebuff = new BuffContainer();
		this.coldDebuff.id = "freezing";
		this.coldDebuff.buffs = new List<BuffContainer.Buff>();
		this.coldDebuff.buffs.AddRange(this.coldEffects);
		this.coldDebuff.loseOverTime = false;
		if (!this.player.curBuffs.HasBuffContainer("freezing"))
		{
			this.player.curBuffs.AddBuff(this.coldDebuff);
		}
		if (!this.player.curBuffs.HasBuffContainer(this.blizzardDebuff.id))
		{
			this.player.curBuffs.AddBuff(this.blizzardDebuff);
		}
		this.nextBlizzardTime = Random.Range(this.blizzardInbetweenTime.x, this.blizzardInbetweenTime.y) + CL_GameManager.gMan.GetGameTime();
		this.blizzardSnow.Play();
		this.blizzardSnow.emission.rateOverTimeMultiplier = 0f;
		Event_Cold.instance = this;
	}

	// Token: 0x06000389 RID: 905 RVA: 0x0002249C File Offset: 0x0002069C
	private void LateUpdate()
	{
		this.coldAmount = Mathf.Clamp01(this.coldDebuff.GetMultiplier() + Time.deltaTime * this.freezeRate);
		if (this.player.curBuffs.GetBuff("warming") > 0.01f)
		{
			this.coldAmount = Mathf.Clamp01(this.coldDebuff.GetMultiplier() - Time.deltaTime * this.freezeRate * 4f);
		}
		this.coldDebuff.SetMultiplier(this.coldAmount);
		if (this.nextBlizzardTime < CL_GameManager.gMan.GetGameTime() && Event_Cold.active)
		{
			this.StartBlizzard();
		}
		this.BlizzardEffects();
		if (this.hasBlizzard)
		{
			if (this.blizzardTimeRemaining <= 0f && this.hasBlizzard)
			{
				this.hasBlizzard = false;
			}
			this.blizzardTimeRemaining -= Time.deltaTime;
		}
		if (Event_Cold.active)
		{
			if (!this.snowEffect.isPlaying)
			{
				this.snowEffect.Play();
				this.nextBlizzardTime = Random.Range(this.blizzardInbetweenTime.x, this.blizzardInbetweenTime.y) + CL_GameManager.gMan.GetGameTime() + this.blizzardTimeRemaining;
				return;
			}
		}
		else if (this.snowEffect.isPlaying)
		{
			this.snowEffect.Stop();
			this.hasBlizzard = false;
		}
	}

	// Token: 0x0600038A RID: 906 RVA: 0x000225F0 File Offset: 0x000207F0
	public void StartBlizzard()
	{
		this.hasBlizzard = true;
		this.blizzardTimeRemaining = Random.Range(this.blizzardLength.x, this.blizzardLength.y);
		this.nextBlizzardTime = Random.Range(this.blizzardInbetweenTime.x, this.blizzardInbetweenTime.y) + CL_GameManager.gMan.GetGameTime() + this.blizzardTimeRemaining;
	}

	// Token: 0x0600038B RID: 907 RVA: 0x00022658 File Offset: 0x00020858
	public void BlizzardEffects()
	{
		if (Camera.main == null)
		{
			return;
		}
		this.blizzardObject.transform.position = Camera.main.transform.position;
		this.blizzardMaterial.SetFloat("_AlphaMult", this.blizzardValue);
		this.blizzardParticleWindZone.transform.rotation = Quaternion.LookRotation(this.blizzardWindDirection);
		if (this.hasBlizzard)
		{
			this.blizzardValue = Mathf.Lerp(this.blizzardValue, 1f, Time.deltaTime * 0.1f);
			Vector3 vector = new Vector3(Mathf.PerlinNoise(Time.time, 0f), Mathf.PerlinNoise(Time.time, 5f), Mathf.PerlinNoise(Time.time, 10f)) - Vector3.one * 0.5f;
			this.blizzardWindDirection = Vector3.Normalize(this.blizzardWindDirection + vector * this.blizzardWindChangeRate);
			this.player.AddForce(this.blizzardWindDirection * this.blizzardValue * this.blizzardWindSpeed * Time.deltaTime);
			CL_GameManager.gMan.ShakeCam(this.blizzardCameraShake * this.blizzardValue * Time.deltaTime);
		}
		else
		{
			this.blizzardValue = Mathf.Lerp(this.blizzardValue, 0f, Time.deltaTime * 0.2f);
		}
		this.blizzardAudio.volume = Mathf.Lerp(this.blizzardAudio.volume, this.blizzardValue * this.blizzardAudioVolume, Time.deltaTime);
		this.blizzardParticleWindZone.windMain = this.blizzardValue * this.blizzardParticleWindZoneForce;
		this.blizzardDebuff.SetMultiplier(this.blizzardValue);
		this.blizzardSnow.emission.rateOverTimeMultiplier = this.blizzardValue * 1000f;
	}

	// Token: 0x0600038C RID: 908 RVA: 0x0002283E File Offset: 0x00020A3E
	public static void SetEventStatus(bool b)
	{
		Event_Cold.active = b;
	}

	// Token: 0x0600038D RID: 909 RVA: 0x00022846 File Offset: 0x00020A46
	public static void ForceBlizzard()
	{
		if (Event_Cold.instance == null)
		{
			return;
		}
		Event_Cold.instance.StartBlizzard();
	}

	// Token: 0x040004DB RID: 1243
	public static bool active = true;

	// Token: 0x040004DC RID: 1244
	private ENT_Player player;

	// Token: 0x040004DD RID: 1245
	private BuffContainer coldDebuff;

	// Token: 0x040004DE RID: 1246
	public List<BuffContainer.Buff> coldEffects;

	// Token: 0x040004DF RID: 1247
	private float coldAmount;

	// Token: 0x040004E0 RID: 1248
	public float freezeRate = 1f;

	// Token: 0x040004E1 RID: 1249
	public ParticleSystem snowEffect;

	// Token: 0x040004E2 RID: 1250
	[MinMaxSlider(5f, 200f)]
	public Vector2 blizzardInbetweenTime;

	// Token: 0x040004E3 RID: 1251
	[MinMaxSlider(0f, 400f)]
	public Vector2 blizzardLength;

	// Token: 0x040004E4 RID: 1252
	private float nextBlizzardTime = 1f;

	// Token: 0x040004E5 RID: 1253
	private bool hasBlizzard;

	// Token: 0x040004E6 RID: 1254
	private float blizzardTimeRemaining;

	// Token: 0x040004E7 RID: 1255
	private float blizzardValue;

	// Token: 0x040004E8 RID: 1256
	public BuffContainer blizzardDebuff;

	// Token: 0x040004E9 RID: 1257
	public Material blizzardMaterial;

	// Token: 0x040004EA RID: 1258
	public ParticleSystem blizzardSnow;

	// Token: 0x040004EB RID: 1259
	public WindZone blizzardParticleWindZone;

	// Token: 0x040004EC RID: 1260
	public float blizzardParticleWindZoneForce = 1f;

	// Token: 0x040004ED RID: 1261
	public float blizzardCameraShake = 0.01f;

	// Token: 0x040004EE RID: 1262
	private Vector3 blizzardWindDirection = Vector3.forward;

	// Token: 0x040004EF RID: 1263
	public float blizzardWindSpeed = 1f;

	// Token: 0x040004F0 RID: 1264
	public float blizzardWindChangeRate = 1f;

	// Token: 0x040004F1 RID: 1265
	public AudioSource blizzardAudio;

	// Token: 0x040004F2 RID: 1266
	public float blizzardAudioVolume = 1f;

	// Token: 0x040004F3 RID: 1267
	public GameObject blizzardObject;

	// Token: 0x040004F4 RID: 1268
	internal static Event_Cold instance;
}
