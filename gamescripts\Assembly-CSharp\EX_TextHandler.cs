﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x02000070 RID: 112
public class EX_TextHandler : MonoBehaviour
{
	// Token: 0x060003F0 RID: 1008 RVA: 0x00023E5B File Offset: 0x0002205B
	private void Start()
	{
	}

	// Token: 0x060003F1 RID: 1009 RVA: 0x00023E5D File Offset: 0x0002205D
	private void Update()
	{
		Debug.Log(TMP_TextUtilities.FindIntersectingWord(this.text, Input.mousePosition, Camera.main));
	}

	// Token: 0x04000551 RID: 1361
	public TextMeshProUGUI text;
}
