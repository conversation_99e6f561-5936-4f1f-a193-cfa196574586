﻿using System;
using UnityEngine;

// Token: 0x02000005 RID: 5
[AddComponentMenu("Image Effects/CRT/Ultimate CRT")]
public class CRTEffect : BaseCRTEffect
{
	// Token: 0x06000022 RID: 34 RVA: 0x00003E8A File Offset: 0x0000208A
	private void OnPreCull()
	{
		base.InternalPreRender();
	}

	// Token: 0x06000023 RID: 35 RVA: 0x00003E92 File Offset: 0x00002092
	private void OnRenderImage(RenderTexture src, RenderTexture dest)
	{
		base.ProcessEffect(src, dest);
	}
}
