﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000137 RID: 311
public class UI_ProgressionUnlockList : MonoBehaviour
{
	// Token: 0x0600090D RID: 2317 RVA: 0x0003F31E File Offset: 0x0003D51E
	private void Start()
	{
		this.lerpOpen = base.GetComponent<UI_LerpOpen>();
		this.Initialize();
	}

	// Token: 0x0600090E RID: 2318 RVA: 0x0003F334 File Offset: 0x0003D534
	private void Initialize()
	{
		this.popupCards = new List<UI_ProgressionPopup>();
		for (int i = 0; i < this.numOfCardsToGenerate; i++)
		{
			UI_ProgressionPopup ui_ProgressionPopup = Object.Instantiate<UI_ProgressionPopup>(this.unlockPopupAsset, this.unlockListRoot);
			this.popupCards.Add(ui_ProgressionPopup);
		}
	}

	// Token: 0x0600090F RID: 2319 RVA: 0x0003F37C File Offset: 0x0003D57C
	public void ShowMenu()
	{
		this.lerpOpen.Show();
		for (int i = 0; i < this.popupCards.Count; i++)
		{
			if (CL_ProgressionManager.sessionPopups.Count > i)
			{
				this.popupCards[i].gameObject.SetActive(true);
				this.popupCards[i].UpdateInformation(CL_ProgressionManager.sessionPopups[i].icon, CL_ProgressionManager.sessionPopups[i].title, CL_ProgressionManager.sessionPopups[i].desc);
			}
			else
			{
				this.popupCards[i].gameObject.SetActive(false);
			}
		}
		this.onShow.Invoke();
	}

	// Token: 0x06000910 RID: 2320 RVA: 0x0003F439 File Offset: 0x0003D639
	public void HideMenu()
	{
		this.lerpOpen.Hide();
		this.onHide.Invoke();
	}

	// Token: 0x06000911 RID: 2321 RVA: 0x0003F451 File Offset: 0x0003D651
	internal void Check()
	{
		if (CL_ProgressionManager.sessionPopups.Count > 0)
		{
			this.ShowMenu();
		}
	}

	// Token: 0x04000A5D RID: 2653
	public UI_ProgressionPopup unlockPopupAsset;

	// Token: 0x04000A5E RID: 2654
	public Transform unlockListRoot;

	// Token: 0x04000A5F RID: 2655
	public int numOfCardsToGenerate = 8;

	// Token: 0x04000A60 RID: 2656
	private UI_LerpOpen lerpOpen;

	// Token: 0x04000A61 RID: 2657
	private List<UI_ProgressionPopup> popupCards;

	// Token: 0x04000A62 RID: 2658
	public UnityEvent onShow;

	// Token: 0x04000A63 RID: 2659
	public UnityEvent onHide;
}
