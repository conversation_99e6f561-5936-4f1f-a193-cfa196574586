﻿using System;
using System.ArrayExtensions;
using System.Collections.Generic;
using System.Reflection;

namespace System
{
	// Token: 0x020001CC RID: 460
	public static class ObjectExtensions
	{
		// Token: 0x06000BB3 RID: 2995 RVA: 0x0004A8F6 File Offset: 0x00048AF6
		public static bool IsPrimitive(this Type type)
		{
			return type == typeof(string) || (type.IsValueType & type.IsPrimitive);
		}

		// Token: 0x06000BB4 RID: 2996 RVA: 0x0004A919 File Offset: 0x00048B19
		public static object Copy(this object originalObject)
		{
			return ObjectExtensions.InternalCopy(originalObject, new Dictionary<object, object>(new ReferenceEqualityComparer()));
		}

		// Token: 0x06000BB5 RID: 2997 RVA: 0x0004A92C File Offset: 0x00048B2C
		private static object InternalCopy(object originalObject, IDictionary<object, object> visited)
		{
			if (originalObject == null)
			{
				return null;
			}
			Type type = originalObject.GetType();
			if (type.IsPrimitive())
			{
				return originalObject;
			}
			if (visited.ContainsKey(originalObject))
			{
				return visited[originalObject];
			}
			if (typeof(Delegate).IsAssignableFrom(type))
			{
				return null;
			}
			object obj = ObjectExtensions.CloneMethod.Invoke(originalObject, null);
			if (type.IsArray && !type.GetElementType().IsPrimitive())
			{
				Array clonedArray = (Array)obj;
				clonedArray.ForEach(delegate(Array array, int[] indices)
				{
					array.SetValue(ObjectExtensions.InternalCopy(clonedArray.GetValue(indices), visited), indices);
				});
			}
			visited.Add(originalObject, obj);
			ObjectExtensions.CopyFields(originalObject, visited, obj, type, BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.FlattenHierarchy, null);
			ObjectExtensions.RecursiveCopyBaseTypePrivateFields(originalObject, visited, obj, type);
			return obj;
		}

		// Token: 0x06000BB6 RID: 2998 RVA: 0x0004AA00 File Offset: 0x00048C00
		private static void RecursiveCopyBaseTypePrivateFields(object originalObject, IDictionary<object, object> visited, object cloneObject, Type typeToReflect)
		{
			if (typeToReflect.BaseType != null)
			{
				ObjectExtensions.RecursiveCopyBaseTypePrivateFields(originalObject, visited, cloneObject, typeToReflect.BaseType);
				ObjectExtensions.CopyFields(originalObject, visited, cloneObject, typeToReflect.BaseType, BindingFlags.Instance | BindingFlags.NonPublic, (FieldInfo info) => info.IsPrivate);
			}
		}

		// Token: 0x06000BB7 RID: 2999 RVA: 0x0004AA58 File Offset: 0x00048C58
		private static void CopyFields(object originalObject, IDictionary<object, object> visited, object cloneObject, Type typeToReflect, BindingFlags bindingFlags = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.FlattenHierarchy, Func<FieldInfo, bool> filter = null)
		{
			foreach (FieldInfo fieldInfo in typeToReflect.GetFields(bindingFlags))
			{
				if ((filter == null || filter(fieldInfo)) && !fieldInfo.FieldType.IsPrimitive())
				{
					object obj = ObjectExtensions.InternalCopy(fieldInfo.GetValue(originalObject), visited);
					fieldInfo.SetValue(cloneObject, obj);
				}
			}
		}

		// Token: 0x06000BB8 RID: 3000 RVA: 0x0004AAB1 File Offset: 0x00048CB1
		public static T Copy<T>(this T original)
		{
			return (T)((object)original.Copy());
		}

		// Token: 0x04000CC9 RID: 3273
		private static readonly MethodInfo CloneMethod = typeof(object).GetMethod("MemberwiseClone", BindingFlags.Instance | BindingFlags.NonPublic);
	}
}
