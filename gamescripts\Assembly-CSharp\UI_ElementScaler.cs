﻿using System;
using UnityEngine;

// Token: 0x02000125 RID: 293
[ExecuteAlways]
public class UI_ElementScaler : MonoBehaviour
{
	// Token: 0x060008AF RID: 2223 RVA: 0x0003DB96 File Offset: 0x0003BD96
	private void Start()
	{
		this.rectTransform = base.GetComponent<RectTransform>();
		this.UpdateScale();
	}

	// Token: 0x060008B0 RID: 2224 RVA: 0x0003DBAC File Offset: 0x0003BDAC
	private void Update()
	{
		Vector2 vector = new Vector2((float)Screen.width, (float)Screen.height);
		if (vector != this.lastWindowSize)
		{
			this.UpdateScale();
			this.lastWindowSize = vector;
		}
	}

	// Token: 0x060008B1 RID: 2225 RVA: 0x0003DBE8 File Offset: 0x0003BDE8
	private void UpdateScale()
	{
		float num = (Mathf.Clamp((float)Screen.width, this.minResolution, this.maxResolution) - this.minResolution) / (this.maxResolution - this.minResolution);
		float num2 = Mathf.Lerp(this.minScale, this.maxScale, num);
		this.rectTransform.localScale = new Vector3(num2, num2, 1f);
	}

	// Token: 0x040009FA RID: 2554
	[Header("Scale Settings")]
	[Tooltip("Minimum scale factor for the UI element.")]
	public float minScale = 0.8f;

	// Token: 0x040009FB RID: 2555
	[Tooltip("Maximum scale factor for the UI element.")]
	public float maxScale = 1.2f;

	// Token: 0x040009FC RID: 2556
	[Header("Resolution Settings")]
	[Tooltip("Minimum window width resolution.")]
	public float minResolution = 800f;

	// Token: 0x040009FD RID: 2557
	[Tooltip("Maximum window width resolution.")]
	public float maxResolution = 1920f;

	// Token: 0x040009FE RID: 2558
	private RectTransform rectTransform;

	// Token: 0x040009FF RID: 2559
	private Vector2 lastWindowSize;
}
