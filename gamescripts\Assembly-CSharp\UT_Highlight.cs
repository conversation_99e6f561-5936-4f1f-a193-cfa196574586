﻿using System;
using UnityEngine;

// Token: 0x02000176 RID: 374
public class UT_Highlight : MonoBehaviour
{
	// Token: 0x06000A6A RID: 2666 RVA: 0x000449E4 File Offset: 0x00042BE4
	private void Update()
	{
		if (this.meshRenderer != null)
		{
			this.meshRenderer.material.SetFloat("_Shimmer", this.highlightAmount);
		}
		this.highlightAmount = Mathf.Lerp(this.highlightAmount, 0f, Time.deltaTime * 3f);
	}

	// Token: 0x06000A6B RID: 2667 RVA: 0x00044A3B File Offset: 0x00042C3B
	public void Highlight()
	{
		if (!this.active)
		{
			return;
		}
		this.highlightAmount = 10f;
	}

	// Token: 0x04000B6B RID: 2923
	public MeshRenderer meshRenderer;

	// Token: 0x04000B6C RID: 2924
	public bool active = true;

	// Token: 0x04000B6D RID: 2925
	private float highlightAmount;
}
