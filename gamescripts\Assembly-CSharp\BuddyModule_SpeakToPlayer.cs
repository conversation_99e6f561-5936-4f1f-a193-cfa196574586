﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000024 RID: 36
public class BuddyModule_SpeakToPlayer : BuddyModule
{
	// Token: 0x06000166 RID: 358 RVA: 0x0000AE94 File Offset: 0x00009094
	public override void Initialize(DEN_Buddy b)
	{
		base.Initialize(b);
		b.StartCoroutine(this.MoveToPlayer());
	}

	// Token: 0x06000167 RID: 359 RVA: 0x0000AEAA File Offset: 0x000090AA
	private IEnumerator MoveToPlayer()
	{
		bool isHanging = this.buddy.IsHanging();
		this.buddy.SetHangoutState(false);
		this.buddy.SetTarget(ENT_Player.playerObject.transform);
		this.buddy.SetLookForTarget(true);
		yield return new WaitForSeconds(5f);
		AudioClip audioClip = this.speakClips[Random.Range(0, this.speakClips.Count)];
		this.buddy.PlaySound(audioClip);
		yield return new WaitForSeconds(audioClip.length);
		this.buddy.SetHangoutState(isHanging);
		this.buddy.ClearEvent();
		yield break;
	}

	// Token: 0x06000168 RID: 360 RVA: 0x0000AEB9 File Offset: 0x000090B9
	public override void Update()
	{
		base.Update();
	}

	// Token: 0x04000131 RID: 305
	public List<AudioClip> speakClips;
}
