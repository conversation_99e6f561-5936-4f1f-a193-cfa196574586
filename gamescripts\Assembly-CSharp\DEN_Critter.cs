﻿using System;
using UnityEngine;

// Token: 0x02000031 RID: 49
public class DEN_Critter : Denizen
{
	// Token: 0x060001C4 RID: 452 RVA: 0x0000E0F0 File Offset: 0x0000C2F0
	public override void Start()
	{
		this.velocity = Vector3.zero;
		this.targetVelocity = Vector3.zero;
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		if (base.GetComponent<AudioSource>() != null)
		{
			base.GetComponent<AudioSource>().Play();
			base.GetComponent<AudioSource>().time = Random.value * base.GetComponent<AudioSource>().clip.length;
		}
		this.hissTimer = Random.value * 5f;
		base.Start();
	}

	// Token: 0x060001C5 RID: 453 RVA: 0x0000E170 File Offset: 0x0000C370
	public override void FixedUpdate()
	{
		base.FixedUpdate();
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		this.AI();
	}

	// Token: 0x060001C6 RID: 454 RVA: 0x0000E18C File Offset: 0x0000C38C
	private void AI()
	{
		if (Physics.Raycast(base.transform.position + base.transform.forward * this.floorOffset, base.transform.up, out this.hit, this.velocity.magnitude * Time.fixedDeltaTime, this.worldMask))
		{
			if (!this.canClimb)
			{
				this.velocity = Vector3.Reflect(this.velocity, this.hit.normal);
				this.targetVelocity = Vector3.Reflect(this.targetVelocity, this.hit.normal);
				Debug.Log("reflecting");
			}
			else
			{
				base.transform.position = this.hit.point + this.hit.normal * this.floorOffset;
				base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(this.hit.normal, base.transform.up), Time.deltaTime * 40f);
				this.velocity = base.transform.up;
			}
			this.onFloor = true;
		}
		else if (Physics.Raycast(base.transform.position + base.transform.forward * this.floorOffset, -base.transform.forward, out this.hit, 0.15f, this.worldMask))
		{
			base.transform.position = this.hit.point + this.hit.normal * this.floorOffset;
			base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(this.hit.normal, base.transform.up + this.velocity.normalized), Time.deltaTime * 40f);
			this.onFloor = true;
		}
		else if (Physics.Raycast(base.transform.position + base.transform.up * this.floorOffset, -base.transform.forward + -base.transform.up, out this.hit, 0.02f, this.worldMask))
		{
			if (this.canClimb)
			{
				base.transform.position = this.hit.point + this.hit.normal * this.floorOffset;
				base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(this.hit.normal, base.transform.up), Time.deltaTime * 40f);
				this.velocity = base.transform.up;
				this.onFloor = true;
			}
			else
			{
				this.velocity = -this.velocity;
				this.targetVelocity = this.velocity;
			}
		}
		else if (this.onFloor)
		{
			this.targetVelocity = -this.targetVelocity;
			this.velocity = -this.velocity;
			this.onFloor = false;
			this.wanderTime = 0.5f;
		}
		else
		{
			base.transform.position += Vector3.down * Time.deltaTime * 2f;
		}
		base.transform.position += this.velocity * Time.deltaTime;
		this.velocity = Vector3.Lerp(this.velocity, this.targetVelocity, Time.deltaTime * 5f);
		if (this.wanderTime <= 0f && !this.sightedPlayer)
		{
			this.Wander();
			this.wanderTime = Random.Range(0f, 3f);
		}
		else
		{
			this.wanderTime -= Time.deltaTime;
		}
		this.hissTimer -= Time.deltaTime;
		if (this.hissTimer <= 0f)
		{
			this.hissTimer = Random.value * 20f;
			this.clipHandler.PlaySound("critter:hiss");
		}
		Collider[] array = Physics.OverlapSphere(base.transform.position, this.fearDistance, this.playerMask);
		if (array.Length != 0 && this.fearPlayer)
		{
			if (!this.sightedPlayer)
			{
				this.clipHandler.PlaySound("critter:fear");
				this.sightedPlayer = true;
			}
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.SphereOutline(base.transform.position, 0.45f, Color.yellow);
			}
			this.targetVelocity = this.targetVelocity.normalized * this.runSpeed + (base.transform.position - array[0].transform.position).normalized * Time.fixedDeltaTime;
			if (Vector3.Distance(array[0].ClosestPoint(base.transform.position), base.transform.position) < 0.45f)
			{
				if (CL_UIManager.debug)
				{
					CL_DebugView.draw.SphereOutline(base.transform.position, 0.45f, Color.red);
				}
				Object.Instantiate<GameObject>(this.deathEffect, base.transform.position, Quaternion.identity);
				Object.Destroy(base.gameObject);
			}
			this.wanderTime = 0.5f;
			return;
		}
		this.sightedPlayer = false;
	}

	// Token: 0x060001C7 RID: 455 RVA: 0x0000E774 File Offset: 0x0000C974
	private void Wander()
	{
		if (Random.value > 0.7f)
		{
			this.state = DEN_Critter.CritterState.idle;
			this.targetVelocity = Vector3.zero;
			return;
		}
		this.state = DEN_Critter.CritterState.wandering;
		Vector2 insideUnitCircle = Random.insideUnitCircle;
		this.targetVelocity = (base.transform.up * insideUnitCircle.x + base.transform.right * insideUnitCircle.y + this.GetFlockVector()) * this.speed;
	}

	// Token: 0x060001C8 RID: 456 RVA: 0x0000E7FA File Offset: 0x0000C9FA
	private Vector3 GetFlockVector()
	{
		return Vector3.zero;
	}

	// Token: 0x040001DC RID: 476
	public LayerMask worldMask;

	// Token: 0x040001DD RID: 477
	public LayerMask playerMask;

	// Token: 0x040001DE RID: 478
	public float floorOffset = 0.01f;

	// Token: 0x040001DF RID: 479
	private RaycastHit hit;

	// Token: 0x040001E0 RID: 480
	private Vector3 velocity;

	// Token: 0x040001E1 RID: 481
	private Vector3 targetVelocity;

	// Token: 0x040001E2 RID: 482
	private float wanderTime;

	// Token: 0x040001E3 RID: 483
	public float speed = 0.5f;

	// Token: 0x040001E4 RID: 484
	private bool onFloor;

	// Token: 0x040001E5 RID: 485
	public GameObject deathEffect;

	// Token: 0x040001E6 RID: 486
	private bool sightedPlayer;

	// Token: 0x040001E7 RID: 487
	private float hissTimer = 1f;

	// Token: 0x040001E8 RID: 488
	public DEN_Critter.CritterState state;

	// Token: 0x040001E9 RID: 489
	public bool canClimb = true;

	// Token: 0x040001EA RID: 490
	public bool flock = true;

	// Token: 0x040001EB RID: 491
	public bool fearPlayer = true;

	// Token: 0x040001EC RID: 492
	public float fearDistance = 4f;

	// Token: 0x040001ED RID: 493
	public float runSpeed = 2f;

	// Token: 0x02000215 RID: 533
	public enum CritterState
	{
		// Token: 0x04000E17 RID: 3607
		idle,
		// Token: 0x04000E18 RID: 3608
		hungry,
		// Token: 0x04000E19 RID: 3609
		wandering
	}
}
