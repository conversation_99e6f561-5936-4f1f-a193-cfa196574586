﻿using System;
using System.Runtime.CompilerServices;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000122 RID: 290
public class UI_CopySelectedColor : MonoBehaviour
{
	// Token: 0x060008A1 RID: 2209 RVA: 0x0003D8A0 File Offset: 0x0003BAA0
	private void Start()
	{
		this.thisGraphic = base.GetComponent<MaskableGraphic>();
		this.selectableEvents = this.selectable.GetComponent<UI_SelectEventTrigger>();
		if (this.selectableEvents == null)
		{
			this.selectableEvents = this.selectable.gameObject.AddComponent<UI_SelectEventTrigger>();
		}
		this.<Start>g__GenerateEvent|8_0(new UnityAction<BaseEventData>(this.OnSelect), EventTriggerType.Select);
		this.<Start>g__GenerateEvent|8_0(new UnityAction<BaseEventData>(this.OnSubmit), EventTriggerType.Submit);
		this.<Start>g__GenerateEvent|8_0(new UnityAction<BaseEventData>(this.OnDeselect), EventTriggerType.Deselect);
		this.<Start>g__GenerateEvent|8_0(new UnityAction<BaseEventData>(this.OnSelect), EventTriggerType.PointerEnter);
		this.<Start>g__GenerateEvent|8_0(new UnityAction<BaseEventData>(this.OnDeselect), EventTriggerType.PointerExit);
	}

	// Token: 0x060008A2 RID: 2210 RVA: 0x0003D950 File Offset: 0x0003BB50
	private void Update()
	{
		if (this.useDisableColor)
		{
			if (!this.isDisabled && !this.selectable.interactable)
			{
				this.thisGraphic.color = this.disabledColor;
				this.isDisabled = true;
				return;
			}
			if (this.isDisabled && this.selectable.interactable)
			{
				this.isDisabled = false;
				this.thisGraphic.color = this.normalColor;
			}
		}
	}

	// Token: 0x060008A3 RID: 2211 RVA: 0x0003D9C0 File Offset: 0x0003BBC0
	private void OnSelect(BaseEventData arg0)
	{
		if (!this.selectable.interactable)
		{
			return;
		}
		Color color = this.selectedColor;
		color.a = this.thisGraphic.color.a;
		this.thisGraphic.color = color;
	}

	// Token: 0x060008A4 RID: 2212 RVA: 0x0003DA08 File Offset: 0x0003BC08
	private void OnDeselect(BaseEventData arg0)
	{
		if (!this.selectable.interactable)
		{
			return;
		}
		Color color = this.normalColor;
		color.a = this.thisGraphic.color.a;
		this.thisGraphic.color = color;
	}

	// Token: 0x060008A5 RID: 2213 RVA: 0x0003DA50 File Offset: 0x0003BC50
	private void OnSubmit(BaseEventData arg0)
	{
		if (!this.selectable.interactable)
		{
			return;
		}
		Color color = this.selectedColor;
		color.a = this.thisGraphic.color.a;
		this.thisGraphic.color = color;
	}

	// Token: 0x060008A7 RID: 2215 RVA: 0x0003DA9D File Offset: 0x0003BC9D
	[CompilerGenerated]
	private void <Start>g__GenerateEvent|8_0(UnityAction<BaseEventData> actionEvent, EventTriggerType triggerType)
	{
		this.selectableEvents.AddEvent(actionEvent, triggerType);
	}

	// Token: 0x040009ED RID: 2541
	public Selectable selectable;

	// Token: 0x040009EE RID: 2542
	private MaskableGraphic thisGraphic;

	// Token: 0x040009EF RID: 2543
	public Color selectedColor;

	// Token: 0x040009F0 RID: 2544
	public Color normalColor;

	// Token: 0x040009F1 RID: 2545
	public bool useDisableColor;

	// Token: 0x040009F2 RID: 2546
	public Color disabledColor;

	// Token: 0x040009F3 RID: 2547
	private bool isDisabled;

	// Token: 0x040009F4 RID: 2548
	private UI_SelectEventTrigger selectableEvents;
}
