﻿using System;
using UnityEngine;

// Token: 0x020001AE RID: 430
public class UT_ObjectiveController : MonoBehaviour
{
	// Token: 0x06000B4E RID: 2894 RVA: 0x0004875A File Offset: 0x0004695A
	private void Start()
	{
		if (this.createObjectiveOnStart)
		{
			this.CreateObjective();
		}
	}

	// Token: 0x06000B4F RID: 2895 RVA: 0x0004876A File Offset: 0x0004696A
	public void CreateObjective()
	{
		UI_ObjectiveViewer.CreateOrUpdateObjective(this.objectiveID, this.objectiveTitle, this.objectiveDesc, true);
	}

	// Token: 0x06000B50 RID: 2896 RVA: 0x00048784 File Offset: 0x00046984
	public void UpdateObjectiveDescription(string s)
	{
		this.objectiveDesc = s;
		UI_ObjectiveViewer.CreateOrUpdateObjective(this.objectiveID, this.objectiveTitle, s, true);
	}

	// Token: 0x06000B51 RID: 2897 RVA: 0x000487A0 File Offset: 0x000469A0
	public void UpdateObjectiveTitle(string s)
	{
		this.objectiveTitle = s;
		UI_ObjectiveViewer.CreateOrUpdateObjective(this.objectiveID, s, this.objectiveDesc, true);
	}

	// Token: 0x06000B52 RID: 2898 RVA: 0x000487BC File Offset: 0x000469BC
	public void PlayEarnSound()
	{
		UI_ObjectiveViewer.PlayEarnSound();
	}

	// Token: 0x04000C40 RID: 3136
	public bool createObjectiveOnStart;

	// Token: 0x04000C41 RID: 3137
	public string objectiveID;

	// Token: 0x04000C42 RID: 3138
	public string objectiveTitle;

	// Token: 0x04000C43 RID: 3139
	public string objectiveDesc;
}
