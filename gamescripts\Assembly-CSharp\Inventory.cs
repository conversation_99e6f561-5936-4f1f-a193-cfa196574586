﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000044 RID: 68
public class Inventory : MonoBehaviour
{
	// Token: 0x060002F6 RID: 758 RVA: 0x0001E134 File Offset: 0x0001C334
	public void Initialize(ENT_Player p)
	{
		this.aud = base.GetComponent<UT_AudioClipHandler>();
		Inventory.instance = this;
		for (int i = 0; i < this.itemRoot.childCount; i++)
		{
			Item_Object component = this.itemRoot.GetChild(i).GetComponent<Item_Object>();
			this.bagItems.Add(component.itemData);
		}
		this.player = p;
		this.showingInventory = false;
		for (int j = 0; j < this.itemHands.Length; j++)
		{
			this.player.hands[j].inventoryHand = this.itemHands[j];
			this.itemHands[j].hand = this.player.hands[j];
		}
	}

	// Token: 0x060002F7 RID: 759 RVA: 0x0001E1E1 File Offset: 0x0001C3E1
	private void Start()
	{
	}

	// Token: 0x060002F8 RID: 760 RVA: 0x0001E1E4 File Offset: 0x0001C3E4
	private void Update()
	{
		if (CL_GameManager.gMan.lockPlayer || CL_GameManager.gMan.isPaused)
		{
			return;
		}
		this.bagCurrentColor = Color.Lerp(this.bagCurrentColor, Color.Lerp(this.bagEncumberedColor, this.bagUnencumberedColor, this.encumberance), Time.deltaTime * 10f);
		this.bagRenderer.material.SetColor("_Color", this.bagCurrentColor);
		if (this.player.IsLocked() || CL_GameManager.gMan.lockPlayerInput)
		{
			return;
		}
		if (!this.player.IsInventoryLocked())
		{
			if (SettingsManager.settings.toggleInventory)
			{
				if (InputManager.GetButton("Inventory").Down)
				{
					if (this.showingInventory)
					{
						this.HideInventory();
					}
					else
					{
						this.ShowInventory();
					}
				}
			}
			else
			{
				if (InputManager.GetButton("Inventory").Down)
				{
					this.ShowInventory();
				}
				if (InputManager.GetButton("Inventory").Up)
				{
					this.HideInventory();
				}
			}
		}
		if (InputManager.GetButton("Hand-Swap").Down)
		{
			this.SwapHands();
		}
		foreach (Inventory.Pocket pocket in this.pockets)
		{
			if (InputManager.GetButton(pocket.key).Down && !this.player.IsInventoryLocked())
			{
				if (this.itemHands[pocket.targetHandID].currentItem == null && pocket.item != null && this.player.hands[pocket.targetHandID].interactState == ENT_Player.InteractType.none)
				{
					pocket.item.gameObject.SetActive(false);
					this.AddItemToHand(pocket.item.itemData, this.player.hands[pocket.targetHandID]);
					pocket.item = null;
				}
				else if (this.itemHands[pocket.targetHandID].currentItem != null && pocket.item == null)
				{
					if (this.itemHands[pocket.targetHandID].currentItem.pocketable)
					{
						this.DropItemFromHand(pocket.transform.position, pocket.targetHandID, true);
					}
					else
					{
						this.aud.PlaySound("inventory:inventory-toobig");
					}
				}
			}
		}
		this.PushItemsApart();
		if (this.showingInventory)
		{
			if (this.player.GetSoftParent().IsParented())
			{
				Vector3 vector = this.player.GetSoftParent().parent.TransformDirection(this.inventoryLookVector);
				this.inventory.rotation = Quaternion.LookRotation(vector);
			}
			else
			{
				this.inventory.rotation = Quaternion.LookRotation(this.inventoryLookVector);
			}
			if (Vector3.Angle(Camera.main.transform.forward, this.inventory.forward) > 70f)
			{
				this.HideInventory();
			}
		}
		else if (this.inventoryLookVector != Vector3.zero)
		{
			this.inventory.rotation = Quaternion.LookRotation(base.transform.TransformDirection(this.inventoryLookVector), base.transform.up);
		}
		for (int i = 0; i < this.itemHands.Length; i++)
		{
			Inventory.ItemHand itemHand = this.itemHands[i];
			ENT_Player.Hand hand = this.player.hands[i];
			if (itemHand.currentItem != null)
			{
				itemHand.currentItem.Execute(this);
			}
			if (itemHand.currentItem != null && hand.cooldown <= 0f && !this.showingInventory)
			{
				if (InputManager.GetButton(hand.fireButton).Down)
				{
					itemHand.currentItem.Use();
				}
				if (InputManager.GetButton(hand.fireButton).Up)
				{
					itemHand.currentItem.StopUse();
				}
			}
		}
		foreach (Item item in this.bagItems)
		{
			item.Execute(this);
		}
	}

	// Token: 0x060002F9 RID: 761 RVA: 0x0001E614 File Offset: 0x0001C814
	private void PushItemsApart()
	{
		Item_Object[] componentsInChildren = this.itemRoot.GetComponentsInChildren<Item_Object>();
		for (int i = 0; i < componentsInChildren.Length; i++)
		{
			Transform transform = componentsInChildren[i].transform;
			if (Vector3.Distance(transform.localPosition, this.inventoryCenterLocator.localPosition) > 1.3f)
			{
				Vector3 vector = (this.inventoryCenterLocator.localPosition - transform.localPosition).normalized * this.moveSpeed * Time.deltaTime;
				vector.z = 0f;
				transform.localPosition += vector * 0.5f;
			}
			for (int j = i + 1; j < componentsInChildren.Length; j++)
			{
				Transform transform2 = componentsInChildren[j].transform;
				if (componentsInChildren[i] != base.transform && componentsInChildren[j] != base.transform)
				{
					Vector3 vector2 = transform.localPosition - transform2.localPosition;
					float magnitude = vector2.magnitude;
					if (magnitude < this.minDistance)
					{
						vector2.Normalize();
						Vector3 vector3 = vector2 * (this.minDistance - magnitude) * this.moveSpeed * Time.deltaTime;
						transform2.localPosition -= vector3 / 2f;
						transform.localPosition += vector3 / 2f;
					}
				}
			}
			foreach (Inventory.Pocket pocket in this.pockets)
			{
				float num = Vector3.Distance(pocket.transform.localPosition, transform.localPosition);
				if (num < this.minDistance * 1f)
				{
					if (pocket.item == null)
					{
						if (componentsInChildren[i].itemData.pocketable && componentsInChildren[i].gameObject.activeSelf)
						{
							pocket.item = componentsInChildren[i];
						}
						else
						{
							bool pocketable = componentsInChildren[i].itemData.pocketable;
						}
					}
					Vector3 vector4 = transform.localPosition - pocket.transform.localPosition;
					vector4.Normalize();
					Vector3 vector5 = vector4 * this.moveSpeed * Time.deltaTime * 0.25f;
					vector5.z = 0f;
					if (pocket.item == componentsInChildren[i])
					{
						float num2 = Mathf.Min(Vector3.Distance(transform.localPosition, pocket.transform.localPosition) * 3f, 1f);
						transform.localPosition -= vector5 * num2;
						transform.localScale = Vector3.Lerp(transform.localScale, Vector3.one * componentsInChildren[i].itemData.inventoryScale * 0.8f, Time.deltaTime * 8f);
						transform.localPosition = Vector3.Normalize(transform.localPosition) * 1.6f;
					}
					else
					{
						transform.localScale = Vector3.Lerp(transform.localScale, Vector3.one * componentsInChildren[i].itemData.inventoryScale * 1.3f, Time.deltaTime * 6f);
						transform.localPosition += vector5;
						if ((double)num < (double)this.minDistance * 0.75)
						{
							this.bagCurrentColor = Color.red;
						}
					}
				}
			}
			transform.localScale = Vector3.Lerp(transform.localScale, Vector3.one * componentsInChildren[i].itemData.inventoryScale * 1.3f, Time.deltaTime * 6f);
			transform.localPosition = Vector3.Normalize(transform.localPosition) * 1.6f;
		}
	}

	// Token: 0x060002FA RID: 762 RVA: 0x0001EA30 File Offset: 0x0001CC30
	private void ShowInventory()
	{
		if (!this.showingInventory)
		{
			this.showingInventory = true;
			this.inventory.gameObject.SetActive(true);
			this.inventory.localRotation = Quaternion.identity;
			Vector3 vector = Vector3.Lerp(base.transform.forward, Camera.main.transform.forward, 0.5f);
			if (this.player.GetSoftParent().IsParented())
			{
				this.inventoryLookVector = this.player.GetSoftParent().parent.InverseTransformDirection(vector);
			}
			else
			{
				this.inventoryLookVector = vector;
			}
			this.inventoryRotation = Quaternion.Lerp(this.inventory.rotation, this.player.transform.rotation, 0.5f);
		}
	}

	// Token: 0x060002FB RID: 763 RVA: 0x0001EAF7 File Offset: 0x0001CCF7
	private void HideInventory()
	{
		if (this.showingInventory)
		{
			this.showingInventory = false;
			this.inventory.gameObject.SetActive(false);
		}
	}

	// Token: 0x060002FC RID: 764 RVA: 0x0001EB1C File Offset: 0x0001CD1C
	private bool CheckOtherHandsForItem(int currentHand, int itemNumber)
	{
		if (this.itemHands[currentHand].currentItemNumber == -1)
		{
			return false;
		}
		for (int i = 0; i < this.itemHands.Length; i++)
		{
			if (i != currentHand && this.itemHands[i].currentItemNumber == itemNumber)
			{
				return true;
			}
		}
		return false;
	}

	// Token: 0x060002FD RID: 765 RVA: 0x0001EB68 File Offset: 0x0001CD68
	public Item GetItem(string itemName)
	{
		foreach (Item item in this.bagItems)
		{
			if (item.itemName.ToLower() == itemName.ToLower())
			{
				return item;
			}
		}
		foreach (Inventory.ItemHand itemHand in this.itemHands)
		{
			if (itemHand.currentItem != null && itemHand.currentItem.itemName.ToLower() == itemName.ToLower())
			{
				return itemHand.currentItem;
			}
		}
		return null;
	}

	// Token: 0x060002FE RID: 766 RVA: 0x0001EC24 File Offset: 0x0001CE24
	public Item GetItemByTag(string itemTag)
	{
		foreach (Inventory.ItemHand itemHand in this.itemHands)
		{
			if (itemHand.currentItem != null && itemHand.currentItem.itemTag.ToLower() == itemTag.ToLower())
			{
				return itemHand.currentItem;
			}
		}
		foreach (Item item in this.bagItems)
		{
			if (item != null)
			{
				if (item.itemTag == null)
				{
					item.itemTag = "";
				}
				else if (item.itemTag.ToLower() == itemTag.ToLower())
				{
					return item;
				}
			}
		}
		return null;
	}

	// Token: 0x060002FF RID: 767 RVA: 0x0001ECF4 File Offset: 0x0001CEF4
	public bool HasItemWithTag(string itemTag, bool inHand = false)
	{
		if (inHand)
		{
			Item itemByTag = this.GetItemByTag(itemTag);
			return itemByTag != null && itemByTag.GetHandItem() != null;
		}
		return this.GetItemByTag(itemTag) != null;
	}

	// Token: 0x06000300 RID: 768 RVA: 0x0001ED2C File Offset: 0x0001CF2C
	public void DestroyItemInHand(int h)
	{
		Inventory.ItemHand itemHand = this.itemHands[h];
		if (itemHand.currentItem == null)
		{
			return;
		}
		itemHand.currentItem.DestroyItem();
	}

	// Token: 0x06000301 RID: 769 RVA: 0x0001ED58 File Offset: 0x0001CF58
	public void ClearItemFromHand(Item i)
	{
		foreach (Inventory.ItemHand itemHand in this.itemHands)
		{
			if (itemHand.currentItem != null && itemHand.currentItem == i)
			{
				Object.Destroy(itemHand.currentItem.GetHandItem().gameObject);
				itemHand.currentItem = null;
				itemHand.handModel.targetOffset = Vector3.zero + Random.insideUnitSphere * 0.03f;
				itemHand.hand.interactState = ENT_Player.InteractType.none;
			}
		}
		this.CalculateEncumberance();
	}

	// Token: 0x06000302 RID: 770 RVA: 0x0001EDE4 File Offset: 0x0001CFE4
	public void AddItemToHand(Item i, ENT_Player.Hand playerHand)
	{
		if (i == null)
		{
			return;
		}
		i.InitializeInHand(this, playerHand);
		this.itemHands[playerHand.id].currentItem = i;
		i.inventory = this;
		Inventory.ItemHand itemHand = this.itemHands[playerHand.id];
		if (i.pickupSounds.Count > 0)
		{
			this.aud.GetGroup("inventory").GetSet("inventory-swap").PlaySound(1f, i.pickupSounds[Random.Range(0, i.pickupSounds.Count)]);
		}
		else
		{
			this.aud.GetGroup("inventory").GetSet("inventory-swap").Play(1f, null);
		}
		if (this.bagItems.Contains(i))
		{
			this.bagItems.Remove(i);
			this.CalculateEncumberance();
		}
		itemHand.currentItem.ActivateItem(itemHand);
		playerHand.interactState = ENT_Player.InteractType.item;
		if (itemHand.currentItem == null)
		{
			playerHand.interactState = ENT_Player.InteractType.none;
		}
		foreach (Inventory.Pocket pocket in this.pockets)
		{
			pocket.item = null;
		}
		itemHand.handModel.targetOffset = Vector3.down * 0.6f;
		this.CalculateEncumberance();
	}

	// Token: 0x06000303 RID: 771 RVA: 0x0001EF44 File Offset: 0x0001D144
	public void AddItemToInventoryScreen(Vector3 pos, int handID)
	{
		if (this.itemHands[handID].currentItem == null)
		{
			return;
		}
		this.AddItemToInventoryScreen(pos, this.itemHands[handID].currentItem, false, false);
	}

	// Token: 0x06000304 RID: 772 RVA: 0x0001EF6C File Offset: 0x0001D16C
	public void AddItemToInventoryScreen(Vector3 pos, Item item, bool localSpacePosition = false, bool useStoredValues = false)
	{
		item.GetDropObject().transform.parent = this.itemRoot;
		item.GetDropObject().gameObject.layer = LayerMask.NameToLayer("Inventory");
		item.inBag = true;
		item.GetDropObject().gameObject.SetActive(true);
		if ((float)this.bagItems.Count >= this.GetUnencumberanceAmount())
		{
			this.aud.PlaySound("inventory:inventory-overencumbered");
		}
		this.bagItems.Add(item);
		Renderer[] componentsInChildren = item.GetDropObject().transform.GetComponentsInChildren<Renderer>();
		for (int i = 0; i < componentsInChildren.Length; i++)
		{
			componentsInChildren[i].gameObject.layer = LayerMask.NameToLayer("Inventory");
		}
		item.GetDropObject().transform.localScale = Vector3.one * item.inventoryScale * 1f;
		if (!localSpacePosition)
		{
			item.GetDropObject().transform.position = item.GetDropObject().transform.position + Random.insideUnitSphere * 0.01f;
		}
		else
		{
			item.GetDropObject().transform.localPosition = pos;
		}
		item.bagPosition = item.GetDropObject().transform.localPosition;
		if (useStoredValues)
		{
			item.GetDropObject().transform.localRotation = item.bagRotation;
		}
		else
		{
			item.GetDropObject().transform.rotation = Quaternion.LookRotation(pos - Camera.main.transform.position + Camera.main.transform.up * 0.5f) * Quaternion.LookRotation(item.upDirection);
			item.bagRotation = item.GetDropObject().transform.localRotation;
		}
		ObjectTagger component = item.GetDropObject().GetComponent<ObjectTagger>();
		if (component != null && component.HasTag("Roach"))
		{
			item.GetDropObject().GetComponent<DEN_Roach>().active = false;
		}
		Rigidbody component2 = item.GetDropObject().GetComponent<Rigidbody>();
		if (component2 != null)
		{
			component2.isKinematic = true;
		}
		this.CalculateEncumberance();
		item.GetDropObject().transform.localPosition = Vector3.Normalize(item.GetDropObject().transform.localPosition) * 1.6f;
	}

	// Token: 0x06000305 RID: 773 RVA: 0x0001F1C0 File Offset: 0x0001D3C0
	public void DropItemFromHand(Vector3 pos, int h, bool addtoInventory = false)
	{
		Inventory.ItemHand itemHand = this.itemHands[h];
		if (itemHand.currentItem != null)
		{
			Item currentItem = itemHand.currentItem;
			if (!currentItem.GetHandItem().CanDrop())
			{
				return;
			}
			if (this.showingInventory || addtoInventory)
			{
				this.aud.GetGroup("inventory").GetSet("inventory-drop").Play(1f, null);
				currentItem.Drop(pos, Camera.main.transform.forward);
				pos = (pos - Camera.main.transform.position).normalized * 1.9f + Camera.main.transform.position;
				this.AddItemToInventoryScreen(pos, h);
				currentItem.inventory = this;
			}
			else
			{
				this.DropItemIntoWorld(currentItem, pos);
			}
			itemHand.currentItem = null;
			itemHand.handModel.targetOffset = Vector3.zero + Random.insideUnitSphere * 0.03f;
			this.player.hands[h].interactState = ENT_Player.InteractType.none;
		}
	}

	// Token: 0x06000306 RID: 774 RVA: 0x0001F2D4 File Offset: 0x0001D4D4
	public void DropItemIntoWorld(Item item, Vector3 pos)
	{
		Inventory.<>c__DisplayClass43_0 CS$<>8__locals1 = new Inventory.<>c__DisplayClass43_0();
		CS$<>8__locals1.item = item;
		this.aud.GetGroup("inventory").GetSet("inventory-drop").Play(1f, null);
		CS$<>8__locals1.item.Drop(pos, Camera.main.transform.forward);
		CS$<>8__locals1.item.inventory = null;
		CS$<>8__locals1.item.GetDropObject().gameObject.layer = LayerMask.NameToLayer("Interactable No Collide");
		Renderer[] componentsInChildren = CS$<>8__locals1.item.GetDropObject().transform.GetComponentsInChildren<Renderer>();
		for (int i = 0; i < componentsInChildren.Length; i++)
		{
			componentsInChildren[i].gameObject.layer = LayerMask.NameToLayer("Interactable No Collide");
		}
		if (WorldLoader.initialized)
		{
			WorldLoader.LevelInfo closestLevelToPosition = WorldLoader.GetClosestLevelToPosition(CS$<>8__locals1.item.GetDropObject().transform.position);
			if (WorldLoader.initialized && closestLevelToPosition.level != null)
			{
				CS$<>8__locals1.item.GetDropObject().transform.parent = WorldLoader.GetClosestLevelToPosition(CS$<>8__locals1.item.GetDropObject().transform.position).level.GetParentRoot();
			}
			else
			{
				CS$<>8__locals1.item.GetDropObject().transform.parent = null;
			}
		}
		else
		{
			CS$<>8__locals1.item.GetDropObject().transform.parent = null;
		}
		CS$<>8__locals1.item.GetDropObject().transform.rotation = this.player.cam.transform.rotation * Quaternion.LookRotation(CS$<>8__locals1.item.upDirection);
		base.StartCoroutine(CS$<>8__locals1.<DropItemIntoWorld>g__scaleUpDropObject|0());
		CS$<>8__locals1.item.GetDropObject().OnDrop();
		Rigidbody component = CS$<>8__locals1.item.GetDropObject().GetComponent<Rigidbody>();
		CS$<>8__locals1.item.inBag = false;
		if (component != null)
		{
			component.isKinematic = false;
			component.AddForce(Camera.main.transform.forward * CS$<>8__locals1.item.dropVel + Camera.main.transform.forward * 2f, ForceMode.VelocityChange);
		}
		ObjectTagger component2 = CS$<>8__locals1.item.GetDropObject().GetComponent<ObjectTagger>();
		if (component2 != null && component2.HasTag("Roach"))
		{
			CS$<>8__locals1.item.GetDropObject().GetComponent<DEN_Roach>().active = true;
		}
		if (this.bagItems.Contains(CS$<>8__locals1.item))
		{
			this.bagItems.Remove(CS$<>8__locals1.item);
		}
		this.CalculateEncumberance();
	}

	// Token: 0x06000307 RID: 775 RVA: 0x0001F574 File Offset: 0x0001D774
	private void SwapHands()
	{
		Item currentItem = this.itemHands[0].currentItem;
		Item currentItem2 = this.itemHands[1].currentItem;
		if (currentItem != null && !this.itemHands[0].currentItem.GetHandItem().CanDrop())
		{
			return;
		}
		if (currentItem2 != null && !this.itemHands[1].currentItem.GetHandItem().CanDrop())
		{
			return;
		}
		if (((this.itemHands[0].hand.interactState == ENT_Player.InteractType.item || this.itemHands[0].hand.interactState == ENT_Player.InteractType.none) && this.itemHands[1].hand.interactState == ENT_Player.InteractType.item) || this.itemHands[1].hand.interactState == ENT_Player.InteractType.none)
		{
			this.DropItemFromHand(Vector3.zero, 0, false);
			this.DropItemFromHand(Vector3.zero, 1, false);
			this.AddItemToHand(currentItem2, this.itemHands[0].hand);
			this.AddItemToHand(currentItem, this.itemHands[1].hand);
		}
	}

	// Token: 0x06000308 RID: 776 RVA: 0x0001F66C File Offset: 0x0001D86C
	private void CalculateEncumberance()
	{
		float buff = this.player.curBuffs.GetBuff("addCapacity");
		if ((float)this.bagItems.Count > this.GetUnencumberanceAmount())
		{
			this.encumberance = Mathf.Clamp(1f - ((float)(this.bagItems.Count - this.unencumberedItems) - buff) / (float)this.maxEncumberedItems, 0.1f, 1f);
			this.bagCurrentColor = Color.red;
			return;
		}
		this.encumberance = 1f;
	}

	// Token: 0x06000309 RID: 777 RVA: 0x0001F6F2 File Offset: 0x0001D8F2
	public float GetEncumberance()
	{
		return this.encumberance;
	}

	// Token: 0x0600030A RID: 778 RVA: 0x0001F6FA File Offset: 0x0001D8FA
	public float GetUnencumberanceAmount()
	{
		return this.player.curBuffs.GetBuff("addCapacity") + (float)this.unencumberedItems;
	}

	// Token: 0x0600030B RID: 779 RVA: 0x0001F71C File Offset: 0x0001D91C
	internal void LoadItemsIntoBag(List<Item> itemList)
	{
		foreach (Item item in itemList.ToArray())
		{
			this.AddItemToInventoryScreen(item.bagPosition, item, true, true);
			item.inventory = this;
		}
	}

	// Token: 0x0600030C RID: 780 RVA: 0x0001F758 File Offset: 0x0001D958
	internal void ClearInventory()
	{
		for (int i = this.bagItems.Count - 1; i >= 0; i--)
		{
			this.bagItems[i].ClearDropObject();
			this.bagItems.RemoveAt(i);
		}
		for (int j = 0; j < this.itemHands.Length; j++)
		{
			this.DestroyItemInHand(j);
		}
		this.CalculateEncumberance();
	}

	// Token: 0x0600030D RID: 781 RVA: 0x0001F7BC File Offset: 0x0001D9BC
	internal void ClearInventoryExceptTag(string tag)
	{
		for (int i = this.bagItems.Count - 1; i >= 0; i--)
		{
			if (this.bagItems[i].itemTag != tag)
			{
				this.bagItems[i].ClearDropObject();
				this.bagItems.RemoveAt(i);
			}
		}
		for (int j = 0; j < this.itemHands.Length; j++)
		{
			if (this.itemHands[j].currentItem != null && this.itemHands[j].currentItem.itemTag != tag)
			{
				this.DestroyItemInHand(j);
			}
		}
		this.CalculateEncumberance();
	}

	// Token: 0x0600030E RID: 782 RVA: 0x0001F860 File Offset: 0x0001DA60
	internal void ClearItemTagFromInventory(string tag)
	{
		for (int i = this.bagItems.Count - 1; i >= 0; i--)
		{
			if (this.bagItems[i].itemTag == tag)
			{
				this.bagItems[i].ClearDropObject();
				this.bagItems.RemoveAt(i);
			}
		}
		for (int j = 0; j < this.itemHands.Length; j++)
		{
			if (this.itemHands[j].currentItem != null && this.itemHands[j].currentItem.itemTag == tag)
			{
				this.DestroyItemInHand(j);
			}
		}
		this.CalculateEncumberance();
	}

	// Token: 0x0600030F RID: 783 RVA: 0x0001F904 File Offset: 0x0001DB04
	public static bool IsShowingInventory()
	{
		return Inventory.instance.showingInventory;
	}

	// Token: 0x0400041E RID: 1054
	public Transform playerInventoryRoot;

	// Token: 0x0400041F RID: 1055
	public Inventory.ItemHand[] itemHands = new Inventory.ItemHand[]
	{
		new Inventory.ItemHand(),
		new Inventory.ItemHand()
	};

	// Token: 0x04000420 RID: 1056
	public Image iconAsset;

	// Token: 0x04000421 RID: 1057
	private UT_AudioClipHandler aud;

	// Token: 0x04000422 RID: 1058
	private ENT_Player player;

	// Token: 0x04000423 RID: 1059
	public bool showingInventory;

	// Token: 0x04000424 RID: 1060
	public Transform inventory;

	// Token: 0x04000425 RID: 1061
	public Transform itemRoot;

	// Token: 0x04000426 RID: 1062
	public List<Item> bagItems = new List<Item>();

	// Token: 0x04000427 RID: 1063
	public MeshRenderer bagRenderer;

	// Token: 0x04000428 RID: 1064
	public Color bagEncumberedColor = Color.red;

	// Token: 0x04000429 RID: 1065
	public Color bagUnencumberedColor = Color.white;

	// Token: 0x0400042A RID: 1066
	private Color bagCurrentColor;

	// Token: 0x0400042B RID: 1067
	public int maxEncumberedItems = 10;

	// Token: 0x0400042C RID: 1068
	public int unencumberedItems = 5;

	// Token: 0x0400042D RID: 1069
	public float encumberance = 1f;

	// Token: 0x0400042E RID: 1070
	private Quaternion inventoryRotation;

	// Token: 0x0400042F RID: 1071
	private Vector3 inventoryLookVector;

	// Token: 0x04000430 RID: 1072
	private GameObject itemObject;

	// Token: 0x04000431 RID: 1073
	public float minDistance = 1f;

	// Token: 0x04000432 RID: 1074
	public float moveSpeed = 1f;

	// Token: 0x04000433 RID: 1075
	public Transform inventoryCenterLocator;

	// Token: 0x04000434 RID: 1076
	public List<Inventory.Pocket> pockets;

	// Token: 0x04000435 RID: 1077
	public static Inventory instance;

	// Token: 0x04000436 RID: 1078
	public List<Item> testBagLoad;

	// Token: 0x02000237 RID: 567
	[Serializable]
	public class ItemHand
	{
		// Token: 0x06000D71 RID: 3441 RVA: 0x00052C0C File Offset: 0x00050E0C
		public bool HasItem()
		{
			return this.currentItem != null;
		}

		// Token: 0x04000EE7 RID: 3815
		[NonSerialized]
		public Item currentItem;

		// Token: 0x04000EE8 RID: 3816
		public int currentItemNumber;

		// Token: 0x04000EE9 RID: 3817
		public Transform handInventoryRoot;

		// Token: 0x04000EEA RID: 3818
		public ViewSway handModel;

		// Token: 0x04000EEB RID: 3819
		[NonSerialized]
		public ENT_Player.Hand hand;
	}

	// Token: 0x02000238 RID: 568
	[Serializable]
	public class Pocket
	{
		// Token: 0x04000EEC RID: 3820
		public Transform transform;

		// Token: 0x04000EED RID: 3821
		public string key;

		// Token: 0x04000EEE RID: 3822
		public int targetHandID;

		// Token: 0x04000EEF RID: 3823
		public Item_Object item;
	}
}
