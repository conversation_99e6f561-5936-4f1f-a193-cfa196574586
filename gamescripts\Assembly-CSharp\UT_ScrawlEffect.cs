﻿using System;
using UnityEngine;

// Token: 0x020000DC RID: 220
public class UT_ScrawlEffect : MonoBehaviour
{
	// Token: 0x060006E4 RID: 1764 RVA: 0x000362FA File Offset: 0x000344FA
	private void Start()
	{
		this.gMan = GameObject.Find("GameManager").GetComponent<CL_GameManager>();
		this.aud = base.GetComponent<AudioSource>();
	}

	// Token: 0x060006E5 RID: 1765 RVA: 0x00036320 File Offset: 0x00034520
	public void Play()
	{
		if (this.playScrawl != "")
		{
			if (this.type == UT_ScrawlEffect.ScrawlType.header)
			{
				this.gMan.uiMan.header.ShowText(this.playScrawl);
			}
			else if (this.type == UT_ScrawlEffect.ScrawlType.ascentHeader)
			{
				this.gMan.uiMan.ascentHeader.ShowText(this.playScrawl);
			}
			else if (this.type == UT_ScrawlEffect.ScrawlType.highscoreHeader)
			{
				this.gMan.uiMan.highscoreHeader.ShowText(this.playScrawl);
			}
			else if (this.type == UT_ScrawlEffect.ScrawlType.tip)
			{
				this.gMan.uiMan.tipHeader.ShowText(this.playScrawl);
			}
		}
		if (this.playEffect != null)
		{
			this.aud.clip = this.playEffect;
			this.aud.Play();
		}
	}

	// Token: 0x04000864 RID: 2148
	private CL_GameManager gMan;

	// Token: 0x04000865 RID: 2149
	private AudioSource aud;

	// Token: 0x04000866 RID: 2150
	public AudioClip playEffect;

	// Token: 0x04000867 RID: 2151
	public UT_ScrawlEffect.ScrawlType type;

	// Token: 0x04000868 RID: 2152
	[TextArea]
	public string playScrawl;

	// Token: 0x02000292 RID: 658
	public enum ScrawlType
	{
		// Token: 0x040010A1 RID: 4257
		header,
		// Token: 0x040010A2 RID: 4258
		ascentHeader,
		// Token: 0x040010A3 RID: 4259
		highscoreHeader,
		// Token: 0x040010A4 RID: 4260
		tip,
		// Token: 0x040010A5 RID: 4261
		descriptor
	}
}
