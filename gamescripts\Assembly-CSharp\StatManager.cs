﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Runtime.CompilerServices;
using TMPro;
using UnityEngine;

// Token: 0x020000C3 RID: 195
public class StatManager : MonoBehaviour
{
	// Token: 0x0600065A RID: 1626 RVA: 0x00033C94 File Offset: 0x00031E94
	private void Awake()
	{
		StatManager.instance = this;
		StatManager.sessionStats = new StatManager.GameStats();
		StatManager.saveData = null;
		this.filePath = Path.Combine(Application.persistentDataPath, "save.json");
		this.LoadStats();
		StatManager.saveData.gameStats.InitializeDictionary();
		StatManager.sessionStats.InitializeDictionary();
		if (StatManager.saveData.gameModes != null)
		{
			foreach (StatManager.SaveData.GameModeData gameModeData in StatManager.saveData.gameModes)
			{
				gameModeData.stats.InitializeDictionary();
			}
		}
		CommandConsole.AddCommand("erasesavedata", new Action<string[]>(this.EraseStats), true);
		StatManager.initialized = true;
	}

	// Token: 0x0600065B RID: 1627 RVA: 0x00033D60 File Offset: 0x00031F60
	private void Start()
	{
		if (CL_GameManager.gamemode != null)
		{
			this.winScreen = Object.Instantiate<UI_ScoreScreen>(CL_GameManager.gamemode.winScreen, base.transform);
			this.loseScreen = Object.Instantiate<UI_ScoreScreen>(CL_GameManager.gamemode.loseScreen, base.transform);
			this.winScreen.gameObject.SetActive(false);
			this.loseScreen.gameObject.SetActive(false);
		}
	}

	// Token: 0x0600065C RID: 1628 RVA: 0x00033DD2 File Offset: 0x00031FD2
	private void OnDestroy()
	{
		if (!this.hasSaved)
		{
			this.SaveStats();
		}
		StatManager.sessionStats = new StatManager.GameStats();
		StatManager.initialized = false;
	}

	// Token: 0x0600065D RID: 1629 RVA: 0x00033DF4 File Offset: 0x00031FF4
	public void SaveStats()
	{
		if (CommandConsole.hasCheated && !CL_GameManager.gamemode.allowCheatedScores)
		{
			return;
		}
		if (CL_GameManager.gMan.allowScores)
		{
			StatManager.SaveData.RoachBank roachBankByID = StatManager.saveData.GetRoachBankByID(CL_GameManager.GetBaseGamemode().GetRoachBankID());
			if (!SettingsManager.settings.g_competitive)
			{
				roachBankByID.value = CL_GameManager.roaches;
			}
			foreach (StatManager.Statistic statistic in StatManager.sessionStats.statistics)
			{
				StatManager.saveData.gameStats.UpdateStatistic(statistic.id, statistic.GetValue(), statistic.type, statistic.defaultModType, statistic.displayType, StatManager.Statistic.ModType.Add);
			}
			if (CL_GameManager.gamemode != null)
			{
				StatManager.saveData.UpdateGamemodeStats(CL_GameManager.GetGamemodeName(true, false), StatManager.sessionStats);
			}
		}
		CL_AchievementManager.SaveAchievements();
		string text = JsonUtility.ToJson(StatManager.saveData, true);
		File.WriteAllText(this.filePath, text);
		this.UpdateUI();
		this.hasSaved = true;
	}

	// Token: 0x0600065E RID: 1630 RVA: 0x00033F10 File Offset: 0x00032110
	public void LoadStats()
	{
		if (File.Exists(this.filePath))
		{
			string text = File.ReadAllText(this.filePath);
			if (text.Length <= 15)
			{
				StatManager.saveData = new StatManager.SaveData();
				StatManager.saveData.gameStats = new StatManager.GameStats();
				return;
			}
			StatManager.SaveData saveData;
			try
			{
				saveData = JsonUtility.FromJson<StatManager.SaveData>(text);
			}
			catch (Exception ex)
			{
				string text2 = "Warning! Unable to load. Checking Backup: ";
				Exception ex2 = ex;
				Debug.LogError(text2 + ((ex2 != null) ? ex2.ToString() : null));
				File.Copy(this.filePath, Path.Combine(Application.persistentDataPath, "save-error-backup.json"), true);
				File.Delete(this.filePath);
				this.AttemptLoadBackup();
				return;
			}
			File.Copy(this.filePath, Path.Combine(Application.persistentDataPath, "save-backup.json"), true);
			int version = saveData.version;
			if (version != this.saveVersion)
			{
				Directory.CreateDirectory(Path.Combine(Application.persistentDataPath, "Backups"));
				File.Copy(this.filePath, Path.Combine(Path.Combine(Application.persistentDataPath, "Backups"), "save-backup-v" + version.ToString() + ".json"), true);
			}
			if (version < 3)
			{
				foreach (StatManager.SaveData.GameModeData gameModeData in saveData.gameModes)
				{
					int statisticInt = StatManager.GetStatisticInt(gameModeData.stats, "bank-roaches");
					string[] array = gameModeData.name.Split('-', StringSplitOptions.None);
					bool flag = false;
					if (array.Length > 1)
					{
						Debug.Log("Converting Gamemode: " + array[0] + " as " + array[1]);
						if (array[1] == "Hardmode")
						{
							flag = true;
						}
					}
					else
					{
						Debug.Log("Converting Gamemode: " + array[0]);
					}
					M_Gamemode gamemodeAsset = CL_AssetManager.GetGamemodeAsset(array[0], "");
					if (gamemodeAsset != null)
					{
						string text3 = gamemodeAsset.roachBankID;
						if (flag)
						{
							text3 += "-hard";
						}
						saveData.GetRoachBankByID(text3).value += statisticInt;
						Debug.Log(string.Format("Roaches added to Bank {0}. Total Value: {1}, from {2}", text3, saveData.GetRoachBankByID(text3).value, statisticInt));
					}
				}
			}
			if (version != this.saveVersion)
			{
				if (saveData.flags != null)
				{
					using (List<StatManager.SaveData.SaveFlags>.Enumerator enumerator2 = saveData.flags.GetEnumerator())
					{
						while (enumerator2.MoveNext())
						{
							if (enumerator2.Current.name == "ACH_HABITATION")
							{
								StatManager.saveData = saveData;
								StatManager.saveData.version = this.saveVersion;
								if (StatManager.saveData.gameStats != null)
								{
									StatManager.saveData.gameStats.InitializeDictionary();
								}
								else
								{
									StatManager.saveData.gameStats = new StatManager.GameStats();
								}
								base.StartCoroutine(StatManager.<LoadStats>g__WaitOneFrameForXPCalc|19_0());
								SettingsManager.WipeSettings();
								return;
							}
						}
					}
				}
				StatManager.showUpgradeDialogue = true;
				File.Copy(this.filePath, Path.Combine(Application.persistentDataPath, "save-backup.json"), true);
				File.Copy(this.filePath, Path.Combine(Application.persistentDataPath, "save-demo.json"), true);
				StatManager.saveData = saveData;
				StatManager.saveData.version = this.saveVersion;
				if (StatManager.saveData.gameStats != null)
				{
					StatManager.saveData.gameStats.InitializeDictionary();
				}
				else
				{
					StatManager.saveData.gameStats = new StatManager.GameStats();
				}
				if (version <= 1)
				{
					SettingsManager.WipeSettings();
				}
				base.StartCoroutine(StatManager.<LoadStats>g__WaitOneFrameForXPCalc|19_0());
				return;
			}
			StatManager.saveData = saveData;
			if (StatManager.saveData.gameStats != null)
			{
				StatManager.saveData.gameStats.InitializeDictionary();
				return;
			}
			StatManager.saveData.gameStats = new StatManager.GameStats();
			return;
		}
		else
		{
			StatManager.saveData = new StatManager.SaveData();
			StatManager.saveData.gameStats = new StatManager.GameStats();
		}
	}

	// Token: 0x0600065F RID: 1631 RVA: 0x00034304 File Offset: 0x00032504
	private bool AttemptLoadBackup()
	{
		if (StatManager.loadAttempts < 2 && File.Exists(Path.Combine(Application.persistentDataPath, "save-backup.json")) && File.ReadAllText(Path.Combine(Application.persistentDataPath, "save-backup.json")).Length > 5)
		{
			Debug.LogError("Error! Failed to load save file. Attempting to load backup.");
			File.Copy(Path.Combine(Application.persistentDataPath, "save-backup.json"), this.filePath, true);
			StatManager.loadAttempts++;
			this.LoadStats();
			return true;
		}
		return false;
	}

	// Token: 0x06000660 RID: 1632 RVA: 0x00034385 File Offset: 0x00032585
	private void UpgradeVersion(string json, StatManager.SaveData newSaveData)
	{
	}

	// Token: 0x06000661 RID: 1633 RVA: 0x00034387 File Offset: 0x00032587
	public void EraseStats(string[] args = null)
	{
		this.EraseStats();
	}

	// Token: 0x06000662 RID: 1634 RVA: 0x00034390 File Offset: 0x00032590
	public void EraseStats()
	{
		CL_AchievementManager.ResetAchievements();
		if (this.filePath != null)
		{
			File.Delete(this.filePath);
		}
		this.LoadStats();
		CommandConsole.Log("Erased all saved statistics and scores.", false);
		CL_GameManager.gMan.RestartScene();
		SettingsManager.SetSetting(new string[] { "g_hard", "false" });
	}

	// Token: 0x06000663 RID: 1635 RVA: 0x000343EC File Offset: 0x000325EC
	public void UpdateUI()
	{
		foreach (TMP_Text tmp_Text in base.GetComponentsInChildren<TMP_Text>())
		{
			if (tmp_Text.name.StartsWith("session-stat:"))
			{
				string text = tmp_Text.name.Substring("session-stat:".Length);
				StatManager.Statistic statistic;
				if (StatManager.sessionStats.statsDictionary.TryGetValue(text, out statistic))
				{
					tmp_Text.text = this.ReplaceTextAfterColon(tmp_Text.text, statistic.GetString());
				}
			}
			else if (tmp_Text.name.StartsWith("global-stat:"))
			{
				string text2 = tmp_Text.name.Substring("global-stat:".Length);
				StatManager.Statistic statistic2;
				if (StatManager.saveData.gameStats.statsDictionary.TryGetValue(text2, out statistic2))
				{
					tmp_Text.text = this.ReplaceTextAfterColon(tmp_Text.text, statistic2.GetString());
				}
			}
			else if (tmp_Text.name.StartsWith("gamemode-stat:") && CL_GameManager.gamemode != null)
			{
				string text3 = tmp_Text.name.Substring("gamemode-stat:".Length);
				StatManager.Statistic statistic3;
				if (StatManager.saveData.GetGameMode(CL_GameManager.GetGamemodeName(true, false)) != null && StatManager.saveData.GetGameMode(CL_GameManager.GetGamemodeName(true, false)).stats.statsDictionary.TryGetValue(text3, out statistic3))
				{
					tmp_Text.text = this.ReplaceTextAfterColon(tmp_Text.text, statistic3.GetString());
				}
			}
		}
	}

	// Token: 0x06000664 RID: 1636 RVA: 0x00034564 File Offset: 0x00032764
	private string ReplaceTextAfterColon(string originalText, string newValue)
	{
		int num = originalText.IndexOf(':');
		if (num != -1)
		{
			return originalText.Substring(0, num + 1) + " " + newValue;
		}
		return originalText;
	}

	// Token: 0x06000665 RID: 1637 RVA: 0x00034595 File Offset: 0x00032795
	public static object GetStatisticValue(StatManager.GameStats stats, string statName)
	{
		if (stats == null)
		{
			Debug.LogWarning("Warning! No stats variable found for " + statName);
		}
		if (!stats.HasStatistic(statName))
		{
			return null;
		}
		return stats.GetStatistic(statName).GetValue();
	}

	// Token: 0x06000666 RID: 1638 RVA: 0x000345C1 File Offset: 0x000327C1
	public static int GetStatisticInt(StatManager.GameStats stats, string statName)
	{
		if (!stats.HasStatistic(statName))
		{
			return 0;
		}
		return (int)stats.GetStatistic(statName).GetValue();
	}

	// Token: 0x06000667 RID: 1639 RVA: 0x000345DF File Offset: 0x000327DF
	public static float GetStatisticFloat(StatManager.GameStats stats, string statName)
	{
		if (!stats.HasStatistic(statName))
		{
			return 0f;
		}
		return (float)stats.GetStatistic(statName).GetValue();
	}

	// Token: 0x06000668 RID: 1640 RVA: 0x00034601 File Offset: 0x00032801
	public static int GetTotalStatisticInt(string statName)
	{
		return (int)StatManager.saveData.gameStats.GetStatistic(statName).GetValue() + (int)StatManager.sessionStats.GetStatistic(statName).GetValue();
	}

	// Token: 0x06000669 RID: 1641 RVA: 0x00034633 File Offset: 0x00032833
	public static bool HasGlobalStat(string statName)
	{
		return StatManager.saveData.gameStats.HasStatistic(statName);
	}

	// Token: 0x0600066A RID: 1642 RVA: 0x00034648 File Offset: 0x00032848
	public LevelStat GetLevelStat(string id)
	{
		foreach (LevelStat levelStat in StatManager.saveData.levelStats)
		{
			if (levelStat.levelId == id)
			{
				return levelStat;
			}
		}
		return new LevelStat
		{
			levelId = id,
			bestTime = -1f
		};
	}

	// Token: 0x0600066B RID: 1643 RVA: 0x000346C4 File Offset: 0x000328C4
	public void SaveLevelStat(LevelStat s)
	{
		if (StatManager.saveData.levelStats == null)
		{
			StatManager.saveData.levelStats = new List<LevelStat>();
		}
		StatManager.saveData.levelStats.Add(s);
	}

	// Token: 0x0600066C RID: 1644 RVA: 0x000346F1 File Offset: 0x000328F1
	public UI_ScoreScreen GetScoreScreen(bool win = false)
	{
		if (win)
		{
			return this.winScreen;
		}
		return this.loseScreen;
	}

	// Token: 0x0600066D RID: 1645 RVA: 0x00034704 File Offset: 0x00032904
	public void ShowScoreScreen(bool win = false)
	{
		this.SaveStats();
		UI_ScoreScreen ui_ScoreScreen;
		if (win)
		{
			ui_ScoreScreen = this.winScreen;
			this.winScreen.gameObject.SetActive(true);
		}
		else
		{
			ui_ScoreScreen = this.loseScreen;
			this.loseScreen.gameObject.SetActive(true);
		}
		ui_ScoreScreen.ShowScreen();
		this.UpdateUI();
	}

	// Token: 0x0600066F RID: 1647 RVA: 0x00034769 File Offset: 0x00032969
	[CompilerGenerated]
	internal static IEnumerator <LoadStats>g__WaitOneFrameForXPCalc|19_0()
	{
		yield return new WaitForSeconds(0.5f);
		StatManager.saveData.gameStats.SetStatistic("progression-experience", CL_AchievementManager.GetCalculatedXP(), StatManager.Statistic.DataType.Int);
		yield break;
	}

	// Token: 0x040007DC RID: 2012
	private int saveVersion = 3;

	// Token: 0x040007DD RID: 2013
	public static StatManager.GameStats sessionStats;

	// Token: 0x040007DE RID: 2014
	private bool hasSaved;

	// Token: 0x040007DF RID: 2015
	public static StatManager.SaveData saveData;

	// Token: 0x040007E0 RID: 2016
	public static StatManager instance;

	// Token: 0x040007E1 RID: 2017
	public static bool initialized;

	// Token: 0x040007E2 RID: 2018
	public static Action refreshStatText;

	// Token: 0x040007E3 RID: 2019
	public static bool showUpgradeDialogue;

	// Token: 0x040007E4 RID: 2020
	private string filePath;

	// Token: 0x040007E5 RID: 2021
	private static int loadAttempts;

	// Token: 0x040007E6 RID: 2022
	private UI_ScoreScreen winScreen;

	// Token: 0x040007E7 RID: 2023
	private UI_ScoreScreen loseScreen;

	// Token: 0x02000287 RID: 647
	[Serializable]
	public class Statistic
	{
		// Token: 0x06000E22 RID: 3618 RVA: 0x00055BDC File Offset: 0x00053DDC
		public object GetValue()
		{
			switch (this.type)
			{
			case StatManager.Statistic.DataType.Int:
			{
				int num = 0;
				if (int.TryParse(this.value, out num))
				{
					return num;
				}
				return 0;
			}
			case StatManager.Statistic.DataType.String:
				return this.value;
			case StatManager.Statistic.DataType.Float:
			{
				float num2 = 0f;
				if (this.value.Contains(","))
				{
					this.value = this.value.Replace(',', '.');
				}
				if (float.TryParse(this.value, NumberStyles.Float, CultureInfo.InvariantCulture, out num2))
				{
					return num2;
				}
				return 0;
			}
			default:
			{
				float num2 = 0f;
				if (this.value.Contains(","))
				{
					this.value = this.value.Replace(',', '.');
				}
				if (float.TryParse(this.value, NumberStyles.Float, CultureInfo.InvariantCulture, out num2))
				{
					return num2;
				}
				return 0;
			}
			}
		}

		// Token: 0x06000E23 RID: 3619 RVA: 0x00055CD4 File Offset: 0x00053ED4
		public void SetValue(object newValue)
		{
			switch (this.type)
			{
			case StatManager.Statistic.DataType.Int:
				this.value = ((int)newValue).ToString();
				return;
			case StatManager.Statistic.DataType.String:
				this.value = (string)newValue;
				return;
			case StatManager.Statistic.DataType.Float:
				this.value = ((float)newValue).ToString(CultureInfo.InvariantCulture);
				return;
			default:
				return;
			}
		}

		// Token: 0x06000E24 RID: 3620 RVA: 0x00055D38 File Offset: 0x00053F38
		public void AddValue(object newValue)
		{
			switch (this.type)
			{
			case StatManager.Statistic.DataType.Int:
				this.value = ((int)this.GetValue() + (int)newValue).ToString();
				return;
			case StatManager.Statistic.DataType.String:
				this.value += (string)newValue;
				return;
			case StatManager.Statistic.DataType.Float:
				this.value = ((float)this.GetValue() + (float)newValue).ToString(CultureInfo.InvariantCulture);
				return;
			default:
				return;
			}
		}

		// Token: 0x06000E25 RID: 3621 RVA: 0x00055DC0 File Offset: 0x00053FC0
		public string GetString()
		{
			string text;
			switch (this.displayType)
			{
			case StatManager.Statistic.DisplayType.Meters:
				text = Math.Round((double)((float)this.GetValue()), 2).ToString(CultureInfo.InvariantCulture) + " Meters";
				break;
			case StatManager.Statistic.DisplayType.Speed:
				text = Math.Round((double)((float)this.GetValue()), 2).ToString(CultureInfo.InvariantCulture) + " m/s";
				break;
			case StatManager.Statistic.DisplayType.Rounded:
				text = Math.Round((double)((float)this.GetValue())).ToString(CultureInfo.InvariantCulture);
				break;
			case StatManager.Statistic.DisplayType.Time:
			{
				TimeSpan timeSpan = TimeSpan.FromSeconds((double)((float)this.GetValue()));
				if (timeSpan.TotalHours < 1.0)
				{
					text = timeSpan.ToString("mm\\:ss\\:ff");
				}
				else if (timeSpan.TotalDays < 1.0)
				{
					text = string.Format(string.Format("H:{0} M:{1} S:{2}.{3}", new object[] { timeSpan.Hours, timeSpan.Minutes, timeSpan.Seconds, timeSpan.Milliseconds }), Array.Empty<object>());
				}
				else
				{
					text = string.Format(string.Format("D:{0} H:{1} M:{2} S:{3}.{4}", new object[] { timeSpan.Days, timeSpan.Hours, timeSpan.Minutes, timeSpan.Seconds, timeSpan.Milliseconds }), Array.Empty<object>());
				}
				break;
			}
			default:
				text = this.value;
				break;
			}
			return text;
		}

		// Token: 0x04001064 RID: 4196
		public string id;

		// Token: 0x04001065 RID: 4197
		public string value;

		// Token: 0x04001066 RID: 4198
		public StatManager.Statistic.DataType type;

		// Token: 0x04001067 RID: 4199
		public StatManager.Statistic.ModType defaultModType = StatManager.Statistic.ModType.Add;

		// Token: 0x04001068 RID: 4200
		public StatManager.Statistic.DisplayType displayType;

		// Token: 0x0200030E RID: 782
		public enum DataType
		{
			// Token: 0x04001306 RID: 4870
			Int,
			// Token: 0x04001307 RID: 4871
			String,
			// Token: 0x04001308 RID: 4872
			Float
		}

		// Token: 0x0200030F RID: 783
		public enum ModType
		{
			// Token: 0x0400130A RID: 4874
			Set,
			// Token: 0x0400130B RID: 4875
			Add,
			// Token: 0x0400130C RID: 4876
			Max,
			// Token: 0x0400130D RID: 4877
			Min,
			// Token: 0x0400130E RID: 4878
			None
		}

		// Token: 0x02000310 RID: 784
		public enum DisplayType
		{
			// Token: 0x04001310 RID: 4880
			Default,
			// Token: 0x04001311 RID: 4881
			Meters,
			// Token: 0x04001312 RID: 4882
			Speed,
			// Token: 0x04001313 RID: 4883
			Rounded,
			// Token: 0x04001314 RID: 4884
			Time
		}
	}

	// Token: 0x02000288 RID: 648
	[Serializable]
	public class GameStats
	{
		// Token: 0x06000E27 RID: 3623 RVA: 0x00055F9C File Offset: 0x0005419C
		public void InitializeDictionary()
		{
			this.statsDictionary = new Dictionary<string, StatManager.Statistic>();
			foreach (StatManager.Statistic statistic in this.statistics)
			{
				this.statsDictionary[statistic.id] = statistic;
			}
		}

		// Token: 0x06000E28 RID: 3624 RVA: 0x00056008 File Offset: 0x00054208
		public StatManager.Statistic GetStatistic(string id)
		{
			StatManager.Statistic statistic;
			if (this.statsDictionary.TryGetValue(id, out statistic))
			{
				return statistic;
			}
			return new StatManager.Statistic
			{
				value = ""
			};
		}

		// Token: 0x06000E29 RID: 3625 RVA: 0x00056038 File Offset: 0x00054238
		public void UpdateStatistic(string id, object value, StatManager.Statistic.DataType type, StatManager.Statistic.ModType modType = StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType displayType = StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType globalMod = StatManager.Statistic.ModType.Add)
		{
			StatManager.Statistic statistic;
			if (this.statsDictionary.TryGetValue(id, out statistic))
			{
				if (modType == StatManager.Statistic.ModType.Set)
				{
					statistic.SetValue(value);
				}
				else if (modType == StatManager.Statistic.ModType.Add)
				{
					statistic.AddValue(value);
				}
				else if (type == StatManager.Statistic.DataType.Float && modType == StatManager.Statistic.ModType.Max)
				{
					statistic.SetValue(Mathf.Max((float)value, (float)statistic.GetValue()));
				}
				else if (type == StatManager.Statistic.DataType.Float && modType == StatManager.Statistic.ModType.Min)
				{
					statistic.SetValue(Mathf.Min((float)value, (float)statistic.GetValue()));
				}
				else if (type == StatManager.Statistic.DataType.Int && modType == StatManager.Statistic.ModType.Max)
				{
					statistic.SetValue(Mathf.Max((int)value, (int)statistic.GetValue()));
				}
				else
				{
					if (type != StatManager.Statistic.DataType.Int || modType != StatManager.Statistic.ModType.Min)
					{
						Debug.LogError("Unable to apply statistic " + id + " Returning.");
						return;
					}
					statistic.SetValue(Mathf.Min((int)value, (int)statistic.GetValue()));
				}
			}
			else
			{
				statistic = new StatManager.Statistic
				{
					id = id,
					type = type,
					displayType = displayType,
					defaultModType = globalMod
				};
				if (this.statsDictionary.ContainsKey(id))
				{
					this.statsDictionary[id] = statistic;
					this.statsDictionary[id].SetValue(value);
				}
				else
				{
					statistic.SetValue(value);
					this.statistics.Add(statistic);
				}
			}
			this.InitializeDictionary();
		}

		// Token: 0x06000E2A RID: 3626 RVA: 0x000561B2 File Offset: 0x000543B2
		public void UpdateStatistic(string id, object value, StatManager.Statistic.DataType type, StatManager.Statistic.DisplayType displayType = StatManager.Statistic.DisplayType.Default)
		{
			this.UpdateStatistic(id, value, type, StatManager.Statistic.ModType.Set, displayType, StatManager.Statistic.ModType.Add);
		}

		// Token: 0x06000E2B RID: 3627 RVA: 0x000561C1 File Offset: 0x000543C1
		public void UpdateStatistic(string id, object value, StatManager.Statistic.DataType type)
		{
			this.UpdateStatistic(id, value, type, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
		}

		// Token: 0x06000E2C RID: 3628 RVA: 0x000561CF File Offset: 0x000543CF
		public void SetStatistic(string id, object value, StatManager.Statistic.DataType type)
		{
			this.UpdateStatistic(id, value, type, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Set);
		}

		// Token: 0x06000E2D RID: 3629 RVA: 0x000561DD File Offset: 0x000543DD
		public bool HasStatistic(string id)
		{
			if (this.statsDictionary.Count == 0)
			{
				this.InitializeDictionary();
			}
			return this.statsDictionary.ContainsKey(id);
		}

		// Token: 0x04001069 RID: 4201
		public List<StatManager.Statistic> statistics = new List<StatManager.Statistic>();

		// Token: 0x0400106A RID: 4202
		[NonSerialized]
		public Dictionary<string, StatManager.Statistic> statsDictionary = new Dictionary<string, StatManager.Statistic>();

		// Token: 0x0400106B RID: 4203
		public int xp;
	}

	// Token: 0x02000289 RID: 649
	[Serializable]
	public class SaveData
	{
		// Token: 0x06000E2F RID: 3631 RVA: 0x0005621C File Offset: 0x0005441C
		public StatManager.SaveData.GameModeData GetGameMode(string gameModeName)
		{
			if (this.gameModes == null)
			{
				return null;
			}
			foreach (StatManager.SaveData.GameModeData gameModeData in this.gameModes)
			{
				if (gameModeData.name == gameModeName)
				{
					return gameModeData;
				}
			}
			return null;
		}

		// Token: 0x06000E30 RID: 3632 RVA: 0x00056288 File Offset: 0x00054488
		public void UpdateGamemodeStats(string gameModeName, StatManager.GameStats stats)
		{
			if (this.gameModes == null)
			{
				this.gameModes = new List<StatManager.SaveData.GameModeData>();
			}
			foreach (StatManager.SaveData.GameModeData gameModeData in this.gameModes)
			{
				if (gameModeData.name == gameModeName)
				{
					foreach (StatManager.Statistic statistic in stats.statistics)
					{
						gameModeData.stats.UpdateStatistic(statistic.id, statistic.GetValue(), statistic.type, statistic.defaultModType, statistic.displayType, StatManager.Statistic.ModType.Add);
					}
					CommandConsole.Log("Saving stats in " + gameModeData.name, false);
					return;
				}
			}
			CommandConsole.Log("Attempted to store data in gamemode :" + gameModeName + ", Gamemode not found, creating new one.", false);
			StatManager.SaveData.GameModeData gameModeData2 = new StatManager.SaveData.GameModeData();
			gameModeData2.name = gameModeName;
			gameModeData2.stats = stats;
			gameModeData2.stats.InitializeDictionary();
			this.gameModes.Add(gameModeData2);
		}

		// Token: 0x06000E31 RID: 3633 RVA: 0x000563C0 File Offset: 0x000545C0
		public void SaveFlag(string name, bool flagged, string data = "")
		{
			if (this.flags == null)
			{
				this.flags = new List<StatManager.SaveData.SaveFlags>();
			}
			foreach (StatManager.SaveData.SaveFlags saveFlags in this.flags)
			{
				if (saveFlags.name == name)
				{
					saveFlags.value = flagged;
					saveFlags.data = data;
					return;
				}
			}
			StatManager.SaveData.SaveFlags saveFlags2 = new StatManager.SaveData.SaveFlags();
			saveFlags2.name = name;
			saveFlags2.value = flagged;
			saveFlags2.data = data;
			this.flags.Add(saveFlags2);
		}

		// Token: 0x06000E32 RID: 3634 RVA: 0x00056464 File Offset: 0x00054664
		public StatManager.SaveData.SaveFlags GetFlag(string name)
		{
			if (this.flags == null)
			{
				return null;
			}
			foreach (StatManager.SaveData.SaveFlags saveFlags in this.flags)
			{
				if (saveFlags.name == name)
				{
					return saveFlags;
				}
			}
			return null;
		}

		// Token: 0x06000E33 RID: 3635 RVA: 0x000564D0 File Offset: 0x000546D0
		public bool GetFlagState(string name)
		{
			if (this.flags == null)
			{
				return false;
			}
			foreach (StatManager.SaveData.SaveFlags saveFlags in this.flags)
			{
				if (saveFlags.name == name)
				{
					return saveFlags.value;
				}
			}
			return false;
		}

		// Token: 0x06000E34 RID: 3636 RVA: 0x00056540 File Offset: 0x00054740
		public StatManager.SaveData.RoachBank GetRoachBankByID(string id)
		{
			if (this.banks == null)
			{
				this.banks = new List<StatManager.SaveData.RoachBank>();
			}
			foreach (StatManager.SaveData.RoachBank roachBank in this.banks)
			{
				if (roachBank.id == id)
				{
					return roachBank;
				}
			}
			Debug.Log("No bank found, creating new one: " + id);
			StatManager.SaveData.RoachBank roachBank2 = new StatManager.SaveData.RoachBank();
			roachBank2.id = id;
			this.banks.Add(roachBank2);
			return roachBank2;
		}

		// Token: 0x0400106C RID: 4204
		public int version = 3;

		// Token: 0x0400106D RID: 4205
		public StatManager.GameStats gameStats;

		// Token: 0x0400106E RID: 4206
		public List<StatManager.SaveData.GameModeData> gameModes;

		// Token: 0x0400106F RID: 4207
		public List<StatManager.SaveData.SaveFlags> flags;

		// Token: 0x04001070 RID: 4208
		public List<StatManager.SaveData.RoachBank> banks;

		// Token: 0x04001071 RID: 4209
		public List<LevelStat> levelStats;

		// Token: 0x04001072 RID: 4210
		public int xp;

		// Token: 0x02000311 RID: 785
		[Serializable]
		public class GameModeData
		{
			// Token: 0x04001315 RID: 4885
			public string name;

			// Token: 0x04001316 RID: 4886
			public StatManager.GameStats stats;
		}

		// Token: 0x02000312 RID: 786
		[Serializable]
		public class SaveFlags
		{
			// Token: 0x04001317 RID: 4887
			public string name;

			// Token: 0x04001318 RID: 4888
			public bool value;

			// Token: 0x04001319 RID: 4889
			public string data;
		}

		// Token: 0x02000313 RID: 787
		[Serializable]
		public class RoachBank
		{
			// Token: 0x0400131A RID: 4890
			public string id;

			// Token: 0x0400131B RID: 4891
			public int value;
		}
	}
}
