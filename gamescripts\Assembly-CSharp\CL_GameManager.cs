﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using UnityEngine;
using UnityEngine.SceneManagement;

// Token: 0x0200001A RID: 26
[DefaultExecutionOrder(-9)]
public class CL_GameManager : MonoBehaviour
{
	// Token: 0x17000004 RID: 4
	// (get) Token: 0x060000C0 RID: 192 RVA: 0x00007460 File Offset: 0x00005660
	// (set) Token: 0x060000C1 RID: 193 RVA: 0x00007467 File Offset: 0x00005667
	public static bool showCursor
	{
		get
		{
			return CL_GameManager.m_showCursor;
		}
		set
		{
			CL_GameManager.m_showCursor = value;
			InputManager.UpdateCursorVisibility();
		}
	}

	// Token: 0x060000C2 RID: 194 RVA: 0x00007474 File Offset: 0x00005674
	private void Awake()
	{
		CultureInfo.DefaultThreadCurrentCulture = CultureInfo.InvariantCulture;
		CultureInfo.DefaultThreadCurrentUICulture = CultureInfo.InvariantCulture;
		CL_GameManager.gMan = this;
		CL_GameManager.dead = false;
		CL_AssetManager.InitializeAssetManager();
		if (CL_GameManager.gamemode == null)
		{
			this.baseGamemode = this.defaultGamemode;
			CL_GameManager.gamemode = this.defaultGamemode;
		}
		else
		{
			this.baseGamemode = CL_GameManager.gamemode;
		}
		this.GrabSavedRoaches();
		string text = "CURRENT GAMEMODE: ";
		M_Gamemode m_Gamemode = CL_GameManager.gamemode;
		CommandConsole.Log(text + ((m_Gamemode != null) ? m_Gamemode.ToString() : null), false);
		if (this.localPlayer)
		{
			this.playerTransform = this.localPlayer.transform;
		}
		ENT_Player.playerObject == null;
		if (this.progressionManager != null)
		{
			this.progressionManager.Initialize();
		}
		this.baseGamemode.Initialize(CL_GameManager.gamemodeArgs.ToArray());
		if (WorldLoader.instance != null && WorldLoader.instance.enabled)
		{
			WorldLoader.instance.Initialize();
		}
		CL_GameManager.gamemodeArgs.Clear();
		if (CL_GameManager.IsHardmode())
		{
			CL_GameManager.currentDifficulty += 1f;
		}
	}

	// Token: 0x060000C3 RID: 195 RVA: 0x0000759C File Offset: 0x0000579C
	private void Start()
	{
		SpeedrunManager.Initialize();
		Application.targetFrameRate = SettingsManager.settings.targetFramerate;
		if (this.localPlayer)
		{
			base.StartCoroutine(this.LoadIn());
			StatManager.sessionStats.UpdateStatistic("player-ascent", 0f, StatManager.Statistic.DataType.Float, StatManager.Statistic.DisplayType.Meters);
			this.previousHighScore = 0f;
			try
			{
				if (CL_GameManager.gamemode == null)
				{
					this.previousHighScore = (float)StatManager.saveData.gameStats.GetStatistic("score").GetValue();
				}
				else
				{
					this.previousHighScore = (float)StatManager.saveData.GetGameMode(CL_GameManager.GetGamemodeName(true, false)).stats.GetStatistic("score").GetValue();
				}
			}
			catch
			{
			}
			this.entityPrefabDictionary = new Dictionary<string, GameEntity>();
			foreach (GameEntity gameEntity in this.gameEntityPrefabs)
			{
				this.entityPrefabDictionary.Add(gameEntity.name.ToLower(), gameEntity);
			}
			CommandConsole.AddCommand("targetframerate", new Action<string[]>(this.SetTargetFramerate), false);
			CommandConsole.AddCommand("load", new Action<string[]>(this.LoadScene), true);
			CommandConsole.AddCommand("loadlevels", new Action<string[]>(this.LoadLevels), true);
			CommandConsole.AddCommand("listentityprefabs", new Action<string[]>(this.ListEntities), true);
			CommandConsole.AddCommand("listlevels", new Action<string[]>(this.ListLevels), true);
			CommandConsole.AddCommand("spawnentity", new Action<string[]>(this.SpawnEntity), true);
			CommandConsole.AddCommand("notarget", new Action<string[]>(this.NoTarget), true);
			CommandConsole.AddCommand("teleportplayer", new Action<string[]>(this.TeleportPlayer), true);
			CommandConsole.AddCommand("setroaches", new Action<string[]>(this.SetRoaches), true);
			CommandConsole.AddCommand("listflags", new Action<string[]>(this.ListFlags), true);
			CommandConsole.AddCommand("offsetworld", new Action<string[]>(this.OffsetWorldCommand), true);
			CommandConsole.AddCommand("timescale", new Action<string[]>(this.TimescaleCommand), true);
			AudioListener.pause = false;
		}
		if (StatManager.saveData != null)
		{
			if (StatManager.saveData.flags == null)
			{
				StatManager.saveData.flags = new List<StatManager.SaveData.SaveFlags>();
			}
			foreach (StatManager.SaveData.SaveFlags saveFlags in StatManager.saveData.flags)
			{
				CL_GameManager.SessionFlag sessionFlag = new CL_GameManager.SessionFlag
				{
					name = saveFlags.name,
					state = saveFlags.value,
					data = saveFlags.data
				};
				this.sessionFlags.Add(sessionFlag);
			}
		}
		if (CL_GameManager.HasActiveFlag("jackkill"))
		{
			CommandConsole.AddCommand("punishme", new Action<string[]>(this.SpawnJack), true);
		}
	}

	// Token: 0x060000C4 RID: 196 RVA: 0x000078B0 File Offset: 0x00005AB0
	private void OnEnable()
	{
		CL_GameManager.gMan = this;
	}

	// Token: 0x060000C5 RID: 197 RVA: 0x000078B8 File Offset: 0x00005AB8
	private void OnDisable()
	{
		this.isPaused = false;
		CL_GameManager.showCursor = true;
	}

	// Token: 0x060000C6 RID: 198 RVA: 0x000078C7 File Offset: 0x00005AC7
	private IEnumerator LoadIn()
	{
		this.isPaused = true;
		Time.timeScale = 0f;
		CL_GameManager.SetLoading(true);
		this.uiMan.SetFadeAmount(1f);
		CL_GameManager.showCursor = false;
		yield return new WaitForSecondsRealtime(this.loadTimer);
		if (WorldLoader.instance != null && WorldLoader.instance.gameObject.activeInHierarchy && WorldLoader.instance.enabled)
		{
			while (!WorldLoader.isLoaded)
			{
				yield return null;
			}
		}
		this.uiMan.FadeIn();
		Time.timeScale = 1f;
		CL_GameManager.SetLoading(false);
		yield return new WaitForSecondsRealtime(1f);
		if (CL_GameManager.gamemode == null)
		{
			this.uiMan.header.ShowText("ASCEND");
		}
		else if (CL_GameManager.IsHardmode())
		{
			this.uiMan.header.ShowText("<color=red>" + CL_GameManager.gamemode.introText);
		}
		else
		{
			this.uiMan.header.ShowText(CL_GameManager.gamemode.introText);
		}
		yield break;
	}

	// Token: 0x060000C7 RID: 199 RVA: 0x000078D8 File Offset: 0x00005AD8
	private void Update()
	{
		if (!Application.isFocused)
		{
			if (!SettingsManager.settings.vsync)
			{
				Application.targetFrameRate = 25;
			}
		}
		else if (!SettingsManager.settings.vsync)
		{
			Application.targetFrameRate = SettingsManager.GetTargetFramerate();
		}
		if (this.loadTimer > 0f)
		{
			this.loadTimer -= Time.deltaTime;
			return;
		}
		if (this.canPause && !OS_Manager.inUse && !this.loading && !this.reviving && InputManager.GetButton("Pause").Down)
		{
			if (!this.isPaused)
			{
				this.Pause();
			}
			else
			{
				this.UnPause();
			}
		}
		if (CL_UIManager.debug && DebugMenu.initialized)
		{
			DebugMenu.UpdateDebugText("player-experience", "<color=yellow>Player Experience: " + CL_ProgressionManager.playerExperience.ToString());
		}
		if ((Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift)) && Input.GetKeyDown(KeyCode.F12))
		{
			Screenshot.TakeScreenshot(3, "");
		}
		if (Input.GetKey(KeyCode.LeftControl) && Input.GetKeyDown(KeyCode.Semicolon))
		{
			this.RestartScene();
		}
		if (CL_GameManager.dead)
		{
			Time.timeScale = Mathf.Lerp(Time.timeScale, 0f, Time.deltaTime * 1f);
			return;
		}
		float num = this.playerAscent * this.ascentRate;
		if (this.uiMan != null && this.uiMan.scoreTracker != null)
		{
			this.uiMan.scoreTracker.text = string.Format("Score: {0}", Mathf.RoundToInt(num));
			this.uiMan.ascentTracker.text = string.Format("Ascent: {0}", Math.Round((double)this.playerAscent, 2));
			this.uiMan.ascentRateTracker.text = string.Format("Ascent Rate: {0}", Math.Round((double)this.ascentRate, 2));
			if (this.previousHighScore > num)
			{
				this.uiMan.highScoreTracker.text = string.Format("High Score: {0}", Mathf.RoundToInt(this.previousHighScore));
			}
			else
			{
				this.uiMan.highScoreTracker.text = string.Format("High Score: {0}", Mathf.RoundToInt(num));
			}
		}
		if (!this.isPaused && !this.loading)
		{
			if (this.allowScores)
			{
				float playerCorrectedHeight = this.GetPlayerCorrectedHeight();
				if (playerCorrectedHeight > this.playerAscent)
				{
					StatManager.sessionStats.UpdateStatistic("player-ascent", this.playerAscent, StatManager.Statistic.DataType.Float, StatManager.Statistic.DisplayType.Meters);
					this.playerAscent = this.GetPlayerCorrectedHeight();
				}
				if (!CL_GameManager.GetBaseGamemode().HasTag("hideascenttracker"))
				{
					if (this.highScoreCooldown <= 0f && num > this.previousHighScore && !this.achievedHighScore && this.previousHighScore > 50f && playerCorrectedHeight > 50f)
					{
						this.achievedHighScore = true;
						AudioManager.PlaySound(this.highScoreClip, base.transform.position, 0.5f, 1f, 0f, false, 1f, null);
						this.uiMan.highscoreHeader.ShowText(string.Concat(new string[]
						{
							"BEST ASCENT - ",
							Mathf.Round(playerCorrectedHeight).ToString(),
							" METERS AT ",
							Math.Round((double)this.ascentRate, 2).ToString(),
							"M/S"
						}));
						this.highScoreCooldown = 5f;
					}
					if (this.highScoreCooldown > 0f)
					{
						this.highScoreCooldown -= Time.deltaTime;
					}
				}
				StatManager.sessionStats.UpdateStatistic("ascent-rate", this.ascentRate, StatManager.Statistic.DataType.Float, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Speed, StatManager.Statistic.ModType.Max);
				StatManager.sessionStats.UpdateStatistic("game-time", this.gameTime, StatManager.Statistic.DataType.Float, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Time, StatManager.Statistic.ModType.Add);
				DebugMenu.UpdateDebugText("ascentRate", "ascent Rate: " + this.ascentRate.ToString());
			}
			if (this.runTimer)
			{
				this.gameTime += Time.deltaTime;
				this.ascentRate = this.playerAscent / this.gameTime;
			}
		}
	}

	// Token: 0x060000C8 RID: 200 RVA: 0x00007D20 File Offset: 0x00005F20
	public void UnPause()
	{
		if (!CL_GameManager.dead)
		{
			Cursor.lockState = CursorLockMode.Locked;
			CL_GameManager.showCursor = false;
			Time.timeScale = 1f;
			this.isPaused = false;
		}
		this.pauseMenu.SetActive(false);
		AudioListener.pause = false;
		CommandConsole.HideConsole();
	}

	// Token: 0x060000C9 RID: 201 RVA: 0x00007D5D File Offset: 0x00005F5D
	public void Pause()
	{
		Cursor.lockState = CursorLockMode.None;
		CL_GameManager.showCursor = true;
		this.pauseMenu.SetActive(true);
		this.isPaused = true;
		AudioListener.pause = true;
		Time.timeScale = 0f;
	}

	// Token: 0x060000CA RID: 202 RVA: 0x00007D90 File Offset: 0x00005F90
	public void Die(string type = "")
	{
		CL_GameManager.<>c__DisplayClass64_0 CS$<>8__locals1 = new CL_GameManager.<>c__DisplayClass64_0();
		CS$<>8__locals1.<>4__this = this;
		if (CL_GameManager.dead)
		{
			return;
		}
		StatManager.sessionStats.UpdateStatistic("deaths", 1, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Add, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
		CS$<>8__locals1.save = CL_SaveManager.GetMostRecentSaveStateByType(CL_SaveManager.SaveState.SaveType.disk);
		if (CS$<>8__locals1.save != null)
		{
			base.StartCoroutine(CS$<>8__locals1.<Die>g__ReviveSequence|0());
			return;
		}
		CL_GameManager.curDeathType = this.deathTypes[0];
		foreach (CL_GameManager.DeathType deathType in this.deathTypes)
		{
			if (type == deathType.name)
			{
				CL_GameManager.curDeathType = deathType;
			}
		}
		this.statManager.GetScoreScreen(false).SetDeathText(CL_GameManager.curDeathType.deathText);
		this.statManager.GetScoreScreen(false).SetTip(this.GetReleventTip());
		base.StartCoroutine(this.EndGameSequence(false));
		this.localPlayer.KillEffects(type);
	}

	// Token: 0x060000CB RID: 203 RVA: 0x00007EA0 File Offset: 0x000060A0
	public void Win()
	{
		base.StartCoroutine(this.EndGameSequence(true));
	}

	// Token: 0x060000CC RID: 204 RVA: 0x00007EB0 File Offset: 0x000060B0
	private IEnumerator EndGameSequence(bool win = false)
	{
		CL_GameManager.dead = true;
		Time.timeScale = 0.1f;
		this.gameTime = (float)Math.Round((double)this.gameTime, 2, MidpointRounding.AwayFromZero);
		float timeDifference = 0f;
		if (CL_GameManager.gamemode.GetGamemodeSaveData() != null && CL_GameManager.gamemode.GetGamemodeSaveData().stats.HasStatistic("best-time"))
		{
			float statisticFloat = StatManager.GetStatisticFloat(CL_GameManager.gamemode.GetGamemodeSaveData().stats, "best-time");
			timeDifference = this.gameTime - statisticFloat;
		}
		Debug.Log("Gametime: " + this.gameTime.ToString());
		this.uiMan.SetFadeTarget(1f);
		yield return new WaitForSecondsRealtime(1f);
		this.isPaused = true;
		CL_GameManager.gamemode.Finish(this.gameTime, win);
		this.EndGameStatUpdate(win);
		yield return null;
		this.statManager.ShowScoreScreen(win);
		if (win && timeDifference < 0f && CL_GameManager.gamemode.timeGoal)
		{
			this.statManager.GetScoreScreen(win).PlayTimeReductionAnimation(timeDifference);
		}
		CL_GameTracker.SaveSession();
		Cursor.lockState = CursorLockMode.None;
		CL_GameManager.showCursor = true;
		yield return new WaitForSecondsRealtime(0.5f);
		CL_ProgressionManager.instance.deathList.Check();
		float fadeOut = 1f;
		AudioManager.FadeOutMusic();
		AudioManager.instance.gameMixer.audioMixer.GetFloat("MasterVolume", out fadeOut);
		while (fadeOut > -80f)
		{
			fadeOut -= Time.unscaledDeltaTime * 10f;
			AudioManager.instance.gameMixer.audioMixer.SetFloat("MasterVolume", fadeOut);
			AudioManager.instance.gameMixer.audioMixer.SetFloat("AmbienceVolume", fadeOut);
			AudioManager.instance.gameMixer.audioMixer.SetFloat("EffectsVolume", fadeOut);
			yield return null;
		}
		yield break;
	}

	// Token: 0x060000CD RID: 205 RVA: 0x00007EC8 File Offset: 0x000060C8
	private void EndGameStatUpdate(bool win = false)
	{
		StatManager.sessionStats.UpdateStatistic("death-height", this.GetPlayerCorrectedHeight(), StatManager.Statistic.DataType.Float, StatManager.Statistic.ModType.Max, StatManager.Statistic.DisplayType.Meters, StatManager.Statistic.ModType.Max);
		float playerScore = CL_GameManager.gamemode.GetPlayerScore(win);
		StatManager.sessionStats.UpdateStatistic("score", playerScore, StatManager.Statistic.DataType.Float, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Max);
		StatManager.sessionStats.UpdateStatistic("highest-ascent", this.playerAscent, StatManager.Statistic.DataType.Float, StatManager.Statistic.ModType.Max, StatManager.Statistic.DisplayType.Meters, StatManager.Statistic.ModType.Max);
		StatManager.sessionStats.UpdateStatistic("runs", 1, StatManager.Statistic.DataType.Int);
		StatManager.sessionStats.UpdateStatistic("combined-ascent-rate", this.ascentRate, StatManager.Statistic.DataType.Float, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Speed, StatManager.Statistic.ModType.Add);
		if (StatManager.saveData.gameStats.HasStatistic("combined-ascent-rate") && StatManager.saveData.gameStats.HasStatistic("runs"))
		{
			float num = ((float)StatManager.saveData.gameStats.GetStatistic("combined-ascent-rate").GetValue() + this.ascentRate) / (float)((int)StatManager.saveData.gameStats.GetStatistic("runs").GetValue() + 1);
			StatManager.sessionStats.UpdateStatistic("average-ascent-rate", num, StatManager.Statistic.DataType.Float, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Speed, StatManager.Statistic.ModType.Set);
		}
		else
		{
			StatManager.sessionStats.UpdateStatistic("average-ascent-rate", this.ascentRate, StatManager.Statistic.DataType.Float, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Speed, StatManager.Statistic.ModType.Set);
		}
		StatManager.sessionStats.UpdateStatistic("game-time", this.gameTime, StatManager.Statistic.DataType.Float, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Time, StatManager.Statistic.ModType.Add);
		if (win)
		{
			StatManager.sessionStats.UpdateStatistic("wins", 1, StatManager.Statistic.DataType.Int);
			if (CL_GameManager.gamemode.timeGoal)
			{
				StatManager.sessionStats.UpdateStatistic("best-time", this.gameTime, StatManager.Statistic.DataType.Float, StatManager.Statistic.ModType.Min, StatManager.Statistic.DisplayType.Time, StatManager.Statistic.ModType.Min);
			}
		}
	}

	// Token: 0x060000CE RID: 206 RVA: 0x00008078 File Offset: 0x00006278
	public static bool isDead()
	{
		return CL_GameManager.dead;
	}

	// Token: 0x060000CF RID: 207 RVA: 0x00008080 File Offset: 0x00006280
	public void SetGamemode(M_Gamemode mode)
	{
		Debug.Log("Gamemode Set to " + ((mode != null) ? mode.ToString() : null));
		CL_GameManager.gamemode.SavePlayerAscent(this.playerAscent);
		CL_GameManager.gamemode = mode;
		if (mode.HasSavedPlayerInfo())
		{
			this.playerAscent = mode.GetSavedPlayerAscent();
		}
		if (this.baseGamemode == null || mode.baseGamemode)
		{
			this.baseGamemode = CL_GameManager.gamemode;
		}
	}

	// Token: 0x060000D0 RID: 208 RVA: 0x000080F4 File Offset: 0x000062F4
	public void LoadScene(string l, bool async = false)
	{
		if (async)
		{
			SceneManager.LoadSceneAsync(l);
			return;
		}
		SceneManager.LoadScene(l);
	}

	// Token: 0x060000D1 RID: 209 RVA: 0x00008107 File Offset: 0x00006307
	public void LoadScene(string[] args)
	{
		SceneManager.LoadScene(args[0]);
	}

	// Token: 0x060000D2 RID: 210 RVA: 0x00008114 File Offset: 0x00006314
	public void LoadLevels(string[] args)
	{
		if (args.Length == 0)
		{
			CommandConsole.Log("Error: Incorrect Formatting. Use level names as arguments, one for each level.", false);
			return;
		}
		CL_GameManager.gamemodeArgs.AddRange(args);
		M_Gamemode gamemodeAsset = CL_AssetManager.GetGamemodeAsset("GM_Level_Tester", "");
		this.SetGamemode(gamemodeAsset);
		this.baseGamemode = gamemodeAsset;
		SceneManager.LoadScene("Game-Main");
	}

	// Token: 0x060000D3 RID: 211 RVA: 0x00008164 File Offset: 0x00006364
	public void RestartScene()
	{
		CL_GameManager.dead = false;
		CL_GameManager.gamemode = this.baseGamemode;
		SceneManager.LoadScene(SceneManager.GetActiveScene().name);
	}

	// Token: 0x060000D4 RID: 212 RVA: 0x00008194 File Offset: 0x00006394
	public static void SetLoading(bool b)
	{
		CL_UIManager.SetLoading(b);
		CL_GameManager.gMan.loading = b;
		if (b)
		{
			Time.timeScale = 0f;
			CL_GameManager.gMan.isPaused = true;
			return;
		}
		Time.timeScale = 1f;
		CL_GameManager.gMan.isPaused = false;
	}

	// Token: 0x060000D5 RID: 213 RVA: 0x000081E0 File Offset: 0x000063E0
	public static void SetGameFlag(string name, bool state, string data = "", bool save = false)
	{
		foreach (CL_GameManager.SessionFlag sessionFlag in CL_GameManager.gMan.sessionFlags)
		{
			if (sessionFlag.name == name)
			{
				sessionFlag.state = state;
				sessionFlag.data = data;
				return;
			}
		}
		CL_GameManager.gMan.sessionFlags.Add(new CL_GameManager.SessionFlag
		{
			name = name,
			state = state,
			data = data
		});
		if (save)
		{
			StatManager.saveData.SaveFlag(name, state, data);
		}
	}

	// Token: 0x060000D6 RID: 214 RVA: 0x00008288 File Offset: 0x00006488
	public static CL_GameManager.SessionFlag GetGameFlag(string name)
	{
		foreach (CL_GameManager.SessionFlag sessionFlag in CL_GameManager.gMan.sessionFlags)
		{
			if (sessionFlag.name == name)
			{
				return sessionFlag;
			}
		}
		return null;
	}

	// Token: 0x060000D7 RID: 215 RVA: 0x000082F0 File Offset: 0x000064F0
	public static bool HasActiveFlag(string name)
	{
		foreach (CL_GameManager.SessionFlag sessionFlag in CL_GameManager.gMan.sessionFlags)
		{
			if (sessionFlag.name == name)
			{
				return sessionFlag.state;
			}
		}
		if (StatManager.saveData == null)
		{
			return false;
		}
		foreach (StatManager.SaveData.SaveFlags saveFlags in StatManager.saveData.flags)
		{
			if (saveFlags.name == name)
			{
				return saveFlags.value;
			}
		}
		return false;
	}

	// Token: 0x060000D8 RID: 216 RVA: 0x000083BC File Offset: 0x000065BC
	public static void ChangeState(string s)
	{
		CommandConsole.Log("State Change: " + s, false);
		if (s == "die")
		{
			CL_GameManager.gMan.Die("");
			return;
		}
		if (s == "win")
		{
			CL_GameManager.gMan.Win();
			CommandConsole.Log("Game Ended", false);
			return;
		}
		if (!(s == "restart"))
		{
			return;
		}
		CL_GameManager.gMan.RestartScene();
	}

	// Token: 0x060000D9 RID: 217 RVA: 0x00008434 File Offset: 0x00006634
	public static void AddRoaches(int i)
	{
		CL_GameManager.roaches = Mathf.Clamp(CL_GameManager.roaches + i, 0, 999);
		Debug.Log(CL_GameManager.roaches);
		StatManager.GetStatisticInt(StatManager.sessionStats, "total-banked-roaches");
		if (!SettingsManager.settings.g_competitive)
		{
			StatManager.sessionStats.UpdateStatistic("bank-roaches", CL_GameManager.roaches, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Set);
		}
		if (i > 0)
		{
			StatManager.sessionStats.UpdateStatistic("total-banked-roaches", i, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Add, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
		}
	}

	// Token: 0x060000DA RID: 218 RVA: 0x000084C0 File Offset: 0x000066C0
	private void GrabSavedRoaches()
	{
		if (this.baseGamemode != null)
		{
			int num = 0;
			if (SettingsManager.settings != null && !SettingsManager.settings.g_competitive && this.baseGamemode.saveRoaches)
			{
				num = StatManager.saveData.GetRoachBankByID(CL_GameManager.IsHardmode() ? (CL_GameManager.gamemode.roachBankID + "-hard") : CL_GameManager.gamemode.roachBankID).value;
			}
			Debug.Log("Pulling roaches: " + num.ToString());
			CL_GameManager.roaches = num;
			return;
		}
		Debug.Log("No base gamemode found. Resetting roaches.");
		CL_GameManager.roaches = 0;
	}

	// Token: 0x060000DB RID: 219 RVA: 0x00008561 File Offset: 0x00006761
	public void SetTargetFramerate(int fps)
	{
		SettingsManager.settings.targetFramerate = fps;
		Application.targetFrameRate = fps;
	}

	// Token: 0x060000DC RID: 220 RVA: 0x00008574 File Offset: 0x00006774
	public void SetTargetFramerate(string[] args)
	{
		if (args.Length == 0)
		{
			CommandConsole.Log("Current Framerate is " + Application.targetFrameRate.ToString(), false);
			return;
		}
		int targetFrameRate = Application.targetFrameRate;
		if (int.TryParse(args[0], out targetFrameRate))
		{
			Application.targetFrameRate = targetFrameRate;
			return;
		}
		CommandConsole.Log("Error: Framerate needs to be an integer", false);
	}

	// Token: 0x060000DD RID: 221 RVA: 0x000085C7 File Offset: 0x000067C7
	public FXManager GetFXManager()
	{
		return this.fxMan;
	}

	// Token: 0x060000DE RID: 222 RVA: 0x000085CF File Offset: 0x000067CF
	public void TransitionFade(float waitTime)
	{
		base.StartCoroutine("TransitionCoroutine", waitTime);
	}

	// Token: 0x060000DF RID: 223 RVA: 0x000085E3 File Offset: 0x000067E3
	private IEnumerator TransitionCoroutine(float waitTime)
	{
		this.lockPlayer = true;
		this.uiMan.FadeOut();
		yield return new WaitForSeconds(waitTime);
		this.uiMan.FadeIn();
		this.lockPlayer = false;
		yield break;
	}

	// Token: 0x060000E0 RID: 224 RVA: 0x000085F9 File Offset: 0x000067F9
	public float PlayerDistance(Vector3 location)
	{
		if (this.localPlayer == null)
		{
			return float.PositiveInfinity;
		}
		return Vector3.Distance(this.localPlayer.transform.position, location);
	}

	// Token: 0x060000E1 RID: 225 RVA: 0x00008625 File Offset: 0x00006825
	public float PlayerHeightDistance(Vector3 location)
	{
		if (!this.localPlayer)
		{
			return 0f;
		}
		return Mathf.Abs(this.localPlayer.transform.position.y - location.y);
	}

	// Token: 0x060000E2 RID: 226 RVA: 0x0000865B File Offset: 0x0000685B
	public float GetPlayerHeight()
	{
		if (!this.localPlayer)
		{
			return 0f;
		}
		return this.localPlayer.transform.position.y;
	}

	// Token: 0x060000E3 RID: 227 RVA: 0x00008685 File Offset: 0x00006885
	public float GetPlayerCorrectedHeight()
	{
		if (!this.localPlayer)
		{
			return 0f;
		}
		return this.localPlayer.transform.position.y - this.currentWorldOffset;
	}

	// Token: 0x060000E4 RID: 228 RVA: 0x000086B6 File Offset: 0x000068B6
	public void TimescaleCommand(string[] args)
	{
		if (args.Length < 1)
		{
			Time.timeScale = 1f;
			return;
		}
		Time.timeScale = float.Parse(args[0]);
	}

	// Token: 0x060000E5 RID: 229 RVA: 0x000086D6 File Offset: 0x000068D6
	private void OffsetWorldCommand(string[] args)
	{
		if (args.Length < 1)
		{
			CommandConsole.Log("Error: Incorrect Formatting. Height Required.", false);
			return;
		}
		this.SetWorldOffset(float.Parse(args[0]));
	}

	// Token: 0x060000E6 RID: 230 RVA: 0x000086F8 File Offset: 0x000068F8
	public void SetWorldOffset(float amount)
	{
		if (WorldLoader.initialized)
		{
			WorldLoader.instance.SetOffset(amount);
		}
		if (AstarPath.active != null)
		{
			AstarPath.active.Scan(null);
		}
		GameEntity.OffsetEntities(amount);
		if (DarkMachineFunctions.offsetAction != null)
		{
			DarkMachineFunctions.offsetAction(amount);
		}
		this.currentWorldOffset += amount;
	}

	// Token: 0x060000E7 RID: 231 RVA: 0x00008755 File Offset: 0x00006955
	public void ShakeCam(float amount)
	{
		this.localPlayer.camRoot.ShakeCamera(amount);
	}

	// Token: 0x060000E8 RID: 232 RVA: 0x00008768 File Offset: 0x00006968
	public void TeleportPlayer(Vector3 pos)
	{
		this.localPlayer.Teleport(pos);
	}

	// Token: 0x060000E9 RID: 233 RVA: 0x00008776 File Offset: 0x00006976
	public void TeleportPlayer(Vector3 pos, Quaternion rot)
	{
		this.localPlayer.Teleport(pos, rot, false);
	}

	// Token: 0x060000EA RID: 234 RVA: 0x00008786 File Offset: 0x00006986
	public void TeleportPlayer(string[] args)
	{
		if (args.Length < 3)
		{
			CommandConsole.Log("Error: Incorrect Formatting. Use teleportplayer x y z.", false);
			return;
		}
		this.localPlayer.Teleport(new Vector3(float.Parse(args[0]), float.Parse(args[1]), float.Parse(args[2])));
	}

	// Token: 0x060000EB RID: 235 RVA: 0x000087C2 File Offset: 0x000069C2
	public void SetRoaches(string[] args)
	{
		if (args.Length < 1)
		{
			CommandConsole.Log("Error: Incorrect Formatting. Use setroaches X", false);
			return;
		}
		CL_GameManager.roaches = int.Parse(args[0]);
	}

	// Token: 0x060000EC RID: 236 RVA: 0x000087E3 File Offset: 0x000069E3
	public float GetGameTime()
	{
		return this.gameTime;
	}

	// Token: 0x060000ED RID: 237 RVA: 0x000087EC File Offset: 0x000069EC
	public static string GetGamemodeName(bool includeOptions = true, bool mainMode = false)
	{
		string text = CL_GameManager.gamemode.GetGamemodeName(includeOptions);
		if (mainMode)
		{
			text = CL_GameManager.gMan.baseGamemode.GetGamemodeName(includeOptions);
			Debug.Log("Getting the main mode: " + text);
		}
		return text;
	}

	// Token: 0x060000EE RID: 238 RVA: 0x0000882A File Offset: 0x00006A2A
	public static M_Gamemode GetCurrentGamemode()
	{
		return CL_GameManager.gamemode;
	}

	// Token: 0x060000EF RID: 239 RVA: 0x00008831 File Offset: 0x00006A31
	public static M_Gamemode GetBaseGamemode()
	{
		return CL_GameManager.gMan.baseGamemode;
	}

	// Token: 0x060000F0 RID: 240 RVA: 0x0000883D File Offset: 0x00006A3D
	public void SetTimerState(bool b)
	{
		this.runTimer = b;
	}

	// Token: 0x060000F1 RID: 241 RVA: 0x00008846 File Offset: 0x00006A46
	public float GetPlayerAscent()
	{
		return this.playerAscent;
	}

	// Token: 0x060000F2 RID: 242 RVA: 0x0000884E File Offset: 0x00006A4E
	public static bool IsLoading()
	{
		return CL_GameManager.gMan.loading;
	}

	// Token: 0x060000F3 RID: 243 RVA: 0x0000885C File Offset: 0x00006A5C
	public void ListEntities(string[] args)
	{
		foreach (GameEntity gameEntity in this.gameEntityPrefabs)
		{
			Item_Object component = gameEntity.GetComponent<Item_Object>();
			if (!(component != null) || component.itemData.IsUnlocked())
			{
				CommandConsole.Log("Game Entity : " + gameEntity.name.ToLower(), false);
			}
		}
	}

	// Token: 0x060000F4 RID: 244 RVA: 0x000088E0 File Offset: 0x00006AE0
	public void ListLevels(string[] args)
	{
		foreach (GameObject gameObject in CL_AssetManager.GetFullCombinedAssetDatabase().levelPrefabs)
		{
			M_Level component = gameObject.GetComponent<M_Level>();
			if (!(component != null) || component.showInLevelList)
			{
				CommandConsole.Log("Level : " + gameObject.name.ToLower(), false);
			}
		}
	}

	// Token: 0x060000F5 RID: 245 RVA: 0x00008964 File Offset: 0x00006B64
	public void NoTarget(string[] args)
	{
		if (args.Length == 0)
		{
			CL_GameManager.noTarget = !CL_GameManager.noTarget;
		}
		else
		{
			bool flag;
			if (!bool.TryParse(args[0], out flag))
			{
				CommandConsole.Log("Unable to parse " + args[0] + " arg needs to be a boolean (true/false/0/1).", false);
				return;
			}
			CL_GameManager.noTarget = Convert.ToBoolean(args[0]);
		}
		CommandConsole.Log("Notarget set to " + CL_GameManager.noTarget.ToString(), false);
	}

	// Token: 0x060000F6 RID: 246 RVA: 0x000089D4 File Offset: 0x00006BD4
	private void ListFlags(string[] obj)
	{
		foreach (CL_GameManager.SessionFlag sessionFlag in this.sessionFlags)
		{
			CommandConsole.Log(string.Format("Flag: {0} -- {1}", sessionFlag.name, sessionFlag.state), false);
		}
	}

	// Token: 0x060000F7 RID: 247 RVA: 0x00008A44 File Offset: 0x00006C44
	public void SpawnEntity(string[] args)
	{
		if (args.Length == 0)
		{
			CommandConsole.Log("Entity Must Have Name.", false);
			return;
		}
		if (!this.entityPrefabDictionary.ContainsKey(args[0]))
		{
			CommandConsole.Log("No entity with the name '" + args[0] + "' exists.", false);
			return;
		}
		Vector3 vector = Camera.main.transform.position + Camera.main.transform.forward;
		Quaternion quaternion = Quaternion.identity;
		RaycastHit raycastHit;
		if (Physics.Raycast(Camera.main.transform.position, Camera.main.transform.forward, out raycastHit, float.PositiveInfinity))
		{
			vector = raycastHit.point;
			quaternion = Quaternion.LookRotation(raycastHit.normal);
			GameEntity gameEntity = Object.Instantiate<GameEntity>(this.entityPrefabDictionary[args[0].ToLower()], vector, quaternion);
			if (gameEntity.spawnWithBounds)
			{
				Collider component = gameEntity.GetComponent<Collider>();
				if (component != null)
				{
					Bounds bounds = component.bounds;
					Vector3 vector2 = raycastHit.normal * bounds.extents.magnitude;
					gameEntity.transform.position += vector2;
					return;
				}
			}
		}
		else
		{
			Object.Instantiate<GameObject>(this.entityPrefabDictionary[args[0].ToLower()].gameObject, vector, quaternion);
		}
	}

	// Token: 0x060000F8 RID: 248 RVA: 0x00008B90 File Offset: 0x00006D90
	public void SpawnJack(string[] args)
	{
		if (args.Length == 0 || args[0] == "")
		{
			CommandConsole.Log("Incorrect Format, include difficulty:\n<color=green>0 (EASY)</color>, 1 (NORMAL), <color=yellow>2 (HARD)</color>, <color=red>3 (NIGHTMARE)</color>. \nExample: punishme 3", false);
			return;
		}
		GameObject gameObject = Object.Instantiate<GameObject>(CL_AssetManager.GetAssetGameObject("DEN_Jack", ""), ENT_Player.playerObject.transform.position + Vector3.up * -40f, Quaternion.identity);
		int num = int.Parse(args[0]);
		DEN_SimpleChaseKiller component = gameObject.GetComponent<DEN_SimpleChaseKiller>();
		switch (num)
		{
		case 0:
			component.speedIncreaseRate = 0f;
			component.speed = 3f;
			CommandConsole.Log("Jack Difficulty: <color=green>EASY</color>", false);
			break;
		case 1:
			component.speedIncreaseRate = 0.005f;
			component.speed = 5f;
			CommandConsole.Log("Jack Difficulty: MEDIUM", false);
			break;
		case 2:
			component.speedIncreaseRate = 0.01f;
			component.speed = 7f;
			CommandConsole.Log("Jack Difficulty: <color=yellow>HARD</color>", false);
			break;
		case 3:
			component.speedIncreaseRate = 0.03f;
			component.speed = 7f;
			component.ignoreCollisions = true;
			CommandConsole.Log("Jack Difficulty: <color=red>NIGHTMARE</color>", false);
			break;
		case 4:
			component.speedIncreaseRate = 0.06f;
			component.speed = 10f;
			component.ignoreCollisions = true;
			CommandConsole.Log("Jack <color=purple>SECRET</color> Difficulty: <color=purple>ULTRA-NIGHTMARE</color>\n<color=red>Good Luck. You will need it.</color>", false);
			break;
		case 5:
			Object.Destroy(gameObject);
			CommandConsole.Log("<color=purple>NO. WHY.</color> Go seek help. Please.", false);
			break;
		}
		CommandConsole.Log("<color=grey>Spawned the little freaky guy.</color> <color=red>Run.</color>", false);
	}

	// Token: 0x060000F9 RID: 249 RVA: 0x00008D0B File Offset: 0x00006F0B
	public bool IsReviving()
	{
		return this.reviving;
	}

	// Token: 0x060000FA RID: 250 RVA: 0x00008D14 File Offset: 0x00006F14
	public string GetReleventTip()
	{
		List<TipList> list = new List<TipList>();
		list.Add(this.generalTips);
		if (CL_GameManager.curDeathType != null && CL_GameManager.curDeathType.tipList != null)
		{
			list.Add(CL_GameManager.curDeathType.tipList);
		}
		if (this.localPlayer != null && CL_GameManager.curDeathType != null && this.localPlayer.GetLastHitSource() != CL_GameManager.curDeathType.name)
		{
			foreach (CL_GameManager.DeathType deathType in this.deathTypes)
			{
				if (this.localPlayer.GetLastHitSource() == deathType.name && deathType.tipList != null)
				{
					list.Add(deathType.tipList);
					break;
				}
			}
		}
		if (WorldLoader.initialized && WorldLoader.instance.GetCurrentLevel() != null)
		{
			if (WorldLoader.instance.GetCurrentLevel().level.subRegion != null && WorldLoader.instance.GetCurrentLevel().level.subRegion.tips != null)
			{
				list.AddRange(WorldLoader.instance.GetCurrentLevel().level.subRegion.tips);
			}
			if (WorldLoader.instance.GetCurrentLevel().level.tips != null)
			{
				list.AddRange(WorldLoader.instance.GetCurrentLevel().level.tips);
			}
		}
		return list[Random.Range(0, list.Count)].GetTip();
	}

	// Token: 0x060000FB RID: 251 RVA: 0x00008EBC File Offset: 0x000070BC
	public float GetPlayerAscentRate()
	{
		return this.ascentRate;
	}

	// Token: 0x060000FC RID: 252 RVA: 0x00008EC4 File Offset: 0x000070C4
	internal void SetGameTime(float f)
	{
		this.gameTime = f;
	}

	// Token: 0x060000FD RID: 253 RVA: 0x00008ECD File Offset: 0x000070CD
	public static bool IsSafe()
	{
		return CL_GameManager.gMan != null && CL_GameManager.gMan.isSafe && ENT_Player.playerObject != null;
	}

	// Token: 0x060000FE RID: 254 RVA: 0x00008EF8 File Offset: 0x000070F8
	public static void SetSafe(bool b)
	{
		if (CL_GameManager.gMan != null)
		{
			CL_GameManager.gMan.isSafe = b;
		}
		if (CL_GameManager.gMan.isSafe)
		{
			CL_UIManager.instance.safeAreaText.Show();
			return;
		}
		CL_UIManager.instance.safeAreaText.Hide();
	}

	// Token: 0x060000FF RID: 255 RVA: 0x00008F48 File Offset: 0x00007148
	public static bool AreAchievementsAllowed()
	{
		return CL_GameManager.gamemode.allowAchievements && CL_GameManager.gMan.baseGamemode.allowAchievements;
	}

	// Token: 0x06000100 RID: 256 RVA: 0x00008F6A File Offset: 0x0000716A
	public static bool IsHardmode()
	{
		if (CL_GameManager.GetCurrentGamemode() == null)
		{
			return SettingsManager.settings.g_hard;
		}
		return CL_GameManager.GetCurrentGamemode().allowHardmode && SettingsManager.settings.g_hard;
	}

	// Token: 0x040000A3 RID: 163
	[Header("Managers")]
	public static CL_GameManager gMan;

	// Token: 0x040000A4 RID: 164
	public FXManager fxMan;

	// Token: 0x040000A5 RID: 165
	public StatManager statManager;

	// Token: 0x040000A6 RID: 166
	public CL_ProgressionManager progressionManager;

	// Token: 0x040000A7 RID: 167
	public ENT_Player localPlayer;

	// Token: 0x040000A8 RID: 168
	public Transform playerSpawn;

	// Token: 0x040000A9 RID: 169
	private Transform playerTransform;

	// Token: 0x040000AA RID: 170
	public CL_UIManager uiMan;

	// Token: 0x040000AB RID: 171
	public float tickRate = 15f;

	// Token: 0x040000AC RID: 172
	[Header("Map Info")]
	public CL_CameraControl levelCam;

	// Token: 0x040000AD RID: 173
	public float gravity = -9f;

	// Token: 0x040000AE RID: 174
	[Header("Pause Vars")]
	public bool isPaused;

	// Token: 0x040000AF RID: 175
	public bool canPause;

	// Token: 0x040000B0 RID: 176
	[Header("GUI Win & Death")]
	public GameObject pauseMenu;

	// Token: 0x040000B1 RID: 177
	public GameObject settingsMenu;

	// Token: 0x040000B2 RID: 178
	public GameObject loadScreen;

	// Token: 0x040000B3 RID: 179
	public List<CL_GameManager.DeathType> deathTypes;

	// Token: 0x040000B4 RID: 180
	public static CL_GameManager.DeathType curDeathType;

	// Token: 0x040000B5 RID: 181
	public TipList generalTips;

	// Token: 0x040000B6 RID: 182
	[Header("Audio")]
	public AudioManager audMan;

	// Token: 0x040000B7 RID: 183
	public bool lockPlayer;

	// Token: 0x040000B8 RID: 184
	public bool freecam;

	// Token: 0x040000B9 RID: 185
	public bool lockPlayerInput;

	// Token: 0x040000BA RID: 186
	private float loadTimer = 0.5f;

	// Token: 0x040000BB RID: 187
	private static bool dead = false;

	// Token: 0x040000BC RID: 188
	private bool reviving;

	// Token: 0x040000BD RID: 189
	public List<GameEntity> gameEntityPrefabs;

	// Token: 0x040000BE RID: 190
	private Dictionary<string, GameEntity> entityPrefabDictionary;

	// Token: 0x040000BF RID: 191
	public static string gameType = "campaign";

	// Token: 0x040000C0 RID: 192
	private float playerAscent;

	// Token: 0x040000C1 RID: 193
	private float currentWorldOffset;

	// Token: 0x040000C2 RID: 194
	private float ascentRate;

	// Token: 0x040000C3 RID: 195
	private float gameTime;

	// Token: 0x040000C4 RID: 196
	private bool runTimer = true;

	// Token: 0x040000C5 RID: 197
	public AudioClip highScoreClip;

	// Token: 0x040000C6 RID: 198
	private float previousHighScore;

	// Token: 0x040000C7 RID: 199
	private bool achievedHighScore;

	// Token: 0x040000C8 RID: 200
	private float highScoreCooldown;

	// Token: 0x040000C9 RID: 201
	public static bool noTarget = false;

	// Token: 0x040000CA RID: 202
	public bool allowScores;

	// Token: 0x040000CB RID: 203
	private bool isSafe;

	// Token: 0x040000CC RID: 204
	public static M_Gamemode gamemode;

	// Token: 0x040000CD RID: 205
	public M_Gamemode defaultGamemode;

	// Token: 0x040000CE RID: 206
	private M_Gamemode baseGamemode;

	// Token: 0x040000CF RID: 207
	private CL_SaveManager.SessionSave saveToLoadOnStart;

	// Token: 0x040000D0 RID: 208
	public List<CL_GameManager.SessionFlag> sessionFlags = new List<CL_GameManager.SessionFlag>();

	// Token: 0x040000D1 RID: 209
	public static int roaches = 0;

	// Token: 0x040000D2 RID: 210
	public static float currentDifficulty = 0f;

	// Token: 0x040000D3 RID: 211
	private static bool m_showCursor = true;

	// Token: 0x040000D4 RID: 212
	private bool loading;

	// Token: 0x040000D5 RID: 213
	private static List<string> gamemodeArgs = new List<string>();

	// Token: 0x020001FB RID: 507
	[Serializable]
	public class DeathType
	{
		// Token: 0x04000DA3 RID: 3491
		public string name;

		// Token: 0x04000DA4 RID: 3492
		public string deathText = "Died";

		// Token: 0x04000DA5 RID: 3493
		public TipList tipList;
	}

	// Token: 0x020001FC RID: 508
	[Serializable]
	public class SessionFlag
	{
		// Token: 0x06000CA4 RID: 3236 RVA: 0x0004F293 File Offset: 0x0004D493
		public void SetState(bool b)
		{
			this.state = b;
		}

		// Token: 0x04000DA6 RID: 3494
		public string name;

		// Token: 0x04000DA7 RID: 3495
		public bool state;

		// Token: 0x04000DA8 RID: 3496
		public string data;
	}
}
