﻿using System;
using UnityEngine;

// Token: 0x02000036 RID: 54
public class DEN_GiantRoach : Denizen
{
	// Token: 0x0400025B RID: 603
	[Header("Roach")]
	public Transform roachMesh;

	// Token: 0x0400025C RID: 604
	public LayerMask worldMask;

	// Token: 0x0400025D RID: 605
	public float crawlSpeed = 1f;

	// Token: 0x0400025E RID: 606
	public float stickForce = 1f;

	// Token: 0x0400025F RID: 607
	public float rotationSpeed = 2f;

	// Token: 0x04000260 RID: 608
	public float restTimeMin = 1f;

	// Token: 0x04000261 RID: 609
	public float restTimeMax = 2f;

	// Token: 0x04000262 RID: 610
	private float restTime;

	// Token: 0x04000263 RID: 611
	public float chargeHitForce = 2f;

	// Token: 0x04000264 RID: 612
	[Header("AI")]
	public DEN_GiantRoach.AIStates aiState = DEN_GiantRoach.AIStates.wandering;

	// Token: 0x04000265 RID: 613
	public DEN_GiantRoach.MovementState moveState;

	// Token: 0x04000266 RID: 614
	public float sightDistance;

	// Token: 0x04000267 RID: 615
	public float chargeDistance;

	// Token: 0x04000268 RID: 616
	public float loseTargetTimer = 5f;

	// Token: 0x04000269 RID: 617
	private float chargeTime;

	// Token: 0x0400026A RID: 618
	public float chargeWindup = 3f;

	// Token: 0x0400026B RID: 619
	public float chargeCooldown = 10f;

	// Token: 0x0400026C RID: 620
	private float chargeCooldownTime;

	// Token: 0x0400026D RID: 621
	private bool foundRestSpot;

	// Token: 0x0400026E RID: 622
	private bool hasCharged;

	// Token: 0x0400026F RID: 623
	private Collision collision;

	// Token: 0x04000270 RID: 624
	public bool alwaysTargetPlayer;

	// Token: 0x04000271 RID: 625
	private Vector3 targetMoveVector;

	// Token: 0x04000272 RID: 626
	public string[] targetTags;

	// Token: 0x04000273 RID: 627
	private Vector3 lookVector;

	// Token: 0x04000274 RID: 628
	private RaycastHit searchRaycast;

	// Token: 0x04000275 RID: 629
	private bool grounded;

	// Token: 0x04000276 RID: 630
	private Vector3 wallNormal;

	// Token: 0x04000277 RID: 631
	private Vector3 facingDir;

	// Token: 0x04000278 RID: 632
	[Header("Physics")]
	public float pGain = 1f;

	// Token: 0x04000279 RID: 633
	public float iGain = 0.1f;

	// Token: 0x0400027A RID: 634
	public float dGain = 0.5f;

	// Token: 0x0400027B RID: 635
	private Rigidbody rigid;

	// Token: 0x0400027C RID: 636
	private Vector3 positionErrorSum = Vector3.zero;

	// Token: 0x0400027D RID: 637
	private Vector3 lastPositionError = Vector3.zero;

	// Token: 0x0400027E RID: 638
	public float noiseIntensity = 0.5f;

	// Token: 0x0400027F RID: 639
	public float noiseFrequency = 1f;

	// Token: 0x04000280 RID: 640
	private float noiseOffsetX;

	// Token: 0x04000281 RID: 641
	private float noiseOffsetY;

	// Token: 0x04000282 RID: 642
	private float noiseOffsetZ;

	// Token: 0x04000283 RID: 643
	private Vector3 landDir;

	// Token: 0x04000284 RID: 644
	[Header("Visuals")]
	public Animator animator;

	// Token: 0x0200021F RID: 543
	public enum AIStates
	{
		// Token: 0x04000E46 RID: 3654
		searching,
		// Token: 0x04000E47 RID: 3655
		wandering,
		// Token: 0x04000E48 RID: 3656
		resting,
		// Token: 0x04000E49 RID: 3657
		hunting,
		// Token: 0x04000E4A RID: 3658
		charging
	}

	// Token: 0x02000220 RID: 544
	public enum MovementState
	{
		// Token: 0x04000E4C RID: 3660
		walking,
		// Token: 0x04000E4D RID: 3661
		running,
		// Token: 0x04000E4E RID: 3662
		resting,
		// Token: 0x04000E4F RID: 3663
		charging,
		// Token: 0x04000E50 RID: 3664
		leaping
	}
}
