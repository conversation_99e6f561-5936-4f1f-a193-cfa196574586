﻿using System;
using Drawing;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020001C4 RID: 452
public class UT_LookTrigger : MonoBehaviour
{
	// Token: 0x06000B91 RID: 2961 RVA: 0x00049D48 File Offset: 0x00047F48
	private void Update()
	{
		if (this.checkOnUpdate)
		{
			if (this.checkTime <= 0f)
			{
				this.checkTime = 0.05f;
				this.LookCheck();
			}
			this.checkTime -= Time.deltaTime;
		}
		if (this.showDebug && CL_UIManager.debug)
		{
			CL_DebugView.draw.Label2D(base.transform.position, (this.isLooking ? "Can See" : "Cannot See") + " :: " + base.name, 14f, LabelAlignment.Center, this.isLooking ? Color.green : Color.red);
		}
	}

	// Token: 0x06000B92 RID: 2962 RVA: 0x00049DF8 File Offset: 0x00047FF8
	public void LookCheck()
	{
		if (this.runOnce && this.hasLooked)
		{
			return;
		}
		CL_CameraControl camRoot = ENT_Player.playerObject.camRoot;
		float num = Vector3.Distance(base.transform.position, ENT_Player.playerObject.camRoot.transform.position);
		if (this.useFacingAngle)
		{
			Vector3 normalized = (camRoot.transform.position - base.transform.position).normalized;
			if (Vector3.Angle(base.transform.forward, normalized) > this.facingAngle)
			{
				if (this.isLooking)
				{
					this.isLooking = false;
					this.onLookAway.Invoke();
				}
				return;
			}
		}
		if (num > this.maxDistance)
		{
			if (this.isLooking)
			{
				this.isLooking = false;
				this.onLookAway.Invoke();
			}
			return;
		}
		if (ENT_Player.playerObject.camRoot.CanSeeTarget(base.transform, this.maxAngle))
		{
			RaycastHit raycastHit;
			if (this.useRaycast && Physics.Linecast(camRoot.transform.position, base.transform.position, out raycastHit, this.raycastMask) && raycastHit.collider.transform != base.transform)
			{
				if (this.isLooking)
				{
					this.isLooking = false;
					this.onLookAway.Invoke();
				}
				return;
			}
			if (!this.isLooking)
			{
				this.isLooking = true;
				this.onLookAt.Invoke();
				this.hasLooked = true;
				return;
			}
		}
		else if (this.isLooking)
		{
			this.isLooking = false;
			this.onLookAway.Invoke();
		}
	}

	// Token: 0x06000B93 RID: 2963 RVA: 0x00049F8C File Offset: 0x0004818C
	public void ResetState()
	{
		this.hasLooked = false;
		this.isLooking = false;
	}

	// Token: 0x06000B94 RID: 2964 RVA: 0x00049F9C File Offset: 0x0004819C
	private void OnDrawGizmos()
	{
		Gizmos.color = new Color(1f, 0f, 0f, 0.2f);
		Gizmos.DrawIcon(base.transform.position, "gizmo-eye");
		Gizmos.DrawWireSphere(base.transform.position, this.maxDistance);
		if (this.useFacingAngle)
		{
			float num = Mathf.Cos(this.facingAngle * 0.00872665f);
			float num2 = Mathf.Sin(this.facingAngle * 0.00872665f);
			Gizmos.color = new Color(1f, 0.5f, 0f, 0.4f);
			Gizmos.DrawRay(base.transform.position, base.transform.forward * this.maxDistance);
			Gizmos.DrawRay(base.transform.position, (base.transform.right * num2 + base.transform.forward * num) * this.maxDistance);
			Gizmos.DrawRay(base.transform.position, (base.transform.right * -num2 + base.transform.forward * num) * this.maxDistance);
			Gizmos.DrawRay(base.transform.position, (base.transform.up * -num2 + base.transform.forward * num) * this.maxDistance);
			Gizmos.DrawRay(base.transform.position, (base.transform.up * num2 + base.transform.forward * num) * this.maxDistance);
		}
	}

	// Token: 0x04000C9E RID: 3230
	public float maxAngle = 90f;

	// Token: 0x04000C9F RID: 3231
	public float maxDistance = 10f;

	// Token: 0x04000CA0 RID: 3232
	public bool checkOnUpdate = true;

	// Token: 0x04000CA1 RID: 3233
	public bool runOnce;

	// Token: 0x04000CA2 RID: 3234
	public UnityEvent onLookAt;

	// Token: 0x04000CA3 RID: 3235
	public UnityEvent onLookAway;

	// Token: 0x04000CA4 RID: 3236
	[Header("Facing Angle")]
	public bool useFacingAngle;

	// Token: 0x04000CA5 RID: 3237
	public float facingAngle = 180f;

	// Token: 0x04000CA6 RID: 3238
	[Header("Raycast")]
	public bool useRaycast;

	// Token: 0x04000CA7 RID: 3239
	public LayerMask raycastMask;

	// Token: 0x04000CA8 RID: 3240
	private bool isLooking;

	// Token: 0x04000CA9 RID: 3241
	private bool hasLooked;

	// Token: 0x04000CAA RID: 3242
	private float checkTime;

	// Token: 0x04000CAB RID: 3243
	[Header("Debug")]
	public bool showDebug;
}
