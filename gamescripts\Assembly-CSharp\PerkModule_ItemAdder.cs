﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000047 RID: 71
public class PerkModule_ItemAdder : PerkModule
{
	// Token: 0x06000322 RID: 802 RVA: 0x0001FD64 File Offset: 0x0001DF64
	public override void AddModule()
	{
		base.AddModule();
		Debug.Log("Adding items into inventory - " + this.perk.name);
		foreach (Item_Object item_Object in this.itemsToAdd)
		{
			Debug.Log(item_Object.name);
		}
		List<Item> list = new List<Item>();
		foreach (Item_Object item_Object2 in this.itemsToAdd)
		{
			if (!(item_Object2 == null))
			{
				Item clone = item_Object2.itemData.GetClone();
				clone.bagPosition = new Vector3(0f, 0f, 1f) + Random.insideUnitSphere * 0.01f;
				clone.bagRotation = Quaternion.LookRotation(clone.upDirection);
				list.Add(clone);
			}
		}
		Inventory.instance.LoadItemsIntoBag(list);
		this.perk.RemoveModule(this);
	}

	// Token: 0x0400044F RID: 1103
	public List<Item_Object> itemsToAdd;
}
