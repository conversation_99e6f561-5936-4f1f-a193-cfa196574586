﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200012F RID: 303
public class UI_MenuScreen : MonoBehaviour
{
	// Token: 0x060008ED RID: 2285 RVA: 0x0003ECF5 File Offset: 0x0003CEF5
	internal void Disable()
	{
		this.group.interactable = false;
	}

	// Token: 0x060008EE RID: 2286 RVA: 0x0003ED03 File Offset: 0x0003CF03
	internal void Initialize(UI_MenuButton uI_MenuButton, UI_Menu parentMenu)
	{
		this.menu = parentMenu;
		this.group = base.GetComponent<CanvasGroup>();
		this.group.interactable = false;
	}

	// Token: 0x060008EF RID: 2287 RVA: 0x0003ED24 File Offset: 0x0003CF24
	public void Open()
	{
		Debug.Log("Opening Menu");
		if (this.group == null)
		{
			this.group = base.GetComponent<CanvasGroup>();
		}
		this.group.interactable = true;
		this.openEvent.Invoke();
	}

	// Token: 0x060008F0 RID: 2288 RVA: 0x0003ED61 File Offset: 0x0003CF61
	public void CloseScreen()
	{
		if (this.menu != null)
		{
			this.menu.EnableMenu();
		}
		this.closeEvent.Invoke();
	}

	// Token: 0x04000A3D RID: 2621
	private UI_Menu menu;

	// Token: 0x04000A3E RID: 2622
	private CanvasGroup group;

	// Token: 0x04000A3F RID: 2623
	public UnityEvent openEvent;

	// Token: 0x04000A40 RID: 2624
	public UnityEvent closeEvent;
}
