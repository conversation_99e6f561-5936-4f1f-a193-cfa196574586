﻿using System;
using System.Text;
using TMPro;
using UnityEngine;

// Token: 0x020000E6 RID: 230
public class AsciiGrid : MonoBehaviour
{
	// Token: 0x0600071F RID: 1823 RVA: 0x00036C88 File Offset: 0x00034E88
	private void Awake()
	{
		this.width = Mathf.Max(1, this.width);
		this.height = Mathf.Max(1, this.height);
		this.grid = new char[this.height, this.width];
		for (int i = 0; i < this.height; i++)
		{
			for (int j = 0; j < this.width; j++)
			{
				this.grid[i, j] = ' ';
			}
		}
		if (this.asciiText != null)
		{
			this.asciiText.enableWordWrapping = false;
			this.asciiText.fontSize = this.fontSize;
			this.asciiText.lineSpacing = this.lineSpacing;
			this.asciiText.characterSpacing = this.characterSpacing;
		}
		this.RebuildText();
	}

	// Token: 0x06000720 RID: 1824 RVA: 0x00036D54 File Offset: 0x00034F54
	public void SetChar(int x, int y, char c)
	{
		if (x < 0 || x >= this.width || y < 0 || y >= this.height)
		{
			return;
		}
		this.grid[y, x] = c;
		this.RebuildText();
	}

	// Token: 0x06000721 RID: 1825 RVA: 0x00036D88 File Offset: 0x00034F88
	public void AddString(int startX, int startY, string text)
	{
		if (string.IsNullOrEmpty(text))
		{
			return;
		}
		int num = startX;
		int num2 = 0;
		while (num2 < text.Length && num >= 0 && num < this.width && startY >= 0 && startY < this.height)
		{
			this.grid[startY, num] = text[num2];
			num++;
			num2++;
		}
		this.RebuildText();
	}

	// Token: 0x06000722 RID: 1826 RVA: 0x00036DEC File Offset: 0x00034FEC
	public void FillRect(int x1, int y1, int x2, int y2, char c)
	{
		int num = Mathf.Min(x1, x2);
		int num2 = Mathf.Max(x1, x2);
		int num3 = Mathf.Min(y1, y2);
		int num4 = Mathf.Max(y1, y2);
		for (int i = num3; i <= num4; i++)
		{
			for (int j = num; j <= num2; j++)
			{
				if (j >= 0 && j < this.width && i >= 0 && i < this.height)
				{
					this.grid[i, j] = c;
				}
			}
		}
		this.RebuildText();
	}

	// Token: 0x06000723 RID: 1827 RVA: 0x00036E68 File Offset: 0x00035068
	private void RebuildText()
	{
		if (this.asciiText == null)
		{
			return;
		}
		StringBuilder stringBuilder = new StringBuilder(this.height * (this.width + 1));
		for (int i = 0; i < this.height; i++)
		{
			for (int j = 0; j < this.width; j++)
			{
				stringBuilder.Append(this.grid[i, j]);
			}
			if (i < this.height - 1)
			{
				stringBuilder.AppendLine();
			}
		}
		this.asciiText.text = stringBuilder.ToString();
	}

	// Token: 0x0400088F RID: 2191
	[Header("Grid Dimensions")]
	public int width = 40;

	// Token: 0x04000890 RID: 2192
	public int height = 20;

	// Token: 0x04000891 RID: 2193
	[Header("TextMeshPro Settings")]
	public TextMeshProUGUI asciiText;

	// Token: 0x04000892 RID: 2194
	public float fontSize = 16f;

	// Token: 0x04000893 RID: 2195
	public float lineSpacing = 1f;

	// Token: 0x04000894 RID: 2196
	public float characterSpacing;

	// Token: 0x04000895 RID: 2197
	private char[,] grid;
}
