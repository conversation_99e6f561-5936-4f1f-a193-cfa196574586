﻿using System;

namespace System.ArrayExtensions
{
	// Token: 0x020001CF RID: 463
	internal class ArrayTraverse
	{
		// Token: 0x06000BBE RID: 3006 RVA: 0x0004AB30 File Offset: 0x00048D30
		public ArrayTraverse(Array array)
		{
			this.maxLengths = new int[array.Rank];
			for (int i = 0; i < array.Rank; i++)
			{
				this.maxLengths[i] = array.GetLength(i) - 1;
			}
			this.Position = new int[array.Rank];
		}

		// Token: 0x06000BBF RID: 3007 RVA: 0x0004AB88 File Offset: 0x00048D88
		public bool Step()
		{
			for (int i = 0; i < this.Position.Length; i++)
			{
				if (this.Position[i] < this.maxLengths[i])
				{
					this.Position[i]++;
					for (int j = 0; j < i; j++)
					{
						this.Position[j] = 0;
					}
					return true;
				}
			}
			return false;
		}

		// Token: 0x04000CCA RID: 3274
		public int[] Position;

		// Token: 0x04000CCB RID: 3275
		private int[] maxLengths;
	}
}
