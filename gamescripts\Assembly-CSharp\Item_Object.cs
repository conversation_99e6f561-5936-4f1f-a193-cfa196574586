﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200009D RID: 157
public class Item_Object : MonoBehaviour, SaveableObject
{
	// Token: 0x0600051F RID: 1311 RVA: 0x0002B55A File Offset: 0x0002975A
	private void Start()
	{
		this.itemData.InitializeItemData(this);
	}

	// Token: 0x06000520 RID: 1312 RVA: 0x0002B568 File Offset: 0x00029768
	public ObjectTagger GetTagger()
	{
		if (this.tagger = null)
		{
			this.tagger = base.gameObject.GetComponent<ObjectTagger>();
		}
		return this.tagger;
	}

	// Token: 0x06000521 RID: 1313 RVA: 0x0002B5A0 File Offset: 0x000297A0
	private void Update()
	{
		if (this.dropTime > 0f)
		{
			this.dropTime -= Time.deltaTime;
			this.dropWait = true;
		}
		else
		{
			this.dropWait = false;
		}
		if (!this.itemData.InInventory())
		{
			this.Execute(null);
		}
	}

	// Token: 0x06000522 RID: 1314 RVA: 0x0002B5F0 File Offset: 0x000297F0
	public void Pickup()
	{
		if (!this.hasPickedUp)
		{
			this.pickupEvent.Invoke();
		}
		this.hasPickedUp = true;
		this.itemData.OnPickup();
	}

	// Token: 0x06000523 RID: 1315 RVA: 0x0002B617 File Offset: 0x00029817
	public void OnDrop()
	{
		this.dropTime = 0.15f;
		this.dropWait = true;
	}

	// Token: 0x06000524 RID: 1316 RVA: 0x0002B62B File Offset: 0x0002982B
	public bool CanPickup()
	{
		return this.canPickup && !this.dropWait;
	}

	// Token: 0x06000525 RID: 1317 RVA: 0x0002B640 File Offset: 0x00029840
	public virtual void Execute(Inventory inv)
	{
	}

	// Token: 0x06000526 RID: 1318 RVA: 0x0002B642 File Offset: 0x00029842
	public SaveableInfo GetSaveInfo()
	{
		SaveableInfo saveableInfo = new SaveableInfo();
		this.itemData.Save();
		saveableInfo.id = this.itemData.prefabName;
		saveableInfo.data = JsonUtility.ToJson(this.itemData);
		return saveableInfo;
	}

	// Token: 0x06000527 RID: 1319 RVA: 0x0002B678 File Offset: 0x00029878
	public void SetSaveInfo(SaveableInfo info)
	{
		this.itemData.data = new List<string>();
		Item item = new Item();
		item = JsonUtility.FromJson<Item>(info.data);
		item.Load();
		this.itemData.data = item.data;
		this.itemData.dataModules = item.dataModules;
		this.itemData.dataModuleSaves = item.dataModuleSaves;
	}

	// Token: 0x040006A6 RID: 1702
	public Item itemData;

	// Token: 0x040006A7 RID: 1703
	public ObjectTagger tagger;

	// Token: 0x040006A8 RID: 1704
	private float dropTime;

	// Token: 0x040006A9 RID: 1705
	public bool canPickup = true;

	// Token: 0x040006AA RID: 1706
	private bool dropWait;

	// Token: 0x040006AB RID: 1707
	public UnityEvent pickupEvent;

	// Token: 0x040006AC RID: 1708
	private bool hasPickedUp;
}
