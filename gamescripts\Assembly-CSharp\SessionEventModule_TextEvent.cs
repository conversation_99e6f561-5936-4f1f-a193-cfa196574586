﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000110 RID: 272
[Serializable]
public class SessionEventModule_TextEvent : SessionEventModule
{
	// Token: 0x06000835 RID: 2101 RVA: 0x0003B96E File Offset: 0x00039B6E
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		this.Activate();
	}

	// Token: 0x06000836 RID: 2102 RVA: 0x0003B980 File Offset: 0x00039B80
	public void Play()
	{
		string text = this.playScrawl;
		if (this.randomLines)
		{
			text = this.lines[Random.Range(0, this.lines.Count)];
		}
		if (text != "")
		{
			if (this.type == SessionEventModule_TextEvent.ScrawlType.header)
			{
				CL_UIManager.instance.header.ShowText(text);
				return;
			}
			if (this.type == SessionEventModule_TextEvent.ScrawlType.ascentHeader)
			{
				CL_UIManager.instance.ascentHeader.ShowText(text);
				return;
			}
			if (this.type == SessionEventModule_TextEvent.ScrawlType.highscoreHeader)
			{
				CL_UIManager.instance.highscoreHeader.ShowText(text);
				return;
			}
			if (this.type == SessionEventModule_TextEvent.ScrawlType.tip)
			{
				CL_UIManager.instance.tipHeader.ShowText(text);
			}
		}
	}

	// Token: 0x06000837 RID: 2103 RVA: 0x0003BA2C File Offset: 0x00039C2C
	public override void Activate()
	{
		base.Activate();
		this.Play();
	}

	// Token: 0x040009A2 RID: 2466
	public SessionEventModule_TextEvent.ScrawlType type;

	// Token: 0x040009A3 RID: 2467
	public bool randomLines;

	// Token: 0x040009A4 RID: 2468
	[TextArea]
	public string playScrawl;

	// Token: 0x040009A5 RID: 2469
	[TextArea]
	public List<string> lines;

	// Token: 0x020002AD RID: 685
	public enum ScrawlType
	{
		// Token: 0x04001112 RID: 4370
		header,
		// Token: 0x04001113 RID: 4371
		ascentHeader,
		// Token: 0x04001114 RID: 4372
		highscoreHeader,
		// Token: 0x04001115 RID: 4373
		tip,
		// Token: 0x04001116 RID: 4374
		descriptor
	}
}
