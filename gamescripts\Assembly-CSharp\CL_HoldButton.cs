﻿using System;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000080 RID: 128
public class CL_HoldButton : MonoBehaviour, Clickable, ValueControl
{
	// Token: 0x0600043F RID: 1087 RVA: 0x000260D2 File Offset: 0x000242D2
	private void Start()
	{
		this.rendererComponent = base.GetComponent<MeshRenderer>();
		this.ChooseMaterial();
	}

	// Token: 0x06000440 RID: 1088 RVA: 0x000260E8 File Offset: 0x000242E8
	public void Update()
	{
		if (this.holding && this.holdUntil)
		{
			if (this.curValue > this.maxValue)
			{
				if (this.disableOnFinish)
				{
					this.canUse = false;
				}
				this.finishEvent.Invoke();
				this.stopEvent.Invoke();
				this.hasFinished = true;
				this.Stop();
				this.curValue = this.maxValue;
			}
			if (this.curValue < this.minValue)
			{
				if (this.disableOnFinish)
				{
					this.canUse = false;
				}
				this.finishEvent.Invoke();
				this.stopEvent.Invoke();
				this.hasFinished = true;
				this.Stop();
				this.curValue = this.minValue;
			}
			this.progressEvent.Invoke(this.curValue);
			this.curValue += Time.deltaTime * this.direction * this.useSpeed;
		}
	}

	// Token: 0x06000441 RID: 1089 RVA: 0x000261D4 File Offset: 0x000243D4
	public virtual void Interact()
	{
		if (this.maxPresses > 0 && this.presses >= this.maxPresses)
		{
			return;
		}
		if (!this.active)
		{
			return;
		}
		if (this.flipFlop)
		{
			this.direction *= -1f;
		}
		this.presses++;
		this.hasBeenPressed = true;
		this.holding = true;
		AudioManager.PlaySound(this.pressSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
		this.ChooseMaterial();
		this.activeEvent.Invoke();
	}

	// Token: 0x06000442 RID: 1090 RVA: 0x00026275 File Offset: 0x00024475
	public void Interact(ENT_Player p, ENT_Player.Hand hand)
	{
		this.Interact();
		this.player = p;
	}

	// Token: 0x06000443 RID: 1091 RVA: 0x00026284 File Offset: 0x00024484
	public void StopInteract(ENT_Player p, string s = "")
	{
		this.Stop();
		this.player = null;
		if (this.curValue <= this.maxValue)
		{
			this.failEvent.Invoke();
		}
		if (this.resetOnStop)
		{
			this.curValue = 0f;
		}
	}

	// Token: 0x06000444 RID: 1092 RVA: 0x000262C0 File Offset: 0x000244C0
	private void Stop()
	{
		this.presses++;
		AudioManager.PlaySound(this.letgoSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
		this.holding = false;
		this.ChooseMaterial();
		this.stopEvent.Invoke();
	}

	// Token: 0x06000445 RID: 1093 RVA: 0x0002631F File Offset: 0x0002451F
	public void SetValue(float v)
	{
		this.curValue = v;
	}

	// Token: 0x06000446 RID: 1094 RVA: 0x00026328 File Offset: 0x00024528
	public void SetInteractable(bool b)
	{
		this.active = b;
		this.ChooseMaterial();
	}

	// Token: 0x06000447 RID: 1095 RVA: 0x00026337 File Offset: 0x00024537
	public ObjectTagger GetTagger()
	{
		return base.gameObject.GetComponent<ObjectTagger>();
	}

	// Token: 0x06000448 RID: 1096 RVA: 0x00026344 File Offset: 0x00024544
	public bool CanInteract(ENT_Player p, ENT_Player.Hand curHand)
	{
		return this.canUse && !this.holding && (this.presses < this.maxPresses || this.maxPresses <= 0) && this.active;
	}

	// Token: 0x06000449 RID: 1097 RVA: 0x00026377 File Offset: 0x00024577
	public GameObject GetGameObject()
	{
		return base.gameObject;
	}

	// Token: 0x0600044A RID: 1098 RVA: 0x00026380 File Offset: 0x00024580
	internal virtual void ChooseMaterial()
	{
		if (!this.active || (this.maxPresses > 0 && this.presses >= this.maxPresses))
		{
			this.SetMaterial(this.inactiveMaterial);
			return;
		}
		if (this.holding)
		{
			this.SetMaterial(this.pressedMaterial);
			return;
		}
		this.SetMaterial(this.unpressedMaterial);
	}

	// Token: 0x0600044B RID: 1099 RVA: 0x000263DC File Offset: 0x000245DC
	public void SetMaterial(Material mat)
	{
		if (this.rendererComponent != null && mat != null)
		{
			Material[] materials = this.rendererComponent.materials;
			materials[this.materialIndex] = mat;
			this.rendererComponent.SetMaterials(materials.ToList<Material>());
		}
	}

	// Token: 0x0400058F RID: 1423
	public bool active = true;

	// Token: 0x04000590 RID: 1424
	public bool canUse = true;

	// Token: 0x04000591 RID: 1425
	public UnityEvent activeEvent;

	// Token: 0x04000592 RID: 1426
	public UnityEvent stopEvent;

	// Token: 0x04000593 RID: 1427
	public UnityEvent finishEvent;

	// Token: 0x04000594 RID: 1428
	public UnityEvent failEvent;

	// Token: 0x04000595 RID: 1429
	public UnityEvent<float> progressEvent;

	// Token: 0x04000596 RID: 1430
	[HideInInspector]
	public bool hasBeenPressed;

	// Token: 0x04000597 RID: 1431
	public int maxPresses;

	// Token: 0x04000598 RID: 1432
	private int presses;

	// Token: 0x04000599 RID: 1433
	public bool projectilesCanPress = true;

	// Token: 0x0400059A RID: 1434
	internal bool cooldown;

	// Token: 0x0400059B RID: 1435
	[Header("Audio")]
	public AudioClip pressSound;

	// Token: 0x0400059C RID: 1436
	public AudioClip letgoSound;

	// Token: 0x0400059D RID: 1437
	[Header("Materials")]
	public int materialIndex;

	// Token: 0x0400059E RID: 1438
	public Material unpressedMaterial;

	// Token: 0x0400059F RID: 1439
	public Material pressedMaterial;

	// Token: 0x040005A0 RID: 1440
	public Material inactiveMaterial;

	// Token: 0x040005A1 RID: 1441
	private MeshRenderer rendererComponent;

	// Token: 0x040005A2 RID: 1442
	public bool holdUntil;

	// Token: 0x040005A3 RID: 1443
	public float curValue;

	// Token: 0x040005A4 RID: 1444
	public float minValue;

	// Token: 0x040005A5 RID: 1445
	public float maxValue = 1f;

	// Token: 0x040005A6 RID: 1446
	public float useSpeed = 1f;

	// Token: 0x040005A7 RID: 1447
	private bool holding;

	// Token: 0x040005A8 RID: 1448
	private float direction = 1f;

	// Token: 0x040005A9 RID: 1449
	private ENT_Player player;

	// Token: 0x040005AA RID: 1450
	public bool resetOnStop;

	// Token: 0x040005AB RID: 1451
	public bool flipFlop;

	// Token: 0x040005AC RID: 1452
	public bool disableOnFinish;

	// Token: 0x040005AD RID: 1453
	[HideInInspector]
	public bool hasFinished;
}
