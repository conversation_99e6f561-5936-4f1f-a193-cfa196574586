﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200013F RID: 319
public class UI_TabFade : MonoBehaviour
{
	// Token: 0x0600093A RID: 2362 RVA: 0x0003FD4C File Offset: 0x0003DF4C
	private void Start()
	{
		this.sprite = base.gameObject.GetComponent<Image>();
		this.text = base.gameObject.GetComponent<TMP_Text>();
	}

	// Token: 0x0600093B RID: 2363 RVA: 0x0003FD70 File Offset: 0x0003DF70
	private void Update()
	{
		if (this.delayTime > 0f)
		{
			this.delayTime -= Time.deltaTime;
		}
		if (this.sprite)
		{
			if (Input.GetKey(KeyCode.Tab))
			{
				Color color = this.sprite.color;
				color.a = Mathf.Lerp(color.a, this.maxOpacity, Time.deltaTime * this.speed);
				this.sprite.color = color;
				return;
			}
			if (this.delayTime <= 0f)
			{
				Color color2 = this.sprite.color;
				color2.a = Mathf.Lerp(color2.a, this.minOpacity, Time.deltaTime * this.speed);
				this.sprite.color = color2;
				return;
			}
		}
		else if (this.text)
		{
			if (Input.GetKey(KeyCode.Tab))
			{
				Color color3 = this.text.color;
				color3.a = Mathf.Lerp(color3.a, this.maxOpacity, Time.deltaTime * this.speed);
				this.text.color = color3;
				return;
			}
			if (this.delayTime <= 0f)
			{
				Color color4 = this.text.color;
				color4.a = Mathf.Lerp(color4.a, this.minOpacity, Time.deltaTime * this.speed);
				this.text.color = color4;
			}
		}
	}

	// Token: 0x0600093C RID: 2364 RVA: 0x0003FEDE File Offset: 0x0003E0DE
	public void SetMinimumOpacity(float f)
	{
		this.minOpacity = f;
	}

	// Token: 0x0600093D RID: 2365 RVA: 0x0003FEE7 File Offset: 0x0003E0E7
	public void SetMaximumOpacity(float f)
	{
		this.maxOpacity = f;
	}

	// Token: 0x04000A7E RID: 2686
	private Image sprite;

	// Token: 0x04000A7F RID: 2687
	private TMP_Text text;

	// Token: 0x04000A80 RID: 2688
	private float speed = 6f;

	// Token: 0x04000A81 RID: 2689
	public float delayTime;

	// Token: 0x04000A82 RID: 2690
	private float minOpacity;

	// Token: 0x04000A83 RID: 2691
	private float maxOpacity = 1f;
}
