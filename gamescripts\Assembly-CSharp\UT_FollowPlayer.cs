﻿using System;
using UnityEngine;

// Token: 0x020001C2 RID: 450
[DefaultExecutionOrder(5)]
public class UT_FollowPlayer : MonoBehaviour
{
	// Token: 0x06000B8C RID: 2956 RVA: 0x00049CD3 File Offset: 0x00047ED3
	private void Update()
	{
		if (!this.OnLateUpdate)
		{
			this.follow.MoveToTarget(base.transform, ENT_Player.playerObject.transform);
		}
	}

	// Token: 0x06000B8D RID: 2957 RVA: 0x00049CF8 File Offset: 0x00047EF8
	private void LateUpdate()
	{
		if (this.OnLateUpdate)
		{
			this.follow.MoveToTarget(base.transform, ENT_Player.playerObject.transform);
		}
	}

	// Token: 0x04000C9B RID: 3227
	public UT_FollowBase follow;

	// Token: 0x04000C9C RID: 3228
	public bool OnLateUpdate;
}
