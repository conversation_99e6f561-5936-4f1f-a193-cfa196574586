﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020001C6 RID: 454
public class UT_Null : MonoBehaviour
{
	// Token: 0x06000B9C RID: 2972 RVA: 0x0004A403 File Offset: 0x00048603
	private void Awake()
	{
		Action<UT_Null> onAwake = UT_Null._OnAwake;
		if (onAwake == null)
		{
			return;
		}
		onAwake(this);
	}

	// Token: 0x06000B9D RID: 2973 RVA: 0x0004A415 File Offset: 0x00048615
	private void OnDestroy()
	{
		Action<UT_Null> onDestroy = UT_Null._OnDestroy;
		if (onDestroy == null)
		{
			return;
		}
		onDestroy(this);
	}

	// Token: 0x04000CAF RID: 3247
	public static Action<UT_Null> _OnAwake;

	// Token: 0x04000CB0 RID: 3248
	public static Action<UT_Null> _OnDestroy;

	// Token: 0x04000CB1 RID: 3249
	public string id;

	// Token: 0x04000CB2 RID: 3250
	public List<string> data;
}
