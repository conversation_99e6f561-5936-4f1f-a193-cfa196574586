﻿using System;
using System.Collections.Generic;
using DitzelGames.FastIK;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200007E RID: 126
public class IK_Controller : MonoBehaviour
{
	// Token: 0x0600042C RID: 1068 RVA: 0x00025CF8 File Offset: 0x00023EF8
	private void Start()
	{
		this.ikSystemsDict = new Dictionary<string, IK_Controller.IKSystems>();
		foreach (IK_Controller.IKSystems iksystems in this.iKSystems)
		{
			this.ikSystemsDict.Add(iksystems.name, iksystems);
		}
	}

	// Token: 0x0600042D RID: 1069 RVA: 0x00025D64 File Offset: 0x00023F64
	private void LateUpdate()
	{
		foreach (IK_Controller.IKSystems iksystems in this.iKSystems)
		{
			iksystems.Update();
		}
	}

	// Token: 0x0600042E RID: 1070 RVA: 0x00025DB4 File Offset: 0x00023FB4
	public void ActivateIK(string ikName)
	{
		this.ikSystemsDict[ikName].blendTarget = 1f;
		this.ikSystemsDict[ikName].SetLockState(true);
		this.ikSystemsDict[ikName].activateEvent.Invoke();
	}

	// Token: 0x0600042F RID: 1071 RVA: 0x00025DF4 File Offset: 0x00023FF4
	public void DeactivateIK(string ikName)
	{
		this.ikSystemsDict[ikName].blendTarget = 0f;
		this.ikSystemsDict[ikName].SetLockState(false);
		this.deactivateIKEvent.Invoke();
	}

	// Token: 0x06000430 RID: 1072 RVA: 0x00025E2C File Offset: 0x0002402C
	public void DeactivateAllIK()
	{
		foreach (IK_Controller.IKSystems iksystems in this.iKSystems)
		{
			iksystems.SetLockState(false);
			iksystems.blendTarget = 0f;
		}
	}

	// Token: 0x06000431 RID: 1073 RVA: 0x00025E88 File Offset: 0x00024088
	public void ResetIKTransforms()
	{
		foreach (IK_Controller.IKSystems iksystems in this.iKSystems)
		{
			iksystems.ik.ResetTransform();
		}
	}

	// Token: 0x0400057B RID: 1403
	public UnityEvent activateIKEvent;

	// Token: 0x0400057C RID: 1404
	public UnityEvent deactivateIKEvent;

	// Token: 0x0400057D RID: 1405
	public List<IK_Controller.IKSystems> iKSystems;

	// Token: 0x0400057E RID: 1406
	private Dictionary<string, IK_Controller.IKSystems> ikSystemsDict;

	// Token: 0x02000251 RID: 593
	[Serializable]
	public class IKSystems
	{
		// Token: 0x06000DA0 RID: 3488 RVA: 0x000533FB File Offset: 0x000515FB
		public void SetTargetPosition(Vector3 p)
		{
			this.ik.Target.position = p;
		}

		// Token: 0x06000DA1 RID: 3489 RVA: 0x00053410 File Offset: 0x00051610
		public void Update()
		{
			if (!this.active)
			{
				this.ik.blend = 0f;
				return;
			}
			this.ik.blend = Mathf.Lerp(this.ik.blend, this.blendTarget, Time.deltaTime * 5f);
			if (this.locked)
			{
				this.ik.Target.position = this.lockPosition;
				this.ik.Target.rotation = this.lockRotation;
			}
		}

		// Token: 0x06000DA2 RID: 3490 RVA: 0x00053498 File Offset: 0x00051698
		public void SetLockState(bool b)
		{
			this.locked = b;
			if (this.useRaycast)
			{
				this.FindLockSurface();
				return;
			}
			this.lockPosition = this.ik.transform.position;
			this.lockRotation = this.ik.transform.rotation;
		}

		// Token: 0x06000DA3 RID: 3491 RVA: 0x000534E8 File Offset: 0x000516E8
		private void FindLockSurface()
		{
			Vector3 vector = this.ik.transform.InverseTransformDirection(this.raycastDirection);
			float num = 0.5f;
			RaycastHit raycastHit;
			if (Physics.SphereCast(this.ik.transform.position - vector * (num / 2f), num, vector, out raycastHit, 0.5f, this.raycastLayerMask))
			{
				this.lockPosition = raycastHit.point;
				return;
			}
			this.lockPosition = this.ik.transform.position;
			this.lockRotation = this.ik.transform.rotation;
		}

		// Token: 0x04000F50 RID: 3920
		public bool active = true;

		// Token: 0x04000F51 RID: 3921
		public string name;

		// Token: 0x04000F52 RID: 3922
		public FastIKFabric ik;

		// Token: 0x04000F53 RID: 3923
		public float blendTarget = 1f;

		// Token: 0x04000F54 RID: 3924
		private bool locked;

		// Token: 0x04000F55 RID: 3925
		private Vector3 lockPosition;

		// Token: 0x04000F56 RID: 3926
		private Quaternion lockRotation;

		// Token: 0x04000F57 RID: 3927
		public bool useRaycast;

		// Token: 0x04000F58 RID: 3928
		public LayerMask raycastLayerMask;

		// Token: 0x04000F59 RID: 3929
		public Vector3 raycastDirection = Vector3.up;

		// Token: 0x04000F5A RID: 3930
		public UnityEvent activateEvent;
	}
}
