﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x0200015F RID: 351
public class UT_ButtonAudio : MonoBeh<PERSON>our, IPointerClickHandler, IEventSystemHandler, IPointerEnterHandler, ISelectHandler
{
	// Token: 0x060009EF RID: 2543 RVA: 0x00043126 File Offset: 0x00041326
	private void Awake()
	{
		this.button = base.GetComponent<Button>();
		if (this.onSubmit)
		{
			this.button.onClick.AddListener(new UnityAction(this.OnSubmit));
		}
	}

	// Token: 0x060009F0 RID: 2544 RVA: 0x00043158 File Offset: 0x00041358
	public void OnSubmit()
	{
		if (this.button == null || !this.button.IsInteractable())
		{
			return;
		}
		AudioManager.PlayUISound(this.clickSound, this.volume, 1f);
	}

	// Token: 0x060009F1 RID: 2545 RVA: 0x0004318C File Offset: 0x0004138C
	public void OnPointerClick(PointerEventData pointerEventData)
	{
		if (this.onSubmit || this.button == null || !this.button.IsInteractable())
		{
			return;
		}
		AudioManager.PlayUISound(this.clickSound, this.volume, 1f);
	}

	// Token: 0x060009F2 RID: 2546 RVA: 0x000431C8 File Offset: 0x000413C8
	public void OnPointerEnter(PointerEventData eventData)
	{
		if (this.button == null || !this.button.IsInteractable())
		{
			return;
		}
		AudioManager.PlayUISound(this.hoverSound, this.volume, 1f);
	}

	// Token: 0x060009F3 RID: 2547 RVA: 0x000431FC File Offset: 0x000413FC
	public void OnSelect(BaseEventData eventData)
	{
		if (!this.onSubmit || this.button == null || !this.button.IsInteractable())
		{
			return;
		}
		AudioManager.PlayUISound(this.hoverSound, this.volume, 1f);
	}

	// Token: 0x04000B0A RID: 2826
	public bool onSubmit;

	// Token: 0x04000B0B RID: 2827
	public AudioClip clickSound;

	// Token: 0x04000B0C RID: 2828
	public AudioClip hoverSound;

	// Token: 0x04000B0D RID: 2829
	[Range(0f, 1f)]
	public float volume = 1f;

	// Token: 0x04000B0E RID: 2830
	private Button button;
}
