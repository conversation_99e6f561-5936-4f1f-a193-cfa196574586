﻿using System;
using UnityEngine;

// Token: 0x02000007 RID: 7
public class FX_QualityMaterialSwapper : MonoBehaviour
{
	// Token: 0x06000029 RID: 41 RVA: 0x00003EEE File Offset: 0x000020EE
	private void Start()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.QualityCheck));
		this.renderer = base.GetComponent<Renderer>();
		this.QualityCheck();
	}

	// Token: 0x0600002A RID: 42 RVA: 0x00003F22 File Offset: 0x00002122
	private void OnDestroy()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Remove(SettingsManager.SettingsRefreshUpdate, new Action(this.QualityCheck));
	}

	// Token: 0x0600002B RID: 43 RVA: 0x00003F44 File Offset: 0x00002144
	public void QualityCheck()
	{
		bool flag = bool.Parse(SettingsManager.GetSetting(this.settingName));
		if (this.disableOnActive)
		{
			base.gameObject.SetActive(!flag);
			return;
		}
		this.renderer.material = (flag ? this.activeMaterial : this.inactiveMaterial);
	}

	// Token: 0x0400003C RID: 60
	public string settingName;

	// Token: 0x0400003D RID: 61
	public bool disableOnActive;

	// Token: 0x0400003E RID: 62
	private Renderer renderer;

	// Token: 0x0400003F RID: 63
	public Material activeMaterial;

	// Token: 0x04000040 RID: 64
	public Material inactiveMaterial;
}
