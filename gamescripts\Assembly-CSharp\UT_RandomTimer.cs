﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020000DA RID: 218
public class UT_RandomTimer : MonoBehaviour
{
	// Token: 0x060006D8 RID: 1752 RVA: 0x000361AE File Offset: 0x000343AE
	private void Awake()
	{
		if (this.runOnAwake)
		{
			this.running = true;
		}
		this.timer = Random.Range(this.randomMin, this.randomMax);
	}

	// Token: 0x060006D9 RID: 1753 RVA: 0x000361D8 File Offset: 0x000343D8
	private void Update()
	{
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		if (!this.running)
		{
			return;
		}
		this.timer -= Time.deltaTime;
		if (this.timer <= 0f)
		{
			if (this.repeat)
			{
				this.Play(true);
			}
			else
			{
				this.running = false;
			}
			this.runEvent.Invoke();
		}
	}

	// Token: 0x060006DA RID: 1754 RVA: 0x0003623D File Offset: 0x0003443D
	public void Play(bool reset = false)
	{
		this.running = true;
		if (reset)
		{
			this.Reset();
		}
	}

	// Token: 0x060006DB RID: 1755 RVA: 0x0003624F File Offset: 0x0003444F
	public void Pause()
	{
		this.running = false;
	}

	// Token: 0x060006DC RID: 1756 RVA: 0x00036258 File Offset: 0x00034458
	public void Reset()
	{
		this.timer = Random.Range(this.randomMin, this.randomMax);
	}

	// Token: 0x0400085C RID: 2140
	public float randomMin;

	// Token: 0x0400085D RID: 2141
	public float randomMax = 1f;

	// Token: 0x0400085E RID: 2142
	private float timer;

	// Token: 0x0400085F RID: 2143
	public bool runOnAwake;

	// Token: 0x04000860 RID: 2144
	public bool repeat = true;

	// Token: 0x04000861 RID: 2145
	private bool running;

	// Token: 0x04000862 RID: 2146
	public UnityEvent runEvent;
}
