﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

// Token: 0x020000EF RID: 239
public class Changeable_Text : MonoBehaviour
{
	// Token: 0x0600075C RID: 1884 RVA: 0x00038885 File Offset: 0x00036A85
	private void Start()
	{
		this.inputField = base.GetComponent<InputField>();
	}

	// Token: 0x0600075D RID: 1885 RVA: 0x00038893 File Offset: 0x00036A93
	private void Update()
	{
	}

	// Token: 0x0600075E RID: 1886 RVA: 0x00038895 File Offset: 0x00036A95
	public void UpdateText()
	{
		this.inputField.DeactivateInputField();
		this.inputField.interactable = false;
		this.updateEvent.Invoke(this.inputField.text);
	}

	// Token: 0x0600075F RID: 1887 RVA: 0x000388C4 File Offset: 0x00036AC4
	public void SetEditable()
	{
		this.inputField.text = this.inputField.textComponent.text;
		this.inputField.interactable = true;
		this.inputField.ActivateInputField();
	}

	// Token: 0x06000760 RID: 1888 RVA: 0x000388F8 File Offset: 0x00036AF8
	public void TypeSound()
	{
		OS_Manager.soundPlayer.PlaySound("tap");
	}

	// Token: 0x040008DC RID: 2268
	public bool canDoubleClick;

	// Token: 0x040008DD RID: 2269
	public float dClickTime = 0.5f;

	// Token: 0x040008DE RID: 2270
	private float cTime;

	// Token: 0x040008DF RID: 2271
	private InputField inputField;

	// Token: 0x040008E0 RID: 2272
	public UnityEvent<string> updateEvent;
}
