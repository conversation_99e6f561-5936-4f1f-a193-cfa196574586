﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000157 RID: 343
public class PacketEncoder
{
	// Token: 0x060009BC RID: 2492 RVA: 0x00042644 File Offset: 0x00040844
	public byte[] EmptyArray()
	{
		return BitConverter.GetBytes(false);
	}

	// Token: 0x060009BD RID: 2493 RVA: 0x0004264C File Offset: 0x0004084C
	public byte[] SoloByte(byte s)
	{
		return BitConverter.GetBytes((short)s);
	}

	// Token: 0x060009BE RID: 2494 RVA: 0x00042654 File Offset: 0x00040854
	public byte[] Bool2Byte(bool i)
	{
		return BitConverter.GetBytes(i);
	}

	// Token: 0x060009BF RID: 2495 RVA: 0x0004265C File Offset: 0x0004085C
	public byte[] Float2Bytes(float i)
	{
		return BitConverter.GetBytes(i);
	}

	// Token: 0x060009C0 RID: 2496 RVA: 0x00042664 File Offset: 0x00040864
	public byte[] Int2Bytes(int i)
	{
		return BitConverter.GetBytes(i);
	}

	// Token: 0x060009C1 RID: 2497 RVA: 0x0004266C File Offset: 0x0004086C
	public byte[] Vector32Bytes(Vector3 i)
	{
		List<byte> list = new List<byte>();
		list.AddRange(BitConverter.GetBytes(i.x));
		list.AddRange(BitConverter.GetBytes(i.y));
		list.AddRange(BitConverter.GetBytes(i.z));
		return list.ToArray();
	}

	// Token: 0x060009C2 RID: 2498 RVA: 0x000426AB File Offset: 0x000408AB
	public bool Bytes2Bool(byte[] b, int s = 0)
	{
		return BitConverter.ToBoolean(b, s);
	}

	// Token: 0x060009C3 RID: 2499 RVA: 0x000426B4 File Offset: 0x000408B4
	public Vector3 Bytes2Vector3(byte[] b, int s = 0)
	{
		if (b.Length < 12)
		{
			Debug.Log("Byte array not long enough " + b.Length.ToString());
			return Vector3.zero;
		}
		return new Vector3
		{
			x = BitConverter.ToSingle(b, s),
			y = BitConverter.ToSingle(b, 4 + s),
			z = BitConverter.ToSingle(b, 8 + s)
		};
	}

	// Token: 0x060009C4 RID: 2500 RVA: 0x00042720 File Offset: 0x00040920
	public byte[] Quaternion2Bytes(Quaternion q)
	{
		return new List<byte>
		{
			(byte)((q.w + 1f) / 2f * 255f),
			(byte)((q.x + 1f) / 2f * 255f),
			(byte)((q.y + 1f) / 2f * 255f),
			(byte)((q.z + 1f) / 2f * 255f)
		}.ToArray();
	}

	// Token: 0x060009C5 RID: 2501 RVA: 0x000427B4 File Offset: 0x000409B4
	public Quaternion Bytes2Quaternion(byte[] b, int s = 0)
	{
		if (b.Length < 4)
		{
			Debug.Log("Byte array not long enough " + b.Length.ToString());
			return Quaternion.identity;
		}
		return new Quaternion
		{
			w = (float)b[s] / 255f * 2f - 1f,
			x = (float)b[1 + s] / 255f * 2f - 1f,
			y = (float)b[2 + s] / 255f * 2f - 1f,
			z = (float)b[3 + s] / 255f * 2f - 1f
		};
	}

	// Token: 0x060009C6 RID: 2502 RVA: 0x0004286C File Offset: 0x00040A6C
	public byte[] Dir2Bytes(Vector3 d)
	{
		return new List<byte>
		{
			(byte)((d.x + 1f) / 2f * 255f),
			(byte)((d.y + 1f) / 2f * 255f),
			(byte)((d.z + 1f) / 2f * 255f)
		}.ToArray();
	}

	// Token: 0x060009C7 RID: 2503 RVA: 0x000428E0 File Offset: 0x00040AE0
	public Vector3 Bytes2Dir(byte[] b, int s = 0)
	{
		if (b.Length < 3)
		{
			Debug.Log("Byte array not long enough " + b.Length.ToString());
			return Vector3.zero;
		}
		return new Vector3
		{
			x = (float)b[s] / 255f * 2f - 1f,
			y = (float)b[1 + s] / 255f * 2f - 1f,
			z = (float)b[2 + s] / 255f * 2f - 1f
		};
	}

	// Token: 0x060009C8 RID: 2504 RVA: 0x00042978 File Offset: 0x00040B78
	public byte[] PackByte(byte x, ushort y, byte command, byte[] z)
	{
		List<byte> list = new List<byte> { x };
		list.AddRange(BitConverter.GetBytes(y));
		list.Add(command);
		list.AddRange(z);
		list.InsertRange(1, BitConverter.GetBytes(Convert.ToUInt16(list.Count + 2)));
		return list.ToArray();
	}
}
