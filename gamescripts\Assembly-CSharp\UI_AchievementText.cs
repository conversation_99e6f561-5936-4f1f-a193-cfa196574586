﻿using System;
using UnityEngine;

// Token: 0x0200011A RID: 282
public class UI_AchievementText : MonoBehaviour
{
	// Token: 0x06000873 RID: 2163 RVA: 0x0003CE31 File Offset: 0x0003B031
	private void Start()
	{
		this.scrawl = base.GetComponent<UT_TextScrawl>();
		CL_AchievementManager.textInstance = this;
	}

	// Token: 0x06000874 RID: 2164 RVA: 0x0003CE45 File Offset: 0x0003B045
	private void OnDestroy()
	{
		if (CL_AchievementManager.textInstance == this)
		{
			CL_AchievementManager.textInstance = null;
		}
	}

	// Token: 0x06000875 RID: 2165 RVA: 0x0003CE5A File Offset: 0x0003B05A
	public void ShowText(string text)
	{
		Debug.Log("Showing Text: " + text);
		if (this.scrawl != null)
		{
			this.scrawl.ShowText(text);
		}
	}

	// Token: 0x040009D2 RID: 2514
	private UT_TextScrawl scrawl;
}
