﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000042 RID: 66
[Serializable]
public class BuffContainer
{
	// Token: 0x0600028B RID: 651 RVA: 0x000172C7 File Offset: 0x000154C7
	public void Initialize()
	{
		this.buffTime = 1f;
		this.multiplier = 1f;
	}

	// Token: 0x0600028C RID: 652 RVA: 0x000172E0 File Offset: 0x000154E0
	public bool Update()
	{
		if (this.loseOverTime)
		{
			float num = Mathf.Max(1f + ENT_Player.playerObject.curBuffs.GetBuff("addLoseRate"), 0.1f);
			this.buffTime -= Time.deltaTime * this.loseRate * num;
		}
		foreach (BuffContainer.Buff buff in this.buffs)
		{
			buff.amount = Mathf.Lerp(buff.maxAmount, 0f, 1f - this.buffTime);
		}
		return this.buffTime < 0f;
	}

	// Token: 0x0600028D RID: 653 RVA: 0x000173A4 File Offset: 0x000155A4
	public float GetMultiplier()
	{
		return this.multiplier;
	}

	// Token: 0x0600028E RID: 654 RVA: 0x000173AC File Offset: 0x000155AC
	public void SetMultiplier(float m)
	{
		this.multiplier = m;
	}

	// Token: 0x04000389 RID: 905
	public string id;

	// Token: 0x0400038A RID: 906
	public string desc;

	// Token: 0x0400038B RID: 907
	public List<BuffContainer.Buff> buffs;

	// Token: 0x0400038C RID: 908
	public float loseRate = 0.1f;

	// Token: 0x0400038D RID: 909
	public bool loseRateEffectedByPerks = true;

	// Token: 0x0400038E RID: 910
	public float buffTime = 1f;

	// Token: 0x0400038F RID: 911
	public bool loseOverTime = true;

	// Token: 0x04000390 RID: 912
	public float multiplier = 1f;

	// Token: 0x0200022A RID: 554
	[Serializable]
	public class Buff
	{
		// Token: 0x04000E8E RID: 3726
		public string id;

		// Token: 0x04000E8F RID: 3727
		public float maxAmount;

		// Token: 0x04000E90 RID: 3728
		[HideInInspector]
		public float amount;
	}
}
