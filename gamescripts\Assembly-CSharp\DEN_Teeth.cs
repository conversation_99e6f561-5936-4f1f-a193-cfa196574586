﻿using System;
using System.Collections;
using System.Collections.Generic;
using DarkMachine.AI;
using Drawing;
using Pathfinding;
using Unity.Mathematics;
using UnityEngine;

// Token: 0x0200003B RID: 59
public class DEN_Teeth : AIGameEntity
{
	// Token: 0x0600022C RID: 556 RVA: 0x00012788 File Offset: 0x00010988
	private void OnEnable()
	{
		base.transform.parent.parent = null;
		if (DEN_Teeth.instance != null)
		{
			this.Despawn();
		}
		else
		{
			DEN_Teeth.instance = this;
		}
		CommandConsole.AddCommand("teeth-toggleailock", new Action<string[]>(this.SetTeethAILock), true);
	}

	// Token: 0x0600022D RID: 557 RVA: 0x000127D8 File Offset: 0x000109D8
	private void OnDisable()
	{
		if (DEN_Teeth.instance == this)
		{
			DEN_Teeth.instance = null;
		}
		CommandConsole.RemoveCommand("teeth-toggleailock");
	}

	// Token: 0x0600022E RID: 558 RVA: 0x000127F8 File Offset: 0x000109F8
	private void Awake()
	{
		this.clipHandler.Initialize();
		this.clipHandler.PlaySound("teeth:spawn");
		CL_CameraControl.Shake(0.15f);
		this.anim.keepAnimatorStateOnDisable = true;
		this.seeker = base.GetComponent<Seeker>();
		this.currentState = this.aiStates[0];
		this.currentState.Enter(this, null);
		this.lastObjectPosition = this.geoRoot.transform.position;
		this.screenHandEffectParent = this.screenHandEffect.transform.parent;
		if (this.target == null)
		{
			this.target = ENT_Player.GetPlayer().transform;
		}
		this.aiStates = new List<AIStateComponent>
		{
			new AIC_Teeth_Wander(),
			new AIC_Teeth_Chase(),
			new AIC_Teeth_Burrow(),
			new AIC_Teeth_Wait(),
			new AIC_Teeth_Retreat()
		};
		foreach (AIStateComponent aistateComponent in this.aiStates)
		{
			aistateComponent.Initialize(this, this.stateVariables, null);
		}
		this.baseSpeed *= Mathf.Clamp(CL_GameManager.gMan.GetPlayerAscentRate(), 0.85f, 2f);
		this.bodyMaterial = this.meshRenderer.material;
		if (CL_GameManager.IsHardmode())
		{
			this.baseSpeed += 1f;
			this.aggression *= 2f;
		}
	}

	// Token: 0x0600022F RID: 559 RVA: 0x000129A0 File Offset: 0x00010BA0
	public override void Update()
	{
		if (this.lockAI)
		{
			return;
		}
		this.realVelocity = (this.geoRoot.transform.position - this.lastObjectPosition) / Time.deltaTime;
		this.lastObjectPosition = this.geoRoot.transform.position;
		base.Update();
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		this.cohesionEffect = Mathf.Max(this.cohesionEffect - Time.deltaTime, 0f);
		this.bodyMaterial.SetFloat("_Wiggle", this.cohesionEffect * this.cohesionEffectMultiplier);
		this.burnEffect = Mathf.Max(this.burnEffect - Time.deltaTime * 0.5f, 0f);
		Color color = Color.Lerp(Color.black, Color.red, this.burnEffect);
		this.bodyMaterial.SetColor("_ShimmerColor", color);
		this.bodyMaterial.SetColor("_EmissionColor", color);
		if (!this.disableAI)
		{
			this.distanceToPlayer = CL_GameManager.gMan.PlayerDistance(base.transform.position);
			this.sight.Update();
			this.currentState.Execute(this, null);
			this.Navigation();
			this.ClimbTowardsTarget();
			if (!this.locked)
			{
				float num = this.aggressionRate;
				if (CL_GameManager.IsHardmode())
				{
					num *= 2f;
				}
				else
				{
					this.aggression = Mathf.Clamp(this.aggression, 0f, 1.35f);
				}
				if (this.distanceToPlayer > 15f)
				{
					this.aggression += num * Time.deltaTime * this.aggressionOutOfRangeModifier;
				}
				else
				{
					this.aggression += num * Time.deltaTime;
				}
			}
		}
		this.Animation();
		if (CL_UIManager.debug)
		{
			CL_DebugView.draw.SphereOutline(base.transform.position, 0.3f, new Color(1f, 0f, 0f, 0.6f));
			float3 @float = base.transform.position + Vector3.up;
			string text = "State: ";
			string name = this.currentState.name;
			string text2 = " Vel: ";
			Vector3 vector = this.realVelocity;
			CL_DebugView.draw.Label2D(@float, text + name + text2 + vector.ToString(), 8f, LabelAlignment.Center, Color.white);
			CL_DebugView.draw.Label2D(base.transform.position + Vector3.up * 1.5f, "Aggression: " + (this.aggression * this.aggressionMult).ToString(), 8f, LabelAlignment.Center, Color.red);
		}
	}

	// Token: 0x06000230 RID: 560 RVA: 0x00012C65 File Offset: 0x00010E65
	private void LateUpdate()
	{
		this.screenHandEffectParent.transform.position = Camera.main.transform.position;
		this.screenHandEffectParent.transform.rotation = Camera.main.transform.rotation;
	}

	// Token: 0x06000231 RID: 561 RVA: 0x00012CA8 File Offset: 0x00010EA8
	private void Navigation()
	{
		if (this.leaping)
		{
			return;
		}
		if (this.pathCheckTime > 0f)
		{
			this.pathCheckTime -= Time.deltaTime;
			return;
		}
		if (!this.findingPath)
		{
			this.findingPath = true;
			this.path = this.seeker.StartPath(base.transform.position, this.targetPosition, new OnPathDelegate(this.OnPathComplete));
			this.currentWaypoint = 0;
			return;
		}
		this.pathCheckTime = 1f;
		this.findingPath = false;
	}

	// Token: 0x06000232 RID: 562 RVA: 0x00012D35 File Offset: 0x00010F35
	public void OnPathComplete(Path p)
	{
		this.findingPath = false;
		this.pathCheckTime = 1f;
		if (!p.error)
		{
			this.path = p;
			this.currentWaypoint = 0;
		}
	}

	// Token: 0x06000233 RID: 563 RVA: 0x00012D60 File Offset: 0x00010F60
	private void Animation()
	{
		if (this.geoRoot.gameObject.activeSelf)
		{
			this.fxZone.blend = Mathf.Lerp(this.fxZone.blend, 1f, Time.deltaTime);
		}
		else
		{
			this.fxZone.blend = Mathf.Lerp(this.fxZone.blend, 0f, Time.deltaTime);
		}
		if (this.realVelocity.magnitude <= 0.3f)
		{
			this.anim.SetBool("idle", true);
		}
		else
		{
			this.anim.SetBool("idle", false);
		}
		this.anim.SetFloat("speedmult", this.speedMult * this.aggression * this.aggressionMult);
		this.handParticle.transform.position = this.handParticleLocator.position;
		this.handParticle.transform.rotation = Quaternion.Lerp(this.handParticle.transform.rotation, this.handParticleLocator.rotation, Time.deltaTime);
		this.handParticle.emission.rateOverTimeMultiplier = Mathf.Lerp(25f, 0f, this.distanceToPlayer / 10f);
		if (this.locked)
		{
			return;
		}
		ParticleSystem.ShapeModule shape = this.screenHandEffect.shape;
		if (this.distanceToPlayer < 10f)
		{
			if (!this.screenHandEffect.isPlaying)
			{
				this.screenHandEffect.Play();
			}
		}
		else if (this.screenHandEffect.isPlaying)
		{
			this.screenHandEffect.Stop();
		}
		shape.radius = Mathf.Lerp(2f, 3f, this.distanceToPlayer / 10f);
		this.screenHandEffect.emission.rateOverTimeMultiplier = Mathf.Lerp(9f, 0f, this.distanceToPlayer / 10f);
		if (this.snapGeo)
		{
			Vector3 vector = this.velocity;
			this.facingDir = Vector3.Lerp(this.facingDir, this.GetFacingNormal().normalized, Time.deltaTime * 4f);
			if (Vector3.Dot(this.facingDir, Vector3.up) > 0.4f)
			{
				if (this.climbing)
				{
					this.climbing = false;
				}
			}
			else if (!this.climbing)
			{
				this.climbing = true;
			}
			if (this.climbing)
			{
				Quaternion quaternion = Quaternion.LookRotation(-this.facingDir, vector.normalized);
				Vector3 vector2 = this.facingDir;
				this.geoRoot.transform.rotation = Quaternion.Lerp(this.geoRoot.transform.rotation, quaternion, Time.deltaTime * 8f);
				this.geoRoot.transform.position = Vector3.Lerp(this.geoRoot.transform.position, this.closestSurface + vector2, Time.deltaTime * 2f);
				this.anim.SetBool("climbing", true);
				return;
			}
			Quaternion quaternion2;
			if (vector.normalized != Vector3.zero)
			{
				quaternion2 = Quaternion.LookRotation(vector.normalized, Vector3.up);
			}
			else
			{
				quaternion2 = Quaternion.LookRotation(Vector3.forward, Vector3.up);
			}
			Vector3 point = this.closestSurface;
			RaycastHit raycastHit;
			if (Physics.Raycast(this.modelCenter.position, Vector3.down, out raycastHit, 3f, this.levelMask))
			{
				point = raycastHit.point;
			}
			this.geoRoot.transform.rotation = Quaternion.Lerp(this.geoRoot.transform.rotation, quaternion2, Time.deltaTime * 4f);
			this.geoRoot.transform.position = Vector3.Lerp(this.geoRoot.transform.position, point + Vector3.up * 1.4f, Time.deltaTime * 4f);
			this.anim.SetBool("climbing", false);
			this.ik.DeactivateAllIK();
		}
	}

	// Token: 0x06000234 RID: 564 RVA: 0x00013170 File Offset: 0x00011370
	private Vector3 GetFacingNormal()
	{
		int num = 16;
		Vector3[] array = this.GenerateEquidistantVectors(num);
		Vector3 vector = Vector3.zero;
		vector = this.velocity.normalized * 0.5f;
		float num2 = 3f;
		this.closestSurface = this.modelCenter.position;
		float num3 = float.PositiveInfinity;
		this.seperationVelocity = Vector3.zero;
		for (int i = 0; i < num; i++)
		{
			Vector3 vector2 = array[i] - Vector3.ClampMagnitude(this.velocity * 0.25f, 0.9f);
			RaycastHit raycastHit;
			if (Physics.Raycast(this.modelCenter.position, vector2, out raycastHit, num2, this.levelMask))
			{
				vector += raycastHit.normal;
				if (raycastHit.distance < num3)
				{
					num3 = raycastHit.distance;
					this.closestSurface = raycastHit.point;
				}
				this.seperationVelocity += raycastHit.normal * ((num2 - raycastHit.distance) / num2) / (float)num * 0.15f;
				if (CL_UIManager.debug)
				{
					CL_DebugView.draw.Line(this.modelCenter.position, raycastHit.point, new Color(1f, 0f, 0f, 0.1f));
					CL_DebugView.draw.Cross(raycastHit.point, 0.3f, new Color(1f, 0f, 0f, 0.1f));
				}
			}
			else if (CL_UIManager.debug)
			{
				CL_DebugView.draw.DashedLine(this.modelCenter.position, this.modelCenter.position + vector2 * 3f, 0.1f, 0.1f, new Color(0f, 1f, 0f, 0.02f));
			}
		}
		this.surfaceDir = Vector3.Lerp(this.surfaceDir, vector, Time.deltaTime * 10f);
		return this.surfaceDir;
	}

	// Token: 0x06000235 RID: 565 RVA: 0x00013398 File Offset: 0x00011598
	private void ClimbTowardsTarget()
	{
		if (this.locked)
		{
			return;
		}
		if (this.leaping)
		{
			return;
		}
		if (this.path == null || this.currentWaypoint >= this.path.path.Count || this.currentWaypoint >= this.path.vectorPath.Count)
		{
			return;
		}
		Vector3 vector = this.targetPosition - base.transform.position;
		this.reachedEndOfPath = false;
		vector = (this.path.vectorPath[this.currentWaypoint] - base.transform.position).normalized;
		NodeLink2 nodeLink = NodeLink2.GetNodeLink(this.path.path[this.currentWaypoint]);
		if (nodeLink != null)
		{
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.Label2D(base.transform.position, "Link Node: " + nodeLink.gameObject.name, 8f, LabelAlignment.Center, Color.white);
			}
			AI_ControlNode component = nodeLink.GetComponent<AI_ControlNode>();
			if (component != null)
			{
				if (component.aiNodeType == AI_ControlNode.NodeType.farLeap || component.aiNodeType == AI_ControlNode.NodeType.shortLeap)
				{
					this.StartLeap(component);
				}
				Debug.Log("Found a link node: " + nodeLink.gameObject.name);
				return;
			}
		}
		if (!this.climbing)
		{
			this.curSpeed = Mathf.Lerp(this.curSpeed, this.walkSpeed, Time.deltaTime * 5f);
		}
		else if (this.charging)
		{
			this.curSpeed = Mathf.Lerp(this.curSpeed, this.chargeSpeed, Time.deltaTime * 5f);
		}
		else
		{
			this.curSpeed = Mathf.Lerp(this.curSpeed, this.baseSpeed, Time.deltaTime * 5f);
		}
		float num = this.aggression * this.aggressionMult;
		if (!this.climbing)
		{
			num = 1f;
		}
		this.velocity = Vector3.Lerp(this.velocity, vector * this.curSpeed * this.speedMult * num, Time.deltaTime * 5f) + this.seperationVelocity;
		base.transform.position += this.velocity * Time.deltaTime;
		while (Vector3.Distance(base.transform.position, this.path.vectorPath[this.currentWaypoint]) < this.nextWaypointDistance * 1f)
		{
			if (this.currentWaypoint + 1 >= this.path.vectorPath.Count)
			{
				this.reachedEndOfPath = true;
				break;
			}
			this.currentWaypoint++;
		}
	}

	// Token: 0x06000236 RID: 566 RVA: 0x00013658 File Offset: 0x00011858
	public void StartLeap(AI_ControlNode controlNode)
	{
		if (this.leaping)
		{
			return;
		}
		NodeLink2 linkNode = controlNode.GetLinkNode();
		float num = Vector3.Distance(base.transform.position, linkNode.StartTransform.position);
		float num2 = Vector3.Distance(base.transform.position, linkNode.EndTransform.position);
		Transform transform = ((num < num2) ? linkNode.EndTransform : linkNode.StartTransform);
		Transform transform2 = ((num > num2) ? linkNode.EndTransform : linkNode.StartTransform);
		base.StartCoroutine(this.Leap(transform, transform2, controlNode));
	}

	// Token: 0x06000237 RID: 567 RVA: 0x000136E1 File Offset: 0x000118E1
	public IEnumerator Leap(Transform leapEnd, Transform leapStart, AI_ControlNode controlNode)
	{
		this.curBarkTime = this.barkTime;
		this.leaping = true;
		this.seeker.traversableTags = this.seeker.traversableTags ^ 2;
		this.snapGeo = false;
		float leapAnimTimer = 2.13f;
		float jumpSpeedMult = 1f;
		if (controlNode.aiNodeType == AI_ControlNode.NodeType.shortLeap)
		{
			leapAnimTimer = 1f;
			jumpSpeedMult = 1.2f;
			this.anim.SetBool("shortleap", true);
		}
		else
		{
			this.anim.SetBool("shortleap", false);
		}
		this.anim.SetBool("leaping", true);
		this.anim.SetTrigger("leap");
		bool flag = Vector3.Dot(leapEnd.position - base.transform.position, Vector3.up) > 0f;
		this.clipHandler.PlaySound("teeth:grunt");
		Quaternion startRotation;
		Quaternion endRotation;
		if (flag)
		{
			startRotation = Quaternion.LookRotation(-leapStart.forward, leapStart.up);
			endRotation = Quaternion.LookRotation(-leapEnd.forward, leapEnd.up);
		}
		else
		{
			startRotation = Quaternion.LookRotation(-leapStart.forward, -leapStart.up);
			endRotation = Quaternion.LookRotation(-leapEnd.forward, -leapEnd.up);
		}
		float animTime = 0f;
		while (animTime < leapAnimTimer)
		{
			animTime += Time.deltaTime;
			this.geoRoot.transform.position = Vector3.Lerp(this.geoRoot.transform.position, leapStart.position, Time.deltaTime * 2f);
			this.geoRoot.transform.rotation = Quaternion.Lerp(this.geoRoot.transform.rotation, startRotation, Time.deltaTime * 2f);
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.Arrow(this.geoRoot.transform.position, this.geoRoot.transform.position + this.geoRoot.transform.up, Color.magenta);
				CL_DebugView.draw.Arrow(this.geoRoot.transform.position, this.geoRoot.transform.position + this.geoRoot.transform.forward, Color.blue);
			}
			yield return null;
		}
		this.clipHandler.PlaySound("teeth:leap");
		this.anim.SetBool("leaping", false);
		this.ik.DeactivateAllIK();
		this.geoRoot.transform.position = base.transform.position;
		float jumpTime = 0f;
		Vector3 startPosition = base.transform.position;
		float leapDistance = Vector3.Distance(base.transform.position, leapEnd.position);
		while (jumpTime < 1f)
		{
			jumpTime += Time.deltaTime / leapDistance * this.jumpSpeed * jumpSpeedMult;
			float num = -Mathf.Pow(2f * jumpTime - 1f, 2f) + 1f;
			base.transform.position = Vector3.Lerp(startPosition, leapEnd.position + leapEnd.forward * 0.5f, jumpTime) + Vector3.up * num * this.jumpArcHeight;
			this.geoRoot.transform.position = base.transform.position;
			this.geoRoot.transform.rotation = Quaternion.Lerp(startRotation, endRotation, jumpTime);
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.Arrow(this.geoRoot.transform.position, this.geoRoot.transform.position + this.geoRoot.transform.up, Color.magenta);
				CL_DebugView.draw.Arrow(this.geoRoot.transform.position, this.geoRoot.transform.position + this.geoRoot.transform.forward, Color.blue);
			}
			yield return null;
		}
		this.geoRoot.transform.rotation = endRotation;
		this.geoRoot.transform.position = Vector3.Lerp(this.geoRoot.transform.position, leapEnd.position + leapEnd.forward * 0.5f, Time.deltaTime * 2f);
		this.anim.SetBool("leaping", false);
		this.path = this.seeker.StartPath(base.transform.position, this.targetPosition, new OnPathDelegate(this.OnPathComplete));
		CL_CameraControl.CalculateShake(base.transform.position, 0.15f, 15f);
		this.snapGeo = true;
		this.facingDir = this.GetFacingNormal().normalized;
		this.clipHandler.PlaySound("teeth:land");
		yield return new WaitForSeconds(0.5f);
		this.facingDir = this.GetFacingNormal().normalized;
		this.geoRoot.transform.rotation = endRotation;
		this.leaping = false;
		yield return new WaitForSeconds(2f);
		this.seeker.traversableTags = this.seeker.traversableTags | 2;
		yield break;
	}

	// Token: 0x06000238 RID: 568 RVA: 0x00013708 File Offset: 0x00011908
	public override bool Damage(float amount, string type)
	{
		if (this.currentState.name == "retreat")
		{
			return false;
		}
		this.clipHandler.PlaySound("teeth:hurt");
		if (type == "Hammer")
		{
			amount /= 8f;
		}
		this.curStunDamage += amount;
		this.curBarkTime = this.barkTime;
		Debug.Log("Taken Damage!");
		if (this.curStunDamage >= this.stunDamage || type == "flare")
		{
			this.curStunDamage = 0f;
			this.ChangeState("retreat");
		}
		return false;
	}

	// Token: 0x06000239 RID: 569 RVA: 0x000137AC File Offset: 0x000119AC
	private Vector3[] GenerateEquidistantVectors(int numPoints)
	{
		Vector3[] array = new Vector3[numPoints];
		float num = 3.1415927f * (3f - Mathf.Sqrt(5f));
		for (int i = 0; i < numPoints; i++)
		{
			float num2 = 1f - (float)i / (float)(numPoints - 1) * 2f;
			float num3 = Mathf.Sqrt(1f - num2 * num2);
			float num4 = num * (float)i;
			float num5 = Mathf.Cos(num4) * num3;
			float num6 = Mathf.Sin(num4) * num3;
			array[i] = new Vector3(num5, num2, num6);
		}
		return array;
	}

	// Token: 0x0600023A RID: 570 RVA: 0x00013832 File Offset: 0x00011A32
	public void Despawn()
	{
		Object.Destroy(base.transform.parent.gameObject);
	}

	// Token: 0x0600023B RID: 571 RVA: 0x0001384C File Offset: 0x00011A4C
	public void SetTeethAILock(string[] args)
	{
		if (args.Length == 0)
		{
			this.lockAI = !this.lockAI;
			CommandConsole.Log("Teeth AI set to " + this.lockAI.ToString(), false);
			return;
		}
		this.lockAI = Convert.ToBoolean(args[0]);
		CommandConsole.Log("Teeth AI set to " + this.lockAI.ToString(), false);
	}

	// Token: 0x0600023C RID: 572 RVA: 0x000138B1 File Offset: 0x00011AB1
	public void SetCohesionEffect(float f)
	{
		this.cohesionEffect = f;
	}

	// Token: 0x0600023D RID: 573 RVA: 0x000138BA File Offset: 0x00011ABA
	public void SetBurnEffect(float f)
	{
		this.burnEffect = f;
	}

	// Token: 0x0600023E RID: 574 RVA: 0x000138C3 File Offset: 0x00011AC3
	public override void OffsetEntity(float amount)
	{
		if (base.transform.parent.parent == null)
		{
			base.transform.parent.position += Vector3.up * amount;
		}
	}

	// Token: 0x040002C5 RID: 709
	public static DEN_Teeth instance;

	// Token: 0x040002C6 RID: 710
	public Transform geoRoot;

	// Token: 0x040002C7 RID: 711
	public Animator anim;

	// Token: 0x040002C8 RID: 712
	public IK_Controller ik;

	// Token: 0x040002C9 RID: 713
	public Transform modelCenter;

	// Token: 0x040002CA RID: 714
	public ParticleSystem handParticle;

	// Token: 0x040002CB RID: 715
	public Transform handParticleLocator;

	// Token: 0x040002CC RID: 716
	public ParticleSystem screenHandEffect;

	// Token: 0x040002CD RID: 717
	private Transform screenHandEffectParent;

	// Token: 0x040002CE RID: 718
	public SkinnedMeshRenderer meshRenderer;

	// Token: 0x040002CF RID: 719
	private Material bodyMaterial;

	// Token: 0x040002D0 RID: 720
	public float cohesionEffectMultiplier = 1f;

	// Token: 0x040002D1 RID: 721
	public float cohesionColorMultiplier = 5f;

	// Token: 0x040002D2 RID: 722
	public FX_Zone fxZone;

	// Token: 0x040002D3 RID: 723
	public Transform cameraTakeover;

	// Token: 0x040002D4 RID: 724
	public float baseSpeed = 3f;

	// Token: 0x040002D5 RID: 725
	public float chargeSpeed = 5f;

	// Token: 0x040002D6 RID: 726
	public float walkSpeed = 1f;

	// Token: 0x040002D7 RID: 727
	public float stopDistance = 3f;

	// Token: 0x040002D8 RID: 728
	public float jumpSpeed = 5f;

	// Token: 0x040002D9 RID: 729
	public float jumpArcHeight = 3f;

	// Token: 0x040002DA RID: 730
	private float curSpeed = 1f;

	// Token: 0x040002DB RID: 731
	internal float speedMult;

	// Token: 0x040002DC RID: 732
	internal bool charging;

	// Token: 0x040002DD RID: 733
	internal bool locked;

	// Token: 0x040002DE RID: 734
	private Vector3 facingDir;

	// Token: 0x040002DF RID: 735
	private bool climbing = true;

	// Token: 0x040002E0 RID: 736
	public LayerMask levelMask;

	// Token: 0x040002E1 RID: 737
	public float attackDamage = 1f;

	// Token: 0x040002E2 RID: 738
	public float aggression = 1f;

	// Token: 0x040002E3 RID: 739
	public float aggressionMult = 1f;

	// Token: 0x040002E4 RID: 740
	public float aggressionRate = 0.01f;

	// Token: 0x040002E5 RID: 741
	public float aggressionOutOfRangeModifier = 3f;

	// Token: 0x040002E6 RID: 742
	public float barkTime = 10f;

	// Token: 0x040002E7 RID: 743
	internal float curBarkTime;

	// Token: 0x040002E8 RID: 744
	private float pathCheckTime = 1f;

	// Token: 0x040002E9 RID: 745
	private bool findingPath;

	// Token: 0x040002EA RID: 746
	private Seeker seeker;

	// Token: 0x040002EB RID: 747
	private Path path;

	// Token: 0x040002EC RID: 748
	private float nextWaypointDistance = 3f;

	// Token: 0x040002ED RID: 749
	private int currentWaypoint;

	// Token: 0x040002EE RID: 750
	private bool reachedEndOfPath;

	// Token: 0x040002EF RID: 751
	private float distanceToPlayer;

	// Token: 0x040002F0 RID: 752
	public bool disableAI;

	// Token: 0x040002F1 RID: 753
	private Vector3 velocity;

	// Token: 0x040002F2 RID: 754
	private Vector3 realVelocity;

	// Token: 0x040002F3 RID: 755
	private Vector3 lastObjectPosition;

	// Token: 0x040002F4 RID: 756
	private Vector3 seperationVelocity;

	// Token: 0x040002F5 RID: 757
	private Vector3 surfaceDir;

	// Token: 0x040002F6 RID: 758
	private Vector3 closestSurface;

	// Token: 0x040002F7 RID: 759
	private bool snapGeo = true;

	// Token: 0x040002F8 RID: 760
	private bool leaping;

	// Token: 0x040002F9 RID: 761
	public float stunDamage = 3f;

	// Token: 0x040002FA RID: 762
	private float curStunDamage;

	// Token: 0x040002FB RID: 763
	private bool lockAI;

	// Token: 0x040002FC RID: 764
	private float cohesionEffect = 0.01f;

	// Token: 0x040002FD RID: 765
	private float burnEffect;

	// Token: 0x02000222 RID: 546
	public enum AIMoveStates
	{
		// Token: 0x04000E56 RID: 3670
		normal,
		// Token: 0x04000E57 RID: 3671
		leaping,
		// Token: 0x04000E58 RID: 3672
		climbing,
		// Token: 0x04000E59 RID: 3673
		crawling,
		// Token: 0x04000E5A RID: 3674
		falling
	}
}
