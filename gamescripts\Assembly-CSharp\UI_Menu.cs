﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200012D RID: 301
public class UI_Menu : MonoBehaviour
{
	// Token: 0x060008E4 RID: 2276 RVA: 0x0003EB63 File Offset: 0x0003CD63
	private void OnEnable()
	{
		this.EnableMenu();
	}

	// Token: 0x060008E5 RID: 2277 RVA: 0x0003EB6C File Offset: 0x0003CD6C
	internal void DisableMenus(UI_MenuButton activeButton)
	{
		this.group.interactable = false;
		foreach (UI_MenuButton ui_MenuButton in this.menuButtons)
		{
			if (ui_MenuButton != activeButton)
			{
				ui_MenuButton.DisableScreen();
			}
		}
	}

	// Token: 0x060008E6 RID: 2278 RVA: 0x0003EBD4 File Offset: 0x0003CDD4
	public void EnableMenu()
	{
		if (this.group == null)
		{
			this.group = base.GetComponent<CanvasGroup>();
		}
		this.group.interactable = true;
	}

	// Token: 0x060008E7 RID: 2279 RVA: 0x0003EBFC File Offset: 0x0003CDFC
	private void Awake()
	{
		this.menuButtons = new List<UI_MenuButton>();
		this.menuButtons.AddRange(base.GetComponentsInChildren<UI_MenuButton>());
		foreach (UI_MenuButton ui_MenuButton in this.menuButtons)
		{
			ui_MenuButton.Initialize(this);
		}
		this.group = base.GetComponent<CanvasGroup>();
		this.DisableMenus(null);
		this.group.interactable = true;
	}

	// Token: 0x04000A39 RID: 2617
	private List<UI_MenuButton> menuButtons;

	// Token: 0x04000A3A RID: 2618
	private CanvasGroup group;
}
