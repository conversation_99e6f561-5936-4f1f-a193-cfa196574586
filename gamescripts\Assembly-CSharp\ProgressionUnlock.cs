﻿using System;
using UnityEngine;

// Token: 0x020000BF RID: 191
[CreateAssetMenu(fileName = "New Unlock", menuName = "White Knuckle/Progression/Unlock")]
public class ProgressionUnlock : ScriptableObject
{
	// Token: 0x0600064B RID: 1611 RVA: 0x000338B0 File Offset: 0x00031AB0
	public bool CheckUnlock()
	{
		if (SettingsManager.settings.unlockAll)
		{
			return true;
		}
		if (this.requirement == ProgressionUnlock.RequirementType.experience)
		{
			if (CL_ProgressionManager.playerExperience >= this.unlockExperience)
			{
				this.state = CL_ProgressionManager.playerExperience >= this.unlockExperience;
			}
		}
		else if (this.requirement == ProgressionUnlock.RequirementType.achievement)
		{
			this.state = CL_AchievementManager.GetAchievementValue(this.unlockAchievement);
		}
		else if (this.requirement == ProgressionUnlock.RequirementType.stat)
		{
			this.state = StatManager.GetTotalStatisticInt(this.statName) >= this.statRequirement;
		}
		if (CL_GameManager.gMan != null)
		{
			CL_GameManager.SetGameFlag("unlock_" + base.name, this.state, "", false);
		}
		return this.state;
	}

	// Token: 0x0600064C RID: 1612 RVA: 0x00033970 File Offset: 0x00031B70
	public void TryUpgrade()
	{
		if (this.upgradeOldStat && !StatManager.HasGlobalStat(this.statName))
		{
			if (StatManager.GetTotalStatisticInt(this.upgradeOldStatID) > 0)
			{
				Debug.Log("Upgrading an old stat!");
				StatManager.saveData.gameStats.UpdateStatistic(this.statName, StatManager.GetTotalStatisticInt(this.upgradeOldStatID), StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
				return;
			}
			StatManager.saveData.gameStats.UpdateStatistic(this.statName, 0, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Set, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
		}
	}

	// Token: 0x0600064D RID: 1613 RVA: 0x000339F3 File Offset: 0x00031BF3
	public float GetLastProgress()
	{
		return this.lastProgress;
	}

	// Token: 0x0600064E RID: 1614 RVA: 0x000339FB File Offset: 0x00031BFB
	public float GetProgress()
	{
		if (this.requirement == ProgressionUnlock.RequirementType.stat)
		{
			this.lastProgress = (float)StatManager.GetTotalStatisticInt(this.statName) / (float)this.statRequirement;
			return this.lastProgress;
		}
		return 1f;
	}

	// Token: 0x0600064F RID: 1615 RVA: 0x00033A2C File Offset: 0x00031C2C
	public string GetProgressString()
	{
		return string.Format("{0}/{1}", StatManager.GetTotalStatisticInt(this.statName), this.statRequirement);
	}

	// Token: 0x040007CA RID: 1994
	public string id;

	// Token: 0x040007CB RID: 1995
	public ProgressionUnlock.RequirementType requirement;

	// Token: 0x040007CC RID: 1996
	public int unlockExperience;

	// Token: 0x040007CD RID: 1997
	public string unlockAchievement;

	// Token: 0x040007CE RID: 1998
	public string statName;

	// Token: 0x040007CF RID: 1999
	public bool upgradeOldStat;

	// Token: 0x040007D0 RID: 2000
	public string upgradeOldStatID;

	// Token: 0x040007D1 RID: 2001
	public int statRequirement;

	// Token: 0x040007D2 RID: 2002
	public bool state;

	// Token: 0x040007D3 RID: 2003
	[Header("Unlock Details")]
	public Sprite unlockIcon;

	// Token: 0x040007D4 RID: 2004
	public string unlockTitle;

	// Token: 0x040007D5 RID: 2005
	[TextArea]
	public string unlockDescription;

	// Token: 0x040007D6 RID: 2006
	[TextArea]
	public string unlockLogDescription;

	// Token: 0x040007D7 RID: 2007
	public string unlockHint;

	// Token: 0x040007D8 RID: 2008
	public bool showProgression;

	// Token: 0x040007D9 RID: 2009
	public string progressionString = "used";

	// Token: 0x040007DA RID: 2010
	private float lastProgress;

	// Token: 0x02000285 RID: 645
	public enum RequirementType
	{
		// Token: 0x0400105F RID: 4191
		experience,
		// Token: 0x04001060 RID: 4192
		achievement,
		// Token: 0x04001061 RID: 4193
		stat
	}
}
