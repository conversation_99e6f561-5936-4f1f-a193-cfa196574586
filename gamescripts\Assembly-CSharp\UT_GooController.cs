﻿using System;
using UnityEngine;

// Token: 0x0200016F RID: 367
public class UT_GooController : MonoBehaviour
{
	// Token: 0x06000A46 RID: 2630 RVA: 0x0004427C File Offset: 0x0004247C
	public void SetSpeedMult(float s = 1f)
	{
		if (DEN_DeathFloor.instance == null)
		{
			return;
		}
		DEN_DeathFloor.instance.SetSpeedMultiplier(s);
	}

	// Token: 0x06000A47 RID: 2631 RVA: 0x00044297 File Offset: 0x00042497
	public void SetSpeed(float s)
	{
		if (DEN_DeathFloor.instance == null)
		{
			return;
		}
		DEN_DeathFloor.instance.SetSpeed(s);
	}

	// Token: 0x06000A48 RID: 2632 RVA: 0x000442B2 File Offset: 0x000424B2
	public void SetHeightRelativeToController(float h)
	{
		if (DEN_DeathFloor.instance == null)
		{
			return;
		}
		DEN_DeathFloor.instance.SetHeightRelativeToTransform(h, base.transform);
	}

	// Token: 0x06000A49 RID: 2633 RVA: 0x000442D3 File Offset: 0x000424D3
	public void MoveToTransformHeight(Transform t)
	{
		if (DEN_DeathFloor.instance == null)
		{
			return;
		}
		DEN_DeathFloor.instance.MoveToHeightOfTransform(t);
	}

	// Token: 0x06000A4A RID: 2634 RVA: 0x000442EE File Offset: 0x000424EE
	public void RaiseOverTime(float amount)
	{
		if (DEN_DeathFloor.instance == null)
		{
			return;
		}
		DEN_DeathFloor.instance.RaiseOverTime(amount * this.raiseOverTimeMultiplier);
	}

	// Token: 0x06000A4B RID: 2635 RVA: 0x00044310 File Offset: 0x00042510
	public void SetHeight(float h)
	{
		if (DEN_DeathFloor.instance == null)
		{
			return;
		}
		DEN_DeathFloor.instance.SetHeight(h);
	}

	// Token: 0x06000A4C RID: 2636 RVA: 0x0004432B File Offset: 0x0004252B
	public void SetSpeedIncreaseMultiplier(float m)
	{
		if (DEN_DeathFloor.instance == null)
		{
			return;
		}
		DEN_DeathFloor.instance.SetSpeedIncreaseMultiplier(m);
	}

	// Token: 0x06000A4D RID: 2637 RVA: 0x00044346 File Offset: 0x00042546
	public void AdjustSpeed(float s)
	{
		if (DEN_DeathFloor.instance == null)
		{
			return;
		}
		DEN_DeathFloor.instance.AdjustSpeed(s);
	}

	// Token: 0x06000A4E RID: 2638 RVA: 0x00044361 File Offset: 0x00042561
	public void SetTargetVolume(float v)
	{
		if (DEN_DeathFloor.instance == null)
		{
			return;
		}
		DEN_DeathFloor.instance.SetTargetVolume(v);
	}

	// Token: 0x06000A4F RID: 2639 RVA: 0x0004437C File Offset: 0x0004257C
	public void SetActive(bool b)
	{
		if (DEN_DeathFloor.instance == null)
		{
			return;
		}
		DEN_DeathFloor.instance.SetActive(b);
	}

	// Token: 0x06000A50 RID: 2640 RVA: 0x00044397 File Offset: 0x00042597
	public void DestroyMass()
	{
		if (DEN_DeathFloor.instance == null)
		{
			return;
		}
		Object.Destroy(DEN_DeathFloor.instance.gameObject);
		DEN_DeathFloor.instance = null;
	}

	// Token: 0x04000B52 RID: 2898
	public float raiseOverTimeMultiplier = 1f;
}
