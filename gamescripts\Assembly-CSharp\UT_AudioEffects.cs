﻿using System;
using UnityEngine;

// Token: 0x020000C7 RID: 199
[RequireComponent(typeof(AudioSource))]
public class UT_AudioEffects : MonoBehaviour
{
	// Token: 0x06000684 RID: 1668 RVA: 0x00034B84 File Offset: 0x00032D84
	private void Awake()
	{
		this.aud = base.GetComponent<AudioSource>();
		if (this.setStartVolume)
		{
			this.aud.volume = this.startVolume;
			this.targetVolume = this.startVolume;
			return;
		}
		this.targetVolume = this.aud.volume;
		this.startVolume = this.aud.volume;
		if (this.startVolume == 0f)
		{
			this.startVolume = 1f;
		}
	}

	// Token: 0x06000685 RID: 1669 RVA: 0x00034C00 File Offset: 0x00032E00
	private void Update()
	{
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		if (this.slowPitch)
		{
			this.aud.pitch = Mathf.Lerp(this.aud.pitch, this.targetPitch, Time.deltaTime * this.pitchSpeed);
		}
		if (this.lerpVolume)
		{
			this.aud.volume = Mathf.Lerp(this.aud.volume, this.targetVolume, this.lerpSpeed * Time.deltaTime);
		}
	}

	// Token: 0x06000686 RID: 1670 RVA: 0x00034C84 File Offset: 0x00032E84
	public void SetPitch(float f)
	{
		if (!this.slowPitch)
		{
			this.aud.pitch = f;
		}
		this.targetPitch = f;
	}

	// Token: 0x06000687 RID: 1671 RVA: 0x00034CA1 File Offset: 0x00032EA1
	public void SetVolume(float f)
	{
		if (!this.aud.isPlaying)
		{
			this.aud.Play();
		}
		this.targetVolume = f;
	}

	// Token: 0x06000688 RID: 1672 RVA: 0x00034CC2 File Offset: 0x00032EC2
	public void FadeOut()
	{
		this.targetVolume = 0f;
	}

	// Token: 0x06000689 RID: 1673 RVA: 0x00034CCF File Offset: 0x00032ECF
	public void FadeIn()
	{
		if (this.aud == null)
		{
			return;
		}
		this.targetVolume = this.startVolume;
		if (!this.aud.isPlaying)
		{
			this.aud.Play();
		}
	}

	// Token: 0x040007F2 RID: 2034
	private AudioSource aud;

	// Token: 0x040007F3 RID: 2035
	public bool slowPitch;

	// Token: 0x040007F4 RID: 2036
	public bool lerpVolume;

	// Token: 0x040007F5 RID: 2037
	public float lerpSpeed = 1f;

	// Token: 0x040007F6 RID: 2038
	public float pitchSpeed = 1f;

	// Token: 0x040007F7 RID: 2039
	private float targetPitch;

	// Token: 0x040007F8 RID: 2040
	private float targetVolume;

	// Token: 0x040007F9 RID: 2041
	public bool setStartVolume;

	// Token: 0x040007FA RID: 2042
	public float startVolume;
}
