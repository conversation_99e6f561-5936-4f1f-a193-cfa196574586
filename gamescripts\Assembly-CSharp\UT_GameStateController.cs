﻿using System;
using UnityEngine;

// Token: 0x0200016D RID: 365
public class UT_GameStateController : MonoBehaviour
{
	// Token: 0x06000A29 RID: 2601 RVA: 0x0004404A File Offset: 0x0004224A
	public void Send()
	{
		CL_GameManager.ChangeState(this.state);
	}

	// Token: 0x06000A2A RID: 2602 RVA: 0x00044057 File Offset: 0x00042257
	public void RestartScene()
	{
		CL_GameManager.ChangeState("restart");
	}

	// Token: 0x06000A2B RID: 2603 RVA: 0x00044063 File Offset: 0x00042263
	public void LoadMainMenu()
	{
		CL_GameManager.gMan.LoadScene("Main-Menu", false);
	}

	// Token: 0x06000A2C RID: 2604 RVA: 0x00044075 File Offset: 0x00042275
	public void SetGamemode(M_Gamemode gamemode)
	{
		CL_GameManager.gMan.SetGamemode(gamemode);
	}

	// Token: 0x06000A2D RID: 2605 RVA: 0x00044082 File Offset: 0x00042282
	public void LoadScene(string sceneName)
	{
		CL_GameManager.gMan.LoadScene(sceneName, false);
	}

	// Token: 0x06000A2E RID: 2606 RVA: 0x00044090 File Offset: 0x00042290
	public void SetTimerState(bool b)
	{
		CL_GameManager.gMan.SetTimerState(b);
	}

	// Token: 0x06000A2F RID: 2607 RVA: 0x0004409D File Offset: 0x0004229D
	public void SetPause(bool b)
	{
		if (b)
		{
			CL_GameManager.gMan.Pause();
			return;
		}
		CL_GameManager.gMan.UnPause();
	}

	// Token: 0x06000A30 RID: 2608 RVA: 0x000440B7 File Offset: 0x000422B7
	public void ResetScores()
	{
		StatManager.instance.EraseStats();
		CL_GameManager.gMan.LoadScene("Main-Menu", false);
	}

	// Token: 0x06000A31 RID: 2609 RVA: 0x000440D4 File Offset: 0x000422D4
	public void ResetWorld(bool keepItems = false)
	{
		CL_SaveManager.SaveState saveState;
		if (!keepItems)
		{
			saveState = CL_SaveManager.CreateOrUpdateSaveState("reset", "reset", CL_SaveManager.SaveState.SaveType.reset, 1, true);
		}
		else
		{
			saveState = CL_SaveManager.CreateOrUpdateSaveState("reset", "reset", CL_SaveManager.SaveState.SaveType.resetwithitems, 1, true);
		}
		CL_SaveManager.LoadSave(saveState, null);
	}

	// Token: 0x06000A32 RID: 2610 RVA: 0x00044113 File Offset: 0x00042313
	public void RefreshStatText()
	{
		if (StatManager.refreshStatText != null)
		{
			StatManager.refreshStatText();
		}
	}

	// Token: 0x06000A33 RID: 2611 RVA: 0x00044126 File Offset: 0x00042326
	public void SetWhiteOutFade(float f)
	{
		CL_UIManager.instance.SetVignetteTarget("whiteout", f);
	}

	// Token: 0x06000A34 RID: 2612 RVA: 0x00044138 File Offset: 0x00042338
	public void FlashVignette(string n)
	{
		CL_UIManager.instance.FlashVignette(n);
	}

	// Token: 0x06000A35 RID: 2613 RVA: 0x00044145 File Offset: 0x00042345
	public void QuickFlashVignette(string n)
	{
		CL_UIManager.instance.QuickFlashVignette(n);
	}

	// Token: 0x06000A36 RID: 2614 RVA: 0x00044152 File Offset: 0x00042352
	public void CreateBugReport()
	{
		BugReporter.CreateBugReport();
	}

	// Token: 0x06000A37 RID: 2615 RVA: 0x00044159 File Offset: 0x00042359
	public void SetPresetSeed(string s)
	{
		WorldLoader.SetPresetSeed(s);
	}

	// Token: 0x06000A38 RID: 2616 RVA: 0x00044161 File Offset: 0x00042361
	public void SetSafe(bool b)
	{
		CL_GameManager.SetSafe(b);
	}

	// Token: 0x06000A39 RID: 2617 RVA: 0x00044169 File Offset: 0x00042369
	public void DeleteSave()
	{
		CL_SaveManager.DeleteSessionSave(CL_GameManager.GetGamemodeName(false, false), SettingsManager.settings.g_hard);
	}

	// Token: 0x06000A3A RID: 2618 RVA: 0x00044181 File Offset: 0x00042381
	public void SaveRun(bool clearStats = true)
	{
		CL_SaveManager.instance.SaveSession(clearStats, true, false, "", false);
	}

	// Token: 0x06000A3B RID: 2619 RVA: 0x00044197 File Offset: 0x00042397
	public void SaveLastSessionToFile()
	{
		CL_SaveManager.instance.SaveLastSessionToFile(true);
	}

	// Token: 0x06000A3C RID: 2620 RVA: 0x000441A4 File Offset: 0x000423A4
	public void ClearSessionStats()
	{
		StatManager.sessionStats = new StatManager.GameStats();
	}

	// Token: 0x06000A3D RID: 2621 RVA: 0x000441B0 File Offset: 0x000423B0
	public void StopAllWorldEvents()
	{
		CL_EventManager.StopAllEvents();
	}

	// Token: 0x06000A3E RID: 2622 RVA: 0x000441B7 File Offset: 0x000423B7
	public void SetRoaches(int i)
	{
		CL_GameManager.roaches = i;
	}

	// Token: 0x06000A3F RID: 2623 RVA: 0x000441BF File Offset: 0x000423BF
	public void KillPlayer()
	{
		ENT_Player.playerObject.Kill("");
	}

	// Token: 0x06000A40 RID: 2624 RVA: 0x000441D0 File Offset: 0x000423D0
	public void LoadGamemode(M_Gamemode gm)
	{
		CL_GameManager.gamemode = gm;
		CL_GameManager.gMan.LoadScene(gm.gamemodeScene, false);
	}

	// Token: 0x04000B4C RID: 2892
	public string state;
}
