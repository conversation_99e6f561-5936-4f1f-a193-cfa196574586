﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x0200019B RID: 411
public class UT_StatText : MonoBehaviour
{
	// Token: 0x06000AFB RID: 2811 RVA: 0x000474BD File Offset: 0x000456BD
	private void Awake()
	{
		if (this.text == null)
		{
			this.text = base.GetComponent<TMP_Text>();
		}
	}

	// Token: 0x06000AFC RID: 2812 RVA: 0x000474D9 File Offset: 0x000456D9
	private void OnEnable()
	{
		this.RefreshText("");
		StatManager.refreshStatText = (Action)Delegate.Combine(StatManager.refreshStatText, new Action(this.Refresh));
	}

	// Token: 0x06000AFD RID: 2813 RVA: 0x00047506 File Offset: 0x00045706
	private void OnDisable()
	{
		StatManager.refreshStatText = (Action)Delegate.Remove(StatManager.refreshStatText, new Action(this.Refresh));
	}

	// Token: 0x06000AFE RID: 2814 RVA: 0x00047528 File Offset: 0x00045728
	public void Refresh()
	{
		this.RefreshText(this.gamemode);
	}

	// Token: 0x06000AFF RID: 2815 RVA: 0x00047538 File Offset: 0x00045738
	public void RefreshText(string customGameMode = "")
	{
		StatManager.GameStats gameStats = null;
		if (this.source == UT_StatText.StatSource.global)
		{
			gameStats = StatManager.saveData.gameStats;
		}
		else if (this.source == UT_StatText.StatSource.gamemode || this.source == UT_StatText.StatSource.custom || this.source == UT_StatText.StatSource.modeHolder)
		{
			if (this.source == UT_StatText.StatSource.custom)
			{
				if (!(customGameMode != ""))
				{
					this.text.text = this.defaultText;
					return;
				}
				this.gamemode = customGameMode;
			}
			else if (this.source == UT_StatText.StatSource.modeHolder && this.modeHolder != null)
			{
				this.gamemode = this.modeHolder.GetComponent<GamemodeHolder>().GetGamemode().gamemodeName;
			}
			if (StatManager.saveData.GetGameMode(this.gamemode) != null)
			{
				gameStats = StatManager.saveData.GetGameMode(this.gamemode).stats;
			}
		}
		else if (this.source == UT_StatText.StatSource.baseGamemode)
		{
			if (StatManager.saveData.GetGameMode(CL_GameManager.GetGamemodeName(true, false)) != null)
			{
				gameStats = StatManager.saveData.GetGameMode(CL_GameManager.GetGamemodeName(true, false)).stats;
			}
		}
		else if (this.source == UT_StatText.StatSource.session)
		{
			gameStats = StatManager.sessionStats;
		}
		else if (gameStats == null)
		{
			this.text.text = this.defaultText;
		}
		if (gameStats == null)
		{
			this.text.text = this.defaultText;
			return;
		}
		StatManager.Statistic statistic = gameStats.GetStatistic(this.statistic);
		string text = statistic.GetString();
		if (this.roundToInt && statistic.type == StatManager.Statistic.DataType.Float && statistic.displayType == StatManager.Statistic.DisplayType.Default)
		{
			text = Mathf.RoundToInt((float)gameStats.GetStatistic(this.statistic).GetValue()).ToString();
		}
		if (text == "")
		{
			this.text.text = this.defaultText;
			return;
		}
		this.text.text = this.textPrefix + text;
	}

	// Token: 0x04000BFE RID: 3070
	public UT_StatText.StatSource source;

	// Token: 0x04000BFF RID: 3071
	public string gamemode;

	// Token: 0x04000C00 RID: 3072
	public GameObject modeHolder;

	// Token: 0x04000C01 RID: 3073
	public string statistic;

	// Token: 0x04000C02 RID: 3074
	public string defaultText = "";

	// Token: 0x04000C03 RID: 3075
	public string textPrefix;

	// Token: 0x04000C04 RID: 3076
	private TMP_Text text;

	// Token: 0x04000C05 RID: 3077
	public bool roundToInt;

	// Token: 0x020002E4 RID: 740
	public enum StatSource
	{
		// Token: 0x04001269 RID: 4713
		gamemode,
		// Token: 0x0400126A RID: 4714
		baseGamemode,
		// Token: 0x0400126B RID: 4715
		session,
		// Token: 0x0400126C RID: 4716
		global,
		// Token: 0x0400126D RID: 4717
		custom,
		// Token: 0x0400126E RID: 4718
		modeHolder
	}
}
