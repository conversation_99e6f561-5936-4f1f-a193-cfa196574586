﻿using System;
using UnityEngine;

// Token: 0x020000D8 RID: 216
public class UT_PlaySoundOnProximity : MonoBehaviour
{
	// Token: 0x060006D2 RID: 1746 RVA: 0x000360E1 File Offset: 0x000342E1
	private void Start()
	{
		this.aud = base.GetComponent<AudioSource>();
	}

	// Token: 0x060006D3 RID: 1747 RVA: 0x000360EF File Offset: 0x000342EF
	private void Update()
	{
	}

	// Token: 0x04000856 RID: 2134
	private AudioSource aud;

	// Token: 0x04000857 RID: 2135
	public bool playOnce = true;
}
