﻿using System;
using UnityEngine;

// Token: 0x0200009C RID: 156
[CreateAssetMenu(fileName = "New Insertable Item Interactor", menuName = "White Knuckle/Item/Interactor-Insertable")]
public class Item_Interactor_Insertable : Item_Interactor
{
	// Token: 0x0600051D RID: 1309 RVA: 0x0002B530 File Offset: 0x00029730
	public override void Interact(ENT_Player.Hand curhand, ref bool interacting, string fireButton, ref InteractHit hit, out Sprite interactSprite, Collider hitCollider, ObjectTagger tagger, Clickable clickable, Item item = null)
	{
		base.Interact(curhand, ref interacting, fireButton, ref hit, out interactSprite, hitCollider, tagger, clickable, item);
	}
}
