﻿using System;
using Drawing;
using UnityEngine;

// Token: 0x02000173 RID: 371
public class UT_Heater : MonoBehaviourGizmos
{
	// Token: 0x06000A5F RID: 2655 RVA: 0x00044794 File Offset: 0x00042994
	private void FixedUpdate()
	{
		if (CL_GameManager.gMan == null || CL_GameManager.gMan.localPlayer == null)
		{
			return;
		}
		if (this.player == null)
		{
			this.player = CL_GameManager.gMan.localPlayer;
		}
		if (CL_GameManager.gMan.PlayerDistance(base.transform.position) > this.playerVicinity)
		{
			return;
		}
		this.Heat();
	}

	// Token: 0x06000A60 RID: 2656 RVA: 0x00044804 File Offset: 0x00042A04
	public void Heat()
	{
		if (this.freezingBuff == null)
		{
			if (this.player.curBuffs.HasBuffContainer("freezing"))
			{
				this.freezingBuff = this.player.curBuffs.GetBuffContainer("freezing");
			}
			return;
		}
		this.freezingBuff.SetMultiplier(Mathf.Clamp01(this.freezingBuff.GetMultiplier() - Time.deltaTime * this.heatRate));
	}

	// Token: 0x06000A61 RID: 2657 RVA: 0x00044874 File Offset: 0x00042A74
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			Draw.SphereOutline(base.transform.position, this.playerVicinity, new Color(1f, 0.2f, 0f, 0.25f));
			return;
		}
		Draw.SphereOutline(base.transform.position, this.playerVicinity, new Color(1f, 0.2f, 0f, 0.003f));
	}

	// Token: 0x04000B61 RID: 2913
	private ENT_Player player;

	// Token: 0x04000B62 RID: 2914
	public float playerVicinity = 10f;

	// Token: 0x04000B63 RID: 2915
	public float heatRate = 0.5f;

	// Token: 0x04000B64 RID: 2916
	private BuffContainer freezingBuff;
}
