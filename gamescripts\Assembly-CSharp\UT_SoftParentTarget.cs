﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000194 RID: 404
public class UT_SoftParentTarget : MonoBehaviour
{
	// Token: 0x06000AE1 RID: 2785 RVA: 0x00046EE0 File Offset: 0x000450E0
	private void OnEnable()
	{
		if (this.children == null)
		{
			return;
		}
		foreach (UT_SoftParent ut_SoftParent in this.children)
		{
			if (!(ut_SoftParent == null))
			{
				ut_SoftParent.CheckEnable();
			}
		}
	}

	// Token: 0x06000AE2 RID: 2786 RVA: 0x00046F44 File Offset: 0x00045144
	public void OnDestroy()
	{
		if (this.children == null)
		{
			return;
		}
		foreach (UT_SoftParent ut_SoftParent in this.children)
		{
			if (!(ut_SoftParent == null))
			{
				ut_SoftParent.DestroySelf();
			}
		}
	}

	// Token: 0x06000AE3 RID: 2787 RVA: 0x00046FA8 File Offset: 0x000451A8
	public void AddChild(UT_SoftParent sp)
	{
		if (this.children == null)
		{
			this.children = new List<UT_SoftParent>();
		}
		this.children.Add(sp);
	}

	// Token: 0x04000BDF RID: 3039
	private List<UT_SoftParent> children;
}
