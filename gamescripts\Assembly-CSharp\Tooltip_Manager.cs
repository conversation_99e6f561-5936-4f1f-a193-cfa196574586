﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x02000103 RID: 259
public class Tooltip_Manager : MonoBehaviour
{
	// Token: 0x060007EC RID: 2028 RVA: 0x0003ACF4 File Offset: 0x00038EF4
	private void OnEnable()
	{
		Tooltip_Manager.OnMouseHover = (Action<string, Vector2>)Delegate.Combine(Tooltip_Manager.OnMouseHover, new Action<string, Vector2>(this.ShowTip));
		Tooltip_Manager.OnMouseLoseFocus = (Action)Delegate.Combine(Tooltip_Manager.OnMouseLoseFocus, new Action(this.HideTip));
	}

	// Token: 0x060007ED RID: 2029 RVA: 0x0003AD44 File Offset: 0x00038F44
	private void OnDisable()
	{
		Tooltip_Manager.OnMouseHover = (Action<string, Vector2>)Delegate.Remove(Tooltip_Manager.OnMouseHover, new Action<string, Vector2>(this.ShowTip));
		Tooltip_Manager.OnMouseLoseFocus = (Action)Delegate.Remove(Tooltip_Manager.OnMouseLoseFocus, new Action(this.HideTip));
	}

	// Token: 0x060007EE RID: 2030 RVA: 0x0003AD91 File Offset: 0x00038F91
	private void Start()
	{
		this.tipText.text = null;
		this.tipWindow.gameObject.SetActive(false);
	}

	// Token: 0x060007EF RID: 2031 RVA: 0x0003ADB0 File Offset: 0x00038FB0
	private void ShowTip(string tip, Vector2 mousePos)
	{
		this.tipText.text = tip;
		this.tipWindow.sizeDelta = new Vector2((this.tipText.preferredWidth > 200f) ? 200f : (this.tipText.preferredWidth + 6f), this.tipText.preferredHeight + 5f);
		this.tipWindow.gameObject.SetActive(true);
		Vector3 vector = new Vector2(mousePos.x, mousePos.y);
		if (OS_Manager.mousePosition.x < this.tipWindow.sizeDelta.x / 2f)
		{
			vector.x += this.tipWindow.sizeDelta.x / 2f;
		}
		if (OS_Manager.mousePosition.x > 512f - this.tipWindow.sizeDelta.x / 2f)
		{
			vector.x -= this.tipWindow.sizeDelta.x / 2f;
		}
		if (OS_Manager.mousePosition.y < this.tipWindow.sizeDelta.y / 2f)
		{
			vector.y += this.tipWindow.sizeDelta.y / 2f;
		}
		if (OS_Manager.mousePosition.y > 344f - this.tipWindow.sizeDelta.y / 2f)
		{
			vector.y -= this.tipWindow.sizeDelta.y / 2f;
		}
		this.tipWindow.transform.position = vector;
	}

	// Token: 0x060007F0 RID: 2032 RVA: 0x0003AF65 File Offset: 0x00039165
	private void HideTip()
	{
		this.tipText.text = null;
		this.tipWindow.gameObject.SetActive(false);
	}

	// Token: 0x04000965 RID: 2405
	public TMP_Text tipText;

	// Token: 0x04000966 RID: 2406
	public RectTransform tipWindow;

	// Token: 0x04000967 RID: 2407
	public static Action<string, Vector2> OnMouseHover;

	// Token: 0x04000968 RID: 2408
	public static Action OnMouseLoseFocus;
}
