﻿using System;
using System.Collections;
using System.Globalization;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000154 RID: 340
public class Message_Loading : Message
{
	// Token: 0x060009B1 RID: 2481 RVA: 0x00042400 File Offset: 0x00040600
	public override void Initialize(Message_Manager.Message_Packet packet)
	{
		float num = float.Parse(packet.data[0], NumberStyles.Float, CultureInfo.InvariantCulture);
		this.textObject.text = packet.message;
		if (packet.closeFunction != null)
		{
			this.closeFunction = packet.closeFunction;
		}
		this.initialized = true;
		base.StartCoroutine(this.LoadingAnimation(num));
	}

	// Token: 0x060009B2 RID: 2482 RVA: 0x00042463 File Offset: 0x00040663
	private void OnDisable()
	{
		if (this.initialized && !this.hasFinished)
		{
			OS_Manager.GetCurrentOS().SetInteractable(true);
			base.CloseMessage("done");
		}
	}

	// Token: 0x060009B3 RID: 2483 RVA: 0x0004248B File Offset: 0x0004068B
	private IEnumerator LoadingAnimation(float loadTime)
	{
		float timer = 0f;
		OS_Manager.GetCurrentOS().SetInteractable(false);
		while (timer < loadTime)
		{
			timer += Time.deltaTime;
			this.loadingBar.material.SetFloat("_Amount", timer / loadTime);
			yield return null;
		}
		this.hasFinished = true;
		OS_Manager.GetCurrentOS().SetInteractable(true);
		base.CloseMessage("done");
		yield break;
	}

	// Token: 0x04000AEC RID: 2796
	public Image loadingBar;

	// Token: 0x04000AED RID: 2797
	private bool initialized;

	// Token: 0x04000AEE RID: 2798
	private bool hasFinished;
}
