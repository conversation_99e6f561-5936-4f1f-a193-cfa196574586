﻿using System;
using System.Collections;
using System.Collections.Generic;
using Drawing;
using Pathfinding;
using UnityEngine;

// Token: 0x0200000D RID: 13
public class AI_NodeGraphCreator : MonoBehaviourGizmos
{
	// Token: 0x06000047 RID: 71 RVA: 0x000047BF File Offset: 0x000029BF
	private void Initialize()
	{
	}

	// Token: 0x06000048 RID: 72 RVA: 0x000047C1 File Offset: 0x000029C1
	private void OnEnable()
	{
		base.StopAllCoroutines();
		this.routingNodes = new List<Transform>();
		base.StartCoroutine(this.WaitForAstar());
	}

	// Token: 0x06000049 RID: 73 RVA: 0x000047E1 File Offset: 0x000029E1
	private IEnumerator WaitForAstar()
	{
		while (AstarPath.active == null)
		{
			yield return null;
		}
		foreach (object obj in this.parentNode)
		{
			Transform transform = (Transform)obj;
			this.routingNodes.Add(transform);
		}
		this.pointGraph = AstarPath.active.data.AddGraph(typeof(PointGraph)) as PointGraph;
		this.pointGraph.name = "PGraph:" + Time.time.ToString();
		this.pointGraph.mask = this.layerMask;
		this.pointGraph.maxDistance = this.maxNodeConnectDistance;
		this.pointGraph.root = base.transform;
		AstarPath.active.Scan(this.pointGraph);
		AstarPath.active.AddWorkItem(delegate
		{
			for (int i = 0; i < this.routingNodes.Count; i++)
			{
				this.pointGraph.AddNode((Int3)this.routingNodes[i].position);
			}
			this.pointGraph.ConnectNodes();
		});
		AstarPath.active.FlushWorkItems();
		this.Initialize();
		yield break;
	}

	// Token: 0x0600004A RID: 74 RVA: 0x000047F0 File Offset: 0x000029F0
	private void OnDisable()
	{
		if (AstarPath.active != null && AstarPath.active.data != null)
		{
			AstarPath.active.data.RemoveGraph(this.pointGraph);
		}
	}

	// Token: 0x0600004B RID: 75 RVA: 0x00004824 File Offset: 0x00002A24
	private void DrawLines()
	{
		foreach (Transform transform in this.routingNodes)
		{
			Draw.SphereOutline(transform.position, 0.2f, Color.green);
			Draw.SphereOutline(transform.position, 0.1f, Color.green);
		}
	}

	// Token: 0x0600004C RID: 76 RVA: 0x000048A4 File Offset: 0x00002AA4
	public override void DrawGizmos()
	{
		this.DrawLines();
	}

	// Token: 0x04000055 RID: 85
	public Transform parentNode;

	// Token: 0x04000056 RID: 86
	public float maxNodeConnectDistance = 10f;

	// Token: 0x04000057 RID: 87
	public LayerMask layerMask;

	// Token: 0x04000058 RID: 88
	public Transform createdNodeParent;

	// Token: 0x04000059 RID: 89
	private List<Transform> routingNodes = new List<Transform>();

	// Token: 0x0400005A RID: 90
	public bool showNodes = true;

	// Token: 0x0400005B RID: 91
	public bool showScanDistance = true;

	// Token: 0x0400005C RID: 92
	public PointGraph pointGraph;

	// Token: 0x0400005D RID: 93
	public PathfindingTag defaultNodeTag = 0U;

	// Token: 0x0400005E RID: 94
	private bool hasGenerated;
}
