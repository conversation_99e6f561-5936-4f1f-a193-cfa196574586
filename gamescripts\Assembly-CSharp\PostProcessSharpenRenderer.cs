﻿using System;
using UnityEngine;
using UnityEngine.Rendering.PostProcessing;

// Token: 0x020001BE RID: 446
public sealed class PostProcessSharpenRenderer : PostProcessEffectRenderer<PostProcessSharpen>
{
	// Token: 0x06000B7F RID: 2943 RVA: 0x00049924 File Offset: 0x00047B24
	public override void Render(PostProcessRenderContext context)
	{
		PropertySheet propertySheet = context.propertySheets.Get(Shader.Find("Hidden/Dark Machine/Sharpen Post Process"));
		propertySheet.properties.SetFloat("_Amount", base.settings.amount);
		Matrix4x4 inverse = GL.GetGPUProjectionMatrix(context.camera.projectionMatrix, true).inverse;
		propertySheet.properties.SetMatrix("_ClipToView", inverse);
		context.command.BlitFullscreenTriangle(context.source, context.destination, propertySheet, 0, false, null, false);
	}
}
