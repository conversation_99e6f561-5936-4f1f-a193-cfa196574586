﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x02000115 RID: 277
public class TextSettingsBinder : MonoBehaviour
{
	// Token: 0x06000855 RID: 2133 RVA: 0x0003C696 File Offset: 0x0003A896
	private void Start()
	{
		this.text = base.GetComponent<TMP_Text>();
		this.PullSetting();
	}

	// Token: 0x06000856 RID: 2134 RVA: 0x0003C6AC File Offset: 0x0003A8AC
	private void PullSetting()
	{
		string setting = SettingsManager.GetSetting(this.settingName);
		if (setting == null || this.text == null)
		{
			return;
		}
		this.text.text = setting;
	}

	// Token: 0x06000857 RID: 2135 RVA: 0x0003C6E4 File Offset: 0x0003A8E4
	public void DisplayFloat(float value)
	{
		float num = (float)Math.Round((double)value, this.roundedLevel);
		if (this.text != null)
		{
			this.text.text = num.ToString() ?? "";
		}
	}

	// Token: 0x040009B7 RID: 2487
	public string settingName;

	// Token: 0x040009B8 RID: 2488
	private TMP_Text text;

	// Token: 0x040009B9 RID: 2489
	public int roundedLevel = 2;
}
