﻿using System;
using UnityEngine;

// Token: 0x0200018C RID: 396
public class UT_ScaleToFull : MonoBehaviour
{
	// Token: 0x06000AC8 RID: 2760 RVA: 0x00046A5D File Offset: 0x00044C5D
	private void Start()
	{
		base.transform.localScale = Vector3.one * 0.01f;
	}

	// Token: 0x06000AC9 RID: 2761 RVA: 0x00046A7C File Offset: 0x00044C7C
	private void Update()
	{
		if (this.scaleTime < 1f)
		{
			this.scaleTime += Time.deltaTime * this.scaleSpeed;
			base.transform.localScale = Vector3.Lerp(Vector3.one * 0.01f, Vector3.one, this.scaleTime);
		}
	}

	// Token: 0x04000BCE RID: 3022
	private float scaleTime;

	// Token: 0x04000BCF RID: 3023
	public float scaleSpeed = 1f;
}
