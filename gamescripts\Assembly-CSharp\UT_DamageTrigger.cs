﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020000C9 RID: 201
public class UT_DamageTrigger : UT_TriggerBase
{
	// Token: 0x06000691 RID: 1681 RVA: 0x00034E8C File Offset: 0x0003308C
	internal override void FixedUpdate()
	{
		base.FixedUpdate();
		if (this.damageOverTime)
		{
			this.damageTime -= Time.fixedDeltaTime;
			if (this.damageTime < 0f)
			{
				this.damageTime = this.damageRate;
				foreach (Damageable damageable in this.damageTargets)
				{
					if (damageable == null)
					{
						this.damageTargets.Remove(damageable);
						break;
					}
					this.Damage(damageable);
				}
			}
		}
	}

	// Token: 0x06000692 RID: 1682 RVA: 0x00034F2C File Offset: 0x0003312C
	private void LateUpdate()
	{
		this.currentSpeed = (base.transform.position - this.lastPosition).magnitude / Time.deltaTime / 50f;
		this.lastPosition = base.transform.position;
		if (this.damageWhenMoving)
		{
			this.curDamage = this.currentSpeed * this.speedMultiplier * this.damage;
		}
	}

	// Token: 0x06000693 RID: 1683 RVA: 0x00034F9C File Offset: 0x0003319C
	private void Update()
	{
		if (CL_UIManager.debug && this.damageWhenMoving)
		{
			if (this.currentSpeed > this.minimumSpeed)
			{
				if (this.curDamage >= 5f)
				{
					CL_DebugView.draw.Label2D(base.transform.position, "VDMG: " + this.curDamage.ToString(), Color.red);
					return;
				}
				CL_DebugView.draw.Label2D(base.transform.position, "VDMG: " + this.curDamage.ToString(), Color.yellow);
				return;
			}
			else
			{
				CL_DebugView.draw.Label2D(base.transform.position, "VDMG: " + this.curDamage.ToString(), Color.green);
			}
		}
	}

	// Token: 0x06000694 RID: 1684 RVA: 0x00035077 File Offset: 0x00033277
	internal override void RemoveIndexFromTrigger(int i)
	{
		base.RemoveIndexFromTrigger(i);
		this.damageTargets.RemoveAt(i);
	}

	// Token: 0x06000695 RID: 1685 RVA: 0x0003508C File Offset: 0x0003328C
	private void Damage(Damageable damageable)
	{
		if (damageable == null)
		{
			return;
		}
		if (!damageable.GetGameObject().activeInHierarchy)
		{
			return;
		}
		if (this.damageAudio != null && this.damageAudio.Count > 0)
		{
			AudioManager.PlaySound(this.damageAudio[Random.Range(0, this.damageAudio.Count)], damageable.GetGameObject().transform.position, this.damageAudioVolume, this.damageAudioPitch, 1f, false, 1f, null);
		}
		if (this.damageWhenMoving)
		{
			float num = this.currentSpeed * this.speedMultiplier;
			if (this.currentSpeed > this.minimumSpeed)
			{
				damageable.Damage(this.damage * num, this.damageType);
				return;
			}
		}
		else
		{
			damageable.Damage(this.damage, this.damageType);
		}
	}

	// Token: 0x06000696 RID: 1686 RVA: 0x00035158 File Offset: 0x00033358
	private void OnTriggerEnter(Collider other)
	{
		if (ObjectTagger.TagCheck(other.gameObject, this.triggerTags))
		{
			Damageable component = other.GetComponent<Damageable>();
			if (component != null)
			{
				if (this.damageOverTime)
				{
					this.damageTime = 0f;
					this.damageTargets.Add(component);
					this.triggerObjects.Add(other.gameObject);
					return;
				}
				this.Damage(component);
			}
		}
	}

	// Token: 0x06000697 RID: 1687 RVA: 0x000351BC File Offset: 0x000333BC
	private void OnTriggerExit(Collider other)
	{
		if (ObjectTagger.TagCheck(other.gameObject, this.triggerTags))
		{
			Damageable component = other.GetComponent<Damageable>();
			if (this.damageTargets.Contains(component))
			{
				this.RemoveIndexFromTrigger(this.damageTargets.IndexOf(component));
			}
		}
	}

	// Token: 0x04000803 RID: 2051
	public float damage = 1f;

	// Token: 0x04000804 RID: 2052
	public string damageType = "trigger";

	// Token: 0x04000805 RID: 2053
	public bool damageOverTime;

	// Token: 0x04000806 RID: 2054
	public float damageRate = 0.5f;

	// Token: 0x04000807 RID: 2055
	private float damageTime;

	// Token: 0x04000808 RID: 2056
	public bool damageWhenMoving;

	// Token: 0x04000809 RID: 2057
	public float speedMultiplier = 1f;

	// Token: 0x0400080A RID: 2058
	public float minimumSpeed;

	// Token: 0x0400080B RID: 2059
	private Vector3 lastPosition;

	// Token: 0x0400080C RID: 2060
	private float currentSpeed;

	// Token: 0x0400080D RID: 2061
	private float curDamage;

	// Token: 0x0400080E RID: 2062
	private List<Damageable> damageTargets = new List<Damageable>();

	// Token: 0x0400080F RID: 2063
	public List<AudioClip> damageAudio;

	// Token: 0x04000810 RID: 2064
	public float damageAudioVolume = 1f;

	// Token: 0x04000811 RID: 2065
	public float damageAudioPitch = 1f;
}
