﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020000E4 RID: 228
public class UT_Toggle : MonoBehaviour, Activatable
{
	// Token: 0x06000712 RID: 1810 RVA: 0x00036BB1 File Offset: 0x00034DB1
	private void Awake()
	{
		this.startState = this.active;
	}

	// Token: 0x06000713 RID: 1811 RVA: 0x00036BBF File Offset: 0x00034DBF
	private void OnEnable()
	{
		if (this.locked)
		{
			return;
		}
		if (this.resetOnEnable)
		{
			this.SetState(this.startState);
		}
	}

	// Token: 0x06000714 RID: 1812 RVA: 0x00036BDE File Offset: 0x00034DDE
	public void Toggle()
	{
		if (this.locked)
		{
			return;
		}
		this.active = !this.active;
		if (this.active)
		{
			this.activateEvent.Invoke();
			return;
		}
		this.deactivateEvent.Invoke();
	}

	// Token: 0x06000715 RID: 1813 RVA: 0x00036C17 File Offset: 0x00034E17
	public void SetState(bool b)
	{
		if (this.locked)
		{
			return;
		}
		this.active = b;
		if (this.active)
		{
			this.activateEvent.Invoke();
			return;
		}
		this.deactivateEvent.Invoke();
	}

	// Token: 0x06000716 RID: 1814 RVA: 0x00036C48 File Offset: 0x00034E48
	public void SetLocked(bool b)
	{
		this.locked = b;
	}

	// Token: 0x06000717 RID: 1815 RVA: 0x00036C51 File Offset: 0x00034E51
	public void Deactivate()
	{
		this.SetState(false);
	}

	// Token: 0x06000718 RID: 1816 RVA: 0x00036C5A File Offset: 0x00034E5A
	public void Activate()
	{
		this.SetState(true);
	}

	// Token: 0x06000719 RID: 1817 RVA: 0x00036C63 File Offset: 0x00034E63
	public void ToggleActivated()
	{
		this.Toggle();
	}

	// Token: 0x0600071A RID: 1818 RVA: 0x00036C6B File Offset: 0x00034E6B
	public GameObject GetGameObject()
	{
		return base.gameObject;
	}

	// Token: 0x04000889 RID: 2185
	public bool active;

	// Token: 0x0400088A RID: 2186
	private bool startState;

	// Token: 0x0400088B RID: 2187
	public bool locked;

	// Token: 0x0400088C RID: 2188
	public UnityEvent activateEvent;

	// Token: 0x0400088D RID: 2189
	public UnityEvent deactivateEvent;

	// Token: 0x0400088E RID: 2190
	public bool resetOnEnable;
}
