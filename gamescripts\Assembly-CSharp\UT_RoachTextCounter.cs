﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x02000188 RID: 392
public class UT_RoachTextCounter : MonoBehaviour
{
	// Token: 0x06000ABB RID: 2747 RVA: 0x0004683F File Offset: 0x00044A3F
	private void Start()
	{
		this.text = base.GetComponent<TMP_Text>();
		this.text.text = CL_GameManager.roaches.ToString();
	}

	// Token: 0x06000ABC RID: 2748 RVA: 0x00046862 File Offset: 0x00044A62
	private void Update()
	{
		if (CL_GameManager.roaches != this.lastRoaches)
		{
			this.text.text = CL_GameManager.roaches.ToString();
			this.lastRoaches = CL_GameManager.roaches;
		}
	}

	// Token: 0x04000BBD RID: 3005
	private TMP_Text text;

	// Token: 0x04000BBE RID: 3006
	private int lastRoaches;
}
