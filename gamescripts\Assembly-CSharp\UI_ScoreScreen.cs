﻿using System;
using System.Collections;
using System.Runtime.CompilerServices;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200014A RID: 330
public class UI_ScoreScreen : MonoBehaviour
{
	// Token: 0x06000975 RID: 2421 RVA: 0x00040CD1 File Offset: 0x0003EED1
	public void ShowScreen()
	{
		if (this.isChallengeScreen)
		{
			this.PlayMedalAnimation();
		}
	}

	// Token: 0x06000976 RID: 2422 RVA: 0x00040CE1 File Offset: 0x0003EEE1
	public void SetTip(string tip)
	{
		if (!this.useTipText)
		{
			return;
		}
		if (this.tipText != null)
		{
			this.tipText.text = tip;
		}
	}

	// Token: 0x06000977 RID: 2423 RVA: 0x00040D06 File Offset: 0x0003EF06
	public void SetDeathText(string text)
	{
		if (!this.useDeathText)
		{
			return;
		}
		if (this.deathText != null)
		{
			this.deathText.text = text;
		}
	}

	// Token: 0x06000978 RID: 2424 RVA: 0x00040D2B File Offset: 0x0003EF2B
	public void SetMedalInfo(string medalTitle, Sprite medalImage, Color textColor)
	{
		this.scoreRankImage.sprite = medalImage;
		this.scoreRankText.text = medalTitle;
		this.scoreRankText.color = textColor;
	}

	// Token: 0x06000979 RID: 2425 RVA: 0x00040D51 File Offset: 0x0003EF51
	public void PlayMedalAnimation()
	{
		base.StartCoroutine(this.<PlayMedalAnimation>g__MedalAnimator|12_0());
	}

	// Token: 0x0600097A RID: 2426 RVA: 0x00040D60 File Offset: 0x0003EF60
	public void PlayTimeReductionAnimation(float timeReduction)
	{
		UI_ScoreScreen.<>c__DisplayClass13_0 CS$<>8__locals1 = new UI_ScoreScreen.<>c__DisplayClass13_0();
		CS$<>8__locals1.<>4__this = this;
		CS$<>8__locals1.timeReduction = timeReduction;
		if (this.timeDifferenceText == null)
		{
			return;
		}
		base.StartCoroutine(CS$<>8__locals1.<PlayTimeReductionAnimation>g__TimeAnimator|0());
	}

	// Token: 0x0600097C RID: 2428 RVA: 0x00040DB3 File Offset: 0x0003EFB3
	[CompilerGenerated]
	private IEnumerator <PlayMedalAnimation>g__MedalAnimator|12_0()
	{
		Color textColor = this.scoreRankText.color;
		this.scoreRankImage.transform.localScale = Vector3.one * 1.8f;
		this.scoreRankImage.color = Color.clear;
		this.scoreRankText.color = Color.clear;
		yield return new WaitForSecondsRealtime(0.5f);
		this.scoreRankImage.DOColor(Color.white, 0.5f).SetUpdate(true);
		yield return this.scoreRankImage.transform.DOPunchScale(Vector3.one * 0.3f, 0.3f, 5, 0.5f).SetUpdate(true).WaitForCompletion();
		yield return this.scoreRankImage.transform.DOScale(1f, 0.35f).SetUpdate(true).SetEase(Ease.InCirc)
			.WaitForCompletion();
		this.scoreRankImage.transform.DOPunchRotation(Vector3.one * 3f, 0.5f, 10, 1f).SetUpdate(true);
		yield return this.scoreRankImage.transform.DOPunchScale(Vector3.one * 0.2f, 0.5f, 5, 0.5f).SetUpdate(true).WaitForCompletion();
		this.scoreRankText.DOColor(textColor, 0.5f).SetUpdate(true);
		yield break;
	}

	// Token: 0x04000ABB RID: 2747
	public bool useTipText = true;

	// Token: 0x04000ABC RID: 2748
	public TMP_Text tipText;

	// Token: 0x04000ABD RID: 2749
	public bool useDeathText = true;

	// Token: 0x04000ABE RID: 2750
	public TMP_Text deathText;

	// Token: 0x04000ABF RID: 2751
	public bool isChallengeScreen;

	// Token: 0x04000AC0 RID: 2752
	public TMP_Text scoreRankText;

	// Token: 0x04000AC1 RID: 2753
	public Image scoreRankImage;

	// Token: 0x04000AC2 RID: 2754
	public TMP_Text timeDifferenceText;
}
