﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000081 RID: 129
[RequireComponent(typeof(ObjectTagger))]
public class CL_Prop : GameEntity, Clickable
{
	// Token: 0x0600044D RID: 1101 RVA: 0x00026464 File Offset: 0x00024664
	public override void Start()
	{
		base.Start();
		this.Initialize();
	}

	// Token: 0x0600044E RID: 1102 RVA: 0x00026474 File Offset: 0x00024674
	private void FixedUpdate()
	{
		this.calculatedVelocity = base.transform.position - this.lastPos;
		this.velMag = this.calculatedVelocity.magnitude / Time.fixedDeltaTime;
		this.lastPos = base.transform.position;
		if (this.loadInTime >= 0f)
		{
			this.loadInTime -= Time.fixedDeltaTime;
		}
		if (this.colliding && !this.stuck)
		{
			if (this.dragAud != null)
			{
				CL_Prop.PropAudioEffect soundFromVelocity = this.GetSoundFromVelocity(this.dragSounds, this.rigid.velocity.magnitude);
				this.dragAud.volume = this.dragVolume * (soundFromVelocity.maxVolume * (this.velMag / soundFromVelocity.maxVelocity));
				this.dragAud.pitch = Mathf.Lerp(soundFromVelocity.minPitch, soundFromVelocity.maxPitch, this.velMag / soundFromVelocity.maxVelocity);
			}
		}
		else if (this.dragAud != null)
		{
			if (this.dragAud.volume > 0f && this.dragAud.isPlaying && !this.stuck)
			{
				this.dragAud.volume = Mathf.Lerp(this.dragAud.volume, 0f, Time.fixedDeltaTime * 30f);
			}
			else
			{
				this.dragAud.Pause();
			}
		}
		if (this.stuck)
		{
			if (this.breakStuckMagnitude > this.stuckStrength)
			{
				this.Unstick();
			}
			else if (this.dragAud)
			{
				this.dragAud.volume = Mathf.Lerp(this.dragAud.volume, this.breakStuckMagnitude / this.stuckStrength * this.dragVolume * 0.25f, Time.fixedDeltaTime * 4f);
				this.dragAud.pitch = Mathf.Clamp(Mathf.Lerp(this.dragAud.pitch, this.breakStuckMagnitude / this.stuckStrength, Time.deltaTime * 3f), 0.8f, 1f);
				if (this.breakStuckMagnitude > 0.1f && !this.dragAud.isPlaying)
				{
					this.dragAud.Play();
				}
			}
			this.breakStuckMagnitude = Mathf.Lerp(this.breakStuckMagnitude, 0f, Time.fixedDeltaTime * 10f);
		}
	}

	// Token: 0x0600044F RID: 1103 RVA: 0x000266E8 File Offset: 0x000248E8
	private void OnCollisionEnter(Collision other)
	{
		if (Time.timeSinceLevelLoad < 1f)
		{
			return;
		}
		if (this.loadInTime >= 0f)
		{
			return;
		}
		if (this.dragAud != null && !this.dragAud.isPlaying)
		{
			this.dragAud.Play();
		}
		if (this.hitSounds.Count > 0)
		{
			CL_Prop.PropAudioEffect soundFromVelocity = this.GetSoundFromVelocity(this.hitSounds, this.rigid.velocity.magnitude);
			this.hitAud.clip = soundFromVelocity.clip[Random.Range(0, soundFromVelocity.clip.Count)];
			this.hitAud.volume = this.volume * (soundFromVelocity.maxVolume * (this.velMag / soundFromVelocity.maxVelocity));
			this.hitAud.Play();
		}
		if (this.usePhysicsHitEffect && other.impulse.magnitude >= this.minPhysHitImpulse)
		{
			Object.Instantiate<GameObject>(this.physHitEffect, other.GetContact(0).point, Quaternion.identity);
		}
	}

	// Token: 0x06000450 RID: 1104 RVA: 0x000267FA File Offset: 0x000249FA
	private void OnCollisionExit(Collision other)
	{
		this.colliding = false;
	}

	// Token: 0x06000451 RID: 1105 RVA: 0x00026804 File Offset: 0x00024A04
	private void OnCollisionStay(Collision other)
	{
		if (Time.timeSinceLevelLoad < 1f)
		{
			return;
		}
		if (this.loadInTime >= 0f)
		{
			return;
		}
		if (this.dragAud != null && !this.dragAud.isPlaying)
		{
			this.dragAud.Play();
		}
		this.colliding = true;
	}

	// Token: 0x06000452 RID: 1106 RVA: 0x0002685C File Offset: 0x00024A5C
	private int GetSoundEffectLevel(List<CL_Prop.PropAudioEffect> effects, float vel)
	{
		int num = 0;
		for (int i = 0; i < effects.Count; i++)
		{
			if (this.rigid.velocity.magnitude > effects[i].minVelocity)
			{
				num = i;
			}
		}
		return num;
	}

	// Token: 0x06000453 RID: 1107 RVA: 0x000268A0 File Offset: 0x00024AA0
	private CL_Prop.PropAudioEffect GetSoundFromVelocity(List<CL_Prop.PropAudioEffect> effects, float vel)
	{
		int num = 0;
		for (int i = 0; i < effects.Count; i++)
		{
			if (this.rigid.velocity.magnitude > effects[i].minVelocity)
			{
				num = i;
			}
		}
		return effects[num];
	}

	// Token: 0x06000454 RID: 1108 RVA: 0x000268EA File Offset: 0x00024AEA
	public Rigidbody GetRigidbody()
	{
		return this.rigid;
	}

	// Token: 0x06000455 RID: 1109 RVA: 0x000268F2 File Offset: 0x00024AF2
	public void Pickup()
	{
		this.Unstick();
		this.rigid.collisionDetectionMode = CollisionDetectionMode.Continuous;
	}

	// Token: 0x06000456 RID: 1110 RVA: 0x00026908 File Offset: 0x00024B08
	public override void Kill(string type = "")
	{
		if (this.breakSounds.Count > 0)
		{
			CL_Prop.PropAudioEffect propAudioEffect = this.breakSounds[0];
			AudioManager.PlaySound(propAudioEffect.clip[Random.Range(0, propAudioEffect.clip.Count)], base.transform.position, propAudioEffect.maxVolume, 1f, 1f, false, 1f, null);
		}
		base.Kill("");
		base.gameObject.SetActive(false);
	}

	// Token: 0x06000457 RID: 1111 RVA: 0x0002698A File Offset: 0x00024B8A
	public void Drop()
	{
		this.Initialize();
		this.dragAud.volume = 0f;
		this.dragAud.Play();
		this.dragAud.Pause();
		this.rigid.collisionDetectionMode = CollisionDetectionMode.ContinuousSpeculative;
	}

	// Token: 0x06000458 RID: 1112 RVA: 0x000269C4 File Offset: 0x00024BC4
	public void Unstick()
	{
		if (!this.stuck)
		{
			return;
		}
		this.unstickEvent.Invoke();
		this.rigid.isKinematic = false;
		this.stuck = false;
		if (this.unstickSounds.Count > 0)
		{
			this.thirdAud.clip = this.unstickSounds[0].clip[Random.Range(0, this.unstickSounds[0].clip.Count)];
			this.thirdAud.volume = Mathf.Clamp(this.rigid.velocity.magnitude * 0.8f, 0.2f, 1f);
			this.thirdAud.Play();
		}
	}

	// Token: 0x06000459 RID: 1113 RVA: 0x00026A81 File Offset: 0x00024C81
	public void Stick()
	{
		this.rigid.isKinematic = true;
		this.stuck = true;
	}

	// Token: 0x0600045A RID: 1114 RVA: 0x00026A98 File Offset: 0x00024C98
	public void Initialize()
	{
		if (this.initialized)
		{
			return;
		}
		this.colliders = new List<Collider>();
		this.colliders.AddRange(base.GetComponentsInChildren<Collider>());
		this.rigid = base.GetComponent<Rigidbody>();
		if (this.stuck)
		{
			this.rigid.isKinematic = true;
		}
		this.hitAud = base.GetComponent<AudioSource>();
		if (this.hitAud == null)
		{
			this.hitAud = base.gameObject.AddComponent<AudioSource>();
			this.hitAud.spatialBlend = 1f;
			this.hitAud.playOnAwake = false;
			this.hitAud.volume = this.volume;
		}
		if (this.dragSounds.Count > 0)
		{
			this.dragAud = base.gameObject.AddComponent<AudioSource>();
			this.dragAud.loop = true;
			this.dragAud.clip = this.dragSounds[0].clip[Random.Range(0, this.dragSounds[0].clip.Count)];
			this.dragAud.spatialBlend = 1f;
			this.dragAud.playOnAwake = false;
			this.dragAud.volume = 0f;
		}
		if (this.unstickSounds.Count > 0)
		{
			this.thirdAud = base.gameObject.AddComponent<AudioSource>();
			this.thirdAud.clip = this.dragSounds[0].clip[Random.Range(0, this.dragSounds[0].clip.Count)];
			this.thirdAud.spatialBlend = 1f;
			this.thirdAud.playOnAwake = false;
		}
		this.lastPos = base.transform.position;
		this.initialized = true;
	}

	// Token: 0x0600045B RID: 1115 RVA: 0x00026C69 File Offset: 0x00024E69
	public new GameObject GetGameObject()
	{
		return base.gameObject;
	}

	// Token: 0x0600045C RID: 1116 RVA: 0x00026C71 File Offset: 0x00024E71
	public override void AddForce(Vector3 v)
	{
		v *= 10f * this.addForceMult;
		this.rigid.AddForce(v);
	}

	// Token: 0x0600045D RID: 1117 RVA: 0x00026C93 File Offset: 0x00024E93
	public override void AddForceAtPosition(Vector3 v, Vector3 p)
	{
		v *= 10f * this.addForceMult;
		this.rigid.AddForceAtPosition(v, p);
	}

	// Token: 0x0600045E RID: 1118 RVA: 0x00026CB6 File Offset: 0x00024EB6
	public override void TonguePull(Vector3 v)
	{
		base.TonguePull(v);
		this.AddForce(v * 1000f);
	}

	// Token: 0x0600045F RID: 1119 RVA: 0x00026CD0 File Offset: 0x00024ED0
	public override void AddForce(Vector3 v, float maxVel)
	{
		this.rigid.AddForce(v * 5f, ForceMode.Acceleration);
	}

	// Token: 0x06000460 RID: 1120 RVA: 0x00026CE9 File Offset: 0x00024EE9
	public void AddBreakingVelocity(Vector3 v)
	{
		this.breakStuckMagnitude = v.magnitude;
	}

	// Token: 0x06000461 RID: 1121 RVA: 0x00026CF8 File Offset: 0x00024EF8
	public void Interact(ENT_Player p, ENT_Player.Hand hand)
	{
		if (this.rigid.isKinematic && !this.stuck)
		{
			this.rigid.isKinematic = false;
		}
		this.rigid.collisionDetectionMode = CollisionDetectionMode.ContinuousSpeculative;
	}

	// Token: 0x06000462 RID: 1122 RVA: 0x00026D28 File Offset: 0x00024F28
	public override bool Damage(float amount, string type)
	{
		CL_Prop.PropAudioEffect soundFromVelocity = this.GetSoundFromVelocity(this.hitSounds, amount);
		this.hitAud.clip = soundFromVelocity.clip[Random.Range(0, soundFromVelocity.clip.Count)];
		this.hitAud.volume = this.volume * soundFromVelocity.maxVolume;
		this.hitAud.Play();
		if (type == "drone")
		{
			amount *= 0.25f;
		}
		if (this.stuckHealth > 0f)
		{
			this.stuckHealth -= amount;
			if (this.stuckHealth <= 0f)
			{
				this.Unstick();
			}
		}
		return this.maxHealth > 0f && base.Damage(amount, type);
	}

	// Token: 0x06000463 RID: 1123 RVA: 0x00026DE8 File Offset: 0x00024FE8
	public bool CanInteract(ENT_Player p, ENT_Player.Hand hand)
	{
		return this.canInteract;
	}

	// Token: 0x06000464 RID: 1124 RVA: 0x00026DF0 File Offset: 0x00024FF0
	public List<Collider> GetColliders()
	{
		return this.colliders;
	}

	// Token: 0x040005AE RID: 1454
	public bool canInteract = true;

	// Token: 0x040005AF RID: 1455
	public float holdDistance = 2f;

	// Token: 0x040005B0 RID: 1456
	public float addForceMult = 1f;

	// Token: 0x040005B1 RID: 1457
	public float loadInTime;

	// Token: 0x040005B2 RID: 1458
	public Vector3 holdOffset = Vector3.zero;

	// Token: 0x040005B3 RID: 1459
	private List<Collider> colliders;

	// Token: 0x040005B4 RID: 1460
	private List<Collision> collisionsInfo = new List<Collision>();

	// Token: 0x040005B5 RID: 1461
	private bool colliding;

	// Token: 0x040005B6 RID: 1462
	private Rigidbody rigid;

	// Token: 0x040005B7 RID: 1463
	[Range(0f, 2f)]
	public float volume = 0.5f;

	// Token: 0x040005B8 RID: 1464
	[Range(0f, 2f)]
	public float dragVolume = 0.5f;

	// Token: 0x040005B9 RID: 1465
	public List<CL_Prop.PropAudioEffect> hitSounds;

	// Token: 0x040005BA RID: 1466
	public List<CL_Prop.PropAudioEffect> dragSounds;

	// Token: 0x040005BB RID: 1467
	public List<CL_Prop.PropAudioEffect> breakSounds;

	// Token: 0x040005BC RID: 1468
	public List<CL_Prop.PropAudioEffect> unstickSounds;

	// Token: 0x040005BD RID: 1469
	private AudioSource hitAud;

	// Token: 0x040005BE RID: 1470
	private AudioSource dragAud;

	// Token: 0x040005BF RID: 1471
	private AudioSource thirdAud;

	// Token: 0x040005C0 RID: 1472
	public bool stuck;

	// Token: 0x040005C1 RID: 1473
	public float stuckStrength = 1f;

	// Token: 0x040005C2 RID: 1474
	public float stuckHealth;

	// Token: 0x040005C3 RID: 1475
	public Vector3 anchorPoint;

	// Token: 0x040005C4 RID: 1476
	public UnityEvent unstickEvent;

	// Token: 0x040005C5 RID: 1477
	public bool usePhysicsHitEffect;

	// Token: 0x040005C6 RID: 1478
	public float minPhysHitImpulse = 1f;

	// Token: 0x040005C7 RID: 1479
	public GameObject physHitEffect;

	// Token: 0x040005C8 RID: 1480
	private float timeSinceCollision;

	// Token: 0x040005C9 RID: 1481
	private Vector3 calculatedVelocity = Vector3.zero;

	// Token: 0x040005CA RID: 1482
	private Vector3 lastPos;

	// Token: 0x040005CB RID: 1483
	private float velMag;

	// Token: 0x040005CC RID: 1484
	private bool initialized;

	// Token: 0x040005CD RID: 1485
	private float breakStuckMagnitude;

	// Token: 0x02000253 RID: 595
	[Serializable]
	public class PropAudioEffect
	{
		// Token: 0x04000F5E RID: 3934
		public string name;

		// Token: 0x04000F5F RID: 3935
		public float minVelocity = 0.5f;

		// Token: 0x04000F60 RID: 3936
		public float maxVelocity = 1f;

		// Token: 0x04000F61 RID: 3937
		public float minPitch = 1f;

		// Token: 0x04000F62 RID: 3938
		public float maxPitch = 1f;

		// Token: 0x04000F63 RID: 3939
		public float maxVolume = 1f;

		// Token: 0x04000F64 RID: 3940
		public List<AudioClip> clip;
	}
}
