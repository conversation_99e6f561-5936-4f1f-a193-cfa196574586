﻿using System;
using UnityEngine;

// Token: 0x020001C1 RID: 449
[Serializable]
public class UT_FollowBase
{
	// Token: 0x06000B8A RID: 2954 RVA: 0x00049B3C File Offset: 0x00047D3C
	public void MoveToTarget(Transform root, Transform target)
	{
		if (target)
		{
			Vector3 position = target.position;
			Quaternion quaternion = target.rotation;
			if (this.lockX)
			{
				position.x = root.position.x;
			}
			if (this.lockY)
			{
				position.y = root.position.y;
			}
			if (this.lockZ)
			{
				position.z = root.position.z;
			}
			if (this.lockXRot)
			{
				quaternion = Quaternion.Euler(root.rotation.eulerAngles.x, quaternion.eulerAngles.y, quaternion.eulerAngles.z);
			}
			if (this.lockYRot)
			{
				quaternion = Quaternion.Euler(quaternion.eulerAngles.x, root.rotation.eulerAngles.y, quaternion.eulerAngles.z);
			}
			if (this.lockZRot)
			{
				quaternion = Quaternion.Euler(quaternion.eulerAngles.x, quaternion.eulerAngles.y, root.rotation.eulerAngles.z);
			}
			if (this.lerpToTarget)
			{
				root.position = Vector3.Lerp(root.position, position, Time.deltaTime * this.speed);
			}
			else
			{
				root.position = position;
			}
			if (this.followRotation)
			{
				if (this.lerpToTarget)
				{
					root.rotation = Quaternion.Lerp(root.rotation, quaternion, Time.deltaTime * this.speed);
					return;
				}
				root.rotation = quaternion;
			}
		}
	}

	// Token: 0x04000C92 RID: 3218
	public bool followRotation;

	// Token: 0x04000C93 RID: 3219
	public bool lerpToTarget;

	// Token: 0x04000C94 RID: 3220
	public float speed = 5f;

	// Token: 0x04000C95 RID: 3221
	[Header("Movement Axis Lock")]
	public bool lockX;

	// Token: 0x04000C96 RID: 3222
	public bool lockY;

	// Token: 0x04000C97 RID: 3223
	public bool lockZ;

	// Token: 0x04000C98 RID: 3224
	[Header("Rotation Axis Lock")]
	public bool lockXRot;

	// Token: 0x04000C99 RID: 3225
	public bool lockYRot;

	// Token: 0x04000C9A RID: 3226
	public bool lockZRot;
}
