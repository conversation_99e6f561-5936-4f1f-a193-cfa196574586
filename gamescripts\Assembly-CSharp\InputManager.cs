﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem;

// Token: 0x020000B9 RID: 185
public class InputManager : MonoBehaviour
{
	// Token: 0x17000005 RID: 5
	// (get) Token: 0x06000613 RID: 1555 RVA: 0x0003250B File Offset: 0x0003070B
	// (set) Token: 0x06000614 RID: 1556 RVA: 0x00032513 File Offset: 0x00030713
	public Vector2 MoveInput { get; private set; }

	// Token: 0x06000615 RID: 1557 RVA: 0x0003251C File Offset: 0x0003071C
	private void Awake()
	{
		if (InputManager.instance == null)
		{
			InputManager.instance = this;
		}
		this.SetupActions();
	}

	// Token: 0x06000616 RID: 1558 RVA: 0x00032537 File Offset: 0x00030737
	private void Start()
	{
		if (global::UnityEngine.InputSystem.Gyroscope.current != null && !global::UnityEngine.InputSystem.Gyroscope.current.enabled)
		{
			Debug.Log("Attempting to Enable Gyro");
			InputSystem.EnableDevice(global::UnityEngine.InputSystem.Gyroscope.current);
		}
	}

	// Token: 0x06000617 RID: 1559 RVA: 0x00032560 File Offset: 0x00030760
	private void OnEnable()
	{
	}

	// Token: 0x06000618 RID: 1560 RVA: 0x00032562 File Offset: 0x00030762
	private void OnDisable()
	{
	}

	// Token: 0x06000619 RID: 1561 RVA: 0x00032564 File Offset: 0x00030764
	private void Update()
	{
		this.UpdateInputs();
		InputManager.UpdateCursorVisibility();
	}

	// Token: 0x0600061A RID: 1562 RVA: 0x00032574 File Offset: 0x00030774
	public void SetupActions()
	{
		this.playerInput = base.GetComponent<PlayerInput>();
		this.buttonInputs = new List<InputManager.ButtonInput>();
		this.buttons = new Dictionary<string, InputManager.ButtonInput>();
		this.vectorInputs = new List<InputManager.VectorInput>();
		this.vectors = new Dictionary<string, InputManager.VectorInput>();
		foreach (InputActionMap inputActionMap in this.playerInput.actions.actionMaps)
		{
			foreach (InputAction inputAction in inputActionMap.actions)
			{
				if (inputAction.type == InputActionType.Button || (inputAction.type == InputActionType.PassThrough && inputAction.expectedControlType == "Button"))
				{
					InputManager.ButtonInput buttonInput = new InputManager.ButtonInput();
					buttonInput.name = inputAction.name;
					buttonInput.action = inputAction;
					this.buttonInputs.Add(buttonInput);
					this.buttons.Add(buttonInput.name, buttonInput);
				}
				else if (inputAction.type == InputActionType.Value || (inputAction.type == InputActionType.PassThrough && inputAction.expectedControlType == "Vector2"))
				{
					InputManager.VectorInput vectorInput = new InputManager.VectorInput();
					vectorInput.name = inputAction.name;
					vectorInput.action = inputAction;
					this.vectorInputs.Add(vectorInput);
					this.vectors.Add(vectorInput.name, vectorInput);
				}
			}
		}
	}

	// Token: 0x0600061B RID: 1563 RVA: 0x00032734 File Offset: 0x00030934
	public void UpdateInputs()
	{
		if (this.buttonInputs == null)
		{
			return;
		}
		foreach (InputManager.ButtonInput buttonInput in this.buttonInputs)
		{
			buttonInput.Pressed = buttonInput.action.IsPressed();
			buttonInput.Down = buttonInput.action.WasPressedThisFrame();
			buttonInput.Up = buttonInput.action.WasReleasedThisFrame();
		}
		foreach (InputManager.VectorInput vectorInput in this.vectorInputs)
		{
			vectorInput.vector = vectorInput.action.ReadValue<Vector2>();
			vectorInput.Pressed = vectorInput.action.IsPressed();
			vectorInput.Down = vectorInput.action.WasPressedThisFrame();
			vectorInput.Up = vectorInput.action.WasReleasedThisFrame();
		}
	}

	// Token: 0x0600061C RID: 1564 RVA: 0x00032838 File Offset: 0x00030A38
	public static InputManager.ButtonInput GetButton(string name)
	{
		if (InputManager.instance.buttons.ContainsKey(name))
		{
			return InputManager.instance.buttons[name];
		}
		Debug.LogWarning("Warning: No button " + name + " found in list. Suppling Fake");
		return new InputManager.ButtonInput
		{
			Down = false,
			Up = false,
			Pressed = false,
			action = null
		};
	}

	// Token: 0x0600061D RID: 1565 RVA: 0x0003289E File Offset: 0x00030A9E
	public static InputManager.VectorInput GetVector(string name)
	{
		if (InputManager.instance.vectors.ContainsKey(name))
		{
			return InputManager.instance.vectors[name];
		}
		Debug.LogError("Missing Vector Input: " + name);
		return null;
	}

	// Token: 0x0600061E RID: 1566 RVA: 0x000328D4 File Offset: 0x00030AD4
	public static PlayerInput GetPlayerInput()
	{
		return InputManager.instance.playerInput;
	}

	// Token: 0x0600061F RID: 1567 RVA: 0x000328E0 File Offset: 0x00030AE0
	public static void RefreshActionStringDict()
	{
		InputManager.actionDisplayStringDict = new Dictionary<string, string>();
		foreach (InputAction inputAction in InputManager.GetPlayerInput().user.actions)
		{
			foreach (string text in InputManager.controlSchemes)
			{
				InputManager.actionDisplayStringDict.Add(text + inputAction.name, InputManager.GetBindingDisplayStringOrCompositeName(text, inputAction));
			}
		}
	}

	// Token: 0x06000620 RID: 1568 RVA: 0x00032998 File Offset: 0x00030B98
	public static string QuickGetBindingDisplayString(string scheme, InputAction action)
	{
		if (InputManager.actionDisplayStringDict == null)
		{
			InputManager.RefreshActionStringDict();
		}
		if (InputManager.actionDisplayStringDict.ContainsKey(scheme + action.name))
		{
			return InputManager.actionDisplayStringDict[scheme + action.name];
		}
		return "";
	}

	// Token: 0x06000621 RID: 1569 RVA: 0x000329E8 File Offset: 0x00030BE8
	public static string GetBindingDisplayStringOrCompositeName(string scheme, InputAction action)
	{
		int count = action.bindings.Count;
		for (int i = 0; i < count; i++)
		{
			InputBinding inputBinding = action.bindings[i];
			if (InputBinding.MaskByGroup(scheme).Matches(inputBinding) && !inputBinding.isComposite && !inputBinding.isPartOfComposite)
			{
				string bindingDisplayString = action.GetBindingDisplayString(i, (InputBinding.DisplayStringOptions)0);
				if (!string.IsNullOrEmpty(bindingDisplayString))
				{
					return InputManager.instance.GetTMPSpriteForButton(bindingDisplayString, scheme);
				}
			}
			if (i + 1 < count && inputBinding.isComposite)
			{
				InputBinding inputBinding2 = action.bindings[i + 1];
				if (InputBinding.MaskByGroup(scheme).Matches(inputBinding2))
				{
					string bindingDisplayString2 = action.GetBindingDisplayString(i, InputBinding.DisplayStringOptions.DontUseShortDisplayNames);
					if (!string.IsNullOrEmpty(bindingDisplayString2))
					{
						return bindingDisplayString2;
					}
				}
			}
		}
		return "[" + action.name + "]";
	}

	// Token: 0x06000622 RID: 1570 RVA: 0x00032AD0 File Offset: 0x00030CD0
	public static void SetControlScheme(string schemeName)
	{
		if (InputManager.instance == null)
		{
			return;
		}
		InputManager.instance.playerInput.user.ActivateControlScheme(schemeName);
	}

	// Token: 0x06000623 RID: 1571 RVA: 0x00032B04 File Offset: 0x00030D04
	public static void SetAutoSwitchControlSchemes(bool b)
	{
		if (InputManager.instance == null)
		{
			return;
		}
		InputManager.instance.playerInput.neverAutoSwitchControlSchemes = !b;
	}

	// Token: 0x06000624 RID: 1572 RVA: 0x00032B27 File Offset: 0x00030D27
	public static List<string> GetControlSchemes()
	{
		return InputManager.controlSchemes;
	}

	// Token: 0x06000625 RID: 1573 RVA: 0x00032B2E File Offset: 0x00030D2E
	public static AnimationCurve GetGamepadLookCurve()
	{
		return InputManager.instance.gamepadLookCurve;
	}

	// Token: 0x06000626 RID: 1574 RVA: 0x00032B3C File Offset: 0x00030D3C
	public static Vector2 GetLookVector()
	{
		Vector2 vector = InputManager.GetVector("Look").vector;
		if (InputManager.instance.playerInput.currentControlScheme == "Gamepad")
		{
			vector = vector.normalized * InputManager.GetGamepadLookCurve().Evaluate(vector.magnitude) * Time.deltaTime * 150f;
			vector *= InputManager.instance.gamepadSpeedAdjust;
			float num = (1f - InputManager.instance.gamepadLookAccelerationCurve.Evaluate(SettingsManager.settings.controllerAcceleration) + 0.01f) * 5f;
			vector = Vector2.Lerp(InputManager.lastGamepadLook, vector, Time.deltaTime * (InputManager.instance.gamepadLookAcceleration * num));
			InputManager.lastGamepadLook = vector;
		}
		return vector;
	}

	// Token: 0x06000627 RID: 1575 RVA: 0x00032C0C File Offset: 0x00030E0C
	public string GetTMPSpriteForButton(string buttonID, string scheme)
	{
		foreach (InputManager.SpriteIcon spriteIcon in this.buttonInputSprites)
		{
			if (spriteIcon.bindingDisplayName == buttonID && spriteIcon.controlScheme == scheme)
			{
				return string.Concat(new string[] { "<sprite=\"", spriteIcon.assetID, "\" name=\"", spriteIcon.spriteid, "\">" });
			}
		}
		return buttonID;
	}

	// Token: 0x06000628 RID: 1576 RVA: 0x00032CB0 File Offset: 0x00030EB0
	public static bool IsGamepad()
	{
		return InputManager.instance.playerInput.currentControlScheme == "Gamepad";
	}

	// Token: 0x06000629 RID: 1577 RVA: 0x00032CCC File Offset: 0x00030ECC
	internal static void ActivateMap(int v)
	{
		InputManager.instance.playerInput.actions.actionMaps[v].Enable();
	}

	// Token: 0x0600062A RID: 1578 RVA: 0x00032CFC File Offset: 0x00030EFC
	internal static void DeactivateMap(int v)
	{
		InputManager.instance.playerInput.actions.actionMaps[v].Disable();
	}

	// Token: 0x0600062B RID: 1579 RVA: 0x00032D2B File Offset: 0x00030F2B
	internal static void UpdateCursorVisibility()
	{
		if (!CL_GameManager.showCursor && !CommandConsole.IsConsoleVisible())
		{
			Cursor.visible = false;
			return;
		}
		if (InputManager.IsGamepad())
		{
			Cursor.visible = false;
			return;
		}
		Cursor.visible = true;
	}

	// Token: 0x0600062C RID: 1580 RVA: 0x00032D58 File Offset: 0x00030F58
	public static global::UnityEngine.InputSystem.Gyroscope GetGyroscope()
	{
		if (InputManager.gyroscope == null)
		{
			string text = "Are we even fucking getting here ";
			global::UnityEngine.InputSystem.Gyroscope current = global::UnityEngine.InputSystem.Gyroscope.current;
			Debug.Log(text + ((current != null) ? current.ToString() : null));
			string text2 = "Are we even fucking getting here aaaa ";
			global::UnityEngine.Gyroscope gyro = Input.gyro;
			Debug.Log(text2 + ((gyro != null) ? gyro.ToString() : null));
			if (global::UnityEngine.InputSystem.Gyroscope.current != null && !global::UnityEngine.InputSystem.Gyroscope.current.enabled)
			{
				Debug.Log("Are we even fucking getting here 2");
				InputSystem.EnableDevice(global::UnityEngine.InputSystem.Gyroscope.current);
			}
			InputManager.gyroscope = global::UnityEngine.InputSystem.Gyroscope.current;
		}
		return InputManager.gyroscope;
	}

	// Token: 0x040007A3 RID: 1955
	public List<InputManager.SpriteIcon> buttonInputSprites;

	// Token: 0x040007A4 RID: 1956
	public static InputManager instance;

	// Token: 0x040007A6 RID: 1958
	private List<InputManager.ButtonInput> buttonInputs;

	// Token: 0x040007A7 RID: 1959
	private Dictionary<string, InputManager.ButtonInput> buttons;

	// Token: 0x040007A8 RID: 1960
	private List<InputManager.VectorInput> vectorInputs;

	// Token: 0x040007A9 RID: 1961
	private Dictionary<string, InputManager.VectorInput> vectors;

	// Token: 0x040007AA RID: 1962
	public static List<string> controlSchemes = new List<string> { "KeyboardMouse", "Gamepad" };

	// Token: 0x040007AB RID: 1963
	public AnimationCurve gamepadLookCurve;

	// Token: 0x040007AC RID: 1964
	public float gamepadSpeedAdjust = 0.8f;

	// Token: 0x040007AD RID: 1965
	public AnimationCurve gamepadLookAccelerationCurve;

	// Token: 0x040007AE RID: 1966
	public float gamepadLookAcceleration = 1f;

	// Token: 0x040007AF RID: 1967
	private static Vector2 lastGamepadLook;

	// Token: 0x040007B0 RID: 1968
	private static global::UnityEngine.InputSystem.Gyroscope gyroscope;

	// Token: 0x040007B1 RID: 1969
	private PlayerInput playerInput;

	// Token: 0x040007B2 RID: 1970
	public static Dictionary<string, string> actionDisplayStringDict;

	// Token: 0x0200027F RID: 639
	public class ButtonInput
	{
		// Token: 0x17000070 RID: 112
		// (get) Token: 0x06000E0F RID: 3599 RVA: 0x00055B3E File Offset: 0x00053D3E
		// (set) Token: 0x06000E10 RID: 3600 RVA: 0x00055B46 File Offset: 0x00053D46
		public bool Pressed { get; set; }

		// Token: 0x17000071 RID: 113
		// (get) Token: 0x06000E11 RID: 3601 RVA: 0x00055B4F File Offset: 0x00053D4F
		// (set) Token: 0x06000E12 RID: 3602 RVA: 0x00055B57 File Offset: 0x00053D57
		public bool Down { get; set; }

		// Token: 0x17000072 RID: 114
		// (get) Token: 0x06000E13 RID: 3603 RVA: 0x00055B60 File Offset: 0x00053D60
		// (set) Token: 0x06000E14 RID: 3604 RVA: 0x00055B68 File Offset: 0x00053D68
		public bool Up { get; set; }

		// Token: 0x04001045 RID: 4165
		public string name;

		// Token: 0x04001046 RID: 4166
		public InputAction action;
	}

	// Token: 0x02000280 RID: 640
	public class VectorInput
	{
		// Token: 0x17000073 RID: 115
		// (get) Token: 0x06000E16 RID: 3606 RVA: 0x00055B79 File Offset: 0x00053D79
		// (set) Token: 0x06000E17 RID: 3607 RVA: 0x00055B81 File Offset: 0x00053D81
		public bool Pressed { get; set; }

		// Token: 0x17000074 RID: 116
		// (get) Token: 0x06000E18 RID: 3608 RVA: 0x00055B8A File Offset: 0x00053D8A
		// (set) Token: 0x06000E19 RID: 3609 RVA: 0x00055B92 File Offset: 0x00053D92
		public bool Down { get; set; }

		// Token: 0x17000075 RID: 117
		// (get) Token: 0x06000E1A RID: 3610 RVA: 0x00055B9B File Offset: 0x00053D9B
		// (set) Token: 0x06000E1B RID: 3611 RVA: 0x00055BA3 File Offset: 0x00053DA3
		public bool Up { get; set; }

		// Token: 0x0400104A RID: 4170
		public string name;

		// Token: 0x0400104B RID: 4171
		public InputAction action;

		// Token: 0x0400104C RID: 4172
		public Vector2 vector;

		// Token: 0x0400104D RID: 4173
		public float axis;
	}

	// Token: 0x02000281 RID: 641
	[Serializable]
	public class SpriteIcon
	{
		// Token: 0x04001051 RID: 4177
		public string controlScheme;

		// Token: 0x04001052 RID: 4178
		public string bindingDisplayName;

		// Token: 0x04001053 RID: 4179
		public string assetID;

		// Token: 0x04001054 RID: 4180
		public string spriteid;
	}
}
