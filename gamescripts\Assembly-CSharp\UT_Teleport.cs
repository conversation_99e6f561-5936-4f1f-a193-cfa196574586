﻿using System;
using UnityEngine;

// Token: 0x020000E1 RID: 225
public class UT_Teleport : MonoBehaviour
{
	// Token: 0x060006FC RID: 1788 RVA: 0x00036760 File Offset: 0x00034960
	public void Teleport(Transform t)
	{
		Transform transform = this.target;
		Debug.Log("Teleporting: " + t.name);
		if (this.useTargetLocator)
		{
			UT_Locator locator = UT_Locator.GetLocator(this.targetLocatorID);
			if (locator != null)
			{
				transform = locator.transform;
				locator.RunEvent();
			}
		}
		if (ObjectTagger.TagCheck(t.gameObject, "Player"))
		{
			ENT_Player component = t.GetComponent<ENT_Player>();
			if (component != null)
			{
				component.Teleport(transform.position, transform.rotation, true);
				return;
			}
		}
		t.position = transform.position + this.offset;
		t.rotation = transform.rotation;
	}

	// Token: 0x060006FD RID: 1789 RVA: 0x0003680C File Offset: 0x00034A0C
	public void SetTarget(Transform t)
	{
		this.target = t;
	}

	// Token: 0x04000878 RID: 2168
	public bool useTargetLocator;

	// Token: 0x04000879 RID: 2169
	public string targetLocatorID;

	// Token: 0x0400087A RID: 2170
	public Transform target;

	// Token: 0x0400087B RID: 2171
	public Vector3 offset;
}
