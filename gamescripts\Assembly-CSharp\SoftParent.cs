﻿using System;
using UnityEngine;

// Token: 0x02000079 RID: 121
public class SoftParent
{
	// Token: 0x0600040B RID: 1035 RVA: 0x0002554D File Offset: 0x0002374D
	public void Initialize(Transform target, Transform parentTarget)
	{
		this.transform = target;
		this.Parent(parentTarget);
	}

	// Token: 0x0600040C RID: 1036 RVA: 0x0002555D File Offset: 0x0002375D
	public Vector3 GetUpdatedPosition()
	{
		return this.parent.TransformPoint(this.parentPosition);
	}

	// Token: 0x0600040D RID: 1037 RVA: 0x00025570 File Offset: 0x00023770
	public Vector3 GetParentPosition()
	{
		return this.parentPosition;
	}

	// Token: 0x0600040E RID: 1038 RVA: 0x00025578 File Offset: 0x00023778
	public Vector3 GetPositionDifference()
	{
		if (this.parent == null)
		{
			return Vector3.zero;
		}
		this.lastParentPosition = this.parentPosition;
		return this.parent.TransformPoint(this.parentPosition) - this.transform.position;
	}

	// Token: 0x0600040F RID: 1039 RVA: 0x000255C8 File Offset: 0x000237C8
	public Quaternion GetRotationalDifference()
	{
		return Quaternion.Euler(Vector3.Scale((this.parent.rotation * Quaternion.Euler(this.rotationOffset) * Quaternion.Inverse(this.transform.rotation)).eulerAngles, this.lockAngles));
	}

	// Token: 0x06000410 RID: 1040 RVA: 0x0002561D File Offset: 0x0002381D
	public Vector3 GetDeltaVelocity()
	{
		return this.parentPosition - this.lastPosition;
	}

	// Token: 0x06000411 RID: 1041 RVA: 0x00025630 File Offset: 0x00023830
	public Vector3 GetDeltaWorldVelocity()
	{
		return this.worldVelocity;
	}

	// Token: 0x06000412 RID: 1042 RVA: 0x00025638 File Offset: 0x00023838
	public Vector3 GetPositionDeltaWorldDifference()
	{
		return this.parent.TransformPoint(this.parentPosition) - this.parentWorldPosition;
	}

	// Token: 0x06000413 RID: 1043 RVA: 0x00025656 File Offset: 0x00023856
	public void UpdateInfo()
	{
		if (this.parent == null)
		{
			return;
		}
		this.UpdatePositionInfo();
		this.UpdateRotationInfo();
	}

	// Token: 0x06000414 RID: 1044 RVA: 0x00025674 File Offset: 0x00023874
	public void UpdatePositionInfo()
	{
		if (this.parent == null)
		{
			return;
		}
		this.lastPosition = this.parentPosition;
		this.parentWorldPosition = this.transform.position;
		this.lastParentWorldMatrix = this.transform.worldToLocalMatrix;
		this.parentPosition = this.parent.InverseTransformPoint(this.transform.position);
		this.lastParentWorldMatrix = this.currentParentWorldMatrix;
		this.currentParentWorldMatrix = this.parent.localToWorldMatrix;
		Vector3 vector = this.lastParentWorldMatrix.MultiplyPoint3x4(this.parentPosition);
		Vector3 vector2 = this.currentParentWorldMatrix.MultiplyPoint3x4(this.parentPosition);
		this.velocity = this.GetDeltaVelocity() / Time.deltaTime;
		this.worldVelocity = (vector2 - vector) / Time.deltaTime;
	}

	// Token: 0x06000415 RID: 1045 RVA: 0x00025748 File Offset: 0x00023948
	public void UpdateRotationInfo()
	{
		if (this.parent == null)
		{
			return;
		}
		this.rotationOffset = (Quaternion.Inverse(this.parent.rotation) * this.transform.rotation).eulerAngles;
	}

	// Token: 0x06000416 RID: 1046 RVA: 0x00025794 File Offset: 0x00023994
	public void UpdatePosition()
	{
		if (this.parent == null)
		{
			return;
		}
		if (this.parent != null && this.parent.gameObject.activeInHierarchy)
		{
			this.transform.gameObject.SetActive(true);
			this.transform.position = this.parent.TransformPoint(this.parentPosition);
			string text = "Moving ";
			Transform transform = this.transform;
			Debug.Log(text + ((transform != null) ? transform.ToString() : null));
		}
	}

	// Token: 0x06000417 RID: 1047 RVA: 0x00025820 File Offset: 0x00023A20
	public void Parent(Transform parentTarget)
	{
		if (parentTarget == null)
		{
			return;
		}
		if (parentTarget == this.parent)
		{
			return;
		}
		this.parent = parentTarget;
		this.parentPosition = this.parent.InverseTransformPoint(this.transform.position);
		this.lastPosition = this.parentPosition;
		this.parentWorldPosition = this.transform.position;
		this.rotationOffset = (Quaternion.Inverse(this.parent.rotation) * this.transform.rotation).eulerAngles;
		this.currentParentWorldMatrix = this.parent.localToWorldMatrix;
		this.lastParentWorldMatrix = this.currentParentWorldMatrix;
	}

	// Token: 0x06000418 RID: 1048 RVA: 0x000258D1 File Offset: 0x00023AD1
	public Vector3 GetVelocity()
	{
		return this.velocity;
	}

	// Token: 0x06000419 RID: 1049 RVA: 0x000258D9 File Offset: 0x00023AD9
	public void Unparent()
	{
		this.parent = null;
	}

	// Token: 0x0600041A RID: 1050 RVA: 0x000258E2 File Offset: 0x00023AE2
	public bool IsParented()
	{
		return this.parent != null;
	}

	// Token: 0x0600041B RID: 1051 RVA: 0x000258F0 File Offset: 0x00023AF0
	public Transform GetParent()
	{
		return this.parent;
	}

	// Token: 0x04000564 RID: 1380
	public Transform parent;

	// Token: 0x04000565 RID: 1381
	public Transform transform;

	// Token: 0x04000566 RID: 1382
	private Vector3 parentPosition;

	// Token: 0x04000567 RID: 1383
	private Vector3 parentLookVector;

	// Token: 0x04000568 RID: 1384
	private Vector3 rotationOffset;

	// Token: 0x04000569 RID: 1385
	private Vector3 lastParentPosition;

	// Token: 0x0400056A RID: 1386
	private Vector3 parentWorldPosition;

	// Token: 0x0400056B RID: 1387
	private Vector3 lastRotation;

	// Token: 0x0400056C RID: 1388
	private Vector3 velocity;

	// Token: 0x0400056D RID: 1389
	private Vector3 worldVelocity;

	// Token: 0x0400056E RID: 1390
	public GameObject destroyEffect;

	// Token: 0x0400056F RID: 1391
	public Vector3 lockAngles = Vector3.one;

	// Token: 0x04000570 RID: 1392
	private bool initialized;

	// Token: 0x04000571 RID: 1393
	private Vector3 lastPosition;

	// Token: 0x04000572 RID: 1394
	private Matrix4x4 lastParentWorldMatrix;

	// Token: 0x04000573 RID: 1395
	private Matrix4x4 currentParentWorldMatrix;
}
