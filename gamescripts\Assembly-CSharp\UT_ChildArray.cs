﻿using System;
using Drawing;
using UnityEngine;

// Token: 0x02000161 RID: 353
public class UT_ChildArray : MonoBehaviourGizmos
{
	// Token: 0x060009FD RID: 2557 RVA: 0x000433E4 File Offset: 0x000415E4
	private void Update()
	{
		if (base.transform.childCount == 0)
		{
			return;
		}
		float num = (float)base.transform.childCount / 2f;
		for (int i = 0; i < base.transform.childCount; i++)
		{
			float num2 = (float)i + 0.5f;
			base.transform.GetChild(i).localPosition = this.localArrayDirection * num2 * this.offset - this.localArrayDirection * num * this.offset;
		}
	}

	// Token: 0x060009FE RID: 2558 RVA: 0x00043478 File Offset: 0x00041678
	public override void DrawGizmos()
	{
		if (this.centered)
		{
			Draw.Arrow(base.transform.position, base.transform.position + base.transform.TransformDirection(this.localArrayDirection));
			return;
		}
		Draw.Arrow(base.transform.position - base.transform.TransformDirection(this.localArrayDirection), base.transform.position + base.transform.TransformDirection(this.localArrayDirection));
	}

	// Token: 0x04000B1A RID: 2842
	public Vector3 localArrayDirection;

	// Token: 0x04000B1B RID: 2843
	public float offset = 1f;

	// Token: 0x04000B1C RID: 2844
	public bool centered = true;
}
