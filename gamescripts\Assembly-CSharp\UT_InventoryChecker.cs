﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000179 RID: 377
public class UT_InventoryChecker : MonoBehaviour
{
	// Token: 0x06000A75 RID: 2677 RVA: 0x00044B81 File Offset: 0x00042D81
	private void Update()
	{
		if (!this.active)
		{
			return;
		}
		if (this.checkEveryFrame)
		{
			this.Check();
		}
	}

	// Token: 0x06000A76 RID: 2678 RVA: 0x00044B9C File Offset: 0x00042D9C
	public void Check()
	{
		bool flag = false;
		foreach (string text in this.inventoryCheckTags)
		{
			if (Inventory.instance.HasItemWithTag(text, false))
			{
				flag = true;
				break;
			}
		}
		if (flag)
		{
			this.hasItemEvent.Invoke();
			return;
		}
		this.noItemEvent.Invoke();
	}

	// Token: 0x06000A77 RID: 2679 RVA: 0x00044C18 File Offset: 0x00042E18
	public void SetActive(bool b)
	{
		this.active = b;
	}

	// Token: 0x04000B77 RID: 2935
	public bool active = true;

	// Token: 0x04000B78 RID: 2936
	public List<string> inventoryCheckTags;

	// Token: 0x04000B79 RID: 2937
	public UnityEvent hasItemEvent;

	// Token: 0x04000B7A RID: 2938
	public UnityEvent noItemEvent;

	// Token: 0x04000B7B RID: 2939
	public bool checkEveryFrame;
}
