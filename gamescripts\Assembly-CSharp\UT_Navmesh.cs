﻿using System;
using Pathfinding;
using UnityEngine;

// Token: 0x02000181 RID: 385
public class UT_Navmesh : MonoBehaviour
{
	// Token: 0x06000A98 RID: 2712 RVA: 0x000457C7 File Offset: 0x000439C7
	private void Awake()
	{
	}

	// Token: 0x06000A99 RID: 2713 RVA: 0x000457CC File Offset: 0x000439CC
	private void Initialize()
	{
		if (this.meshCollider == null)
		{
			this.meshCollider = base.GetComponent<MeshCollider>();
		}
		this.navmesh.recalculateNormals = false;
		this.navmesh.sourceMesh = this.meshCollider.sharedMesh;
		this.navmesh.offset = base.transform.position;
		this.navmesh.rotation = base.transform.rotation.eulerAngles;
	}

	// Token: 0x06000A9A RID: 2714 RVA: 0x00045849 File Offset: 0x00043A49
	private void OnEnable()
	{
		this.Initialize();
		AstarPath.active.data.AddGraph(this.navmesh);
		AstarPath.active.Scan(null);
	}

	// Token: 0x06000A9B RID: 2715 RVA: 0x00045871 File Offset: 0x00043A71
	private void OnDisable()
	{
		if (AstarPath.active != null && AstarPath.active.data != null)
		{
			AstarPath.active.data.RemoveGraph(this.navmesh);
			AstarPath.active.Scan(null);
		}
	}

	// Token: 0x04000B98 RID: 2968
	private MeshCollider meshCollider;

	// Token: 0x04000B99 RID: 2969
	private NavMeshGraph navmesh = new NavMeshGraph();
}
