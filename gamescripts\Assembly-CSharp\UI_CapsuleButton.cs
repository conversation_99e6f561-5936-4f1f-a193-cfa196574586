﻿using System;
using System.Collections;
using DG.Tweening;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x0200011F RID: 287
public class UI_CapsuleButton : <PERSON>o<PERSON>eh<PERSON>our, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler, ISelectHandler, IDeselectHandler
{
	// Token: 0x0600088C RID: 2188 RVA: 0x0003D529 File Offset: 0x0003B729
	private void Start()
	{
		this.button = base.GetComponent<Selectable>();
		this.group = base.GetComponent<CanvasGroup>();
	}

	// Token: 0x0600088D RID: 2189 RVA: 0x0003D543 File Offset: 0x0003B743
	private void Update()
	{
		this.CheckAchievement();
	}

	// Token: 0x0600088E RID: 2190 RVA: 0x0003D54C File Offset: 0x0003B74C
	private void CheckAchievement()
	{
		if (this.unlockAchievement != "")
		{
			bool achievementValue = CL_AchievementManager.GetAchievementValue(this.unlockAchievement);
			if (this.unlockIcon != null)
			{
				this.unlockIcon.gameObject.SetActive(!achievementValue);
			}
			if (this.group != null)
			{
				this.group.interactable = achievementValue;
				this.group.alpha = (achievementValue ? 1f : 0.5f);
			}
			this.button.interactable = achievementValue;
			return;
		}
		if (this.unlockIcon != null)
		{
			this.unlockIcon.gameObject.SetActive(false);
		}
		if (this.group != null)
		{
			this.group.interactable = true;
			this.group.alpha = 1f;
		}
		this.button.interactable = true;
	}

	// Token: 0x0600088F RID: 2191 RVA: 0x0003D62F File Offset: 0x0003B82F
	public void Show()
	{
		if (this.button == null)
		{
			return;
		}
		if (!this.button.gameObject.activeInHierarchy)
		{
			return;
		}
		base.StartCoroutine(this.ShowAnimation());
	}

	// Token: 0x06000890 RID: 2192 RVA: 0x0003D660 File Offset: 0x0003B860
	public IEnumerator ShowAnimation()
	{
		yield return new WaitForSeconds(this.showDelayAnimation);
		base.transform.DOPunchScale(Vector3.one * 0.04f, 0.5f, 5, 0.5f);
		yield break;
	}

	// Token: 0x06000891 RID: 2193 RVA: 0x0003D670 File Offset: 0x0003B870
	public void OnPointerEnter(PointerEventData eventData)
	{
		if (this.button == null || !this.button.interactable)
		{
			return;
		}
		base.transform.DOPunchScale(Vector3.one * 0.04f, 0.25f, 5, 0.5f);
		base.transform.DOScale(1.05f, 0.25f);
	}

	// Token: 0x06000892 RID: 2194 RVA: 0x0003D6D5 File Offset: 0x0003B8D5
	public void OnPointerExit(PointerEventData eventData)
	{
		if (this.button == null || !this.button.interactable)
		{
			return;
		}
		base.transform.DOScale(1f, 0.25f);
	}

	// Token: 0x06000893 RID: 2195 RVA: 0x0003D70C File Offset: 0x0003B90C
	public void OnSelect(BaseEventData data)
	{
		if (this.button == null || !this.button.interactable)
		{
			return;
		}
		base.transform.DOPunchScale(Vector3.one * 0.04f, 0.25f, 5, 0.5f);
		base.transform.DOScale(1.05f, 0.25f);
	}

	// Token: 0x06000894 RID: 2196 RVA: 0x0003D771 File Offset: 0x0003B971
	public void OnDeselect(BaseEventData data)
	{
		if (this.button != null && !this.button.interactable)
		{
			return;
		}
		base.transform.DOScale(1f, 0.25f);
	}

	// Token: 0x040009E2 RID: 2530
	public float showDelayAnimation;

	// Token: 0x040009E3 RID: 2531
	private Selectable button;

	// Token: 0x040009E4 RID: 2532
	public string unlockAchievement;

	// Token: 0x040009E5 RID: 2533
	public Image unlockIcon;

	// Token: 0x040009E6 RID: 2534
	private CanvasGroup group;
}
