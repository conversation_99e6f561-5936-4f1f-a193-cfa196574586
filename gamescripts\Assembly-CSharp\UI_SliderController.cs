﻿using System;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200013E RID: 318
public class UI_SliderController : MonoBehaviour
{
	// Token: 0x06000937 RID: 2359 RVA: 0x0003FD34 File Offset: 0x0003DF34
	private void Start()
	{
		this.slider = base.GetComponent<Slider>();
	}

	// Token: 0x06000938 RID: 2360 RVA: 0x0003FD42 File Offset: 0x0003DF42
	private void Update()
	{
	}

	// Token: 0x04000A7C RID: 2684
	private Slider slider;

	// Token: 0x04000A7D RID: 2685
	public bool primed;
}
