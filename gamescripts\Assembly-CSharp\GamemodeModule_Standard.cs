﻿using System;

// Token: 0x020000A4 RID: 164
[Serializable]
public class GamemodeModule_Standard : GamemodeModule
{
	// Token: 0x0600054A RID: 1354 RVA: 0x0002C1E3 File Offset: 0x0002A3E3
	public override void OnFinish(bool hasFinished)
	{
	}

	// Token: 0x0600054B RID: 1355 RVA: 0x0002C1E8 File Offset: 0x0002A3E8
	public override float GetScore(bool hasFinished = false)
	{
		float num = CL_GameManager.gMan.GetPlayerAscent() * CL_GameManager.gMan.GetPlayerAscentRate();
		if (hasFinished)
		{
			num *= this.winScoreMultiplier;
		}
		return (float)Math.Round((double)num, 2, MidpointRounding.AwayFromZero);
	}

	// Token: 0x040006CC RID: 1740
	public float winScoreMultiplier = 5f;
}
