﻿using System;
using UnityEngine;

// Token: 0x02000135 RID: 309
public class UI_ProgressionLog : MonoBehaviour
{
	// Token: 0x06000905 RID: 2309 RVA: 0x0003F0B4 File Offset: 0x0003D2B4
	private void Start()
	{
		this.popup = base.GetComponent<UI_ProgressionPopup>();
		bool flag = this.unlock.CheckUnlock();
		if (this.prerequisite != null && flag)
		{
			flag = this.prerequisite.CheckUnlock();
		}
		if (flag)
		{
			this.popup.UpdateInformation(this.unlock.unlockIcon, this.unlock.unlockLogDescription, "Unlocked");
			return;
		}
		if (this.unlock.showProgression && this.unlock.GetProgress() > 0f)
		{
			this.popup.UpdateInformation(this.unknownIcon, "REDACTED", "Progress: " + this.unlock.GetProgressString() + " " + this.unlock.progressionString);
			return;
		}
		this.popup.UpdateInformation(this.unknownIcon, "REDACTED", this.unlock.unlockHint);
	}

	// Token: 0x04000A50 RID: 2640
	public ProgressionUnlock unlock;

	// Token: 0x04000A51 RID: 2641
	private UI_ProgressionPopup popup;

	// Token: 0x04000A52 RID: 2642
	public ProgressionUnlock prerequisite;

	// Token: 0x04000A53 RID: 2643
	public Sprite unknownIcon;
}
