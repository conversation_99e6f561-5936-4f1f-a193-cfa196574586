﻿using System;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000EA RID: 234
public class App_PictureViewer : MonoBehaviour
{
	// Token: 0x06000746 RID: 1862 RVA: 0x00037E64 File Offset: 0x00036064
	private void Start()
	{
		this.window = base.GetComponent<OS_Window>();
		if (this.window.file.fileInfo.imageAssetData == null)
		{
			this.picture = OS_Manager.activeComputer.resources.GetPicture(this.window.file.fileInfo.data);
		}
		else
		{
			this.picture = this.window.file.fileInfo.imageAssetData;
		}
		this.image.sprite = this.picture;
		this.rectTransform = base.GetComponent<RectTransform>();
		if (this.picture.rect.width > 100f)
		{
			this.rectTransform.sizeDelta = new Vector2(this.picture.rect.width, this.picture.rect.height);
			return;
		}
		this.rectTransform.sizeDelta = new Vector2(this.picture.rect.width * 2f, this.picture.rect.height * 2f);
	}

	// Token: 0x06000747 RID: 1863 RVA: 0x00037F91 File Offset: 0x00036191
	private void Update()
	{
	}

	// Token: 0x040008BE RID: 2238
	private OS_Window window;

	// Token: 0x040008BF RID: 2239
	private Sprite picture;

	// Token: 0x040008C0 RID: 2240
	public Image image;

	// Token: 0x040008C1 RID: 2241
	private RectTransform rectTransform;
}
