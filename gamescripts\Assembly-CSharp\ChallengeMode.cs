﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020000A3 RID: 163
public class ChallengeMode
{
	// Token: 0x06000544 RID: 1348 RVA: 0x0002C048 File Offset: 0x0002A248
	public static int GetMedalRankFromGamemode(M_Gamemode mode)
	{
		if (mode.GetGamemodeSaveData() == null)
		{
			return 0;
		}
		if (mode.gamemodeModule != null && mode.gamemodeModule.GetType() == typeof(GamemodeModule_Challenge))
		{
			GamemodeModule_Challenge gamemodeModule_Challenge = (GamemodeModule_Challenge)mode.gamemodeModule;
			float statisticFloat = StatManager.GetStatisticFloat(mode.GetGamemodeSaveData().stats, "score");
			return gamemodeModule_Challenge.GetMedalRankFromScore(statisticFloat);
		}
		return 0;
	}

	// Token: 0x06000545 RID: 1349 RVA: 0x0002C0AC File Offset: 0x0002A2AC
	public static GamemodeModule_Challenge.Medal GetMedalFromGamemode(M_Gamemode mode, int rank)
	{
		if (mode.gamemodeModule.GetType() != typeof(GamemodeModule_Challenge))
		{
			return null;
		}
		return ((GamemodeModule_Challenge)mode.gamemodeModule).medals[rank];
	}

	// Token: 0x06000546 RID: 1350 RVA: 0x0002C0E4 File Offset: 0x0002A2E4
	public static int GetTotalMedalsForRank(int rank)
	{
		int num = 0;
		using (List<M_Gamemode>.Enumerator enumerator = CL_AssetManager.GetFullCombinedAssetDatabase().gamemodeAssets.GetEnumerator())
		{
			while (enumerator.MoveNext())
			{
				if (ChallengeMode.GetMedalRankFromGamemode(enumerator.Current) == rank)
				{
					num++;
				}
			}
		}
		return num;
	}

	// Token: 0x06000547 RID: 1351 RVA: 0x0002C144 File Offset: 0x0002A344
	public static int GetTotalMedalsAtOrAboveRank(int rank)
	{
		int num = 0;
		for (int i = rank; i <= 4; i++)
		{
			num += ChallengeMode.GetTotalMedalsForRank(i);
		}
		return num;
	}

	// Token: 0x06000548 RID: 1352 RVA: 0x0002C16C File Offset: 0x0002A36C
	public static void CheckChallengeAchievements()
	{
		Debug.Log("Gold+ Medals: " + ChallengeMode.GetTotalMedalsAtOrAboveRank(3).ToString());
		Debug.Log("Plat+ Medals: " + ChallengeMode.GetTotalMedalsAtOrAboveRank(4).ToString());
		if (ChallengeMode.GetTotalMedalsAtOrAboveRank(3) >= 5)
		{
			CL_AchievementManager.SetAchievementValue("ACH_CHA_GOLD_5", true);
		}
		if (ChallengeMode.GetTotalMedalsAtOrAboveRank(4) >= 5)
		{
			CL_AchievementManager.SetAchievementValue("ACH_CHA_PLAT_5", true);
		}
	}
}
