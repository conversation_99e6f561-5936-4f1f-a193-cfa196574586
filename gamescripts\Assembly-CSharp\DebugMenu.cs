﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

// Token: 0x020000BB RID: 187
public class DebugMenu : MonoBehaviour
{
	// Token: 0x06000633 RID: 1587 RVA: 0x00033184 File Offset: 0x00031384
	private void Start()
	{
		DebugMenu.CreateDebugText = (Action<string, string>)Delegate.Combine(DebugMenu.CreateDebugText, new Action<string, string>(this.RegisterDebugText));
		DebugMenu.UpdateDebugText = (Action<string, string>)Delegate.Combine(DebugMenu.UpdateDebugText, new Action<string, string>(this.SetDebugText));
		DebugMenu.DeleteDebugText = (Action<string>)Delegate.Combine(DebugMenu.DeleteDebugText, new Action<string>(this.RemoveDebugText));
		this.defaultText = base.transform.GetChild(0).gameObject;
		this.defaultText.SetActive(false);
		base.gameObject.SetActive(false);
		DebugMenu.initialized = true;
	}

	// Token: 0x06000634 RID: 1588 RVA: 0x00033228 File Offset: 0x00031428
	private void OnDestroy()
	{
		DebugMenu.CreateDebugText = (Action<string, string>)Delegate.Remove(DebugMenu.CreateDebugText, new Action<string, string>(this.RegisterDebugText));
		DebugMenu.UpdateDebugText = (Action<string, string>)Delegate.Remove(DebugMenu.UpdateDebugText, new Action<string, string>(this.SetDebugText));
		DebugMenu.DeleteDebugText = (Action<string>)Delegate.Remove(DebugMenu.DeleteDebugText, new Action<string>(this.RemoveDebugText));
		DebugMenu.initialized = false;
	}

	// Token: 0x06000635 RID: 1589 RVA: 0x0003329C File Offset: 0x0003149C
	public void RegisterDebugText(string name, string content)
	{
		DebugMenu.DebugText debugText = new DebugMenu.DebugText
		{
			name = name,
			text = content,
			textObject = Object.Instantiate<GameObject>(this.defaultText, base.transform).GetComponent<TMP_Text>()
		};
		debugText.textObject.gameObject.SetActive(true);
		debugText.textObject.text = content;
		this.debugTextDictionary.Add(name, debugText);
	}

	// Token: 0x06000636 RID: 1590 RVA: 0x00033304 File Offset: 0x00031504
	public void SetDebugText(string name, string content)
	{
		if (!this.debugTextDictionary.ContainsKey(name))
		{
			this.RegisterDebugText(name, content);
		}
		this.debugTextDictionary[name].text = content;
		this.debugTextDictionary[name].textObject.text = content;
	}

	// Token: 0x06000637 RID: 1591 RVA: 0x00033350 File Offset: 0x00031550
	public void RemoveDebugText(string name)
	{
		if (this.debugTextDictionary.ContainsKey(name))
		{
			Object.Destroy(this.debugTextDictionary[name].textObject.gameObject);
			this.debugTextDictionary.Remove(name);
		}
	}

	// Token: 0x040007B9 RID: 1977
	private GameObject defaultText;

	// Token: 0x040007BA RID: 1978
	public static Action<string, string> CreateDebugText;

	// Token: 0x040007BB RID: 1979
	public static Action<string, string> UpdateDebugText;

	// Token: 0x040007BC RID: 1980
	public static Action<string> DeleteDebugText;

	// Token: 0x040007BD RID: 1981
	private Dictionary<string, DebugMenu.DebugText> debugTextDictionary = new Dictionary<string, DebugMenu.DebugText>();

	// Token: 0x040007BE RID: 1982
	public static bool initialized;

	// Token: 0x040007BF RID: 1983
	public static bool visible;

	// Token: 0x02000282 RID: 642
	public class DebugText
	{
		// Token: 0x04001055 RID: 4181
		public string name;

		// Token: 0x04001056 RID: 4182
		public string text;

		// Token: 0x04001057 RID: 4183
		public TMP_Text textObject;
	}
}
