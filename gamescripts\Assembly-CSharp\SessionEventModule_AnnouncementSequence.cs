﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200005F RID: 95
[Serializable]
public class SessionEventModule_AnnouncementSequence : SessionEventModule
{
	// Token: 0x060003B3 RID: 947 RVA: 0x00022C7C File Offset: 0x00020E7C
	public override void Initialize(SessionEvent s)
	{
		this.timer = 0f;
		base.Initialize(s);
	}

	// Token: 0x060003B4 RID: 948 RVA: 0x00022C90 File Offset: 0x00020E90
	public override void Update()
	{
		this.timer += Time.deltaTime;
		base.Update();
		foreach (SessionEventModule_AnnouncementSequence.AnnouncerEvent announcerEvent in this.announcements)
		{
			if (!announcerEvent.hasBeenPlayed && this.timer > announcerEvent.time)
			{
				announcerEvent.hasBeenPlayed = true;
				AnnouncementController.instance.StartAnnouncement(announcerEvent.audioClips[Random.Range(0, announcerEvent.audioClips.Count)], announcerEvent.tone, false);
			}
		}
	}

	// Token: 0x04000508 RID: 1288
	public List<SessionEventModule_AnnouncementSequence.AnnouncerEvent> announcements;

	// Token: 0x04000509 RID: 1289
	private float timer;

	// Token: 0x02000248 RID: 584
	[Serializable]
	public class AnnouncerEvent
	{
		// Token: 0x04000F2C RID: 3884
		public string name;

		// Token: 0x04000F2D RID: 3885
		public float time;

		// Token: 0x04000F2E RID: 3886
		public AudioClip tone;

		// Token: 0x04000F2F RID: 3887
		public List<AudioClip> audioClips;

		// Token: 0x04000F30 RID: 3888
		internal bool hasBeenPlayed;
	}
}
