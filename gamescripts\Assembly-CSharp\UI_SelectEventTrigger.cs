﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;

// Token: 0x0200013B RID: 315
public class UI_SelectEventTrigger : <PERSON>oBeh<PERSON>our, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler, ISelectHandler, IDeselectHandler, ISubmitHandler
{
	// Token: 0x06000924 RID: 2340 RVA: 0x0003F840 File Offset: 0x0003DA40
	private void Awake()
	{
		this.OnSelectEvent = new UnityEvent<BaseEventData>();
		this.OnDeselectEvent = new UnityEvent<BaseEventData>();
		this.OnPointerEnterEvent = new UnityEvent<BaseEventData>();
		this.OnPointerExitEvent = new UnityEvent<BaseEventData>();
		this.OnSubmitEvent = new UnityEvent<BaseEventData>();
	}

	// Token: 0x06000925 RID: 2341 RVA: 0x0003F87C File Offset: 0x0003DA7C
	public void AddEvent(UnityAction<BaseEventData> eventData, EventTriggerType triggerType)
	{
		if (triggerType <= EventTriggerType.PointerExit)
		{
			if (triggerType == EventTriggerType.PointerEnter)
			{
				this.OnPointerEnterEvent.AddListener(eventData);
				return;
			}
			if (triggerType != EventTriggerType.PointerExit)
			{
				return;
			}
			this.OnPointerExitEvent.AddListener(eventData);
			return;
		}
		else
		{
			if (triggerType == EventTriggerType.Select)
			{
				this.OnSelectEvent.AddListener(eventData);
				return;
			}
			if (triggerType == EventTriggerType.Deselect)
			{
				this.OnDeselectEvent.AddListener(eventData);
				return;
			}
			if (triggerType != EventTriggerType.Submit)
			{
				return;
			}
			this.OnSubmitEvent.AddListener(eventData);
			return;
		}
	}

	// Token: 0x06000926 RID: 2342 RVA: 0x0003F8E5 File Offset: 0x0003DAE5
	public void OnSelect(BaseEventData eventData)
	{
		this.OnSelectEvent.Invoke(eventData);
	}

	// Token: 0x06000927 RID: 2343 RVA: 0x0003F8F3 File Offset: 0x0003DAF3
	public void OnDeselect(BaseEventData eventData)
	{
		this.OnDeselectEvent.Invoke(eventData);
	}

	// Token: 0x06000928 RID: 2344 RVA: 0x0003F901 File Offset: 0x0003DB01
	public void OnPointerEnter(PointerEventData eventData)
	{
		this.OnPointerEnterEvent.Invoke(eventData);
	}

	// Token: 0x06000929 RID: 2345 RVA: 0x0003F90F File Offset: 0x0003DB0F
	public void OnPointerExit(PointerEventData eventData)
	{
		this.OnPointerExitEvent.Invoke(eventData);
	}

	// Token: 0x0600092A RID: 2346 RVA: 0x0003F91D File Offset: 0x0003DB1D
	public void OnSubmit(BaseEventData eventData)
	{
		this.OnSubmitEvent.Invoke(eventData);
	}

	// Token: 0x04000A6B RID: 2667
	internal UnityEvent<BaseEventData> OnSelectEvent;

	// Token: 0x04000A6C RID: 2668
	internal UnityEvent<BaseEventData> OnDeselectEvent;

	// Token: 0x04000A6D RID: 2669
	internal UnityEvent<BaseEventData> OnPointerEnterEvent;

	// Token: 0x04000A6E RID: 2670
	internal UnityEvent<BaseEventData> OnPointerExitEvent;

	// Token: 0x04000A6F RID: 2671
	internal UnityEvent<BaseEventData> OnSubmitEvent;

	// Token: 0x04000A70 RID: 2672
	public List<EventTrigger.Entry> triggers;
}
