﻿using System;
using UnityEngine;

// Token: 0x0200007B RID: 123
public class FX_Cloud : MonoBehaviour
{
	// Token: 0x0600041E RID: 1054 RVA: 0x000259B0 File Offset: 0x00023BB0
	private void Start()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.QualityCheck));
		this.rand = Random.value;
		this.QualityCheck();
	}

	// Token: 0x0600041F RID: 1055 RVA: 0x000259E3 File Offset: 0x00023BE3
	private void OnDestroy()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Remove(SettingsManager.SettingsRefreshUpdate, new Action(this.QualityCheck));
	}

	// Token: 0x06000420 RID: 1056 RVA: 0x00025A08 File Offset: 0x00023C08
	public void QualityCheck()
	{
		if (SettingsManager.settings.cloudQuality >= 2)
		{
			base.gameObject.SetActive(true);
			return;
		}
		if (SettingsManager.settings.cloudQuality != 1)
		{
			if (SettingsManager.settings.cloudQuality == 0)
			{
				base.gameObject.SetActive(false);
			}
			return;
		}
		if (!this.force)
		{
			base.gameObject.SetActive(this.rand > 0.5f);
			return;
		}
		base.gameObject.SetActive(true);
	}

	// Token: 0x04000574 RID: 1396
	public bool force;

	// Token: 0x04000575 RID: 1397
	private float rand;
}
