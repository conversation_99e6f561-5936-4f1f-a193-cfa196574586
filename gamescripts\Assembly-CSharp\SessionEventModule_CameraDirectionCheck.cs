﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000062 RID: 98
[Serializable]
public class SessionEventModule_CameraDirectionCheck : SessionEventModule
{
	// Token: 0x060003BD RID: 957 RVA: 0x00022EAC File Offset: 0x000210AC
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		this.hasTriggered = false;
		Transform transform = ENT_Player.playerObject.cam.transform;
		this.startDirection = transform.forward;
		if (this.relativeToCameraAtInitialize)
		{
			this.targetDirection = transform.TransformVector(this.direction);
			return;
		}
		this.targetDirection = this.direction;
	}

	// Token: 0x060003BE RID: 958 RVA: 0x00022F0C File Offset: 0x0002110C
	public override void Update()
	{
		base.Update();
		if (this.hasTriggered)
		{
			return;
		}
		if (Vector3.Angle(ENT_Player.playerObject.cam.transform.forward, this.targetDirection) < this.maxAngle)
		{
			this.hasTriggered = true;
			foreach (SessionEventModule sessionEventModule in this.modulesOnCheck)
			{
				this.sessionEvent.AddModule(sessionEventModule);
			}
		}
	}

	// Token: 0x04000514 RID: 1300
	public Vector3 direction;

	// Token: 0x04000515 RID: 1301
	public bool relativeToCameraAtInitialize = true;

	// Token: 0x04000516 RID: 1302
	public float maxAngle;

	// Token: 0x04000517 RID: 1303
	private Vector3 startDirection;

	// Token: 0x04000518 RID: 1304
	private Vector3 targetDirection;

	// Token: 0x04000519 RID: 1305
	[SerializeReference]
	public List<SessionEventModule> modulesOnCheck;

	// Token: 0x0400051A RID: 1306
	private bool hasTriggered;
}
