﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020000C8 RID: 200
public class UT_CameraTakeover : MonoBehaviour
{
	// Token: 0x0600068B RID: 1675 RVA: 0x00034D22 File Offset: 0x00032F22
	private void Start()
	{
		this.player = CL_GameManager.gMan.localPlayer;
		this.cam = this.player.cam;
	}

	// Token: 0x0600068C RID: 1676 RVA: 0x00034D48 File Offset: 0x00032F48
	private void Update()
	{
		if (CL_GameManager.gMan.isPaused)
		{
			return;
		}
		if (this.active)
		{
			this.cam.transform.position = Vector3.Lerp(this.cam.transform.position, base.transform.position, Time.deltaTime * this.speed);
			this.cam.transform.rotation = Quaternion.Lerp(this.cam.transform.rotation, base.transform.rotation, Time.deltaTime * this.speed);
			this.cam.fieldOfView = Mathf.Lerp(this.cam.fieldOfView, this.fov, Time.deltaTime * this.speed);
		}
	}

	// Token: 0x0600068D RID: 1677 RVA: 0x00034E12 File Offset: 0x00033012
	public void TakeoverCamera()
	{
		this.player.Lock();
		this.active = true;
		this.activateEvent.Invoke();
		this.isTakingOver = true;
	}

	// Token: 0x0600068E RID: 1678 RVA: 0x00034E38 File Offset: 0x00033038
	public void EndTakeover()
	{
		this.active = false;
		this.deactivateEvent.Invoke();
		this.isTakingOver = false;
		this.player.UnLock();
	}

	// Token: 0x0600068F RID: 1679 RVA: 0x00034E5E File Offset: 0x0003305E
	private IEnumerator LerpBack()
	{
		float timer = 0f;
		while (timer < 1f)
		{
			timer += Time.deltaTime * this.speed;
			this.cam.transform.position = Vector3.Lerp(base.transform.position, this.player.GetCameraTargetPosition(), timer);
			this.cam.transform.rotation = Quaternion.Lerp(base.transform.rotation, this.player.GetCameraTargetRotation(), timer);
			this.cam.fieldOfView = Mathf.Lerp(this.cam.fieldOfView, this.player.GetCurrentFOV(), timer);
			yield return new WaitForEndOfFrame();
		}
		this.isTakingOver = false;
		this.player.UnLock();
		yield break;
	}

	// Token: 0x040007FB RID: 2043
	private ENT_Player player;

	// Token: 0x040007FC RID: 2044
	private Camera cam;

	// Token: 0x040007FD RID: 2045
	private bool active;

	// Token: 0x040007FE RID: 2046
	public float speed = 5f;

	// Token: 0x040007FF RID: 2047
	public float fov = 90f;

	// Token: 0x04000800 RID: 2048
	public UnityEvent activateEvent;

	// Token: 0x04000801 RID: 2049
	public UnityEvent deactivateEvent;

	// Token: 0x04000802 RID: 2050
	public bool isTakingOver;
}
