﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000146 RID: 326
public class UI_MedalViewer : MonoBehaviour
{
	// Token: 0x06000960 RID: 2400 RVA: 0x000407B3 File Offset: 0x0003E9B3
	private void OnEnable()
	{
		this.Refresh();
	}

	// Token: 0x06000961 RID: 2401 RVA: 0x000407BB File Offset: 0x0003E9BB
	public void Refresh()
	{
		base.StartCoroutine(this.<Refresh>g__WaitOneFrame|9_0());
	}

	// Token: 0x06000962 RID: 2402 RVA: 0x000407CC File Offset: 0x0003E9CC
	private void UpdateMedalIcon()
	{
		M_Gamemode m_Gamemode;
		if (this.gamemodeHolder != null)
		{
			m_Gamemode = this.gamemodeHolder.GetComponent<GamemodeHolder>().GetGamemode();
			Debug.Log(this.gamemodeHolder.GetComponent<GamemodeHolder>());
			Debug.Log("Attempting to Get Gamemode from Holder");
		}
		else
		{
			m_Gamemode = CL_GameManager.GetCurrentGamemode();
		}
		Debug.Log(m_Gamemode);
		if (!(m_Gamemode != null))
		{
			this.medalImage.sprite = this.medalSprites[0];
			this.medalImage.color = Color.clear;
			return;
		}
		int num = ChallengeMode.GetMedalRankFromGamemode(m_Gamemode);
		bool flag = m_Gamemode.HasWonGamemode();
		string[] array = new string[6];
		array[0] = "Gamemode: ";
		int num2 = 1;
		M_Gamemode m_Gamemode2 = m_Gamemode;
		array[num2] = ((m_Gamemode2 != null) ? m_Gamemode2.ToString() : null);
		array[2] = " :: ";
		array[3] = num.ToString();
		array[4] = " :: ";
		array[5] = flag.ToString();
		Debug.Log(string.Concat(array));
		if (this.onlyShowIfSuccessful)
		{
			num--;
		}
		if (num < 0 || (this.onlyShowIfSuccessful && !flag))
		{
			this.medalImage.color = Color.clear;
			return;
		}
		Debug.Log("Best Medal Found: " + num.ToString());
		this.medalImage.sprite = this.medalSprites[num];
		this.medalImage.color = Color.white;
	}

	// Token: 0x06000963 RID: 2403 RVA: 0x0004091C File Offset: 0x0003EB1C
	public void UpdateGoalInfo()
	{
		M_Gamemode m_Gamemode;
		if (this.gamemodeHolder != null)
		{
			m_Gamemode = this.gamemodeHolder.GetComponent<GamemodeHolder>().GetGamemode();
		}
		else
		{
			m_Gamemode = CL_GameManager.GetCurrentGamemode();
		}
		if (m_Gamemode != null && m_Gamemode.gamemodeModule.GetType() == typeof(GamemodeModule_Challenge))
		{
			GamemodeModule_Challenge gamemodeModule_Challenge = (GamemodeModule_Challenge)m_Gamemode.gamemodeModule;
			GamemodeModule_Challenge.Medal medal = gamemodeModule_Challenge.medals[this.medalGoalLevel];
			this.medalImage.sprite = this.medalSprites[this.medalGoalLevel];
			if (gamemodeModule_Challenge.useTime)
			{
				string text = TimeSpan.FromSeconds((double)Mathf.Round(gamemodeModule_Challenge.startingScore / medal.scoreRequirement)).ToString("mm\\:ss");
				this.counterText.text = text;
				return;
			}
			this.counterText.text = medal.scoreRequirement.ToString();
		}
	}

	// Token: 0x06000965 RID: 2405 RVA: 0x00040A12 File Offset: 0x0003EC12
	[CompilerGenerated]
	private IEnumerator <Refresh>g__WaitOneFrame|9_0()
	{
		yield return null;
		if (!this.showMedalGoal)
		{
			this.UpdateMedalIcon();
		}
		else
		{
			this.UpdateGoalInfo();
		}
		yield break;
	}

	// Token: 0x04000AA9 RID: 2729
	public List<Sprite> medalSprites;

	// Token: 0x04000AAA RID: 2730
	public Image medalImage;

	// Token: 0x04000AAB RID: 2731
	public TMP_Text counterText;

	// Token: 0x04000AAC RID: 2732
	public bool showMedalGoal;

	// Token: 0x04000AAD RID: 2733
	public int medalGoalLevel;

	// Token: 0x04000AAE RID: 2734
	public bool onlyShowIfSuccessful;

	// Token: 0x04000AAF RID: 2735
	public GameObject gamemodeHolder;

	// Token: 0x020002C7 RID: 711
	public enum ViewerType
	{
		// Token: 0x040011CC RID: 4556
		currentGamemode,
		// Token: 0x040011CD RID: 4557
		allGamemodes
	}
}
