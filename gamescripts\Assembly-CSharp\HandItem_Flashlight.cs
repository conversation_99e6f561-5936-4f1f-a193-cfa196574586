﻿using System;
using System.Globalization;
using UnityEngine;

// Token: 0x0200008C RID: 140
public class HandItem_Flashlight : HandItem
{
	// Token: 0x060004B5 RID: 1205 RVA: 0x00028E3C File Offset: 0x0002703C
	public override void Initialize(Item i, ENT_Player.Hand h)
	{
		base.Initialize(i, h);
		if (this.item.data[0].Contains("charge:"))
		{
			this.charge = float.Parse(this.item.data[0].Split(':', StringSplitOptions.None)[1], NumberStyles.Float, CultureInfo.InvariantCulture);
			this.maxCharge = float.Parse(this.item.data[1].Split(':', StringSplitOptions.None)[1], NumberStyles.Float, CultureInfo.InvariantCulture);
			this.chargeRate = float.Parse(this.item.GetFirstDataStringByType("chargerate", false), NumberStyles.Float, CultureInfo.InvariantCulture);
		}
	}

	// Token: 0x060004B6 RID: 1206 RVA: 0x00028EF7 File Offset: 0x000270F7
	public override void Use()
	{
		base.Use();
		if (this.inUse)
		{
			return;
		}
		this.anim.SetTrigger("pump");
		this.inUse = true;
	}

	// Token: 0x060004B7 RID: 1207 RVA: 0x00028F20 File Offset: 0x00027120
	public void Finish()
	{
		this.charge = Mathf.Clamp(this.chargeRate + this.charge, this.maxCharge / 2f, this.maxCharge);
		this.item.data[0] = string.Format("charge:{0}", this.charge);
		CL_CameraControl.Shake(0.02f);
		this.inUse = false;
	}

	// Token: 0x060004B8 RID: 1208 RVA: 0x00028F94 File Offset: 0x00027194
	private void Update()
	{
		this.charge = float.Parse(this.item.data[0].Split(':', StringSplitOptions.None)[1]);
		SpriteRenderer[] array = this.flareSprites;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].color = new Color(1f, 1f, 1f, this.charge / this.maxCharge);
		}
	}

	// Token: 0x060004B9 RID: 1209 RVA: 0x00029005 File Offset: 0x00027205
	public override bool CanDrop()
	{
		return true;
	}

	// Token: 0x04000638 RID: 1592
	public CL_Lamp lamp;

	// Token: 0x04000639 RID: 1593
	public SpriteRenderer[] flareSprites;

	// Token: 0x0400063A RID: 1594
	private float maxCharge = 20f;

	// Token: 0x0400063B RID: 1595
	private float charge = 1f;

	// Token: 0x0400063C RID: 1596
	private float chargeRate = 5f;

	// Token: 0x0400063D RID: 1597
	public Transform lightPosition;

	// Token: 0x0400063E RID: 1598
	private bool inUse;
}
