﻿using System;
using System.IO;
using UnityEngine;

// Token: 0x020000C1 RID: 193
public class Screenshot
{
	// Token: 0x06000654 RID: 1620 RVA: 0x00033A74 File Offset: 0x00031C74
	public static void TakeScreenshot(int size = 1, string setPath = "")
	{
		string text = Path.Combine(Application.persistentDataPath, "Screenshots");
		Directory.CreateDirectory(text);
		if (setPath != "")
		{
			text = setPath;
		}
		string text2 = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss-fff");
		string text3 = "Screenshot_" + text2 + ".png";
		string text4 = Path.Combine(text, text3);
		ScreenCapture.CaptureScreenshot(text4, size);
		Debug.Log("Screenshot saved to: " + text4);
	}
}
