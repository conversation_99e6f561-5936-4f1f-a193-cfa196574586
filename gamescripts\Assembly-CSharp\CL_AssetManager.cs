﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using UnityEngine;

// Token: 0x02000017 RID: 23
[Serializable]
public class CL_AssetManager
{
	// Token: 0x06000092 RID: 146 RVA: 0x00005F47 File Offset: 0x00004147
	public void Initialize()
	{
		if (this.initialized)
		{
			return;
		}
		this.initialized = true;
		CL_AssetManager.instance = this;
	}

	// Token: 0x06000093 RID: 147 RVA: 0x00005F60 File Offset: 0x00004160
	public static GameObject GetAssetGameObject(string name, string database = "")
	{
		if (CL_AssetManager.instance == null || CL_AssetManager.databases == null)
		{
			CL_AssetManager.InitializeAssetManager();
		}
		bool flag = database != "" && database != null;
		foreach (CL_AssetManager.WKDatabaseHolder wkdatabaseHolder in CL_AssetManager.databases)
		{
			if ((!flag || !(wkdatabaseHolder.id != database)) && wkdatabaseHolder.HasAssetInDictionary(name))
			{
				return wkdatabaseHolder.GetAssetFromDictionary(name);
			}
		}
		Debug.LogWarning("Entity Not Found in Asset Database: " + name);
		return null;
	}

	// Token: 0x06000094 RID: 148 RVA: 0x0000600C File Offset: 0x0000420C
	public static M_Region GetRegionAsset(string name, string database = "")
	{
		bool flag = database != "" && database != null;
		foreach (CL_AssetManager.WKDatabaseHolder wkdatabaseHolder in CL_AssetManager.databases)
		{
			if (!flag || !(wkdatabaseHolder.id != database))
			{
				foreach (M_Region m_Region in wkdatabaseHolder.database.regionAssets)
				{
					if (m_Region.name == name)
					{
						return m_Region;
					}
				}
			}
		}
		return null;
	}

	// Token: 0x06000095 RID: 149 RVA: 0x000060D8 File Offset: 0x000042D8
	public static M_Subregion GetSubregionAsset(string name, string database = "")
	{
		bool flag = database != "" && database != null;
		foreach (CL_AssetManager.WKDatabaseHolder wkdatabaseHolder in CL_AssetManager.databases)
		{
			if (!flag || !(wkdatabaseHolder.id != database))
			{
				foreach (M_Subregion m_Subregion in wkdatabaseHolder.database.subRegionAssets)
				{
					if (m_Subregion.name == name)
					{
						return m_Subregion;
					}
				}
			}
		}
		return null;
	}

	// Token: 0x06000096 RID: 150 RVA: 0x000061A4 File Offset: 0x000043A4
	public static M_Level GetLevelAsset(string name, string database = "")
	{
		bool flag = database != "" && database != null;
		foreach (CL_AssetManager.WKDatabaseHolder wkdatabaseHolder in CL_AssetManager.databases)
		{
			if (!flag || !(wkdatabaseHolder.id != database))
			{
				foreach (GameObject gameObject in wkdatabaseHolder.database.levelPrefabs)
				{
					if (gameObject.name.ToLower() == name.ToLower())
					{
						return gameObject.GetComponent<M_Level>();
					}
				}
			}
		}
		return null;
	}

	// Token: 0x06000097 RID: 151 RVA: 0x00006280 File Offset: 0x00004480
	public static M_Gamemode GetGamemodeAsset(string name, string database = "")
	{
		bool flag = database != "" && database != null;
		foreach (CL_AssetManager.WKDatabaseHolder wkdatabaseHolder in CL_AssetManager.databases)
		{
			if (!flag || !(wkdatabaseHolder.id != database))
			{
				foreach (M_Gamemode m_Gamemode in wkdatabaseHolder.database.gamemodeAssets)
				{
					if (m_Gamemode.gamemodeName == name || m_Gamemode.name == name)
					{
						return m_Gamemode;
					}
				}
			}
		}
		return null;
	}

	// Token: 0x06000098 RID: 152 RVA: 0x0000635C File Offset: 0x0000455C
	public static OS_DiskData GetDiskDataAsset(string id, string database = "")
	{
		bool flag = database != "" && database != null;
		foreach (CL_AssetManager.WKDatabaseHolder wkdatabaseHolder in CL_AssetManager.databases)
		{
			if (!flag || !(wkdatabaseHolder.id != database))
			{
				foreach (OS_DiskData os_DiskData in wkdatabaseHolder.database.diskData)
				{
					if (os_DiskData.id == id)
					{
						return os_DiskData;
					}
				}
			}
		}
		return null;
	}

	// Token: 0x06000099 RID: 153 RVA: 0x00006428 File Offset: 0x00004628
	public static Sprite GetSpriteAsset(string name, string database = "")
	{
		bool flag = database != "" && database != null;
		foreach (CL_AssetManager.WKDatabaseHolder wkdatabaseHolder in CL_AssetManager.databases)
		{
			if (!flag || !(wkdatabaseHolder.id != database))
			{
				foreach (Sprite sprite in wkdatabaseHolder.database.spriteAssets)
				{
					if (sprite.name == name)
					{
						return sprite;
					}
				}
			}
		}
		return null;
	}

	// Token: 0x0600009A RID: 154 RVA: 0x000064F4 File Offset: 0x000046F4
	public static Perk GetPerkAsset(string id, string database = "")
	{
		bool flag = database != "" && database != null;
		foreach (CL_AssetManager.WKDatabaseHolder wkdatabaseHolder in CL_AssetManager.databases)
		{
			if (!flag || !(wkdatabaseHolder.id != database))
			{
				foreach (Perk perk in wkdatabaseHolder.database.perkAssets)
				{
					if (perk.id.ToLower().Contains(id.ToLower()))
					{
						return perk;
					}
				}
			}
		}
		return null;
	}

	// Token: 0x0600009B RID: 155 RVA: 0x000065CC File Offset: 0x000047CC
	public static SessionEvent GetSessionEvent(string name, string database = "")
	{
		bool flag = database != "" && database != null;
		foreach (CL_AssetManager.WKDatabaseHolder wkdatabaseHolder in CL_AssetManager.databases)
		{
			if (!flag || !(wkdatabaseHolder.id != database))
			{
				foreach (SessionEvent sessionEvent in wkdatabaseHolder.database.sessionEvents)
				{
					if (sessionEvent.name == name || sessionEvent.name == name)
					{
						return sessionEvent;
					}
				}
			}
		}
		return null;
	}

	// Token: 0x0600009C RID: 156 RVA: 0x000066A8 File Offset: 0x000048A8
	public static GameObject GetNullItemObject()
	{
		return CL_AssetManager.databases[0].database.nullItemObject;
	}

	// Token: 0x0600009D RID: 157 RVA: 0x000066C0 File Offset: 0x000048C0
	public static void InitializeDatabases()
	{
		if (CL_AssetManager.databases != null)
		{
			return;
		}
		CL_AssetManager.databases = new List<CL_AssetManager.WKDatabaseHolder>();
		CL_AssetManager.activeDatabases = new Dictionary<string, CL_AssetManager.WKDatabaseHolder>();
		WKAssetDatabase wkassetDatabase = Resources.Load<WKAssetDatabase>("WK_AssetDatabase");
		CL_AssetManager.AddNewDatabase(wkassetDatabase.id, wkassetDatabase);
	}

	// Token: 0x0600009E RID: 158 RVA: 0x00006700 File Offset: 0x00004900
	public static void AddNewDatabase(string id, WKAssetDatabase database)
	{
		if (CL_AssetManager.databases == null || CL_AssetManager.databases.Count == 0)
		{
			CL_AssetManager.InitializeDatabases();
		}
		if (CL_AssetManager.activeDatabases.ContainsKey(id))
		{
			return;
		}
		CL_AssetManager.WKDatabaseHolder wkdatabaseHolder = new CL_AssetManager.WKDatabaseHolder
		{
			id = id,
			database = database
		};
		wkdatabaseHolder.RefreshDictionary();
		CommandConsole.Log("Initialized Asset Database: " + id, false);
		CL_AssetManager.activeDatabases.Add(id, wkdatabaseHolder);
		CL_AssetManager.databases.Add(wkdatabaseHolder);
	}

	// Token: 0x0600009F RID: 159 RVA: 0x00006778 File Offset: 0x00004978
	public static WKAssetDatabase GetDatabase(string id)
	{
		if (CL_AssetManager.databases == null)
		{
			Debug.LogError("No databases found. Initialize databases before you attempt to call.");
			return null;
		}
		if (CL_AssetManager.activeDatabases.ContainsKey(id))
		{
			return CL_AssetManager.activeDatabases[id].database;
		}
		Debug.LogError("Error: No database of " + id + " found in database.");
		return null;
	}

	// Token: 0x060000A0 RID: 160 RVA: 0x000067CC File Offset: 0x000049CC
	public static void RemoveDatabase(string id)
	{
		if (CL_AssetManager.activeDatabases.ContainsKey(id))
		{
			CL_AssetManager.WKDatabaseHolder wkdatabaseHolder = CL_AssetManager.activeDatabases[id];
			CL_AssetManager.activeDatabases.Remove(id);
			CL_AssetManager.databases.Remove(wkdatabaseHolder);
			CommandConsole.Log("Database " + id + " Removed.", false);
		}
	}

	// Token: 0x060000A1 RID: 161 RVA: 0x00006820 File Offset: 0x00004A20
	public static void InitializeAssetManager()
	{
		if (CL_AssetManager.instance != null && CL_AssetManager.databases != null)
		{
			return;
		}
		CL_AssetManager.InitializeDatabases();
		CL_AssetManager.instance = new CL_AssetManager();
		CL_AssetManager.instance.Initialize();
	}

	// Token: 0x060000A2 RID: 162 RVA: 0x0000684A File Offset: 0x00004A4A
	private static WKAssetDatabase GetStandardDatabaseAssetFromResources()
	{
		return Resources.Load<WKAssetDatabase>("WK_AssetDatabase");
	}

	// Token: 0x060000A3 RID: 163 RVA: 0x00006856 File Offset: 0x00004A56
	public static CL_AssetManager GetAssetManager()
	{
		if (CL_AssetManager.instance != null)
		{
			return CL_AssetManager.instance;
		}
		CL_AssetManager.InitializeAssetManager();
		return CL_AssetManager.instance;
	}

	// Token: 0x060000A4 RID: 164 RVA: 0x0000686F File Offset: 0x00004A6F
	public static WKAssetDatabase GetBaseAssetDatabase()
	{
		return CL_AssetManager.databases[0].database;
	}

	// Token: 0x060000A5 RID: 165 RVA: 0x00006884 File Offset: 0x00004A84
	public static void RefreshDatabaseList()
	{
		foreach (CL_AssetManager.WKDatabaseHolder wkdatabaseHolder in CL_AssetManager.databases)
		{
			wkdatabaseHolder.RefreshDictionary();
		}
	}

	// Token: 0x060000A6 RID: 166 RVA: 0x000068D4 File Offset: 0x00004AD4
	public static WKAssetDatabase GetFullCombinedAssetDatabase()
	{
		WKAssetDatabase wkassetDatabase = new WKAssetDatabase
		{
			denizenPrefabs = new List<GameObject>(),
			entityPrefabs = new List<GameObject>(),
			itemPrefabs = new List<GameObject>(),
			levelPrefabs = new List<GameObject>(),
			regionAssets = new List<M_Region>(),
			subRegionAssets = new List<M_Subregion>(),
			gamemodeAssets = new List<M_Gamemode>(),
			perkAssets = new List<Perk>(),
			diskData = new List<OS_DiskData>(),
			sessionEvents = new List<SessionEvent>(),
			spriteAssets = new List<Sprite>()
		};
		foreach (CL_AssetManager.WKDatabaseHolder wkdatabaseHolder in CL_AssetManager.databases)
		{
			wkassetDatabase.denizenPrefabs.AddRange(wkdatabaseHolder.database.denizenPrefabs);
			wkassetDatabase.entityPrefabs.AddRange(wkdatabaseHolder.database.entityPrefabs);
			wkassetDatabase.itemPrefabs.AddRange(wkdatabaseHolder.database.itemPrefabs);
			wkassetDatabase.levelPrefabs.AddRange(wkdatabaseHolder.database.levelPrefabs);
			wkassetDatabase.regionAssets.AddRange(wkdatabaseHolder.database.regionAssets);
			wkassetDatabase.subRegionAssets.AddRange(wkdatabaseHolder.database.subRegionAssets);
			wkassetDatabase.gamemodeAssets.AddRange(wkdatabaseHolder.database.gamemodeAssets);
			wkassetDatabase.perkAssets.AddRange(wkdatabaseHolder.database.perkAssets);
			wkassetDatabase.diskData.AddRange(wkdatabaseHolder.database.diskData);
			wkassetDatabase.sessionEvents.AddRange(wkdatabaseHolder.database.sessionEvents);
			wkassetDatabase.spriteAssets.AddRange(wkdatabaseHolder.database.spriteAssets);
		}
		return wkassetDatabase;
	}

	// Token: 0x04000091 RID: 145
	private static List<CL_AssetManager.WKDatabaseHolder> databases;

	// Token: 0x04000092 RID: 146
	private static Dictionary<string, CL_AssetManager.WKDatabaseHolder> activeDatabases;

	// Token: 0x04000093 RID: 147
	public List<M_Level> levelPrefabs;

	// Token: 0x04000094 RID: 148
	public static CL_AssetManager instance;

	// Token: 0x04000095 RID: 149
	private bool initialized;

	// Token: 0x020001FA RID: 506
	public class WKDatabaseHolder
	{
		// Token: 0x06000C9E RID: 3230 RVA: 0x0004F1AF File Offset: 0x0004D3AF
		public void RefreshDictionary()
		{
			this.assetDictionary = new Dictionary<string, GameObject>();
			this.<RefreshDictionary>g__FillObjectDictionaryFromList|3_0(this.database.itemPrefabs);
			this.<RefreshDictionary>g__FillObjectDictionaryFromList|3_0(this.database.levelPrefabs);
			this.<RefreshDictionary>g__FillObjectDictionaryFromList|3_0(this.database.entityPrefabs);
		}

		// Token: 0x06000C9F RID: 3231 RVA: 0x0004F1EF File Offset: 0x0004D3EF
		public GameObject GetAssetFromDictionary(string id)
		{
			return this.assetDictionary[id];
		}

		// Token: 0x06000CA0 RID: 3232 RVA: 0x0004F1FD File Offset: 0x0004D3FD
		public bool HasAssetInDictionary(string id)
		{
			return this.assetDictionary.ContainsKey(id);
		}

		// Token: 0x06000CA2 RID: 3234 RVA: 0x0004F214 File Offset: 0x0004D414
		[CompilerGenerated]
		private void <RefreshDictionary>g__FillObjectDictionaryFromList|3_0(List<GameObject> objectList)
		{
			foreach (GameObject gameObject in objectList)
			{
				if (!this.assetDictionary.ContainsKey(gameObject.name))
				{
					this.assetDictionary.Add(gameObject.name, gameObject);
				}
			}
		}

		// Token: 0x04000DA0 RID: 3488
		public string id;

		// Token: 0x04000DA1 RID: 3489
		public WKAssetDatabase database;

		// Token: 0x04000DA2 RID: 3490
		private Dictionary<string, GameObject> assetDictionary;
	}
}
