﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020001C8 RID: 456
public class UT_SendPlayerTransform : MonoBehaviour
{
	// Token: 0x06000BA2 RID: 2978 RVA: 0x0004A4C7 File Offset: 0x000486C7
	private void Start()
	{
		if (this.sendOnStart)
		{
			this.SendTransform();
		}
	}

	// Token: 0x06000BA3 RID: 2979 RVA: 0x0004A4D7 File Offset: 0x000486D7
	public void SendTransform()
	{
		this.output.Invoke(ENT_Player.playerObject.transform);
	}

	// Token: 0x04000CB6 RID: 3254
	public UnityEvent<Transform> output;

	// Token: 0x04000CB7 RID: 3255
	public bool sendOnStart;
}
