﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200010C RID: 268
[CreateAssetMenu(fileName = "New Disk Data File", menuName = "White Knuckle/OS/Disk Data")]
public class OS_DiskData : ScriptableObject
{
	// Token: 0x06000829 RID: 2089 RVA: 0x0003B6A6 File Offset: 0x000398A6
	public void Initialize()
	{
		this.hasInitialized = true;
		this.selectedDisk = Random.Range(0, this.diskDataSelectors.Count);
	}

	// Token: 0x0600082A RID: 2090 RVA: 0x0003B6C8 File Offset: 0x000398C8
	public List<OS_Filesystem.FileInfo> GetFiles()
	{
		if (this.type == OS_DiskData.DiskDataType.diskDataList)
		{
			if (this.chosenDisk == null)
			{
				this.chosenDisk = Object.Instantiate<OS_DiskData>(this.randomDisks[Random.Range(0, this.randomDisks.Count)]);
			}
			this.chosenDisk.Initialize();
			return this.chosenDisk.GetFiles();
		}
		if (!this.useRandomSelector)
		{
			return this.files;
		}
		if (this.selectedDisk == -1)
		{
			this.Initialize();
		}
		return this.diskDataSelectors[this.selectedDisk].files;
	}

	// Token: 0x0600082B RID: 2091 RVA: 0x0003B760 File Offset: 0x00039960
	internal string GetDiskName()
	{
		if (this.type == OS_DiskData.DiskDataType.diskDataList)
		{
			if (this.chosenDisk == null)
			{
				this.chosenDisk = this.randomDisks[Random.Range(0, this.randomDisks.Count)];
			}
			return this.chosenDisk.GetDiskName();
		}
		return this.diskName;
	}

	// Token: 0x04000990 RID: 2448
	public string diskName;

	// Token: 0x04000991 RID: 2449
	public string id;

	// Token: 0x04000992 RID: 2450
	public OS_DiskData.DiskDataType type;

	// Token: 0x04000993 RID: 2451
	public List<OS_DiskData> randomDisks;

	// Token: 0x04000994 RID: 2452
	public List<OS_Filesystem.FileInfo> files;

	// Token: 0x04000995 RID: 2453
	public bool useRandomSelector;

	// Token: 0x04000996 RID: 2454
	private int selectedDisk = -1;

	// Token: 0x04000997 RID: 2455
	public List<OS_DiskData.DiskDataSelector> diskDataSelectors;

	// Token: 0x04000998 RID: 2456
	private bool hasInitialized;

	// Token: 0x04000999 RID: 2457
	private OS_DiskData chosenDisk;

	// Token: 0x020002AB RID: 683
	public enum DiskDataType
	{
		// Token: 0x0400110D RID: 4365
		standard,
		// Token: 0x0400110E RID: 4366
		diskDataList
	}

	// Token: 0x020002AC RID: 684
	[Serializable]
	public class DiskDataSelector
	{
		// Token: 0x0400110F RID: 4367
		public string name;

		// Token: 0x04001110 RID: 4368
		public List<OS_Filesystem.FileInfo> files;
	}
}
