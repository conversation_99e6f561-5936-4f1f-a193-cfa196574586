﻿using System;
using UnityEngine;

namespace DarkMachine.AI
{
	// Token: 0x020001D3 RID: 467
	[Serializable]
	public class AISightComponent
	{
		// Token: 0x06000BD3 RID: 3027 RVA: 0x0004AEF6 File Offset: 0x000490F6
		public virtual void Update()
		{
			if (this.memoryTime > 0f && this.useMemory)
			{
				this.memoryTime -= Time.deltaTime;
			}
		}

		// Token: 0x06000BD4 RID: 3028 RVA: 0x0004AF1F File Offset: 0x0004911F
		public bool CanRemember()
		{
			return !this.useMemory || this.memoryTime > 0f;
		}

		// Token: 0x06000BD5 RID: 3029 RVA: 0x0004AF38 File Offset: 0x00049138
		public virtual bool CanSeeTarget(Vector3 origin, Transform target, float dist, Transform customAimTarget = null)
		{
			if (this.seeBackface)
			{
				Physics.queriesHitBackfaces = true;
			}
			RaycastHit raycastHit;
			if (!Physics.Raycast(origin, ((customAimTarget == null) ? target.position : customAimTarget.position) - origin, out raycastHit, dist, this.sightMask))
			{
				if (this.seeBackface)
				{
					Physics.queriesHitBackfaces = false;
				}
				return false;
			}
			if (this.seeBackface)
			{
				Physics.queriesHitBackfaces = false;
			}
			if (raycastHit.collider.transform == target)
			{
				this.lastSeenPosition = raycastHit.point;
				this.memoryTime = this.memoryLength;
				return true;
			}
			return false;
		}

		// Token: 0x06000BD6 RID: 3030 RVA: 0x0004AFD6 File Offset: 0x000491D6
		public virtual bool CanSeeTarget(Transform target)
		{
			return this.CanSeeTarget(this.eyeObject.position, target, this.sightDistance, null);
		}

		// Token: 0x06000BD7 RID: 3031 RVA: 0x0004AFF1 File Offset: 0x000491F1
		public Vector3 GetLastSeenPosition()
		{
			return this.lastSeenPosition;
		}

		// Token: 0x04000CD0 RID: 3280
		public float sightDistance = 10f;

		// Token: 0x04000CD1 RID: 3281
		public Transform eyeObject;

		// Token: 0x04000CD2 RID: 3282
		public bool seeBackface;

		// Token: 0x04000CD3 RID: 3283
		public bool useMemory;

		// Token: 0x04000CD4 RID: 3284
		public float memoryLength = 10f;

		// Token: 0x04000CD5 RID: 3285
		private float memoryTime;

		// Token: 0x04000CD6 RID: 3286
		public LayerMask sightMask;

		// Token: 0x04000CD7 RID: 3287
		private Vector3 lastSeenPosition = Vector3.zero;
	}
}
