﻿using System;
using Drawing;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000170 RID: 368
public class UT_GooTrigger : MonoBehaviourGizmos
{
	// Token: 0x06000A52 RID: 2642 RVA: 0x000443CF File Offset: 0x000425CF
	private void Update()
	{
		if (this.checkEveryFrame)
		{
			this.Check();
		}
	}

	// Token: 0x06000A53 RID: 2643 RVA: 0x000443E0 File Offset: 0x000425E0
	public void Check()
	{
		if (!this.active)
		{
			return;
		}
		if (this.runOnce && this.triggered)
		{
			return;
		}
		bool flag = false;
		if (DEN_DeathFloor.instance != null)
		{
			UT_GooTrigger.Trigger trigger = this.trigger;
			if (trigger != UT_GooTrigger.Trigger.above)
			{
				if (trigger == UT_GooTrigger.Trigger.below)
				{
					flag = DEN_DeathFloor.instance.transform.position.y < base.transform.position.y;
				}
			}
			else
			{
				flag = DEN_DeathFloor.instance.transform.position.y > base.transform.position.y;
			}
		}
		else if (!this.checkEveryFrame)
		{
			flag = true;
		}
		if (flag)
		{
			if (!this.triggered || this.triggerEveryCheck)
			{
				this.triggered = true;
				this.outputEvent.Invoke();
				return;
			}
		}
		else
		{
			if (this.triggered)
			{
				this.triggered = false;
			}
			if (!this.checkEveryFrame)
			{
				this.failEvent.Invoke();
			}
		}
	}

	// Token: 0x06000A54 RID: 2644 RVA: 0x000444D4 File Offset: 0x000426D4
	public void SetActive(bool b)
	{
		this.active = b;
	}

	// Token: 0x06000A55 RID: 2645 RVA: 0x000444E0 File Offset: 0x000426E0
	public override void DrawGizmos()
	{
		Draw.Label2D(base.transform.position + Vector3.up * 0.1f, string.Concat(new string[]
		{
			base.name,
			": ",
			this.active.ToString(),
			"\nTYPE: ",
			this.trigger.ToString(),
			" : ",
			(!this.checkEveryFrame) ? "ON CALL" : "EVERY FRAME"
		}), Color.red);
	}

	// Token: 0x04000B53 RID: 2899
	public bool active = true;

	// Token: 0x04000B54 RID: 2900
	public UT_GooTrigger.Trigger trigger;

	// Token: 0x04000B55 RID: 2901
	public UnityEvent outputEvent;

	// Token: 0x04000B56 RID: 2902
	public UnityEvent failEvent;

	// Token: 0x04000B57 RID: 2903
	public bool runOnce = true;

	// Token: 0x04000B58 RID: 2904
	public bool checkEveryFrame = true;

	// Token: 0x04000B59 RID: 2905
	public bool triggerEveryCheck;

	// Token: 0x04000B5A RID: 2906
	private bool triggered;

	// Token: 0x020002DA RID: 730
	public enum Trigger
	{
		// Token: 0x04001240 RID: 4672
		above,
		// Token: 0x04001241 RID: 4673
		below
	}
}
