﻿using System;
using UnityEngine;

// Token: 0x020000DD RID: 221
public class UT_ScreenShake : MonoBehaviour
{
	// Token: 0x060006E7 RID: 1767 RVA: 0x00036409 File Offset: 0x00034609
	public void ShakeScreen()
	{
		if (!this.globalShake)
		{
			CL_CameraControl.CalculateShake(base.transform.position, this.intensity, this.radius);
			return;
		}
		CL_CameraControl.Shake(this.intensity);
	}

	// Token: 0x060006E8 RID: 1768 RVA: 0x00036440 File Offset: 0x00034640
	public void ShakeAmount(float f)
	{
		CL_CameraControl.Shake(f);
	}

	// Token: 0x060006E9 RID: 1769 RVA: 0x0003644D File Offset: 0x0003464D
	private void FixedUpdate()
	{
		if (this.constantShake)
		{
			CL_CameraControl.CalculateShake(base.transform.position, this.intensity * Time.fixedDeltaTime, this.radius);
		}
	}

	// Token: 0x04000869 RID: 2153
	public bool globalShake;

	// Token: 0x0400086A RID: 2154
	public float radius = 10f;

	// Token: 0x0400086B RID: 2155
	public float intensity = 0.2f;

	// Token: 0x0400086C RID: 2156
	public bool constantShake;
}
