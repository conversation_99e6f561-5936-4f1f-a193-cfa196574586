﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x0200006F RID: 111
public class Event_HerDomain : MonoBehaviour
{
	// Token: 0x060003EC RID: 1004 RVA: 0x00023DE3 File Offset: 0x00021FE3
	private void Start()
	{
		this.timer = this.timerLength;
	}

	// Token: 0x060003ED RID: 1005 RVA: 0x00023DF4 File Offset: 0x00021FF4
	private void Update()
	{
		if (this.timerIsRunning)
		{
			this.timer -= Time.deltaTime;
			TimeSpan timeSpan = TimeSpan.FromSeconds((double)this.timer);
			this.timerText.text = timeSpan.ToString("mm\\:ss");
		}
	}

	// Token: 0x060003EE RID: 1006 RVA: 0x00023E3F File Offset: 0x0002203F
	public void RunTimer()
	{
		this.timerIsRunning = true;
	}

	// Token: 0x0400054C RID: 1356
	public float timerLength = 1800f;

	// Token: 0x0400054D RID: 1357
	public TMP_Text timerText;

	// Token: 0x0400054E RID: 1358
	private float timer;

	// Token: 0x0400054F RID: 1359
	private bool timerIsRunning;

	// Token: 0x04000550 RID: 1360
	private float eventTimer;
}
