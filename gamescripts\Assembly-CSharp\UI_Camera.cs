﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;

// Token: 0x02000104 RID: 260
public class UI_Camera : MonoBeh<PERSON>our, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler
{
	// Token: 0x060007F2 RID: 2034 RVA: 0x0003AF8C File Offset: 0x0003918C
	private void Start()
	{
		this.osMan = GameObject.Find("OSManager").GetComponent<OS_Manager>();
	}

	// Token: 0x060007F3 RID: 2035 RVA: 0x0003AFA3 File Offset: 0x000391A3
	private void Update()
	{
		this.MouseOverGame();
	}

	// Token: 0x060007F4 RID: 2036 RVA: 0x0003AFAC File Offset: 0x000391AC
	public bool MouseOverGame()
	{
		return UI_Camera.mouseOver;
	}

	// Token: 0x060007F5 RID: 2037 RVA: 0x0003AFB3 File Offset: 0x000391B3
	public void OnPointerEnter(PointerEventData eventData)
	{
		this.osMan.SetCursorImage(OS_Manager.cursorType.crosshairs);
		UI_Camera.mouseOver = true;
	}

	// Token: 0x060007F6 RID: 2038 RVA: 0x0003AFC7 File Offset: 0x000391C7
	public void OnPointerExit(PointerEventData eventData)
	{
		this.osMan.SetCursorImage(OS_Manager.cursorType.pointer);
		UI_Camera.mouseOver = false;
	}

	// Token: 0x04000969 RID: 2409
	public Camera cam;

	// Token: 0x0400096A RID: 2410
	public Vector3 mousePos;

	// Token: 0x0400096B RID: 2411
	private OS_Manager osMan;

	// Token: 0x0400096C RID: 2412
	public static bool mouseOver;
}
