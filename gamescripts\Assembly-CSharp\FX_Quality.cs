﻿using System;
using UnityEngine;

// Token: 0x0200007D RID: 125
public class FX_Quality : MonoBehaviour
{
	// Token: 0x06000428 RID: 1064 RVA: 0x00025C0E File Offset: 0x00023E0E
	private void Start()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.QualityCheck));
		this.rand = Random.value;
		this.QualityCheck();
	}

	// Token: 0x06000429 RID: 1065 RVA: 0x00025C41 File Offset: 0x00023E41
	private void OnDestroy()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Remove(SettingsManager.SettingsRefreshUpdate, new Action(this.QualityCheck));
	}

	// Token: 0x0600042A RID: 1066 RVA: 0x00025C64 File Offset: 0x00023E64
	public void QualityCheck()
	{
		if (SettingsManager.settings.effectQuality >= 2)
		{
			if (this.hideOnLowQuality)
			{
				base.gameObject.SetActive(true);
				return;
			}
		}
		else if (SettingsManager.settings.effectQuality == 1)
		{
			if (this.hideOnLowQuality)
			{
				base.gameObject.SetActive(this.quality < SettingsManager.settings.effectQuality);
				return;
			}
		}
		else if (SettingsManager.settings.effectQuality == 0 && this.hideOnLowQuality)
		{
			base.gameObject.SetActive(false);
		}
	}

	// Token: 0x04000578 RID: 1400
	public bool hideOnLowQuality = true;

	// Token: 0x04000579 RID: 1401
	public int quality;

	// Token: 0x0400057A RID: 1402
	private float rand;
}
