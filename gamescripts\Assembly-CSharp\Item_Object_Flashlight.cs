﻿using System;
using System.Collections.Generic;
using System.Globalization;
using UnityEngine;

// Token: 0x0200009E RID: 158
public class Item_Object_Flashlight : Item_Object
{
	// Token: 0x06000529 RID: 1321 RVA: 0x0002B6EF File Offset: 0x000298EF
	private void Start()
	{
		this.maxCharge = 30f;
		float.TryParse(this.itemData.GetFirstDataStringByType("maxcharge", false), NumberStyles.Float, CultureInfo.InvariantCulture, out this.maxCharge);
	}

	// Token: 0x0600052A RID: 1322 RVA: 0x0002B724 File Offset: 0x00029924
	public override void Execute(Inventory inv)
	{
		base.Execute(inv);
		if (this.maxCharge == 0f)
		{
			float.TryParse(this.itemData.GetFirstDataStringByType("maxcharge", false), NumberStyles.Float, CultureInfo.InvariantCulture, out this.maxCharge);
		}
		float num = 1f;
		if (this.itemData.InInventory())
		{
			if (this.itemData.GetHandItem() != null)
			{
				bool activeSelf = this.lamp.gameObject.activeSelf;
				if (this.flashlightHand == null)
				{
					this.flashlightHand = (HandItem_Flashlight)this.itemData.GetHandItem();
				}
				else
				{
					this.lamp.transform.parent = null;
					this.lamp.transform.position = this.flashlightHand.lightPosition.position;
					this.lamp.transform.rotation = Quaternion.Lerp(this.lamp.transform.rotation, this.flashlightHand.lightPosition.rotation, Time.deltaTime * 25f);
					this.lamp.MoveToFrontOfQueue();
				}
			}
			else
			{
				if (!this.lamp.gameObject.activeSelf)
				{
					this.lamp.gameObject.SetActive(true);
				}
				this.lamp.transform.parent = null;
				this.lamp.transform.position = Camera.main.transform.position;
				this.lamp.transform.rotation = Quaternion.Lerp(this.lamp.transform.rotation, Camera.main.transform.rotation, Time.deltaTime * 8f);
				num = 0.96f;
			}
		}
		else
		{
			if (!this.lamp.gameObject.activeSelf)
			{
				this.lamp.gameObject.SetActive(true);
			}
			this.lamp.transform.parent = base.transform.GetChild(0);
			this.lamp.transform.localPosition = Vector3.zero;
			this.lamp.transform.localRotation = Quaternion.identity;
		}
		float.TryParse(this.itemData.GetFirstDataStringByType("charge", false), NumberStyles.Float, CultureInfo.InvariantCulture, out this.charge);
		if (this.charge > 0f)
		{
			this.charge -= Time.deltaTime;
		}
		if (this.itemData.data == null || this.itemData.data.Count == 0)
		{
			this.itemData.data = new List<string>(1);
		}
		this.itemData.data[0] = "charge:" + this.charge.ToString(CultureInfo.InvariantCulture);
		if (this.maxCharge <= 0f)
		{
			this.lamp.animAmplitude = 0f;
			this.lamp.animSpeed = 0f;
			return;
		}
		this.lamp.intensity = this.flashlightBrightnessCurve.Evaluate(this.charge / this.maxCharge) * 1.22f * num;
		if (this.charge < this.maxCharge / 2f)
		{
			this.lamp.animAmplitude = Mathf.Lerp(2f, 0f, this.charge / (this.maxCharge / 2f));
			this.lamp.animSpeed = Mathf.Lerp(5f, 1f, this.charge / (this.maxCharge / 2f));
			return;
		}
		this.lamp.animAmplitude = 0f;
		this.lamp.animSpeed = 0f;
	}

	// Token: 0x0600052B RID: 1323 RVA: 0x0002BAE2 File Offset: 0x00029CE2
	private void OnDestroy()
	{
		if (this.lamp != null)
		{
			Object.Destroy(this.lamp.gameObject);
		}
	}

	// Token: 0x040006AD RID: 1709
	public CL_Lamp lamp;

	// Token: 0x040006AE RID: 1710
	private float charge;

	// Token: 0x040006AF RID: 1711
	private float maxCharge;

	// Token: 0x040006B0 RID: 1712
	private HandItem_Flashlight flashlightHand;

	// Token: 0x040006B1 RID: 1713
	public AnimationCurve flashlightBrightnessCurve;
}
