﻿using System;
using System.Collections.Generic;
using DarkMachine.UI;
using UnityEngine;

// Token: 0x02000011 RID: 17
public class App_TrainingSectorControls : MonoBehaviour
{
	// Token: 0x06000063 RID: 99 RVA: 0x00004DFA File Offset: 0x00002FFA
	private void Start()
	{
		this.floodSpeedSlider.value = GamemodeModule_Training.GetSectorSettings().floodSpeed;
		this.UpdateToggles();
	}

	// Token: 0x06000064 RID: 100 RVA: 0x00004E17 File Offset: 0x00003017
	public void AddToggle(App_TrainingSectorControls_Toggle toggle)
	{
		if (this.toggleList == null)
		{
			this.toggleList = new Dictionary<string, App_TrainingSectorControls_Toggle>();
		}
		this.toggleList.Add(toggle.id, toggle);
	}

	// Token: 0x06000065 RID: 101 RVA: 0x00004E40 File Offset: 0x00003040
	public void UpdateToggles()
	{
		this.UpdateToggleState("modeStandard", !GamemodeModule_Training.GetSectorSettings().race);
		this.UpdateToggleState("modeRace", GamemodeModule_Training.GetSectorSettings().race);
		if (!GamemodeModule_Training.GetSectorSettings().zoneClimbing && !GamemodeModule_Training.GetSectorSettings().zoneItems && !GamemodeModule_Training.GetSectorSettings().zoneMastery)
		{
			GamemodeModule_Training.GetSectorSettings().zoneClimbing = true;
		}
		if (GamemodeModule_Training.GetSectorSettings().race)
		{
			GamemodeModule_Training.GetSectorSettings().hazardBarnacles = false;
			GamemodeModule_Training.GetSectorSettings().hazardFlood = false;
			GamemodeModule_Training.GetSectorSettings().hazardDrones = false;
			GamemodeModule_Training.GetSectorSettings().hazardTurrets = false;
		}
		this.UpdateToggleState("zoneClimbing", GamemodeModule_Training.GetSectorSettings().zoneClimbing);
		this.UpdateToggleState("zoneItems", GamemodeModule_Training.GetSectorSettings().zoneItems);
		this.UpdateToggleState("zoneMastery", GamemodeModule_Training.GetSectorSettings().zoneMastery);
		this.UpdateToggleState("hazardBarnacles", GamemodeModule_Training.GetSectorSettings().hazardBarnacles);
		this.UpdateToggleState("hazardFlood", GamemodeModule_Training.GetSectorSettings().hazardFlood);
		this.UpdateToggleState("hazardDrones", GamemodeModule_Training.GetSectorSettings().hazardDrones);
		this.UpdateToggleState("hazardTurrets", GamemodeModule_Training.GetSectorSettings().hazardTurrets);
		GamemodeModule_Training.GetSectorSettings().floodSpeed = this.floodSpeedSlider.value;
		this.UpdateInteractableState("hazardBarnacles", !GamemodeModule_Training.GetSectorSettings().race);
		this.UpdateInteractableState("hazardFlood", !GamemodeModule_Training.GetSectorSettings().race);
		this.UpdateInteractableState("hazardDrones", !GamemodeModule_Training.GetSectorSettings().race && CL_AchievementManager.GetAchievementValue("ACH_PIPEWORKS"));
		this.UpdateInteractableState("hazardTurrets", !GamemodeModule_Training.GetSectorSettings().race && CL_AchievementManager.GetAchievementValue("ACH_HABITATION"));
		this.floodSpeedSlider.interactable = !GamemodeModule_Training.GetSectorSettings().race && GamemodeModule_Training.GetSectorSettings().hazardFlood;
		GamemodeModule_Training.UpdateSectorFlags();
	}

	// Token: 0x06000066 RID: 102 RVA: 0x0000502C File Offset: 0x0000322C
	private void UpdateToggleState(string id, bool b)
	{
		if (this.toggleList.ContainsKey(id) && this.toggleList[id].GetToggle().isOn != b)
		{
			Debug.Log("Updating Toggle State: " + id + " :: " + b.ToString());
			this.toggleList[id].GetToggle().SetIsOnWithoutNotify(b);
		}
	}

	// Token: 0x06000067 RID: 103 RVA: 0x00005093 File Offset: 0x00003293
	private void UpdateInteractableState(string id, bool b)
	{
		if (this.toggleList.ContainsKey(id) && this.toggleList[id].GetToggle().interactable != b)
		{
			this.toggleList[id].SetInteractable(b);
		}
	}

	// Token: 0x06000068 RID: 104 RVA: 0x000050D0 File Offset: 0x000032D0
	public void ToggleSetting(string id, bool b)
	{
		uint num = <PrivateImplementationDetails>.ComputeStringHash(id);
		if (num <= 1931007779U)
		{
			if (num <= 1537724879U)
			{
				if (num != 1261389028U)
				{
					if (num == 1537724879U)
					{
						if (id == "modeRace")
						{
							GamemodeModule_Training.GetSectorSettings().race = true;
						}
					}
				}
				else if (id == "hazardTurrets")
				{
					GamemodeModule_Training.GetSectorSettings().hazardTurrets = b;
				}
			}
			else if (num != 1820829708U)
			{
				if (num == 1931007779U)
				{
					if (id == "hazardFlood")
					{
						GamemodeModule_Training.GetSectorSettings().hazardFlood = b;
					}
				}
			}
			else if (id == "zoneMastery")
			{
				GamemodeModule_Training.GetSectorSettings().zoneMastery = b;
			}
		}
		else if (num <= 3053506840U)
		{
			if (num != 2797688252U)
			{
				if (num == 3053506840U)
				{
					if (id == "zoneClimbing")
					{
						GamemodeModule_Training.GetSectorSettings().zoneClimbing = b;
					}
				}
			}
			else if (id == "hazardBarnacles")
			{
				GamemodeModule_Training.GetSectorSettings().hazardBarnacles = b;
			}
		}
		else if (num != 3148424451U)
		{
			if (num != 3160943817U)
			{
				if (num == 3289178318U)
				{
					if (id == "hazardDrones")
					{
						GamemodeModule_Training.GetSectorSettings().hazardDrones = b;
					}
				}
			}
			else if (id == "zoneItems")
			{
				GamemodeModule_Training.GetSectorSettings().zoneItems = b;
			}
		}
		else if (id == "modeStandard")
		{
			GamemodeModule_Training.GetSectorSettings().race = false;
		}
		this.UpdateToggles();
	}

	// Token: 0x0400006C RID: 108
	public List<M_Region> sectorRegions;

	// Token: 0x0400006D RID: 109
	public Dictionary<string, App_TrainingSectorControls_Toggle> toggleList;

	// Token: 0x0400006E RID: 110
	public SubmitSlider floodSpeedSlider;
}
