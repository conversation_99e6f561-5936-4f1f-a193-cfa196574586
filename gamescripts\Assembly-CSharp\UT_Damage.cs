﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000164 RID: 356
public class UT_Damage : MonoBehaviour, Damageable
{
	// Token: 0x06000A08 RID: 2568 RVA: 0x000436AC File Offset: 0x000418AC
	public bool Damage(float damage, string type)
	{
		if (this.DamageAction != null)
		{
			this.DamageAction(damage);
		}
		this.DamageEvent.Invoke();
		this.health -= damage;
		if (this.health <= 0f && !this.dead)
		{
			this.dead = true;
			this.Kill();
			return true;
		}
		return false;
	}

	// Token: 0x06000A09 RID: 2569 RVA: 0x0004370C File Offset: 0x0004190C
	public void Kill()
	{
		this.KillEvent.Invoke();
		if (this.KillAction != null)
		{
			this.KillAction();
		}
		UT_Damage.KillType killType = this.killType;
		if (killType == UT_Damage.KillType.hide)
		{
			base.gameObject.SetActive(false);
			return;
		}
		if (killType != UT_Damage.KillType.destroy)
		{
			return;
		}
		Object.Destroy(base.gameObject);
	}

	// Token: 0x06000A0A RID: 2570 RVA: 0x0004375E File Offset: 0x0004195E
	public void ResetHealth()
	{
		this.health = this.maxHealth;
		this.dead = false;
	}

	// Token: 0x06000A0B RID: 2571 RVA: 0x00043773 File Offset: 0x00041973
	public GameObject GetGameObject()
	{
		return base.gameObject;
	}

	// Token: 0x04000B21 RID: 2849
	public Action<float> DamageAction;

	// Token: 0x04000B22 RID: 2850
	public Action KillAction;

	// Token: 0x04000B23 RID: 2851
	public UnityEvent DamageEvent;

	// Token: 0x04000B24 RID: 2852
	public UnityEvent KillEvent;

	// Token: 0x04000B25 RID: 2853
	public float health = 1f;

	// Token: 0x04000B26 RID: 2854
	public float maxHealth = 1f;

	// Token: 0x04000B27 RID: 2855
	private bool dead;

	// Token: 0x04000B28 RID: 2856
	public UT_Damage.KillType killType;

	// Token: 0x020002D8 RID: 728
	public enum KillType
	{
		// Token: 0x04001237 RID: 4663
		hide,
		// Token: 0x04001238 RID: 4664
		destroy,
		// Token: 0x04001239 RID: 4665
		nothing
	}
}
