﻿using System;
using UnityEngine;

// Token: 0x0200002D RID: 45
public class Denizen : AIGameEntity
{
	// Token: 0x0600019D RID: 413 RVA: 0x0000C234 File Offset: 0x0000A434
	public override void Start()
	{
		this.tickCounter = Random.Range(-10, 0);
		base.Start();
	}

	// Token: 0x0600019E RID: 414 RVA: 0x0000C24A File Offset: 0x0000A44A
	public virtual void FixedUpdate()
	{
		this.tickCounter++;
		if (this.tickCounter == this.tickReset)
		{
			this.tickFrame = true;
			this.tickCounter = 0;
			return;
		}
		this.tickFrame = false;
	}

	// Token: 0x0600019F RID: 415 RVA: 0x0000C27E File Offset: 0x0000A47E
	public bool CanSeeTarget(Transform t, float dist = 10f)
	{
		return this.sight.CanSeeTarget(base.transform.position, t, dist, null);
	}

	// Token: 0x060001A0 RID: 416 RVA: 0x0000C29C File Offset: 0x0000A49C
	public override void Kill(string type = "")
	{
		base.Kill("");
		CL_GameTracker.AddEventToSession(CL_GameTracker.CreateEvent(base.name, base.gameObject.GetInstanceID().ToString(), this.objectType, CL_GameTracker.Session.Tick.Event.EventType.kill, base.transform.position));
		base.gameObject.SetActive(false);
	}

	// Token: 0x060001A1 RID: 417 RVA: 0x0000C2F5 File Offset: 0x0000A4F5
	public bool IsTickFrame()
	{
		return this.tickFrame;
	}

	// Token: 0x060001A2 RID: 418 RVA: 0x0000C2FD File Offset: 0x0000A4FD
	public override void SessionUpdate()
	{
		base.SessionUpdate();
	}

	// Token: 0x04000183 RID: 387
	[Header("Tick Rate")]
	public int tickReset = 5;

	// Token: 0x04000184 RID: 388
	private int tickCounter;

	// Token: 0x04000185 RID: 389
	private bool tickFrame;
}
