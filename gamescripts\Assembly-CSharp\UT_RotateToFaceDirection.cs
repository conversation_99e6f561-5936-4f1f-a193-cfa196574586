﻿using System;
using UnityEngine;

// Token: 0x02000189 RID: 393
public class UT_RotateToFaceDirection : MonoBehaviour
{
	// Token: 0x06000ABE RID: 2750 RVA: 0x00046899 File Offset: 0x00044A99
	private void Start()
	{
		if (this.faceAtStart)
		{
			this.facing = true;
		}
	}

	// Token: 0x06000ABF RID: 2751 RVA: 0x000468AC File Offset: 0x00044AAC
	private void Update()
	{
		if (this.facing)
		{
			Vector3 vector = base.transform.TransformDirection(this.upDirection);
			base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(this.direction, vector), Time.deltaTime * 5f);
		}
	}

	// Token: 0x06000AC0 RID: 2752 RVA: 0x00046905 File Offset: 0x00044B05
	public void FaceDirection()
	{
		this.facing = true;
	}

	// Token: 0x04000BBF RID: 3007
	public bool faceAtStart;

	// Token: 0x04000BC0 RID: 3008
	private bool facing;

	// Token: 0x04000BC1 RID: 3009
	public Vector3 direction = Vector3.forward;

	// Token: 0x04000BC2 RID: 3010
	public Vector3 upDirection = Vector3.forward;
}
