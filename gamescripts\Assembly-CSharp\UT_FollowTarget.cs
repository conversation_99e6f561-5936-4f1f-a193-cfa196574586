﻿using System;
using UnityEngine;

// Token: 0x0200016A RID: 362
[DefaultExecutionOrder(5)]
public class UT_FollowTarget : MonoBehaviour
{
	// Token: 0x06000A19 RID: 2585 RVA: 0x00043A64 File Offset: 0x00041C64
	private void Update()
	{
		if (!this.OnLateUpdate)
		{
			this.follow.MoveToTarget(base.transform, this.target);
		}
	}

	// Token: 0x06000A1A RID: 2586 RVA: 0x00043A85 File Offset: 0x00041C85
	private void LateUpdate()
	{
		if (this.OnLateUpdate)
		{
			this.follow.MoveToTarget(base.transform, this.target);
		}
	}

	// Token: 0x06000A1B RID: 2587 RVA: 0x00043AA6 File Offset: 0x00041CA6
	public void SetTarget(Transform t)
	{
		this.target = t;
	}

	// Token: 0x04000B38 RID: 2872
	public Transform target;

	// Token: 0x04000B39 RID: 2873
	public UT_FollowBase follow;

	// Token: 0x04000B3A RID: 2874
	public bool OnLateUpdate;
}
