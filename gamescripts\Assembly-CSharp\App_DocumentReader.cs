﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000E7 RID: 231
public class App_DocumentReader : MonoBehaviour
{
	// Token: 0x06000725 RID: 1829 RVA: 0x00036F20 File Offset: 0x00035120
	private void Start()
	{
		this.window = base.GetComponent<OS_Window>();
		string data;
		if (this.window.file.fileInfo.textAssetData == null)
		{
			data = this.window.file.fileInfo.data;
		}
		else
		{
			data = this.window.file.fileInfo.textAssetData.text;
		}
		this.pages = new string[] { data };
		Debug.Log(data);
		if (data.Contains("</page>"))
		{
			this.pages = data.Split("</page>", StringSplitOptions.None);
		}
		for (int i = 0; i < this.pages.Length; i++)
		{
			if (this.pages[i].Length > 0)
			{
				this.pages[i] = this.pages[i].TrimStart();
			}
		}
		this.text.text = this.pages[0];
		this.rectTransform = base.GetComponent<RectTransform>();
		this.UpdateButtons();
	}

	// Token: 0x06000726 RID: 1830 RVA: 0x00037024 File Offset: 0x00035224
	public void ChangePage(int i)
	{
		if (this.curPage + i < 0 || this.curPage + i >= this.pages.Length)
		{
			return;
		}
		this.curPage += i;
		this.text.text = this.pages[this.curPage];
		OS_Manager.soundPlayer.PlaySound("os:click");
		this.UpdateButtons();
	}

	// Token: 0x06000727 RID: 1831 RVA: 0x0003708C File Offset: 0x0003528C
	private void UpdateButtons()
	{
		if (this.pages.Length > 1)
		{
			this.pageCounter.text = string.Format("Page {0}/{1}", this.curPage + 1, this.pages.Length);
		}
		else
		{
			this.pageCounter.text = "";
		}
		if (this.curPage + 1 >= this.pages.Length)
		{
			this.rightButton.gameObject.SetActive(false);
		}
		else
		{
			this.rightButton.gameObject.SetActive(true);
		}
		if (this.curPage - 1 < 0)
		{
			this.leftButton.gameObject.SetActive(false);
			return;
		}
		this.leftButton.gameObject.SetActive(true);
	}

	// Token: 0x04000896 RID: 2198
	private OS_Window window;

	// Token: 0x04000897 RID: 2199
	public TMP_Text text;

	// Token: 0x04000898 RID: 2200
	public TMP_Text pageCounter;

	// Token: 0x04000899 RID: 2201
	public Button rightButton;

	// Token: 0x0400089A RID: 2202
	public Button leftButton;

	// Token: 0x0400089B RID: 2203
	private RectTransform rectTransform;

	// Token: 0x0400089C RID: 2204
	private string[] pages;

	// Token: 0x0400089D RID: 2205
	private int curPage;
}
