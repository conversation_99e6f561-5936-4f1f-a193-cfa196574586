﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000088 RID: 136
public class HandItem : MonoBehaviour
{
	// Token: 0x06000496 RID: 1174 RVA: 0x0002861C File Offset: 0x0002681C
	public virtual void Initialize(Item i, ENT_Player.Hand h)
	{
		this.item = i;
		this.used = i.used;
		this.hand = h;
		this.handSprites = base.gameObject.GetComponentsInChildren<Hand_SpriteController>();
	}

	// Token: 0x06000497 RID: 1175 RVA: 0x00028649 File Offset: 0x00026849
	public virtual void Use()
	{
		if (!this.canUseWhileHanging)
		{
			this.hand.GetPlayer().IsHanging();
			return;
		}
	}

	// Token: 0x06000498 RID: 1176 RVA: 0x00028665 File Offset: 0x00026865
	public virtual void StopUse()
	{
		if (!this.canUseWhileHanging)
		{
			this.hand.GetPlayer().IsHanging();
			return;
		}
	}

	// Token: 0x06000499 RID: 1177 RVA: 0x00028684 File Offset: 0x00026884
	internal virtual void UpdateStats()
	{
		this.item.IsUnlocked();
		foreach (string text in this.useStats)
		{
			StatManager.sessionStats.UpdateStatistic(text, 1, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Add, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Add);
		}
		this.item.UpdateProgressionStat(1);
		CL_ProgressionManager.UpdateUnlocks(true);
	}

	// Token: 0x0600049A RID: 1178 RVA: 0x00028704 File Offset: 0x00026904
	public virtual bool ShowAimCircle()
	{
		return this.useAimCircle;
	}

	// Token: 0x0600049B RID: 1179 RVA: 0x0002870C File Offset: 0x0002690C
	public virtual RaycastHit GetAimCircleHit()
	{
		return default(RaycastHit);
	}

	// Token: 0x0600049C RID: 1180 RVA: 0x00028724 File Offset: 0x00026924
	public void SetHandColor(Color c)
	{
		if (this.handSprites.Length == 0)
		{
			return;
		}
		Hand_SpriteController[] array = this.handSprites;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].SetColor(c);
		}
	}

	// Token: 0x0600049D RID: 1181 RVA: 0x00028759 File Offset: 0x00026959
	private void OnEnable()
	{
	}

	// Token: 0x0600049E RID: 1182 RVA: 0x0002875B File Offset: 0x0002695B
	private void Start()
	{
	}

	// Token: 0x0600049F RID: 1183 RVA: 0x0002875D File Offset: 0x0002695D
	private void Update()
	{
	}

	// Token: 0x060004A0 RID: 1184 RVA: 0x0002875F File Offset: 0x0002695F
	public virtual bool CanDrop()
	{
		return true;
	}

	// Token: 0x060004A1 RID: 1185 RVA: 0x00028762 File Offset: 0x00026962
	public virtual void RemoveItem()
	{
		this.item.inventory.DestroyItemInHand(this.hand.id);
	}

	// Token: 0x060004A2 RID: 1186 RVA: 0x0002877F File Offset: 0x0002697F
	public virtual void Activate()
	{
	}

	// Token: 0x060004A3 RID: 1187 RVA: 0x00028781 File Offset: 0x00026981
	internal void Execute(Inventory inventory)
	{
	}

	// Token: 0x0400061A RID: 1562
	[HideInInspector]
	public Item item;

	// Token: 0x0400061B RID: 1563
	[HideInInspector]
	public ENT_Player.Hand hand;

	// Token: 0x0400061C RID: 1564
	public bool active;

	// Token: 0x0400061D RID: 1565
	public bool used;

	// Token: 0x0400061E RID: 1566
	public Animator anim;

	// Token: 0x0400061F RID: 1567
	public bool canUseWhileHanging = true;

	// Token: 0x04000620 RID: 1568
	public bool useAimCircle;

	// Token: 0x04000621 RID: 1569
	public List<string> useStats;

	// Token: 0x04000622 RID: 1570
	private Hand_SpriteController[] handSprites;
}
