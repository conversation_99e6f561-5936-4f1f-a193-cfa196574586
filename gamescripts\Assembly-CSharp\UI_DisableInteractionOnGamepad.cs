﻿using System;
using UnityEngine;

// Token: 0x02000124 RID: 292
public class UI_DisableInteractionOnGamepad : MonoBehaviour
{
	// Token: 0x060008AC RID: 2220 RVA: 0x0003DB6B File Offset: 0x0003BD6B
	private void Start()
	{
		this.group = base.GetComponent<CanvasGroup>();
	}

	// Token: 0x060008AD RID: 2221 RVA: 0x0003DB79 File Offset: 0x0003BD79
	private void Update()
	{
		this.group.interactable = !InputManager.IsGamepad();
	}

	// Token: 0x040009F9 RID: 2553
	private CanvasGroup group;
}
