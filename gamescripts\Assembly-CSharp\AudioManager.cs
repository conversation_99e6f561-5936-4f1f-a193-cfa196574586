﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Audio;

// Token: 0x02000016 RID: 22
public class AudioManager : MonoBehaviour
{
	// Token: 0x0600007F RID: 127 RVA: 0x000057B8 File Offset: 0x000039B8
	private void Start()
	{
		AudioManager.auds = new List<AudioSource>();
		AudioManager.musicLibrary = new Dictionary<string, AudioManager.Soundtrack>();
		for (int i = 0; i < this.audCount; i++)
		{
			AudioManager.auds.Add(base.gameObject.AddComponent<AudioSource>());
			AudioManager.auds[i].loop = true;
			AudioManager.auds[i].bypassReverbZones = true;
			AudioManager.auds[i].outputAudioMixerGroup = this.soundtrackMixer;
		}
		foreach (AudioManager.Soundtrack soundtrack in this.music)
		{
			AudioManager.musicLibrary.Add(soundtrack.name, soundtrack);
		}
		AudioManager.ResetGameAudioVolume();
	}

	// Token: 0x06000080 RID: 128 RVA: 0x0000588C File Offset: 0x00003A8C
	private void Awake()
	{
		if (AudioManager.instance == null)
		{
			AudioManager.instance = this;
			this.InitializePool();
			return;
		}
		Object.Destroy(base.gameObject);
	}

	// Token: 0x06000081 RID: 129 RVA: 0x000058B4 File Offset: 0x00003AB4
	public static void ResetGameAudioVolume()
	{
		AudioManager.instance.gameMixer.audioMixer.SetFloat("MasterVolume", 0f);
		AudioManager.instance.gameMixer.audioMixer.SetFloat("AmbienceVolume", 0f);
		AudioManager.instance.gameMixer.audioMixer.SetFloat("EffectsVolume", 0f);
	}

	// Token: 0x06000082 RID: 130 RVA: 0x00005920 File Offset: 0x00003B20
	private void InitializePool()
	{
		this.audioSourcePool = new Queue<AudioSource>();
		for (int i = 0; i < this.poolSize; i++)
		{
			GameObject gameObject = Object.Instantiate<GameObject>(this.audioSourcePrefab, this.poolParent);
			AudioSource component = gameObject.GetComponent<AudioSource>();
			gameObject.SetActive(false);
			this.audioSourcePool.Enqueue(component);
		}
	}

	// Token: 0x06000083 RID: 131 RVA: 0x00005974 File Offset: 0x00003B74
	public static void PlaySound(AudioClip clip, Vector3 position, float volume = 1f, float pitch = 1f, float spacial = 1f, bool bypass = false, float reverbMix = 1f, AudioMixerGroup mixerGroup = null)
	{
		if (clip == null)
		{
			return;
		}
		if (AudioManager.instance == null)
		{
			Debug.LogWarning("AudioManager instance not found.");
			return;
		}
		AudioSource pooledAudioSource = AudioManager.instance.GetPooledAudioSource();
		pooledAudioSource.bypassListenerEffects = bypass;
		pooledAudioSource.outputAudioMixerGroup = AudioManager.instance.gameMixer;
		pooledAudioSource.transform.position = position;
		pooledAudioSource.volume = volume;
		pooledAudioSource.pitch = pitch;
		pooledAudioSource.clip = clip;
		pooledAudioSource.spatialBlend = spacial;
		pooledAudioSource.reverbZoneMix = reverbMix;
		pooledAudioSource.ignoreListenerPause = false;
		pooledAudioSource.gameObject.SetActive(true);
		if (mixerGroup != null)
		{
			Debug.Log("Playing Sound on special mixer: " + clip.name);
			pooledAudioSource.outputAudioMixerGroup = mixerGroup;
		}
		else
		{
			pooledAudioSource.outputAudioMixerGroup = AudioManager.instance.gameMixer;
		}
		pooledAudioSource.Play();
		AudioManager.instance.StartCoroutine(AudioManager.instance.ReturnToPoolAfterPlaying(pooledAudioSource));
	}

	// Token: 0x06000084 RID: 132 RVA: 0x00005A60 File Offset: 0x00003C60
	public static void PlayUISound(AudioClip clip, float volume = 1f, float pitch = 1f)
	{
		if (AudioManager.instance == null)
		{
			Debug.LogWarning("AudioManager instance not found.");
			return;
		}
		AudioSource pooledAudioSource = AudioManager.instance.GetPooledAudioSource();
		pooledAudioSource.ignoreListenerPause = true;
		pooledAudioSource.outputAudioMixerGroup = AudioManager.instance.UIMixer;
		pooledAudioSource.transform.position = Vector3.zero;
		pooledAudioSource.volume = volume;
		pooledAudioSource.pitch = pitch;
		pooledAudioSource.clip = clip;
		pooledAudioSource.spatialBlend = 0f;
		pooledAudioSource.gameObject.SetActive(true);
		pooledAudioSource.Play();
		AudioManager.instance.StartCoroutine(AudioManager.instance.ReturnToPoolAfterPlaying(pooledAudioSource));
	}

	// Token: 0x06000085 RID: 133 RVA: 0x00005B00 File Offset: 0x00003D00
	public static void PlayUISound(string clipName, float volume = 1f, float pitch = 1f)
	{
		if (AudioManager.instance == null)
		{
			Debug.LogWarning("AudioManager instance not found.");
			return;
		}
		AudioSource pooledAudioSource = AudioManager.instance.GetPooledAudioSource();
		pooledAudioSource.ignoreListenerPause = true;
		pooledAudioSource.outputAudioMixerGroup = AudioManager.instance.UIMixer;
		pooledAudioSource.transform.position = Vector3.zero;
		pooledAudioSource.volume = volume;
		pooledAudioSource.pitch = pitch;
		pooledAudioSource.clip = AudioManager.instance.GetAudioClip(clipName);
		pooledAudioSource.spatialBlend = 0f;
		pooledAudioSource.gameObject.SetActive(true);
		pooledAudioSource.Play();
		AudioManager.instance.StartCoroutine(AudioManager.instance.ReturnToPoolAfterPlaying(pooledAudioSource));
	}

	// Token: 0x06000086 RID: 134 RVA: 0x00005BA9 File Offset: 0x00003DA9
	private AudioSource GetPooledAudioSource()
	{
		if (this.audioSourcePool.Count > 0)
		{
			return this.audioSourcePool.Dequeue();
		}
		return Object.Instantiate<GameObject>(this.audioSourcePrefab, this.poolParent).GetComponent<AudioSource>();
	}

	// Token: 0x06000087 RID: 135 RVA: 0x00005BDB File Offset: 0x00003DDB
	private IEnumerator ReturnToPoolAfterPlaying(AudioSource audioSource)
	{
		yield return new WaitForSeconds(audioSource.clip.length);
		audioSource.Stop();
		audioSource.clip = null;
		audioSource.gameObject.SetActive(false);
		this.audioSourcePool.Enqueue(audioSource);
		yield break;
	}

	// Token: 0x06000088 RID: 136 RVA: 0x00005BF1 File Offset: 0x00003DF1
	public void AdjustVolume(float sVol, float mVol)
	{
		this.mixer.SetFloat("musicVol", mVol);
		this.mixer.SetFloat("soundVol", sVol);
	}

	// Token: 0x06000089 RID: 137 RVA: 0x00005C18 File Offset: 0x00003E18
	public static void PlayTrack(string t)
	{
		AudioManager.instance.StopCoroutine("FadeOut");
		AudioManager.instance.StopAllCoroutines();
		foreach (AudioSource audioSource in AudioManager.auds)
		{
			audioSource.Stop();
		}
		AudioManager.auds[0].clip = AudioManager.musicLibrary[t].clip;
		AudioManager.auds[0].loop = AudioManager.musicLibrary[t].loop;
		AudioManager.auds[0].volume = AudioManager.musicLibrary[t].volume;
		AudioManager.auds[0].Play();
	}

	// Token: 0x0600008A RID: 138 RVA: 0x00005CF0 File Offset: 0x00003EF0
	public static void PlayTrack(string t, int a = 0, bool stopAll = true, float volume = 1f)
	{
		AudioManager.instance.StopCoroutine("FadeOut");
		AudioManager.instance.StopAllCoroutines();
		if (stopAll)
		{
			foreach (AudioSource audioSource in AudioManager.auds)
			{
				audioSource.Stop();
			}
		}
		AudioManager.auds[a].clip = AudioManager.musicLibrary[t].clip;
		AudioManager.auds[a].loop = AudioManager.musicLibrary[t].loop;
		AudioManager.auds[a].volume = AudioManager.musicLibrary[t].volume;
		AudioManager.auds[a].Play();
		CommandConsole.Log("Playing Track: " + t, false);
	}

	// Token: 0x0600008B RID: 139 RVA: 0x00005DDC File Offset: 0x00003FDC
	public static void StopMusic()
	{
		AudioManager.instance.StopCoroutine("FadeOut");
		AudioManager.instance.StopAllCoroutines();
		for (int i = 0; i < AudioManager.auds.Count; i++)
		{
			AudioManager.auds[i].Stop();
		}
	}

	// Token: 0x0600008C RID: 140 RVA: 0x00005E27 File Offset: 0x00004027
	public static void FadeOutMusic()
	{
		AudioManager.instance.StopCoroutine("FadeOut");
		AudioManager.instance.StopAllCoroutines();
		AudioManager.instance.StartCoroutine(AudioManager.instance.FadeOut(AudioManager.auds[0]));
	}

	// Token: 0x0600008D RID: 141 RVA: 0x00005E62 File Offset: 0x00004062
	public IEnumerator FadeOut(AudioSource audiosource)
	{
		while (audiosource.volume > 0f)
		{
			audiosource.volume -= Time.unscaledDeltaTime;
			yield return new WaitForEndOfFrame();
		}
		yield break;
	}

	// Token: 0x0600008E RID: 142 RVA: 0x00005E71 File Offset: 0x00004071
	public static void SetMusicVolumeSetting(float f)
	{
		AudioManager.targetMusicVolume = f;
		AudioManager.instance.soundtrackMixer.audioMixer.SetFloat("MasterVolume", Mathf.Lerp(-30f, 0f, f));
	}

	// Token: 0x0600008F RID: 143 RVA: 0x00005EA4 File Offset: 0x000040A4
	public AudioClip GetAudioClip(string a)
	{
		foreach (AudioClip audioClip in this.clips)
		{
			if (audioClip.name == a)
			{
				return audioClip;
			}
		}
		return this.clips[0];
	}

	// Token: 0x04000082 RID: 130
	private static List<AudioSource> auds = new List<AudioSource>();

	// Token: 0x04000083 RID: 131
	public AudioMixer mixer;

	// Token: 0x04000084 RID: 132
	public AudioMixerGroup soundtrackMixer;

	// Token: 0x04000085 RID: 133
	public AudioMixerGroup gameMixer;

	// Token: 0x04000086 RID: 134
	public AudioMixerGroup UIMixer;

	// Token: 0x04000087 RID: 135
	[SerializeField]
	private int audCount = 5;

	// Token: 0x04000088 RID: 136
	public List<AudioManager.Soundtrack> music;

	// Token: 0x04000089 RID: 137
	private static Dictionary<string, AudioManager.Soundtrack> musicLibrary = new Dictionary<string, AudioManager.Soundtrack>();

	// Token: 0x0400008A RID: 138
	public static AudioManager instance;

	// Token: 0x0400008B RID: 139
	[SerializeField]
	private int poolSize = 10;

	// Token: 0x0400008C RID: 140
	[SerializeField]
	private GameObject audioSourcePrefab;

	// Token: 0x0400008D RID: 141
	[SerializeField]
	private Transform poolParent;

	// Token: 0x0400008E RID: 142
	private Queue<AudioSource> audioSourcePool;

	// Token: 0x0400008F RID: 143
	public List<AudioClip> clips;

	// Token: 0x04000090 RID: 144
	private static float targetMusicVolume = 0f;

	// Token: 0x020001F7 RID: 503
	[Serializable]
	public class Soundtrack
	{
		// Token: 0x04000D95 RID: 3477
		public string name;

		// Token: 0x04000D96 RID: 3478
		public AudioClip clip;

		// Token: 0x04000D97 RID: 3479
		public bool loop;

		// Token: 0x04000D98 RID: 3480
		public float volume = 1f;
	}
}
