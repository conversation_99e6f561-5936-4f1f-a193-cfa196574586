﻿using System;
using UnityEngine;

// Token: 0x02000075 RID: 117
public static class RandomMeshPointSampler
{
	// Token: 0x06000402 RID: 1026 RVA: 0x0002516C File Offset: 0x0002336C
	public static Vector3 SamplePointLocal(Mesh mesh)
	{
		if (mesh == null)
		{
			Debug.LogError("Mesh is null");
			return Vector3.zero;
		}
		int[] triangles = mesh.triangles;
		Vector3[] vertices = mesh.vertices;
		float[] array = new float[triangles.Length / 3];
		float num = 0f;
		int i = 0;
		int num2 = 0;
		while (i < triangles.Length)
		{
			Vector3 vector = vertices[triangles[i]];
			Vector3 vector2 = vertices[triangles[i + 1]];
			Vector3 vector3 = vertices[triangles[i + 2]];
			float num3 = Vector3.Cross(vector2 - vector, vector3 - vector).magnitude * 0.5f;
			num += num3;
			array[num2] = num;
			i += 3;
			num2++;
		}
		float num4 = Random.value * num;
		int num5 = Array.BinarySearch<float>(array, num4);
		if (num5 < 0)
		{
			num5 = ~num5;
		}
		int num6 = triangles[num5 * 3];
		int num7 = triangles[num5 * 3 + 1];
		int num8 = triangles[num5 * 3 + 2];
		Vector3 vector4 = vertices[num6];
		Vector3 vector5 = vertices[num7];
		Vector3 vector6 = vertices[num8];
		float num9 = Random.value;
		float num10 = Random.value;
		if (num9 + num10 > 1f)
		{
			num9 = 1f - num9;
			num10 = 1f - num10;
		}
		return vector4 + num9 * (vector5 - vector4) + num10 * (vector6 - vector4);
	}

	// Token: 0x06000403 RID: 1027 RVA: 0x000252D7 File Offset: 0x000234D7
	public static Vector3 SamplePointWorld(Mesh mesh, Transform meshTransform)
	{
		return meshTransform.TransformPoint(RandomMeshPointSampler.SamplePointLocal(mesh));
	}
}
