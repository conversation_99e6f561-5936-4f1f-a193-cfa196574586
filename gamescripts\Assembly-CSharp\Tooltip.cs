﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.EventSystems;

// Token: 0x02000102 RID: 258
public class Tooltip : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler
{
	// Token: 0x060007E6 RID: 2022 RVA: 0x0003AC5F File Offset: 0x00038E5F
	public void OnPointerEnter(PointerEventData eventData)
	{
		base.StopAllCoroutines();
		base.StartCoroutine(this.StartTimer());
		this.isShowing = true;
	}

	// Token: 0x060007E7 RID: 2023 RVA: 0x0003AC7B File Offset: 0x00038E7B
	public void OnPointerExit(PointerEventData eventData)
	{
		base.StopAllCoroutines();
		Tooltip_Manager.OnMouseLoseFocus();
		this.isShowing = false;
	}

	// Token: 0x060007E8 RID: 2024 RVA: 0x0003AC94 File Offset: 0x00038E94
	private void OnDisable()
	{
		if (this.isShowing)
		{
			base.StopAllCoroutines();
			Tooltip_Manager.OnMouseLoseFocus();
		}
	}

	// Token: 0x060007E9 RID: 2025 RVA: 0x0003ACAE File Offset: 0x00038EAE
	private void ShowMessage()
	{
		Tooltip_Manager.OnMouseHover(this.tip, OS_Manager.mouseRealPosition + this.offset);
	}

	// Token: 0x060007EA RID: 2026 RVA: 0x0003ACD0 File Offset: 0x00038ED0
	private IEnumerator StartTimer()
	{
		yield return new WaitForSeconds(this.timeToWait);
		this.ShowMessage();
		yield break;
	}

	// Token: 0x04000961 RID: 2401
	[TextArea]
	public string tip;

	// Token: 0x04000962 RID: 2402
	private float timeToWait = 0.5f;

	// Token: 0x04000963 RID: 2403
	private bool isShowing;

	// Token: 0x04000964 RID: 2404
	public Vector2 offset;
}
