﻿using System;

// Token: 0x0200006A RID: 106
[Serializable]
public class SessionEventModule_StartEvent : SessionEventModule
{
	// Token: 0x060003DA RID: 986 RVA: 0x00023839 File Offset: 0x00021A39
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		this.Activate();
	}

	// Token: 0x060003DB RID: 987 RVA: 0x00023848 File Offset: 0x00021A48
	public override void Activate()
	{
		base.Activate();
		CL_EventManager.StartEvent(this.newEvent);
	}

	// Token: 0x04000537 RID: 1335
	public SessionEvent newEvent;
}
