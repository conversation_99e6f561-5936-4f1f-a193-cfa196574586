﻿using System;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200011E RID: 286
[RequireComponent(typeof(CanvasScaler))]
public class UI_CanvasScaler : MonoBehaviour
{
	// Token: 0x06000885 RID: 2181 RVA: 0x0003D3DB File Offset: 0x0003B5DB
	private void Start()
	{
		this.canvasScaler = base.GetComponent<CanvasScaler>();
		this.canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ConstantPixelSize;
		this.Refresh();
		this.UpdateScale();
	}

	// Token: 0x06000886 RID: 2182 RVA: 0x0003D401 File Offset: 0x0003B601
	private void Update()
	{
		this.UpdateScale();
	}

	// Token: 0x06000887 RID: 2183 RVA: 0x0003D40C File Offset: 0x0003B60C
	private void UpdateScale()
	{
		float num = (Mathf.Clamp((float)Screen.width, this.minResolution, this.maxResolution) - this.minResolution) / (this.maxResolution - this.minResolution);
		num = this.scaleCurve.Evaluate(num);
		float num2 = Mathf.Lerp(this.minScale, this.maxScale, num) * this.currentScale;
		if ((float)(Screen.width / Screen.height) > 2.5f)
		{
			num2 *= 0.6f;
		}
		this.canvasScaler.scaleFactor = num2;
	}

	// Token: 0x06000888 RID: 2184 RVA: 0x0003D494 File Offset: 0x0003B694
	private void OnEnable()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.Refresh));
	}

	// Token: 0x06000889 RID: 2185 RVA: 0x0003D4B6 File Offset: 0x0003B6B6
	private void OnDisable()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Remove(SettingsManager.SettingsRefreshUpdate, new Action(this.Refresh));
	}

	// Token: 0x0600088A RID: 2186 RVA: 0x0003D4D8 File Offset: 0x0003B6D8
	private void Refresh()
	{
		this.currentScale = SettingsManager.settings.UIScale;
	}

	// Token: 0x040009DB RID: 2523
	[Header("Scale Settings")]
	[Tooltip("Minimum scale factor for UI elements.")]
	public float minScale = 0.8f;

	// Token: 0x040009DC RID: 2524
	[Tooltip("Maximum scale factor for UI elements.")]
	public float maxScale = 1.2f;

	// Token: 0x040009DD RID: 2525
	[Header("Resolution Settings")]
	[Tooltip("Minimum screen width resolution.")]
	public float minResolution = 800f;

	// Token: 0x040009DE RID: 2526
	[Tooltip("Maximum screen width resolution.")]
	public float maxResolution = 1920f;

	// Token: 0x040009DF RID: 2527
	private CanvasScaler canvasScaler;

	// Token: 0x040009E0 RID: 2528
	public AnimationCurve scaleCurve;

	// Token: 0x040009E1 RID: 2529
	private float currentScale = 1f;
}
