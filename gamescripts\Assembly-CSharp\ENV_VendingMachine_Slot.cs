﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000053 RID: 83
public class ENV_VendingMachine_Slot : MonoBehaviour, Clickable
{
	// Token: 0x0600036A RID: 874 RVA: 0x00021CCE File Offset: 0x0001FECE
	public void Interact()
	{
		this.activeEvent.Invoke();
		this.hasBeenPressed = true;
	}

	// Token: 0x0600036B RID: 875 RVA: 0x00021CE4 File Offset: 0x0001FEE4
	public void Interact(ENT_Player p, string s = "")
	{
		int num = int.Parse(s);
		this.activeEvent.Invoke();
		this.activeEventWithAmount.Invoke((float)num);
		if (this.vendingMachine != null)
		{
			this.vendingMachine.AddRoaches(num);
		}
		else
		{
			CL_GameManager.AddRoaches(num);
		}
		this.hasBeenPressed = true;
	}

	// Token: 0x0600036C RID: 876 RVA: 0x00021D39 File Offset: 0x0001FF39
	public void Reset()
	{
		this.hasBeenPressed = false;
	}

	// Token: 0x0600036D RID: 877 RVA: 0x00021D42 File Offset: 0x0001FF42
	public bool CanInteract(ENT_Player p, ENT_Player.Hand hand)
	{
		return this.active && (hand.interactState == ENT_Player.InteractType.item && hand.inventoryHand.currentItem.interactType == Item.InteractType.roach);
	}

	// Token: 0x0600036E RID: 878 RVA: 0x00021D6D File Offset: 0x0001FF6D
	public GameObject GetGameObject()
	{
		return base.gameObject;
	}

	// Token: 0x0600036F RID: 879 RVA: 0x00021D75 File Offset: 0x0001FF75
	public ObjectTagger GetTagger()
	{
		return base.gameObject.GetComponent<ObjectTagger>();
	}

	// Token: 0x06000370 RID: 880 RVA: 0x00021D82 File Offset: 0x0001FF82
	public void SetInteractable(bool b)
	{
		this.active = b;
	}

	// Token: 0x040004AF RID: 1199
	public bool active = true;

	// Token: 0x040004B0 RID: 1200
	public UnityEvent activeEvent;

	// Token: 0x040004B1 RID: 1201
	public UnityEvent<float> activeEventWithAmount;

	// Token: 0x040004B2 RID: 1202
	public ENV_VendingMachine vendingMachine;

	// Token: 0x040004B3 RID: 1203
	[HideInInspector]
	public bool hasBeenPressed;
}
