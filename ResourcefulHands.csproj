﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netstandard2.1</TargetFramework>
        <LangVersion>latest</LangVersion>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <Compile Remove="Libs\**" />
      <Compile Remove="gamescripts\**" />
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Remove="Libs\**" />
      <EmbeddedResource Include="rh_assets.bundle" />
      <None Remove="rh_assets.bundle" />
      <EmbeddedResource Remove="gamescripts\**" />
      <None Remove="gamescripts\**" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="Libs\**" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="0Harmony">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\BepInEx\core\0Harmony.dll</HintPath>
      </Reference>
      <Reference Include="ALINE">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\ALINE.dll</HintPath>
      </Reference>
      <Reference Include="Assembly-CSharp">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Assembly-CSharp.dll</HintPath>
      </Reference>
      <Reference Include="BepInEx">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\BepInEx\core\BepInEx.dll</HintPath>
      </Reference>
      <Reference Include="BepInEx.Harmony">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\BepInEx\core\BepInEx.Harmony.dll</HintPath>
      </Reference>
      <Reference Include="DarkMachineUI">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\DarkMachineUI.dll</HintPath>
      </Reference>
      <Reference Include="Facepunch.Steamworks.Win64">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Facepunch.Steamworks.Win64.dll</HintPath>
      </Reference>
      <Reference Include="NAudio-Unity">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\NAudio-Unity.dll</HintPath>
      </Reference>
      <Reference Include="Newtonsoft.Json">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Newtonsoft.Json.dll</HintPath>
      </Reference>
      <Reference Include="Sirenix.Utilities">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Sirenix.Utilities.dll</HintPath>
      </Reference>
      <Reference Include="Unity.TextMeshPro">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\Unity.TextMeshPro.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.AssetBundleModule">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.AssetBundleModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.AudioModule">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.AudioModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.CoreModule">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.CoreModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.ImageConversionModule">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.ImageConversionModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.IMGUIModule">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.IMGUIModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.TextRenderingModule">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.TextRenderingModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UI">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.UI.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UIElementsModule">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.UIElementsModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UIModule">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.UIModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityWebRequestAudioModule">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityWebRequestModule">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.UnityWebRequestModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityWebRequestTextureModule">
        <HintPath>..\..\..\..\Program Files (x86)\Steam\steamapps\common\White Knuckle\White Knuckle_Data\Managed\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      </Reference>
    </ItemGroup>

</Project>
