﻿using System;
using Unity.VisualScripting;
using UnityEngine;

// Token: 0x02000193 RID: 403
public class UT_SoftParent : MonoBehaviour
{
	// Token: 0x06000ADB RID: 2779 RVA: 0x00046CF8 File Offset: 0x00044EF8
	public void Initialize(Transform p)
	{
		this.parent = p;
		this.parentPosition = this.parent.InverseTransformPoint(base.transform.position);
		this.rotationOffset = (Quaternion.Inverse(this.parent.rotation) * base.transform.rotation).eulerAngles;
		if (!ObjectTagger.TagCheck(this.parent.gameObject, "ignoresoftparent"))
		{
			this.target = this.parent.AddComponent<UT_SoftParentTarget>();
			this.target.AddChild(this);
		}
	}

	// Token: 0x06000ADC RID: 2780 RVA: 0x00046D8A File Offset: 0x00044F8A
	public void SoftMove(Vector3 moveVec)
	{
		this.parentPosition = this.parent.InverseTransformPoint(base.transform.position + moveVec);
	}

	// Token: 0x06000ADD RID: 2781 RVA: 0x00046DAE File Offset: 0x00044FAE
	private void Update()
	{
		this.CheckEnable();
	}

	// Token: 0x06000ADE RID: 2782 RVA: 0x00046DB8 File Offset: 0x00044FB8
	internal void CheckEnable()
	{
		if (this.parent != null && this.parent.gameObject.activeInHierarchy)
		{
			if (!base.gameObject.activeSelf)
			{
				base.gameObject.SetActive(true);
			}
			base.transform.position = this.parent.TransformPoint(this.parentPosition);
			base.transform.rotation = this.parent.rotation * Quaternion.Euler(this.rotationOffset);
			return;
		}
		if (this.parent == null || !this.parent.gameObject.activeInHierarchy)
		{
			base.gameObject.SetActive(false);
			if (this.destroyEffect)
			{
				Object.Instantiate<GameObject>(this.destroyEffect, base.transform.position, base.transform.rotation);
			}
		}
	}

	// Token: 0x06000ADF RID: 2783 RVA: 0x00046E9C File Offset: 0x0004509C
	internal void DestroySelf()
	{
		if (this.destroyEffect)
		{
			Object.Instantiate<GameObject>(this.destroyEffect, base.transform.position, base.transform.rotation);
		}
		Object.Destroy(base.gameObject);
	}

	// Token: 0x04000BD9 RID: 3033
	public Transform parent;

	// Token: 0x04000BDA RID: 3034
	private Vector3 parentPosition;

	// Token: 0x04000BDB RID: 3035
	private Vector3 parentLookVector;

	// Token: 0x04000BDC RID: 3036
	private Vector3 rotationOffset;

	// Token: 0x04000BDD RID: 3037
	public GameObject destroyEffect;

	// Token: 0x04000BDE RID: 3038
	private UT_SoftParentTarget target;
}
