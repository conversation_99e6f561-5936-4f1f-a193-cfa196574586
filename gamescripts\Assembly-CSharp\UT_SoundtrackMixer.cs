﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Audio;

// Token: 0x02000195 RID: 405
public class UT_SoundtrackMixer : MonoBehaviour
{
	// Token: 0x06000AE5 RID: 2789 RVA: 0x00046FD4 File Offset: 0x000451D4
	private void Start()
	{
		for (int i = 0; i < this.clips.Count; i++)
		{
			this.auds.Add(base.gameObject.AddComponent<AudioSource>());
			this.auds[i].loop = true;
			this.auds[i].bypassReverbZones = true;
			this.auds[i].outputAudioMixerGroup = this.mixer;
			this.auds[i].clip = this.clips[i];
			this.auds[i].playOnAwake = false;
		}
	}

	// Token: 0x06000AE6 RID: 2790 RVA: 0x0004707D File Offset: 0x0004527D
	private void Update()
	{
		if (!this.running)
		{
			return;
		}
		if (this.type == UT_SoundtrackMixer.MixType.DistanceBlend)
		{
			this.DistanceBlend();
		}
	}

	// Token: 0x06000AE7 RID: 2791 RVA: 0x00047098 File Offset: 0x00045298
	private void DistanceBlend()
	{
		float num = Vector3.Distance(this.target.position, base.transform.position);
		Mathf.Clamp01(num / this.blendDistance);
		for (int i = 0; i < this.clips.Count; i++)
		{
			float num2 = this.soundDistance * ((float)i / (float)this.clips.Count);
			float num3 = (num2 - num) / num2;
			if (num > num2)
			{
				num3 = 0f;
			}
			this.auds[i].volume = num3 * this.volume;
		}
	}

	// Token: 0x06000AE8 RID: 2792 RVA: 0x00047125 File Offset: 0x00045325
	public void SetBlendTarget(Transform t)
	{
		this.target = t;
	}

	// Token: 0x06000AE9 RID: 2793 RVA: 0x00047130 File Offset: 0x00045330
	public void Play()
	{
		this.running = true;
		foreach (AudioSource audioSource in this.auds)
		{
			audioSource.Play();
		}
	}

	// Token: 0x06000AEA RID: 2794 RVA: 0x00047188 File Offset: 0x00045388
	public void Pause()
	{
		this.running = false;
		foreach (AudioSource audioSource in this.auds)
		{
			audioSource.Pause();
		}
	}

	// Token: 0x04000BE0 RID: 3040
	[SerializeField]
	private UT_SoundtrackMixer.MixType type;

	// Token: 0x04000BE1 RID: 3041
	public List<AudioClip> clips = new List<AudioClip>();

	// Token: 0x04000BE2 RID: 3042
	[SerializeField]
	private AudioMixerGroup mixer;

	// Token: 0x04000BE3 RID: 3043
	private Transform target;

	// Token: 0x04000BE4 RID: 3044
	[SerializeField]
	private float blendDistance = 1f;

	// Token: 0x04000BE5 RID: 3045
	[SerializeField]
	private float soundDistance = 15f;

	// Token: 0x04000BE6 RID: 3046
	[SerializeField]
	private float volume = 1f;

	// Token: 0x04000BE7 RID: 3047
	private List<AudioSource> auds = new List<AudioSource>();

	// Token: 0x04000BE8 RID: 3048
	private bool running;

	// Token: 0x020002E1 RID: 737
	[SerializeField]
	private enum MixType
	{
		// Token: 0x0400125F RID: 4703
		Standard,
		// Token: 0x04001260 RID: 4704
		DistanceBlend
	}
}
