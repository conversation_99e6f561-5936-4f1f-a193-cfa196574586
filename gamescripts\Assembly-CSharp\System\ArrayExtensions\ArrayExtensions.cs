﻿using System;

namespace System.ArrayExtensions
{
	// Token: 0x020001CE RID: 462
	public static class ArrayExtensions
	{
		// Token: 0x06000BBD RID: 3005 RVA: 0x0004AAFC File Offset: 0x00048CFC
		public static void ForEach(this Array array, Action<Array, int[]> action)
		{
			if (array.LongLength == 0L)
			{
				return;
			}
			ArrayTraverse arrayTraverse = new ArrayTraverse(array);
			do
			{
				action(array, arrayTraverse.Position);
			}
			while (arrayTraverse.Step());
		}
	}
}
