﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000144 RID: 324
public class UI_CopyColor : MonoBehaviour
{
	// Token: 0x06000955 RID: 2389 RVA: 0x000406DF File Offset: 0x0003E8DF
	private void Start()
	{
		this.curImage = base.GetComponent<Image>();
		this.curText = base.GetComponent<TMP_Text>();
	}

	// Token: 0x06000956 RID: 2390 RVA: 0x000406FC File Offset: 0x0003E8FC
	private void Update()
	{
		if (this.curImage != null)
		{
			this.curImage.color = this.targetImage.color;
		}
		if (this.curText != null)
		{
			this.curText.color = this.targetImage.color;
		}
	}

	// Token: 0x04000A9B RID: 2715
	public Image targetImage;

	// Token: 0x04000A9C RID: 2716
	private Image curImage;

	// Token: 0x04000A9D RID: 2717
	private TMP_Text curText;
}
