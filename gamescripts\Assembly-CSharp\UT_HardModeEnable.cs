﻿using System;
using UnityEngine;

// Token: 0x02000172 RID: 370
public class UT_HardModeEnable : MonoBehaviour
{
	// Token: 0x06000A5A RID: 2650 RVA: 0x00044716 File Offset: 0x00042916
	private void Awake()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.CheckStatus));
	}

	// Token: 0x06000A5B RID: 2651 RVA: 0x00044738 File Offset: 0x00042938
	private void OnDestroy()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Remove(SettingsManager.SettingsRefreshUpdate, new Action(this.CheckStatus));
	}

	// Token: 0x06000A5C RID: 2652 RVA: 0x0004475A File Offset: 0x0004295A
	private void OnEnable()
	{
		this.CheckStatus();
	}

	// Token: 0x06000A5D RID: 2653 RVA: 0x00044762 File Offset: 0x00042962
	public void CheckStatus()
	{
		base.gameObject.SetActive(SettingsManager.settings.g_hard ? (!this.disable) : this.disable);
	}

	// Token: 0x04000B60 RID: 2912
	public bool disable;
}
