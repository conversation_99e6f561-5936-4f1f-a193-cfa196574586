﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000084 RID: 132
public class CL_Handhold_Breakable : CL_Handhold
{
	// Token: 0x06000480 RID: 1152 RVA: 0x00027496 File Offset: 0x00025696
	private void Start()
	{
		this.localRotation = base.transform.localRotation;
	}

	// Token: 0x06000481 RID: 1153 RVA: 0x000274AC File Offset: 0x000256AC
	internal override void Update()
	{
		base.Update();
		if (this.useTotalHoldTime)
		{
			if (this.holdShakeTimer <= 0f)
			{
				base.transform.localRotation = this.localRotation * Quaternion.Euler(Random.insideUnitSphere * this.totalHoldTime * this.holdShakeAmount);
				this.holdShakeTimer = this.holdShakeRate;
			}
			this.holdShakeTimer -= Time.deltaTime;
			if (this.holdShakeSoundTimer <= 0f)
			{
				if (this.shakeStartSound != null && this.totalHoldTime > this.handShakeHoldTime)
				{
					AudioManager.PlaySound(this.shakeStartSound, base.transform.position, this.totalHoldTime / this.maxHoldTime, 1f, 1f, false, 1f, null);
					this.holdShakeSoundTimer = this.shakeStartSound.length;
				}
			}
			else
			{
				this.holdShakeSoundTimer -= Time.deltaTime;
			}
		}
		if (base.GetHolding())
		{
			if (this.hands.Count > 0)
			{
				if (!this.useTotalHoldTime)
				{
					for (int i = 0; i < this.hands.Count; i++)
					{
						if (this.handHoldTime.ContainsKey(this.hands[i]))
						{
							bool flag = this.handHoldTime[this.hands[i]] > this.handShakeHoldTime;
							Dictionary<ENT_Player.Hand, float> dictionary = this.handHoldTime;
							ENT_Player.Hand hand = this.hands[i];
							dictionary[hand] += Time.deltaTime;
							this.hands[i].OffsetHoldPosition(Vector3.down * Time.deltaTime * this.slipSpeed);
							if (this.handHoldTime[this.hands[i]] > this.handShakeHoldTime)
							{
								this.hands[i].ShakeHand(this.handShakeAmount * Time.deltaTime);
								CL_CameraControl.Shake(0.0005f);
								if (!flag && this.shakeStartSound != null)
								{
									AudioManager.PlaySound(this.shakeStartSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
								}
							}
							if (this.handHoldTime[this.hands[i]] > this.maxHoldTime)
							{
								for (int j = this.hands.Count - 1; j >= 0; j--)
								{
									this.hands[j].DropHand(false);
								}
								if (this.slipSound != null)
								{
									AudioManager.PlaySound(this.slipSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
								}
								this.slipEvent.Invoke();
								base.gameObject.SetActive(false);
								return;
							}
						}
					}
					return;
				}
				this.totalHoldTime += Time.deltaTime;
				bool flag2 = false;
				for (int k = 0; k < this.hands.Count; k++)
				{
					flag2 = this.handHoldTime[this.hands[k]] > this.handShakeHoldTime;
					Dictionary<ENT_Player.Hand, float> dictionary = this.handHoldTime;
					ENT_Player.Hand hand = this.hands[k];
					dictionary[hand] += Time.deltaTime;
					this.hands[k].OffsetHoldPosition(Vector3.down * Time.deltaTime * this.slipSpeed);
				}
				if (this.totalHoldTime > this.handShakeHoldTime)
				{
					for (int l = 0; l < this.hands.Count; l++)
					{
						this.hands[l].ShakeHand(this.handShakeAmount * Time.deltaTime);
					}
					CL_CameraControl.Shake(0.0005f);
					if (!flag2)
					{
						this.shakeStartSound != null;
					}
				}
				if (this.totalHoldTime > this.maxHoldTime)
				{
					for (int m = this.hands.Count - 1; m >= 0; m--)
					{
						this.hands[m].DropHand(false);
					}
					if (this.slipSound != null)
					{
						AudioManager.PlaySound(this.slipSound, base.transform.position, 1f, 1f, 1f, false, 1f, null);
					}
					this.slipEvent.Invoke();
					base.gameObject.SetActive(false);
					return;
				}
			}
		}
		else if (this.useTotalHoldTime && this.drainTotalHoldTime)
		{
			this.totalHoldTime = Mathf.Max(this.totalHoldTime - Time.deltaTime, 0f);
		}
	}

	// Token: 0x06000482 RID: 1154 RVA: 0x0002797C File Offset: 0x00025B7C
	public override void Interact(ENT_Player p, ENT_Player.Hand hand)
	{
		if (!this.handHoldTime.ContainsKey(hand))
		{
			this.handHoldTime.Add(hand, 0f);
		}
		base.Interact(p, hand);
		Vector3 holdPosition = hand.GetHoldPosition();
		hand.SetHoldPosition(holdPosition);
	}

	// Token: 0x06000483 RID: 1155 RVA: 0x000279BE File Offset: 0x00025BBE
	public override void StopInteract(ENT_Player p, ENT_Player.Hand dropHand, string s = "")
	{
		if (this.handHoldTime.ContainsKey(dropHand))
		{
			this.handHoldTime.Remove(dropHand);
		}
		base.StopInteract(p, dropHand, s);
	}

	// Token: 0x040005EB RID: 1515
	public float maxHoldTime = 3f;

	// Token: 0x040005EC RID: 1516
	public bool useTotalHoldTime;

	// Token: 0x040005ED RID: 1517
	public bool drainTotalHoldTime;

	// Token: 0x040005EE RID: 1518
	public float slipSpeed = 1f;

	// Token: 0x040005EF RID: 1519
	public float handShakeHoldTime = 1f;

	// Token: 0x040005F0 RID: 1520
	public float handShakeAmount = 0.1f;

	// Token: 0x040005F1 RID: 1521
	public float holdShakeAmount = 1f;

	// Token: 0x040005F2 RID: 1522
	public float holdShakeRate = 0.2f;

	// Token: 0x040005F3 RID: 1523
	private float holdShakeTimer;

	// Token: 0x040005F4 RID: 1524
	private float holdShakeSoundTimer;

	// Token: 0x040005F5 RID: 1525
	[Space]
	[Header("Audio")]
	public AudioClip slipSound;

	// Token: 0x040005F6 RID: 1526
	public AudioClip shakeStartSound;

	// Token: 0x040005F7 RID: 1527
	public UnityEvent slipEvent;

	// Token: 0x040005F8 RID: 1528
	private Dictionary<ENT_Player.Hand, float> handHoldTime = new Dictionary<ENT_Player.Hand, float>();

	// Token: 0x040005F9 RID: 1529
	private float totalHoldTime;

	// Token: 0x040005FA RID: 1530
	private Quaternion localRotation;
}
