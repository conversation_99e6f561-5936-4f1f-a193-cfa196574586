﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000130 RID: 304
public class UI_OutlineOnSelect : MonoBehaviour, ISelectHandler, IEventSystemHandler, ISubmitHandler, IDeselectHandler
{
	// Token: 0x060008F2 RID: 2290 RVA: 0x0003ED8F File Offset: 0x0003CF8F
	private void Start()
	{
		this.outline = base.GetComponent<Outline>();
		this.outlineColor = this.outline.effectColor;
	}

	// Token: 0x060008F3 RID: 2291 RVA: 0x0003EDAE File Offset: 0x0003CFAE
	private void Update()
	{
	}

	// Token: 0x060008F4 RID: 2292 RVA: 0x0003EDB0 File Offset: 0x0003CFB0
	public void OnSelect(BaseEventData eventData)
	{
		this.outline.effectColor = this.selectColor;
	}

	// Token: 0x060008F5 RID: 2293 RVA: 0x0003EDC3 File Offset: 0x0003CFC3
	public void OnSubmit(BaseEventData eventData)
	{
		this.outline.effectColor = this.submitColor;
	}

	// Token: 0x060008F6 RID: 2294 RVA: 0x0003EDD6 File Offset: 0x0003CFD6
	public void OnDeselect(BaseEventData eventData)
	{
		this.outline.effectColor = this.deselectColor;
	}

	// Token: 0x060008F7 RID: 2295 RVA: 0x0003EDEC File Offset: 0x0003CFEC
	private void SetOutlineAlpha(float alpha)
	{
		if (this.outline == null)
		{
			return;
		}
		Color effectColor = this.outline.effectColor;
		effectColor.a = alpha;
		this.outline.effectColor = effectColor;
	}

	// Token: 0x04000A41 RID: 2625
	private Outline outline;

	// Token: 0x04000A42 RID: 2626
	private Color outlineColor;

	// Token: 0x04000A43 RID: 2627
	public Color selectColor;

	// Token: 0x04000A44 RID: 2628
	public Color submitColor;

	// Token: 0x04000A45 RID: 2629
	public Color deselectColor;
}
