﻿using System;

// Token: 0x0200008D RID: 141
public class HandItem_Food : HandItem
{
	// Token: 0x060004BB RID: 1211 RVA: 0x00029031 File Offset: 0x00027231
	private void Start()
	{
	}

	// Token: 0x060004BC RID: 1212 RVA: 0x00029033 File Offset: 0x00027233
	private void Update()
	{
	}

	// Token: 0x060004BD RID: 1213 RVA: 0x00029035 File Offset: 0x00027235
	public override void Initialize(Item i, ENT_Player.Hand h)
	{
		base.Initialize(i, h);
		if (this.used)
		{
			this.anim.SetBool("Empty", true);
		}
	}

	// Token: 0x060004BE RID: 1214 RVA: 0x00029058 File Offset: 0x00027258
	public void Eat()
	{
		this.hand.GetPlayer().AddGripStrength(this.gripAmountAdded, false);
		this.item.SetUsed(true);
		this.item.SetItemAsset(this.usedObject);
		if (this.useBuff)
		{
			this.buff.Initialize();
		}
		CL_GameManager.gMan.localPlayer.Buff(this.buff);
		this.UpdateStats();
	}

	// Token: 0x060004BF RID: 1215 RVA: 0x000290C8 File Offset: 0x000272C8
	public override void Use()
	{
		base.Use();
		if (this.used)
		{
			this.anim.SetBool("Empty", true);
		}
		else
		{
			this.anim.SetTrigger("Eat");
			this.anim.SetBool("Empty", true);
			this.used = true;
			this.inUse = true;
		}
		if (this.active)
		{
			bool used = this.used;
		}
	}

	// Token: 0x060004C0 RID: 1216 RVA: 0x00029134 File Offset: 0x00027334
	public void FinishEating()
	{
		this.inUse = false;
	}

	// Token: 0x060004C1 RID: 1217 RVA: 0x0002913D File Offset: 0x0002733D
	public override bool CanDrop()
	{
		return !this.inUse;
	}

	// Token: 0x0400063F RID: 1599
	public Item_Object usedObject;

	// Token: 0x04000640 RID: 1600
	private bool inUse;

	// Token: 0x04000641 RID: 1601
	public float gripAmountAdded = 10f;

	// Token: 0x04000642 RID: 1602
	public bool useBuff;

	// Token: 0x04000643 RID: 1603
	public BuffContainer buff;
}
