﻿using System;
using Drawing;
using UnityEngine;

// Token: 0x020001A3 RID: 419
public class UT_AddForceToPlayer : MonoBehaviourGizmos
{
	// Token: 0x06000B1E RID: 2846 RVA: 0x00047FF7 File Offset: 0x000461F7
	public void AddForce()
	{
		ENT_Player.playerObject.AddForce(base.transform.InverseTransformVector(this.force));
	}

	// Token: 0x06000B1F RID: 2847 RVA: 0x00048014 File Offset: 0x00046214
	public override void DrawGizmos()
	{
		Draw.SphereOutline(base.transform.position, 0.2f, Color.yellow);
		if (GizmoContext.InSelection(this))
		{
			Draw.Arrow(base.transform.position, base.transform.position + base.transform.InverseTransformVector(this.force), Color.yellow);
		}
	}

	// Token: 0x04000C26 RID: 3110
	public Vector3 force;
}
