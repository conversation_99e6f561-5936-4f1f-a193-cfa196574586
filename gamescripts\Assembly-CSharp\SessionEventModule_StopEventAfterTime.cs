﻿using System;
using UnityEngine;

// Token: 0x0200006C RID: 108
[Serializable]
public class SessionEventModule_StopEventAfterTime : SessionEventModule
{
	// Token: 0x060003DF RID: 991 RVA: 0x000239A6 File Offset: 0x00021BA6
	public override void Initialize(SessionEvent s)
	{
		this.timeRemaining = this.timer;
		base.Initialize(s);
	}

	// Token: 0x060003E0 RID: 992 RVA: 0x000239BB File Offset: 0x00021BBB
	public override void Update()
	{
		this.timeRemaining -= Time.deltaTime;
		base.Update();
		if (this.timeRemaining <= 0f && this.sessionEvent != null)
		{
			this.sessionEvent.StopEvent();
		}
	}

	// Token: 0x04000539 RID: 1337
	public float timer;

	// Token: 0x0400053A RID: 1338
	private float timeRemaining;
}
