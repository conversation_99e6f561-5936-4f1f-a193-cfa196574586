﻿using System;
using System.Collections.Generic;

namespace System
{
	// Token: 0x020001CD RID: 461
	public class ReferenceEqualityComparer : EqualityComparer<object>
	{
		// Token: 0x06000BBA RID: 3002 RVA: 0x0004AAE0 File Offset: 0x00048CE0
		public override bool Equals(object x, object y)
		{
			return x == y;
		}

		// Token: 0x06000BBB RID: 3003 RVA: 0x0004AAE6 File Offset: 0x00048CE6
		public override int GetHashCode(object obj)
		{
			if (obj == null)
			{
				return 0;
			}
			return obj.GetHashCode();
		}
	}
}
