﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020000AC RID: 172
[CreateAssetMenu(fileName = "New Region", menuName = "White Knuckle/Region")]
public class M_Region : ScriptableObject
{
	// Token: 0x06000599 RID: 1433 RVA: 0x0002DDE8 File Offset: 0x0002BFE8
	public List<M_Level> GetLevels(M_Region lastRegion, M_Subregion lastSubregion = null)
	{
		List<M_Subregion> list = new List<M_Subregion>();
		List<M_Subregion> list2 = new List<M_Subregion>();
		foreach (M_Region.SubregionGroup subregionGroup in this.subregionGroups)
		{
			M_Subregion subregion = subregionGroup.GetSubregion();
			if (!(subregion == null))
			{
				list2.Add(subregion);
			}
		}
		Queue<M_Subregion> queue = this.GetSubregionQueue(list2, lastSubregion, lastRegion);
		for (int i = 0; i < 10; i++)
		{
			if (queue.Count == 0)
			{
				queue = this.GetSubregionQueue(list2, list[i - 1], lastRegion);
			}
			list.Add(queue.Dequeue());
		}
		this.allLevels = new List<M_Level>();
		foreach (M_Subregion m_Subregion in list)
		{
			foreach (M_Level m_Level in m_Subregion.levels)
			{
				if (m_Subregion.levels.Count > 1)
				{
					if (m_Level.spawnSettings.GetEffectiveSpawnChance() > Random.value)
					{
						this.allLevels.Add(m_Level);
						if (!m_Level.setCustomRegionInfo)
						{
							m_Level.region = this;
							m_Level.subRegion = m_Subregion;
						}
					}
				}
				else
				{
					this.allLevels.Add(m_Level);
					if (!m_Level.setCustomRegionInfo)
					{
						m_Level.region = this;
						m_Level.subRegion = m_Subregion;
					}
				}
			}
		}
		List<M_Level> list3 = new List<M_Level>();
		bool flag = false;
		float num = 0f;
		int num2 = Random.Range(list[0].subregionMinLength, list[0].subregionMaxLength);
		float num3 = 0f;
		int num4 = 0;
		int num5 = 0;
		this.RefillLevelList(list[0], null);
		if (lastRegion == null || this.forceStartLevel)
		{
			if (this.startLevels.Count > 0)
			{
				list3.Add(this.startLevels[Random.Range(0, this.startLevels.Count)]);
				if (!list3[0].setCustomRegionInfo)
				{
					list3[0].region = this;
					list3[0].subRegion = list[num5];
				}
			}
		}
		else if (this.transitionLevels.Count > 0)
		{
			M_Level transitionLevel = this.GetTransitionLevel(lastRegion);
			if (transitionLevel != null)
			{
				list3.Add(transitionLevel);
			}
		}
		bool flag2 = false;
		while (!flag)
		{
			M_Subregion m_Subregion2 = list[num5];
			if (this.levelOrdering.Count == 0)
			{
				this.RefillLevelList(list[num5], list3[list3.Count - 1]);
			}
			M_Level randomLevel = this.GetRandomLevel();
			list3.Add(randomLevel);
			num += randomLevel.GetLength();
			randomLevel.region = this;
			if (num > this.regionHeight && this.useRegionHeight)
			{
				return list3;
			}
			bool flag3 = false;
			if (m_Subregion2.subregionOrder == M_Subregion.SubregionOrder.single)
			{
				flag3 = true;
			}
			else if (!m_Subregion2.useLevelCount && num3 >= list[num5].subregionHeight)
			{
				flag3 = true;
			}
			else if (m_Subregion2.useLevelCount && num4 + 1 >= num2)
			{
				flag3 = true;
			}
			if (flag3)
			{
				num4 = 0;
				num3 = 0f;
				if (list2.Count > 1)
				{
					if (list[num5] != list[num5 + 1])
					{
						this.RefillLevelList(list[num5 + 1], list3[list3.Count - 1]);
					}
				}
				else
				{
					this.RefillLevelList(list[num5], list3[list3.Count - 1]);
				}
				num5++;
				if (num5 > list2.Count - 1)
				{
					flag2 = true;
					num5 = 0;
				}
				num2 = Random.Range(list[num5].subregionMinLength, list[num5].subregionMaxLength);
			}
			if (!this.useRegionHeight && flag2)
			{
				return list3;
			}
			num4++;
			num3 += randomLevel.GetLength();
			num2 = Random.Range(list[num5].subregionMinLength, list[num5].subregionMaxLength);
		}
		return list3;
	}

	// Token: 0x0600059A RID: 1434 RVA: 0x0002E228 File Offset: 0x0002C428
	private M_Level GetRandomLevel()
	{
		M_Level m_Level = this.levelOrdering[Random.Range(0, this.levelOrdering.Count)];
		this.levelOrdering.Remove(m_Level);
		return m_Level;
	}

	// Token: 0x0600059B RID: 1435 RVA: 0x0002E260 File Offset: 0x0002C460
	private void RefillLevelList(M_Subregion currentSubRegion, M_Level lastLevel = null)
	{
		this.levelOrdering = new List<M_Level>();
		foreach (M_Level m_Level in this.allLevels)
		{
			if (!this.levelOrdering.Contains(m_Level) && currentSubRegion.levels.Contains(m_Level))
			{
				m_Level.region = this;
				this.levelOrdering.Add(m_Level);
			}
		}
		if (this.levelOrdering.Count > 1 && lastLevel != null && this.levelOrdering.Contains(lastLevel))
		{
			this.levelOrdering.Remove(lastLevel);
		}
	}

	// Token: 0x0600059C RID: 1436 RVA: 0x0002E318 File Offset: 0x0002C518
	private Queue<M_Subregion> GetSubregionQueue(List<M_Subregion> subregions, M_Subregion lastSubregion = null, M_Region lastRegion = null)
	{
		List<M_Subregion> list = new List<M_Subregion>();
		list.AddRange(subregions);
		Queue<M_Subregion> queue = new Queue<M_Subregion>();
		bool flag = this.regionOrder == M_Region.RegionOrder.shuffleAfterGameStart || this.regionOrder == M_Region.RegionOrder.shuffleAfterFirst || this.regionOrder == M_Region.RegionOrder.shuffle;
		if (this.regionOrder == M_Region.RegionOrder.shuffleAfterGameStart || this.regionOrder == M_Region.RegionOrder.shuffleAfterFirst)
		{
			if (this.regionOrder == M_Region.RegionOrder.shuffleAfterFirst)
			{
				if (list.Count > 1 && list.Contains(subregions[0]))
				{
					list.Remove(subregions[0]);
				}
				queue.Enqueue(subregions[0]);
			}
			else
			{
				if (lastSubregion != null && list.Count > 1 && list.Contains(lastSubregion))
				{
					list.Remove(lastSubregion);
				}
				if (lastRegion == null)
				{
					queue.Enqueue(subregions[0]);
				}
			}
		}
		else if (this.regionOrder == M_Region.RegionOrder.playlist)
		{
			for (int i = 0; i < subregions.Count; i++)
			{
				queue.Enqueue(subregions[i]);
			}
		}
		while (list.Count > 0)
		{
			M_Subregion m_Subregion;
			if (flag)
			{
				if (lastRegion == null && list.Count > 1 && this.regionOrder != M_Region.RegionOrder.shuffle)
				{
					m_Subregion = list[Random.Range(1, list.Count)];
				}
				else
				{
					m_Subregion = list[Random.Range(0, list.Count)];
				}
			}
			else
			{
				m_Subregion = list[list.Count - 1];
			}
			queue.Enqueue(m_Subregion);
			list.Remove(m_Subregion);
		}
		return queue;
	}

	// Token: 0x0600059D RID: 1437 RVA: 0x0002E490 File Offset: 0x0002C690
	public M_Level GetTransitionLevel(M_Region regionFrom)
	{
		if (this.transitionLevelDictionary == null)
		{
			this.transitionLevelDictionary = new Dictionary<string, M_Region.TransitionLevels>();
			foreach (M_Region.TransitionLevels transitionLevels in this.transitionLevels)
			{
				this.transitionLevelDictionary.Add(transitionLevels.fromRegion, transitionLevels);
			}
		}
		if (!this.transitionLevelDictionary.ContainsKey(regionFrom.regionName))
		{
			return null;
		}
		return this.transitionLevelDictionary[regionFrom.regionName].levels[Random.Range(0, this.transitionLevelDictionary[regionFrom.regionName].levels.Count)];
	}

	// Token: 0x04000735 RID: 1845
	public string regionName;

	// Token: 0x04000736 RID: 1846
	public string introText;

	// Token: 0x04000737 RID: 1847
	public bool useRegionHeight = true;

	// Token: 0x04000738 RID: 1848
	public float regionHeight = 500f;

	// Token: 0x04000739 RID: 1849
	public M_Region.RegionOrder regionOrder;

	// Token: 0x0400073A RID: 1850
	[Space]
	[Tooltip("Force the start level to always spawn.")]
	public bool forceStartLevel;

	// Token: 0x0400073B RID: 1851
	[Tooltip("Start Level for the region, used if there are no prior regions. Chosen at random.")]
	public List<M_Level> startLevels;

	// Token: 0x0400073C RID: 1852
	public List<M_Region.SubregionGroup> subregionGroups;

	// Token: 0x0400073D RID: 1853
	[Space]
	public M_Region nextRegion;

	// Token: 0x0400073E RID: 1854
	public List<SessionEventList> sessionEventLists;

	// Token: 0x0400073F RID: 1855
	private List<M_Level> levelOrdering = new List<M_Level>();

	// Token: 0x04000740 RID: 1856
	private List<M_Level> allLevels = new List<M_Level>();

	// Token: 0x04000741 RID: 1857
	public bool useCustomFXSettings = true;

	// Token: 0x04000742 RID: 1858
	public FXManager.FXData fxData = new FXManager.FXData();

	// Token: 0x04000743 RID: 1859
	public Color fogColor;

	// Token: 0x04000744 RID: 1860
	public int ditherLevels = 36;

	// Token: 0x04000745 RID: 1861
	public List<M_Region.TransitionLevels> transitionLevels;

	// Token: 0x04000746 RID: 1862
	private Dictionary<string, M_Region.TransitionLevels> transitionLevelDictionary;

	// Token: 0x0200026C RID: 620
	public enum RegionOrder
	{
		// Token: 0x04000FD5 RID: 4053
		shuffle,
		// Token: 0x04000FD6 RID: 4054
		playlist,
		// Token: 0x04000FD7 RID: 4055
		shuffleAfterGameStart,
		// Token: 0x04000FD8 RID: 4056
		shuffleAfterFirst
	}

	// Token: 0x0200026D RID: 621
	[Serializable]
	public class TransitionLevels
	{
		// Token: 0x04000FD9 RID: 4057
		public string name;

		// Token: 0x04000FDA RID: 4058
		public string fromRegion;

		// Token: 0x04000FDB RID: 4059
		public List<M_Level> levels;
	}

	// Token: 0x0200026E RID: 622
	[Serializable]
	public class SubregionGroup
	{
		// Token: 0x06000DD7 RID: 3543 RVA: 0x0005492C File Offset: 0x00052B2C
		public M_Subregion GetSubregion()
		{
			List<M_Subregion> list = new List<M_Subregion>();
			foreach (M_Subregion m_Subregion in this.subregions)
			{
				if (m_Subregion.CanSpawn())
				{
					list.Add(m_Subregion);
				}
			}
			if (list.Count == 0)
			{
				return null;
			}
			return list[Random.Range(0, list.Count)];
		}

		// Token: 0x04000FDC RID: 4060
		public string name;

		// Token: 0x04000FDD RID: 4061
		public List<M_Subregion> subregions;
	}
}
