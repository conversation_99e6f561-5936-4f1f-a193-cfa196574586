﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;

// Token: 0x020000F0 RID: 240
public class CloseOnMouseExit : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerExitHandler, IEventSystemHandler
{
	// Token: 0x06000762 RID: 1890 RVA: 0x0003891C File Offset: 0x00036B1C
	public void OnPointerExit(PointerEventData eventData)
	{
		Debug.Log(eventData);
		if (this.destroy)
		{
			Object.Destroy(base.gameObject);
		}
		base.gameObject.SetActive(false);
	}

	// Token: 0x040008E1 RID: 2273
	public bool destroy;
}
