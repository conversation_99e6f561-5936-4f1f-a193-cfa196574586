﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace DarkMachine.AI
{
	// Token: 0x020001DA RID: 474
	public class AI_BurrowManager
	{
		// Token: 0x06000BF6 RID: 3062 RVA: 0x0004BF74 File Offset: 0x0004A174
		public void AddBurrow(AI_Burrow t)
		{
			if (!this.burrows.Contains(t))
			{
				this.burrows.Add(t);
			}
		}

		// Token: 0x06000BF7 RID: 3063 RVA: 0x0004BF90 File Offset: 0x0004A190
		public void RemoveBurrow(AI_Burrow t)
		{
			if (this.burrows.Contains(t))
			{
				this.burrows.Remove(t);
			}
		}

		// Token: 0x06000BF8 RID: 3064 RVA: 0x0004BFB0 File Offset: 0x0004A1B0
		public AI_Burrow GetClosestBurrowToPosition(Vector3 pos)
		{
			if (this.burrows == null || this.burrows.Count == 0)
			{
				return null;
			}
			AI_Burrow ai_Burrow = null;
			float num = float.PositiveInfinity;
			for (int i = 0; i < this.burrows.Count; i++)
			{
				float num2 = Vector3.Distance(this.burrows[i].transform.position, pos);
				if (num2 < num)
				{
					ai_Burrow = this.burrows[i];
					num = num2;
				}
			}
			return ai_Burrow;
		}

		// Token: 0x06000BF9 RID: 3065 RVA: 0x0004C023 File Offset: 0x0004A223
		internal void Initialize()
		{
			this.burrows = new List<AI_Burrow>();
		}

		// Token: 0x04000CF1 RID: 3313
		public List<AI_Burrow> burrows;
	}
}
