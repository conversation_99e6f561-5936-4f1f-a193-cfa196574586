﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

// Token: 0x02000052 RID: 82
public class ENV_VendingMachine : MonoBehaviour, Seedable, SaveableObject
{
	// Token: 0x0600035A RID: 858 RVA: 0x00021568 File Offset: 0x0001F768
	private void Start()
	{
		if (base.transform.lossyScale.x < 0f)
		{
			base.transform.localScale = new Vector3(-1f, 1f, 1f);
		}
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		this.lastRoaches = 0;
		if (!this.hasGenerated)
		{
			if (this.randomGeneration)
			{
				this.GenerateOptions();
			}
			this.UpdateButtons();
		}
	}

	// Token: 0x0600035B RID: 859 RVA: 0x000215DA File Offset: 0x0001F7DA
	private void OnEnable()
	{
		this.digits = this.GetDigits(CL_GameManager.roaches);
		this.UpdateButtons();
	}

	// Token: 0x0600035C RID: 860 RVA: 0x000215F3 File Offset: 0x0001F7F3
	private void Update()
	{
		if (this.lastRoaches != CL_GameManager.roaches)
		{
			this.digits = this.GetDigits(CL_GameManager.roaches);
			this.UpdateButtons();
			this.lastRoaches = CL_GameManager.roaches;
		}
		this.RollCounters();
	}

	// Token: 0x0600035D RID: 861 RVA: 0x0002162A File Offset: 0x0001F82A
	private void RegenerateOptions()
	{
		this.GenerateOptions();
		this.UpdateButtons();
	}

	// Token: 0x0600035E RID: 862 RVA: 0x00021638 File Offset: 0x0001F838
	private void GenerateOptions()
	{
		List<ENV_VendingMachine.Purchase> list = new List<ENV_VendingMachine.Purchase>();
		if (!this.ignoreRequirements)
		{
			for (int i = this.purchaseList.Count - 1; i > 0; i--)
			{
				if (this.purchaseList[i].itemObject != null)
				{
					if (this.purchaseList[i].requiredItemTag != "" && !Inventory.instance.HasItemWithTag(this.purchaseList[i].requiredItemTag, false))
					{
						this.purchaseList.RemoveAt(i);
					}
					else if (!this.purchaseList[i].itemObject.itemData.IsUnlocked())
					{
						this.purchaseList.RemoveAt(i);
					}
				}
			}
		}
		int num = 0;
		while (list.Count < this.buttons.Length)
		{
			ENV_VendingMachine.Purchase purchase;
			if (this.fillFromPurchaseListInOrder)
			{
				purchase = this.purchaseList[num];
				num++;
				if (num >= this.purchaseList.Count)
				{
					num = 0;
				}
			}
			else
			{
				purchase = this.purchaseList[Random.Range(0, this.purchaseList.Count)];
			}
			if (this.fillFromPurchaseListInOrder || Random.value < purchase.chance)
			{
				list.Add(purchase);
				this.buttons[list.Count - 1].purchase = purchase;
			}
		}
		this.hasGenerated = true;
	}

	// Token: 0x0600035F RID: 863 RVA: 0x00021798 File Offset: 0x0001F998
	private void UpdateButtons()
	{
		foreach (ENV_VendingMachine.VendingButton vendingButton in this.buttons)
		{
			vendingButton.sprite.sprite = vendingButton.purchase.purchaseSprite;
			if (!vendingButton.available)
			{
				vendingButton.sprite.color = this.outColor;
			}
			else if (vendingButton.purchase.price > CL_GameManager.roaches)
			{
				vendingButton.sprite.color = this.unaffordableColor;
			}
			else
			{
				vendingButton.sprite.color = this.availableColor;
			}
			string text = "";
			for (int j = 0; j < vendingButton.purchase.price; j++)
			{
				text += ".";
			}
			vendingButton.text.text = text;
		}
	}

	// Token: 0x06000360 RID: 864 RVA: 0x00021863 File Offset: 0x0001FA63
	public void AddRoaches(int i)
	{
		CL_GameManager.AddRoaches(i);
		this.clipHandler.PlaySound("vending:kaching");
		this.digits = this.GetDigits(CL_GameManager.roaches);
		this.UpdateButtons();
	}

	// Token: 0x06000361 RID: 865 RVA: 0x00021894 File Offset: 0x0001FA94
	private void RollCounters()
	{
		if (this.digits == null)
		{
			this.digits = new int[3];
		}
		for (int i = 0; i < this.counters.Length; i++)
		{
			Transform transform = this.counters[i];
			int num = 0;
			if (this.digits.Length > i)
			{
				num = this.digits[i] * -36;
			}
			if (Quaternion.Angle(Quaternion.Euler((float)num, 0f, 0f), transform.localRotation) > 10f)
			{
				this.clipHandler.PlaySound("vending:ticker");
			}
			transform.localRotation = Quaternion.Lerp(transform.localRotation, Quaternion.Euler((float)num, 0f, 0f), Time.deltaTime * 8f);
		}
	}

	// Token: 0x06000362 RID: 866 RVA: 0x00021950 File Offset: 0x0001FB50
	private int[] GetDigits(int number)
	{
		List<int> list = new List<int>();
		while (number > 0)
		{
			list.Add(number % 10);
			number /= 10;
		}
		return list.ToArray();
	}

	// Token: 0x06000363 RID: 867 RVA: 0x00021980 File Offset: 0x0001FB80
	public void Buy(int i)
	{
		if (!this.buttons[i].available || this.buttons[i].purchase.price > CL_GameManager.roaches)
		{
			this.clipHandler.PlaySound("vending:deny");
			return;
		}
		if (this.singlePurchase)
		{
			this.buttons[i].available = false;
		}
		List<GameObject> list = new List<GameObject>();
		if (this.buttons[i].purchase.spawnAssets == null || this.buttons[i].purchase.spawnAssets.Count == 0)
		{
			list.Add(this.buttons[i].purchase.itemObject.gameObject);
		}
		else
		{
			list = this.buttons[i].purchase.spawnAssets;
		}
		foreach (GameObject gameObject in list)
		{
			Rigidbody component = Object.Instantiate<GameObject>(gameObject, this.spawnSpot.position, this.spawnSpot.rotation, base.transform.parent).GetComponent<Rigidbody>();
			if (component != null)
			{
				component.isKinematic = false;
				component.velocity = this.spawnSpot.forward * 5f;
			}
		}
		this.AddRoaches(-this.buttons[i].purchase.price);
		this.clipHandler.PlaySound("vending:approve");
		this.UpdateButtons();
	}

	// Token: 0x06000364 RID: 868 RVA: 0x00021B00 File Offset: 0x0001FD00
	public void SetSeed(int seed)
	{
		this.localSeed = seed;
	}

	// Token: 0x06000365 RID: 869 RVA: 0x00021B09 File Offset: 0x0001FD09
	public bool CanSave(string[] flags = null)
	{
		if (flags != null)
		{
			Debug.Log("Vendor Flag: " + flags[0]);
			if (flags[0] == "savetype:disk")
			{
				return false;
			}
		}
		return this.vendorId != "";
	}

	// Token: 0x06000366 RID: 870 RVA: 0x00021B44 File Offset: 0x0001FD44
	public SaveableInfo GetSaveInfo()
	{
		SaveableInfo saveableInfo = new SaveableInfo();
		saveableInfo.id = this.vendorId;
		ENV_VendingMachine.VendorSaveData vendorSaveData = new ENV_VendingMachine.VendorSaveData();
		vendorSaveData.purchaseData = new ENV_VendingMachine.VendingPurchaseData[this.buttons.Length];
		for (int i = 0; i < this.buttons.Length; i++)
		{
			vendorSaveData.purchaseData[i] = new ENV_VendingMachine.VendingPurchaseData();
			vendorSaveData.purchaseData[i].available = this.buttons[i].available;
			vendorSaveData.purchaseData[i].name = this.buttons[i].purchase.name;
		}
		saveableInfo.data = JsonUtility.ToJson(vendorSaveData);
		return saveableInfo;
	}

	// Token: 0x06000367 RID: 871 RVA: 0x00021BE4 File Offset: 0x0001FDE4
	public void SetSaveInfo(SaveableInfo info)
	{
		ENV_VendingMachine.VendorSaveData vendorSaveData = JsonUtility.FromJson<ENV_VendingMachine.VendorSaveData>(info.data);
		ENV_VendingMachine.VendingPurchaseData[] purchaseData = vendorSaveData.purchaseData;
		if (vendorSaveData.purchaseData.Length != this.buttons.Length)
		{
			return;
		}
		for (int i = 0; i < this.buttons.Length; i++)
		{
			foreach (ENV_VendingMachine.Purchase purchase in this.purchaseList)
			{
				if (purchase.name == purchaseData[i].name)
				{
					this.buttons[i].purchase = purchase;
					break;
				}
			}
			this.buttons[i].available = purchaseData[i].available;
		}
		this.hasGenerated = true;
		this.UpdateButtons();
	}

	// Token: 0x06000368 RID: 872 RVA: 0x00021CB0 File Offset: 0x0001FEB0
	public string GetSaveID()
	{
		return this.vendorId;
	}

	// Token: 0x0400049E RID: 1182
	public string vendorId;

	// Token: 0x0400049F RID: 1183
	public Transform spawnSpot;

	// Token: 0x040004A0 RID: 1184
	public bool singlePurchase = true;

	// Token: 0x040004A1 RID: 1185
	public bool randomGeneration = true;

	// Token: 0x040004A2 RID: 1186
	public bool fillFromPurchaseListInOrder;

	// Token: 0x040004A3 RID: 1187
	public bool ignoreRequirements;

	// Token: 0x040004A4 RID: 1188
	public List<ENV_VendingMachine.Purchase> purchaseList;

	// Token: 0x040004A5 RID: 1189
	public Transform[] counters;

	// Token: 0x040004A6 RID: 1190
	public ENV_VendingMachine.VendingButton[] buttons;

	// Token: 0x040004A7 RID: 1191
	public Color availableColor;

	// Token: 0x040004A8 RID: 1192
	public Color outColor;

	// Token: 0x040004A9 RID: 1193
	public Color unaffordableColor;

	// Token: 0x040004AA RID: 1194
	private int lastRoaches;

	// Token: 0x040004AB RID: 1195
	private int[] digits;

	// Token: 0x040004AC RID: 1196
	private UT_AudioClipHandler clipHandler;

	// Token: 0x040004AD RID: 1197
	private int localSeed;

	// Token: 0x040004AE RID: 1198
	private bool hasGenerated;

	// Token: 0x0200023F RID: 575
	[Serializable]
	public class Purchase
	{
		// Token: 0x04000F01 RID: 3841
		public string name;

		// Token: 0x04000F02 RID: 3842
		public float chance = 1f;

		// Token: 0x04000F03 RID: 3843
		public Item_Object itemObject;

		// Token: 0x04000F04 RID: 3844
		public List<GameObject> spawnAssets;

		// Token: 0x04000F05 RID: 3845
		public int price;

		// Token: 0x04000F06 RID: 3846
		public Sprite purchaseSprite;

		// Token: 0x04000F07 RID: 3847
		public string requiredItemTag;

		// Token: 0x04000F08 RID: 3848
		public bool ignoreUnlocked;
	}

	// Token: 0x02000240 RID: 576
	[Serializable]
	public class VendingButton
	{
		// Token: 0x04000F09 RID: 3849
		public SpriteRenderer sprite;

		// Token: 0x04000F0A RID: 3850
		public TMP_Text text;

		// Token: 0x04000F0B RID: 3851
		public ENV_VendingMachine.Purchase purchase;

		// Token: 0x04000F0C RID: 3852
		public bool available;
	}

	// Token: 0x02000241 RID: 577
	[Serializable]
	public class VendingPurchaseData
	{
		// Token: 0x04000F0D RID: 3853
		public bool available;

		// Token: 0x04000F0E RID: 3854
		public string name;
	}

	// Token: 0x02000242 RID: 578
	[Serializable]
	public class VendorSaveData
	{
		// Token: 0x04000F0F RID: 3855
		public ENV_VendingMachine.VendingPurchaseData[] purchaseData;
	}
}
