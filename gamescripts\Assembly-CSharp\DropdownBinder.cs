﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

// Token: 0x02000111 RID: 273
public class DropdownBinder : MonoBehaviour
{
	// Token: 0x06000839 RID: 2105 RVA: 0x0003BA42 File Offset: 0x00039C42
	private void Start()
	{
		this.PullSetting();
	}

	// Token: 0x0600083A RID: 2106 RVA: 0x0003BA4C File Offset: 0x00039C4C
	private void PullSetting()
	{
		string setting = SettingsManager.GetSetting(this.settingName);
		this.dropdown.value = int.Parse(setting);
	}

	// Token: 0x0600083B RID: 2107 RVA: 0x0003BA78 File Offset: 0x00039C78
	public void OnUpdateDropDown()
	{
		SettingsManager.SetSetting(new string[]
		{
			this.settingName,
			this.dropdown.value.ToString()
		});
		SettingsManager.RefreshSettings("");
	}

	// Token: 0x040009A6 RID: 2470
	public string settingName;

	// Token: 0x040009A7 RID: 2471
	public TMP_Dropdown dropdown;

	// Token: 0x040009A8 RID: 2472
	private Dictionary<int, string> dropdownDictionary;
}
