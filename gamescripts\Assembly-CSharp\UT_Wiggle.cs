﻿using System;
using UnityEngine;

// Token: 0x020001A1 RID: 417
public class UT_Wiggle : MonoBehaviour
{
	// Token: 0x06000B18 RID: 2840 RVA: 0x00047E92 File Offset: 0x00046092
	private void Start()
	{
		this.startPos = base.transform.localPosition;
		this.randomOffset = Random.value * this.randomOffset;
	}

	// Token: 0x06000B19 RID: 2841 RVA: 0x00047EB8 File Offset: 0x000460B8
	private void FixedUpdate()
	{
		base.transform.localPosition = this.startPos + base.transform.right * Mathf.Sin(Time.time * this.wiggleSpeed + this.randomOffset) * this.wiggleAmplitude + base.transform.up * Mathf.Cos(Time.time * this.wiggleSpeed + this.wiggleOffset) * this.wiggleAmplitude;
		base.transform.localRotation *= Quaternion.Euler(0f, 0f, Mathf.Sin(Time.time * this.wiggleSpeed + this.randomOffset) * this.wiggleRot * 0.01f);
	}

	// Token: 0x04000C1C RID: 3100
	public float wiggleAmplitude = 1f;

	// Token: 0x04000C1D RID: 3101
	public float wiggleSpeed = 1f;

	// Token: 0x04000C1E RID: 3102
	public float wiggleOffset = 1f;

	// Token: 0x04000C1F RID: 3103
	public float wiggleRot;

	// Token: 0x04000C20 RID: 3104
	public float randomOffset;

	// Token: 0x04000C21 RID: 3105
	private Vector3 startPos;
}
