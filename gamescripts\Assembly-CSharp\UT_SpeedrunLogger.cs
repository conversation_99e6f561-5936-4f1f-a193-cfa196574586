﻿using System;
using UnityEngine;

// Token: 0x020001B3 RID: 435
public class UT_SpeedrunLogger : MonoBehaviour
{
	// Token: 0x06000B62 RID: 2914 RVA: 0x00048B18 File Offset: 0x00046D18
	public void Log()
	{
		SpeedrunManager.AddEvent(this.logString);
	}

	// Token: 0x06000B63 RID: 2915 RVA: 0x00048B25 File Offset: 0x00046D25
	public void SaveLog()
	{
		SpeedrunManager.SaveLog();
	}

	// Token: 0x04000C4E RID: 3150
	public string logString;
}
