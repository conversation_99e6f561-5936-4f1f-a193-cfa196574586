﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200017C RID: 380
public class UT_LightGroupController : MonoBehaviour
{
	// Token: 0x06000A82 RID: 2690 RVA: 0x00044D00 File Offset: 0x00042F00
	private void Start()
	{
		this.lamps = base.gameObject.GetComponentsInChildren<CL_Lamp>();
		if (this.setOffAtStart)
		{
			CL_Lamp[] array = this.lamps;
			for (int i = 0; i < array.Length; i++)
			{
				array[i].lampActive = false;
			}
		}
	}

	// Token: 0x06000A83 RID: 2691 RVA: 0x00044D44 File Offset: 0x00042F44
	private void Update()
	{
	}

	// Token: 0x06000A84 RID: 2692 RVA: 0x00044D48 File Offset: 0x00042F48
	public void PlayAnimation(string anim)
	{
		foreach (UT_LightGroupController.LightGroupAnimation lightGroupAnimation in this.lightGroupAnimations)
		{
			if (lightGroupAnimation.name == anim)
			{
				base.StartCoroutine(this.AnimationPlayer(lightGroupAnimation));
				break;
			}
		}
	}

	// Token: 0x06000A85 RID: 2693 RVA: 0x00044DB4 File Offset: 0x00042FB4
	private IEnumerator AnimationPlayer(UT_LightGroupController.LightGroupAnimation animation)
	{
		float playbackTime = 0f;
		CL_Lamp[] array = this.lamps;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].lampActive = true;
		}
		while (playbackTime < 1f)
		{
			playbackTime += Time.deltaTime / animation.animationTime;
			array = this.lamps;
			for (int i = 0; i < array.Length; i++)
			{
				array[i].SetIntensityMultiplier(animation.animation.Evaluate(playbackTime));
			}
			yield return null;
		}
		if (animation.endEvent == UT_LightGroupController.LightGroupAnimation.EndEvent.deactivate)
		{
			array = this.lamps;
			for (int i = 0; i < array.Length; i++)
			{
				array[i].lampActive = false;
			}
		}
		yield break;
	}

	// Token: 0x04000B84 RID: 2948
	public List<UT_LightGroupController.LightGroupAnimation> lightGroupAnimations;

	// Token: 0x04000B85 RID: 2949
	private CL_Lamp[] lamps;

	// Token: 0x04000B86 RID: 2950
	public bool setOffAtStart;

	// Token: 0x020002DE RID: 734
	[Serializable]
	public class LightGroupAnimation
	{
		// Token: 0x0400124B RID: 4683
		public string name;

		// Token: 0x0400124C RID: 4684
		public float animationTime = 1f;

		// Token: 0x0400124D RID: 4685
		public AnimationCurve animation;

		// Token: 0x0400124E RID: 4686
		public UT_LightGroupController.LightGroupAnimation.EndEvent endEvent;

		// Token: 0x0200031B RID: 795
		public enum EndEvent
		{
			// Token: 0x0400133D RID: 4925
			none,
			// Token: 0x0400133E RID: 4926
			deactivate
		}
	}
}
