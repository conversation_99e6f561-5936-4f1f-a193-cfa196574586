﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000066 RID: 102
[Serializable]
public class SessionEventModule_ModuleSequencer : SessionEventModule
{
	// Token: 0x060003CE RID: 974 RVA: 0x0002340C File Offset: 0x0002160C
	public override void Initialize(SessionEvent s)
	{
		this.submodules = new List<SessionEventModule>();
		this.eventDict = new Dictionary<string, SessionEventModule_ModuleSequencer.SequenceEvent>();
		foreach (SessionEventModule_ModuleSequencer.SequenceEvent sequenceEvent in this.sequenceEvents)
		{
			this.eventDict.Add(sequenceEvent.name, sequenceEvent);
		}
		this.timer = 0f;
		base.Initialize(s);
	}

	// Token: 0x060003CF RID: 975 RVA: 0x00023494 File Offset: 0x00021694
	public override void Update()
	{
		this.timer += Time.deltaTime;
		base.Update();
		foreach (SessionEventModule_ModuleSequencer.SequenceEvent sequenceEvent in this.sequenceEvents)
		{
			if (!sequenceEvent.hasBeenPlayed && this.timer > sequenceEvent.time)
			{
				sequenceEvent.hasBeenPlayed = true;
				if (sequenceEvent.type == SessionEventModule_ModuleSequencer.SequenceEvent.SequenceEventType.addModule)
				{
					using (List<SessionEventModule>.Enumerator enumerator2 = sequenceEvent.modules.GetEnumerator())
					{
						while (enumerator2.MoveNext())
						{
							SessionEventModule sessionEventModule = enumerator2.Current;
							sessionEventModule.Initialize(this.sessionEvent);
							sessionEventModule.OnModuleStart();
							sessionEventModule.SetParentEvent(this);
							this.submodules.Add(sessionEventModule);
						}
						continue;
					}
				}
				if (sequenceEvent.type == SessionEventModule_ModuleSequencer.SequenceEvent.SequenceEventType.messageModule)
				{
					using (List<SessionEventModule>.Enumerator enumerator2 = this.eventDict[sequenceEvent.sequenceIDTarget].modules.GetEnumerator())
					{
						while (enumerator2.MoveNext())
						{
							SessionEventModule sessionEventModule2 = enumerator2.Current;
							sessionEventModule2.SendMessage(sequenceEvent.message);
						}
						continue;
					}
				}
				if (sequenceEvent.type == SessionEventModule_ModuleSequencer.SequenceEvent.SequenceEventType.deleteModule)
				{
					foreach (SessionEventModule sessionEventModule3 in this.eventDict[sequenceEvent.sequenceIDTarget].modules)
					{
						this.DestroyModule(sessionEventModule3);
					}
				}
			}
		}
		foreach (SessionEventModule sessionEventModule4 in this.submodules)
		{
			sessionEventModule4.Update();
		}
	}

	// Token: 0x060003D0 RID: 976 RVA: 0x000236C4 File Offset: 0x000218C4
	public void DestroyModule(SessionEventModule m)
	{
		this.submodules.Remove(m);
		m.OnModuleDestroy();
	}

	// Token: 0x0400052C RID: 1324
	public List<SessionEventModule_ModuleSequencer.SequenceEvent> sequenceEvents;

	// Token: 0x0400052D RID: 1325
	private Dictionary<string, SessionEventModule_ModuleSequencer.SequenceEvent> eventDict;

	// Token: 0x0400052E RID: 1326
	private float timer;

	// Token: 0x0400052F RID: 1327
	private List<SessionEventModule> submodules;

	// Token: 0x0200024C RID: 588
	[Serializable]
	public class SequenceEvent
	{
		// Token: 0x04000F3E RID: 3902
		public string name;

		// Token: 0x04000F3F RID: 3903
		public float time;

		// Token: 0x04000F40 RID: 3904
		public SessionEventModule_ModuleSequencer.SequenceEvent.SequenceEventType type;

		// Token: 0x04000F41 RID: 3905
		[SerializeReference]
		public List<SessionEventModule> modules;

		// Token: 0x04000F42 RID: 3906
		public string sequenceIDTarget;

		// Token: 0x04000F43 RID: 3907
		public string message;

		// Token: 0x04000F44 RID: 3908
		internal bool hasBeenPlayed;

		// Token: 0x02000306 RID: 774
		public enum SequenceEventType
		{
			// Token: 0x040012E5 RID: 4837
			addModule,
			// Token: 0x040012E6 RID: 4838
			messageModule,
			// Token: 0x040012E7 RID: 4839
			deleteModule
		}
	}
}
