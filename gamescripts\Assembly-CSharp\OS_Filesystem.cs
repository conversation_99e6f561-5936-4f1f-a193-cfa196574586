﻿using System;
using System.Collections.Generic;
using Unity.VisualScripting;
using UnityEngine;

// Token: 0x020000F9 RID: 249
public class OS_Filesystem : MonoBehaviour
{
	// Token: 0x06000791 RID: 1937 RVA: 0x00039847 File Offset: 0x00037A47
	private void OnEnable()
	{
	}

	// Token: 0x06000792 RID: 1938 RVA: 0x00039849 File Offset: 0x00037A49
	private void OnDisable()
	{
	}

	// Token: 0x06000793 RID: 1939 RVA: 0x0003984C File Offset: 0x00037A4C
	public void Initialize(OS_Manager o)
	{
		this.os = o;
		this.fileSystem = new List<OS_Filesystem.FileInfo>();
		OS_Filesystem.FileInfo fileInfo = new OS_Filesystem.FileInfo();
		fileInfo.name = "Desktop";
		fileInfo.location = "";
		fileInfo.type = OS_Filesystem.FileInfo.fileType.folder;
		fileInfo.open = true;
		fileInfo.targetWindow = this.desktop;
		List<OS_Filesystem.FileInfo> files = Object.Instantiate<OS_DiskData>(o.worldInterface.desktopData).GetFiles();
		fileInfo.children = files;
		this.desktop.file = Object.Instantiate<OS_File>(this.filePrefab, this.desktop.transform.position, Quaternion.identity, this.desktop.transform.parent);
		this.desktop.file.Initialize(fileInfo, this.desktop, this.os);
		this.desktop.file.gameObject.SetActive(false);
		this.fileSystem.Add(fileInfo);
		this.CreateFiles(files, this.desktop);
	}

	// Token: 0x06000794 RID: 1940 RVA: 0x00039945 File Offset: 0x00037B45
	private void Update()
	{
	}

	// Token: 0x06000795 RID: 1941 RVA: 0x00039948 File Offset: 0x00037B48
	public void CreateFiles(List<OS_Filesystem.FileInfo> fileList, OS_Folder folder)
	{
		if (fileList == null)
		{
			fileList = new List<OS_Filesystem.FileInfo>();
		}
		foreach (OS_Filesystem.FileInfo fileInfo in fileList)
		{
			Object.Instantiate<OS_File>(this.filePrefab, fileInfo.position, Quaternion.identity, folder.contents.transform).Initialize(fileInfo, folder, this.os);
			folder.AddFile(fileInfo);
		}
	}

	// Token: 0x06000796 RID: 1942 RVA: 0x000399D4 File Offset: 0x00037BD4
	public OS_File CreateFiles(OS_Filesystem.FileInfo file, OS_Folder folder)
	{
		OS_File os_File = Object.Instantiate<OS_File>(this.filePrefab, file.position, Quaternion.identity, folder.contents.transform);
		os_File.Initialize(file, folder, this.os);
		return os_File;
	}

	// Token: 0x06000797 RID: 1943 RVA: 0x00039A0C File Offset: 0x00037C0C
	public OS_File CreateFile(OS_Filesystem.FileInfo file, OS_Folder folder)
	{
		if (file.type == OS_Filesystem.FileInfo.fileType.folder)
		{
			if (file.icon == null)
			{
				file.icon = this.GetIcon("folder").icon;
				file.openIcon = this.GetIcon("folder").openIcon;
			}
			file.windowAsset = this.folderApp;
		}
		OS_File os_File = Object.Instantiate<OS_File>(this.filePrefab, file.position, Quaternion.identity);
		os_File.transform.SetParent(folder.contents.transform, true);
		os_File.Initialize(file, folder, this.os);
		folder.AddFile(file);
		return os_File;
	}

	// Token: 0x06000798 RID: 1944 RVA: 0x00039AAF File Offset: 0x00037CAF
	public void CreateFolder(string name, OS_Folder location, Sprite icon = null)
	{
	}

	// Token: 0x06000799 RID: 1945 RVA: 0x00039AB4 File Offset: 0x00037CB4
	public OS_Filesystem.IconType GetIcon(string name)
	{
		foreach (OS_Filesystem.IconType iconType in this.icons)
		{
			if (iconType.name == name)
			{
				return iconType;
			}
		}
		return this.icons[0];
	}

	// Token: 0x0600079A RID: 1946 RVA: 0x00039B20 File Offset: 0x00037D20
	public OS_Filesystem.FileInfo GetDefaultFiletype(string name)
	{
		foreach (OS_Filesystem.FileInfoDefault fileInfoDefault in this.defaultFiletypes)
		{
			if (fileInfoDefault.name == name)
			{
				return fileInfoDefault.info;
			}
		}
		return this.defaultFiletypes[0].info;
	}

	// Token: 0x04000906 RID: 2310
	private OS_Manager os;

	// Token: 0x04000907 RID: 2311
	public OS_Folder desktop;

	// Token: 0x04000908 RID: 2312
	public List<OS_Filesystem.FileInfo> desktopFiles;

	// Token: 0x04000909 RID: 2313
	public List<OS_Filesystem.FileInfo> fileSystem;

	// Token: 0x0400090A RID: 2314
	public OS_File filePrefab;

	// Token: 0x0400090B RID: 2315
	public List<OS_Filesystem.IconType> icons = new List<OS_Filesystem.IconType>();

	// Token: 0x0400090C RID: 2316
	public Sprite folderDefaultIcon;

	// Token: 0x0400090D RID: 2317
	public Sprite folderOpenDefaultIcon;

	// Token: 0x0400090E RID: 2318
	public OS_Folder folderApp;

	// Token: 0x0400090F RID: 2319
	public List<OS_Filesystem.FileInfoDefault> defaultFiletypes = new List<OS_Filesystem.FileInfoDefault>();

	// Token: 0x0200029D RID: 669
	[Serializable]
	public class FileInfo
	{
		// Token: 0x06000E85 RID: 3717 RVA: 0x0005823C File Offset: 0x0005643C
		internal void DeleteChild(OS_Filesystem.FileInfo file)
		{
			Debug.Log("Deleting Child: " + file.name);
			if (file.open)
			{
				file.fileObject.CloseFileRecursive();
			}
			this.children.Remove(file);
			Object.Destroy(file.fileObject.gameObject);
		}

		// Token: 0x040010CA RID: 4298
		public string name;

		// Token: 0x040010CB RID: 4299
		[HideInInspector]
		public string location = "Desktop/";

		// Token: 0x040010CC RID: 4300
		public OS_Filesystem.FileInfo.fileType type;

		// Token: 0x040010CD RID: 4301
		public string data;

		// Token: 0x040010CE RID: 4302
		public TextAsset textAssetData;

		// Token: 0x040010CF RID: 4303
		public Sprite imageAssetData;

		// Token: 0x040010D0 RID: 4304
		public bool useCustomFiletype = true;

		// Token: 0x040010D1 RID: 4305
		public bool openOnInitialize;

		// Token: 0x040010D2 RID: 4306
		internal bool hasInitialized;

		// Token: 0x040010D3 RID: 4307
		public List<OS_Filesystem.FileInfo> children;

		// Token: 0x040010D4 RID: 4308
		[DoNotSerialize]
		public OS_Filesystem.FileInfo parent;

		// Token: 0x040010D5 RID: 4309
		public Sprite icon;

		// Token: 0x040010D6 RID: 4310
		public Sprite openIcon;

		// Token: 0x040010D7 RID: 4311
		[HideInInspector]
		public OS_File fileObject;

		// Token: 0x040010D8 RID: 4312
		public Vector2 position;

		// Token: 0x040010D9 RID: 4313
		[HideInInspector]
		public OS_Window targetWindow;

		// Token: 0x040010DA RID: 4314
		public OS_Window windowAsset;

		// Token: 0x040010DB RID: 4315
		public bool useWorldPosition;

		// Token: 0x040010DC RID: 4316
		[HideInInspector]
		public bool open;

		// Token: 0x02000315 RID: 789
		public enum fileType
		{
			// Token: 0x04001321 RID: 4897
			program,
			// Token: 0x04001322 RID: 4898
			folder,
			// Token: 0x04001323 RID: 4899
			data,
			// Token: 0x04001324 RID: 4900
			alias,
			// Token: 0x04001325 RID: 4901
			image,
			// Token: 0x04001326 RID: 4902
			audio,
			// Token: 0x04001327 RID: 4903
			text
		}
	}

	// Token: 0x0200029E RID: 670
	[Serializable]
	public class FileInfoDefault
	{
		// Token: 0x040010DD RID: 4317
		public string name;

		// Token: 0x040010DE RID: 4318
		public OS_Filesystem.FileInfo info;
	}

	// Token: 0x0200029F RID: 671
	[Serializable]
	public class IconType
	{
		// Token: 0x040010DF RID: 4319
		public string name;

		// Token: 0x040010E0 RID: 4320
		public Sprite icon;

		// Token: 0x040010E1 RID: 4321
		public Sprite openIcon;
	}
}
