﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using Drawing;
using Pathfinding;
using UnityEngine;

// Token: 0x0200003F RID: 63
public class DEN_VentThing : AIGameEntity, AIGameEntity.Grappler
{
	// Token: 0x06000258 RID: 600 RVA: 0x00014E6B File Offset: 0x0001306B
	private void OnDisable()
	{
		if (this.hasGrabbedTarget)
		{
			this.DropTarget(true);
		}
	}

	// Token: 0x06000259 RID: 601 RVA: 0x00014E7C File Offset: 0x0001307C
	private void Awake()
	{
		if (DEN_VentThing.grabberVentThing == null)
		{
			DEN_VentThing.playerIsGrabbed = false;
		}
		this.seeker = base.gameObject.AddComponent<Seeker>();
		this.handRigidbody = this.hand.GetComponent<Rigidbody>();
		this.bodyAnimator = base.GetComponent<Animator>();
		this.SetTarget(ENT_Player.playerObject.transform);
		this.curHandStamina = this.handStamina;
		this.clipHandler.Initialize();
		this.clipHandler.GetGroup("hand").GetSet("fatigue").Play(0f, null);
		this.clipHandler.GetGroup("hand").GetSet("tension").Play(0f, null);
		this.arms = new List<Transform>();
		this.armRenderers = new List<MeshRenderer>();
		this.chasePlayerPath = new List<Vector3>();
		this.navOffset = Random.insideUnitSphere * 0.2f;
		if (base.transform.lossyScale.x < 0f || base.transform.lossyScale.y < 0f || base.transform.lossyScale.z < 0f)
		{
			base.transform.localScale = new Vector3(-1f, 1f, 1f);
		}
	}

	// Token: 0x0600025A RID: 602 RVA: 0x00014FD8 File Offset: 0x000131D8
	public override void Update()
	{
		base.Update();
		this.handAnimator.SetBool("retreating", this.retreating);
		if (this.reachPath != null && this.reachPath.vectorPath != null && this.currentWaypoint >= this.reachPath.vectorPath.Count && this.currentWaypoint > 0)
		{
			this.currentWaypoint = this.reachPath.vectorPath.Count - 1;
		}
		if (CL_UIManager.debug)
		{
			CL_DebugView.draw.Label2D(this.hand.position, string.Concat(new string[]
			{
				"Cur Waypoint: ",
				this.currentWaypoint.ToString(),
				" Last: ",
				this.lastWaypoint.ToString(),
				" Target Waypoint: ",
				this.targetWaypointIndex.ToString(),
				" Can See Target: ",
				this.targetVisible.ToString()
			}), 14f);
			if (this.reachPath != null && this.reachPath.vectorPath.Count >= this.currentWaypoint - 1)
			{
				CL_DebugView.draw.Arrow(this.hand.position, this.reachPath.vectorPath[this.currentWaypoint]);
				for (int i = 0; i < this.reachPath.vectorPath.Count - 1; i++)
				{
					CL_DebugView.draw.Arrow(this.reachPath.vectorPath[i] + this.navOffset, this.reachPath.vectorPath[i + 1] + this.navOffset, Color.red);
					CL_DebugView.draw.Label2D(this.reachPath.vectorPath[i] + this.navOffset, "RP:" + i.ToString(), 14f);
				}
				CL_DebugView.draw.Label2D(this.hand.position + Vector3.up, string.Concat(new string[]
				{
					"Reachpath Count: ",
					this.reachPath.vectorPath.Count.ToString(),
					" Repathing: ",
					this.repathing.ToString(),
					" Retreating: ",
					this.retreating.ToString()
				}), 14f);
			}
			if (this.chasePlayerPath != null && this.chasePlayerPath.Count > 0)
			{
				for (int j = 0; j < this.chasePlayerPath.Count - 1; j++)
				{
					CL_DebugView.draw.Arrow(this.chasePlayerPath[j], this.chasePlayerPath[j + 1], Color.red);
					CL_DebugView.draw.Label2D(this.chasePlayerPath[j], "CP:" + j.ToString(), 14f);
				}
			}
			if (this.targetPath != null && this.targetPath.vectorPath.Count >= this.currentWaypoint - 1)
			{
				for (int k = 0; k < this.targetPath.vectorPath.Count - 1; k++)
				{
					CL_DebugView.draw.Arrow(this.targetPath.vectorPath[k], this.targetPath.vectorPath[k + 1], Color.blue);
					CL_DebugView.draw.Label2D(this.targetPath.vectorPath[k] + Vector3.up, "TP:" + k.ToString(), Color.blue);
				}
			}
		}
	}

	// Token: 0x0600025B RID: 603 RVA: 0x000153D0 File Offset: 0x000135D0
	private void LateUpdate()
	{
		this.CalculateArms();
		this.sight.Update();
		this.targetPosition = this.target.position;
		this.targetDistance = Vector3.Distance(this.hand.position, this.target.position);
		this.distanceFromBody = Vector3.Distance(base.transform.position, this.target.position);
		if (Vector3.Distance(base.transform.position, this.hand.position) < 1f && this.retreating)
		{
			this.hand.position = base.transform.position;
		}
		if (this.distanceFromBody > this.activationDistance)
		{
			if (this.hasGrabbedTarget)
			{
				this.DropTarget(true);
			}
			this.retreatTime = 2f;
		}
		Vector3 vector = Vector3.zero;
		if (!this.hasGrabbedTarget)
		{
			this.curHandStamina = Mathf.Clamp(this.curHandStamina + Time.deltaTime, 0f, this.handStamina);
		}
		if (!this.hasGrabbedTarget)
		{
			if (Vector3.Distance(this.lastHandPosition, this.hand.position) < 0.2f * Time.deltaTime)
			{
				this.stuckTime += Time.deltaTime;
				if (this.stuckTime > 2f)
				{
					this.unstickTime = 1f;
				}
			}
			else
			{
				this.stuckTime = 0f;
			}
		}
		if ((this.unstickTime > 0f || this.retreating || this.repathing) && !this.hasGrabbedTarget)
		{
			this.unstickTime -= Time.deltaTime;
			this.handRigidbody.detectCollisions = false;
		}
		else
		{
			this.handRigidbody.detectCollisions = true;
		}
		this.lastHandPosition = this.hand.position;
		if (!this.targetIsCloseToBody && this.distanceFromBody < 15f)
		{
			this.targetIsCloseToBody = true;
			this.clipHandler.PlaySound("body:anger", base.transform.position);
		}
		else if (this.targetIsCloseToBody && this.distanceFromBody > 15f)
		{
			this.targetIsCloseToBody = false;
			this.clipHandler.PlaySound("body:targetescape", base.transform.position);
		}
		if (DEN_VentThing.playerIsGrabbed && !this.hasGrabbedTarget)
		{
			this.retreatTime = 2.5f;
			this.retreating = true;
		}
		else if (this.targetDistance < 15f && this.distanceFromBody > 8f && !this.hasGrabbedTarget && !this.retreating && ENT_Player.playerObject.camRoot.CanSeeTarget(this.hand, 45f) && !this.retreating)
		{
			if (!this.targetVisible)
			{
				this.handRigidbody.velocity = Vector3.zero;
				this.stuckTime = 0f;
				return;
			}
			this.retreatTime = 2.5f;
			this.retreating = true;
			if (this.targetWaypointIndex > 0)
			{
				this.targetWaypointIndex = 0;
			}
			if (this.targetDistance < 10f)
			{
				this.clipHandler.PlaySound("hand:fear", this.hand.position);
			}
			CL_CameraControl.ShakeAtPosition(this.hand.transform.position, 2.5f * Time.deltaTime, 20f);
		}
		this.handSpriteRenderer.color = Color.Lerp(Color.red, Color.white, this.curHandStamina / this.handStamina);
		UT_AudioClipHandler.AudioSet set = this.clipHandler.GetGroup("hand").GetSet("fatigue");
		set.SetVolume(Mathf.Lerp(set.GetCurrentVolume(), 1f - this.curHandStamina / this.handStamina, Time.deltaTime * 3f));
		if (DEN_VentThing.playerIsGrabbed)
		{
			UT_AudioClipHandler.AudioSet set2 = this.clipHandler.GetGroup("hand").GetSet("tension");
			set2.SetVolume(Mathf.Lerp(set2.GetCurrentVolume(), set2.maxVolume, Time.deltaTime * 1f));
		}
		else
		{
			UT_AudioClipHandler.AudioSet set3 = this.clipHandler.GetGroup("hand").GetSet("tension");
			set3.SetVolume(Mathf.Lerp(set3.GetCurrentVolume(), 0f, Time.deltaTime * 1f));
		}
		if (this.repathing)
		{
			this.targetWaypointIndex = 0;
		}
		if (this.retreatTime > 0f)
		{
			this.retreatTime -= Time.deltaTime;
			this.retreating = true;
			this.targetWaypointIndex = 0;
			this.isChasingPlayer = false;
		}
		else if (this.retreating)
		{
			this.isChasingPlayer = false;
			this.retreating = false;
		}
		if ((!this.targetVisible && !this.hasGrabbedTarget) || this.retreating)
		{
			this.NavigationUpdate();
			vector = this.GetPathVector();
		}
		else if (!this.retreating)
		{
			this.isChasingPlayer = false;
			if (!this.hasGrabbedTarget && this.targetDistance < 5f && this.targetVisible && !this.aboutToGrabTarget)
			{
				this.aboutToGrabTarget = true;
				this.clipHandler.PlaySound("hand:reachfortarget", this.hand.position);
			}
			else if (this.targetDistance > 8f)
			{
				this.aboutToGrabTarget = false;
			}
			if (!this.hasGrabbedTarget && this.targetDistance < 1f && this.targetVisible)
			{
				this.hasGrabbedTarget = true;
				this.clipHandler.PlaySound("hand:grabplayer", this.hand.position);
				this.bodyAnimator.SetBool("chomping", true);
				DEN_VentThing.playerIsGrabbed = true;
				DEN_VentThing.grabberVentThing = this;
				if (this.targetEntity != null)
				{
					this.targetEntity.Damage(1f, this.objectType);
				}
			}
			if (this.hasGrabbedTarget)
			{
				this.targetWaypointIndex = 0;
				vector = this.GetPathVector();
				this.PullTarget(vector);
				return;
			}
			if (this.targetVisible)
			{
				this.isChasingPlayer = true;
			}
			else
			{
				this.isChasingPlayer = false;
			}
			vector = this.ChasePlayer();
		}
		this.HandLocomotion(vector);
	}

	// Token: 0x0600025C RID: 604 RVA: 0x000159D8 File Offset: 0x00013BD8
	private Vector3 ChasePlayer()
	{
		Vector3 normalized = (this.targetPosition - this.hand.position).normalized;
		if (this.chasePlayerPath.Count == 0)
		{
			this.chasePlayerPath.Add(this.hand.position);
		}
		RaycastHit raycastHit;
		if (Physics.Linecast(this.chasePlayerPath[this.chasePlayerPath.Count - 1], this.hand.position, out raycastHit, this.sight.sightMask) && Vector3.Distance(raycastHit.point, this.chasePlayerPath[this.chasePlayerPath.Count - 1]) > 0.5f)
		{
			this.chasePlayerPath.Add(raycastHit.point + raycastHit.normal * 0.2f);
		}
		return normalized;
	}

	// Token: 0x0600025D RID: 605 RVA: 0x00015AB5 File Offset: 0x00013CB5
	private void StopChasingPlayer()
	{
		this.chasePlayerPath = new List<Vector3>();
	}

	// Token: 0x0600025E RID: 606 RVA: 0x00015AC2 File Offset: 0x00013CC2
	public void SetTarget(Transform t)
	{
		this.target = t;
		this.targetEntity = t.GetComponent<GameEntity>();
	}

	// Token: 0x0600025F RID: 607 RVA: 0x00015AD8 File Offset: 0x00013CD8
	public void NavigationUpdate()
	{
		if (this.pathCheckTime > 0f)
		{
			this.pathCheckTime -= Time.deltaTime;
			return;
		}
		float num = Vector3.Distance(this.lastTargetPosition, this.targetPosition);
		this.pathCheckTime = 1f;
		if (num > 1f || this.targetPath == null)
		{
			Vector3 position = this.hand.position;
			this.lastTargetPosition = this.targetPosition;
			this.targetPath = this.seeker.StartPath(base.transform.position, this.targetPosition, new OnPathDelegate(this.OnTargetPathComplete));
		}
		this.pathCheckTime = 1f;
	}

	// Token: 0x06000260 RID: 608 RVA: 0x00015B84 File Offset: 0x00013D84
	public override void TickUpdate()
	{
		base.TickUpdate();
		if (this.sight.CanSeeTarget(this.sight.eyeObject.position, this.target, this.sight.sightDistance, null))
		{
			this.targetVisible = true;
			return;
		}
		this.targetVisible = false;
	}

	// Token: 0x06000261 RID: 609 RVA: 0x00015BD8 File Offset: 0x00013DD8
	public void OnTargetPathComplete(Path p)
	{
		if (!p.error)
		{
			this.targetPath = p;
			if (this.reachPath == null)
			{
				this.reachPath = this.targetPath;
			}
			if (this.targetPath != null && this.reachPath != null)
			{
				this.repathTargetIndex = this.GetLastCommonNodeIndexOnPath(this.reachPath, this.targetPath);
			}
		}
	}

	// Token: 0x06000262 RID: 610 RVA: 0x00015C30 File Offset: 0x00013E30
	public int GetLastCommonNodeIndexOnPath(Path a, Path b)
	{
		for (int i = 0; i < a.path.Count; i++)
		{
			if (i >= b.path.Count)
			{
				return Mathf.Max(b.path.Count - 1, 0);
			}
			if (!(a.path[i].position == b.path[i].position))
			{
				return Mathf.Max(i - 1, 0);
			}
			if (i == a.path.Count - 1)
			{
				return i;
			}
		}
		return 0;
	}

	// Token: 0x06000263 RID: 611 RVA: 0x00015CBC File Offset: 0x00013EBC
	public void HandLocomotion(Vector3 vel)
	{
		Vector3 vector = vel;
		if (this.reachPath != null && this.reachPath.vectorPath != null && this.reachPath.vectorPath.Count >= this.currentWaypoint)
		{
			if (this.currentWaypoint >= 1)
			{
				vector = this.reachPath.vectorPath[this.currentWaypoint] - this.reachPath.vectorPath[this.currentWaypoint - 1];
			}
			else if (this.currentWaypoint == 0)
			{
				vector = this.hand.position - base.transform.position;
			}
		}
		if (!this.isChasingPlayer)
		{
			if (this.arms != null && this.arms.Count > 0)
			{
				if ((this.hand.position - this.arms[this.lastArmID].position).sqrMagnitude != 0f)
				{
					this.hand.rotation = Quaternion.Lerp(this.hand.rotation, Quaternion.LookRotation(this.hand.position - this.arms[this.lastArmID].position, Vector3.up), Time.deltaTime * 3f);
				}
			}
			else
			{
				this.hand.rotation = Quaternion.Lerp(this.hand.rotation, Quaternion.LookRotation(vector, Vector3.up), Time.deltaTime * 3f);
			}
		}
		else
		{
			this.hand.rotation = Quaternion.Lerp(this.hand.rotation, Quaternion.LookRotation(this.target.position - this.hand.position, Vector3.up), Time.deltaTime * 3f);
		}
		Vector3 vector2 = (this.hand.up * Mathf.Sin(Time.time * this.handbobFrequency) + this.hand.right * Mathf.Cos(Time.time * this.handbobFrequency)) * this.handbobAmplitude;
		float num = (this.repathing ? this.handRetreatSpeed : this.handSpeed);
		if (CL_GameManager.IsHardmode())
		{
			num *= 1.5f;
		}
		if (!this.retreating && this.targetDistance > 20f)
		{
			num *= this.outOfRangeSpeedMult;
		}
		else if (!this.retreating && this.targetVisible && !ENT_Player.playerObject.camRoot.CanSeeTarget(this.hand, 90f) && this.targetDistance < 5f)
		{
			num *= this.finalStrikeSpeedMult;
		}
		this.handRigidbody.velocity = vel * num + vector2;
	}

	// Token: 0x06000264 RID: 612 RVA: 0x00015F88 File Offset: 0x00014188
	public Vector3 GetPathVector()
	{
		if (this.reachPath == null || this.currentWaypoint >= this.reachPath.vectorPath.Count)
		{
			return (base.transform.position - this.hand.position).normalized;
		}
		Vector3 vector = (this.reachPath.vectorPath[this.currentWaypoint] + this.navOffset - this.hand.position).normalized;
		List<Vector3> list = new List<Vector3>();
		list.AddRange(this.reachPath.vectorPath);
		bool flag = false;
		if (this.chasePlayerPath.Count > 0)
		{
			list.AddRange(this.chasePlayerPath);
			flag = true;
		}
		if (flag)
		{
			float num = Vector3.Distance(this.hand.position, this.chasePlayerPath[this.chasePlayerPath.Count - 1]);
			vector = this.chasePlayerPath[this.chasePlayerPath.Count - 1] - this.hand.position;
			if (num < this.waypointSwitchDistance * 1.5f)
			{
				this.chasePlayerPath.RemoveAt(this.chasePlayerPath.Count - 1);
			}
			return vector;
		}
		this.reachedEndOfPath = false;
		if (this.repathTargetIndex >= 0)
		{
			this.targetWaypointIndex = this.repathTargetIndex;
		}
		bool flag2 = false;
		if ((this.targetWaypointIndex < this.currentWaypoint || this.retreating) && this.lastWaypoint <= this.currentWaypoint)
		{
			flag2 = true;
			vector = (this.reachPath.vectorPath[this.currentWaypoint] + this.navOffset - this.hand.position).normalized;
		}
		float num2 = Vector3.Distance(this.hand.position, list[this.currentWaypoint]);
		float num3 = this.waypointSwitchDistance;
		if (this.hasGrabbedTarget)
		{
			num3 *= 1.5f;
		}
		if (num2 < num3 * 1f)
		{
			flag2 = true;
		}
		if (flag2)
		{
			if (this.currentWaypoint < this.targetWaypointIndex && !this.retreating)
			{
				if (this.currentWaypoint + 1 < this.reachPath.vectorPath.Count)
				{
					this.lastWaypoint = this.currentWaypoint;
					this.currentWaypoint++;
				}
				else
				{
					this.reachedEndOfPath = true;
				}
				if (!this.hasGrabbedTarget)
				{
					this.repathing = false;
				}
			}
			else if (this.currentWaypoint > this.targetWaypointIndex || this.retreating)
			{
				if (this.currentWaypoint > 0)
				{
					this.lastWaypoint = this.currentWaypoint;
					this.currentWaypoint--;
				}
				if (!this.hasGrabbedTarget)
				{
					this.repathing = true;
				}
				vector = (this.reachPath.vectorPath[this.currentWaypoint] + this.navOffset - this.hand.position).normalized;
			}
			else if (this.currentWaypoint == this.targetWaypointIndex)
			{
				this.reachPath = this.targetPath;
				this.targetWaypointIndex = this.targetPath.path.Count - 1;
				this.repathTargetIndex = -1;
				this.lastWaypoint = this.currentWaypoint;
				if (!this.hasGrabbedTarget)
				{
					this.repathing = false;
				}
			}
		}
		return vector;
	}

	// Token: 0x06000265 RID: 613 RVA: 0x000162D4 File Offset: 0x000144D4
	public void PullTarget(Vector3 dir)
	{
		if (this.hasGrabbedTarget)
		{
			this.targetWaypointIndex = 0;
			Vector3 vector = Vector3.zero;
			if (this.reachPath != null)
			{
				vector = dir;
			}
			else
			{
				vector = base.transform.position - this.hand.position;
			}
			if (this.targetEntity != null)
			{
				this.hand.position = this.targetPosition + vector.normalized * 1f;
				this.targetEntity.AddForce(vector.normalized * Time.deltaTime * this.pullForce);
				if (this.targetEntity == ENT_Player.playerObject)
				{
					ENT_Player.playerObject.SetGrappled(true, this);
					ENT_Player.playerObject.SetGravity(false);
					ENT_Player.playerObject.SetGravityMult(0f);
					this.handSpriteRenderer.gameObject.SetActive(true);
					this.handRenderer.gameObject.SetActive(false);
					Transform transform = ENT_Player.playerObject.cam.transform;
					this.grabTime = Mathf.Lerp(this.grabTime, 1f, Time.deltaTime * 5f);
					this.shakeTime -= Time.deltaTime;
					if (this.shakeTime <= 0f)
					{
						this.shakeTime = 0.1f;
						this.handShake = Random.insideUnitSphere * (1f - this.curHandStamina / this.handStamina) * 0.02f;
					}
					Vector3 vector2 = transform.TransformPoint(this.handOffset);
					this.handSpriteRenderer.transform.position = Vector3.Lerp(vector2 + -transform.up, vector2, this.grabTime) + this.handShake;
					this.handSpriteRenderer.transform.rotation = transform.rotation;
					Quaternion rotation = ENT_Player.playerObject.transform.rotation;
					if (this.distanceFromBody > 10f)
					{
						if (Vector3.Angle(-Vector3.Scale(vector, new Vector3(1f, 0f, 1f)), ENT_Player.playerObject.transform.forward) > 60f)
						{
							ENT_Player.playerObject.transform.rotation = Quaternion.Lerp(rotation, Quaternion.LookRotation(Vector3.Scale(-vector, new Vector3(1f, 0f, 1f)), Vector3.up), Time.deltaTime * 2f);
						}
					}
					else if (Vector3.Angle(Vector3.Scale(vector, new Vector3(1f, 0f, 1f)), ENT_Player.playerObject.transform.forward) > 10f)
					{
						ENT_Player.playerObject.transform.rotation = Quaternion.Lerp(rotation, Quaternion.LookRotation(Vector3.Scale(vector, new Vector3(1f, 0f, 1f)), Vector3.up), Time.deltaTime * 2f);
					}
					if (this.distanceFromBody < 2f)
					{
						this.DropTarget(true);
						this.targetEntity.Kill(this.objectType);
					}
					if (ENT_Player.playerObject.IsHanging())
					{
						float num = 1f;
						if (CL_GameManager.IsHardmode())
						{
							num = 1.5f;
						}
						ENT_Player.playerObject.AddGripStrength(-this.playerStaminaDrainMult * Time.deltaTime * num, true);
						CL_CameraControl.Shake(0.05f * Time.deltaTime);
						this.curHandStamina = Mathf.Clamp(this.curHandStamina - Time.deltaTime, 0f, this.handStamina);
						if (this.curHandStamina <= 0f)
						{
							this.DropTarget(true);
							return;
						}
						this.stuckPosition = this.hand.position;
					}
					else if (!ENT_Player.playerObject.IsHanging())
					{
						if (Vector3.Distance(this.stuckPosition, this.hand.position) < 1f)
						{
							this.stuckTime += Time.deltaTime;
							if (this.stuckTime > 5f)
							{
								this.DropTarget(true);
								this.stuckTime = 0f;
								return;
							}
						}
						else
						{
							this.stuckPosition = this.hand.position;
							this.stuckTime = 0f;
						}
						ENT_Player.playerObject.AddGripStrength(this.playerStaminaRecoveryMult * Time.deltaTime, false);
					}
					CL_UIManager.instance.SetVignetteTarget("bloodied", 0.8f);
					CL_UIManager.instance.SetVignetteTarget("fade", 0.5f);
					CL_UIManager.instance.SetVignetteTarget("hurt", 0.5f);
					return;
				}
				Debug.Log("Grabbed Something Other Than Player");
				return;
			}
			else
			{
				this.target.position = this.hand.position + -vector.normalized * 1f;
			}
		}
	}

	// Token: 0x06000266 RID: 614 RVA: 0x000167B4 File Offset: 0x000149B4
	public void DropTarget(bool damage = true)
	{
		this.clipHandler.PlaySound("hand:dropplayer", this.hand.position);
		this.grabTime = 0f;
		this.hasGrabbedTarget = false;
		this.retreatTime = 5f;
		this.handRenderer.gameObject.SetActive(true);
		this.bodyAnimator.SetBool("chomping", false);
		if (this.targetEntity != null)
		{
			if (this.targetEntity == ENT_Player.playerObject)
			{
				DEN_VentThing.playerIsGrabbed = false;
				DEN_VentThing.grabberVentThing = null;
				ENT_Player.playerObject.SetGravity(true);
				ENT_Player.playerObject.SetGrappled(false, this);
				ENT_Player.playerObject.SetGravityMult(1f);
				CL_UIManager.instance.SetVignetteTarget("bloodied", 0f);
				CL_UIManager.instance.SetVignetteTarget("fade", 0f);
				CL_UIManager.instance.SetVignetteTarget("hurt", 0f);
				if (base.gameObject != null)
				{
					base.StartCoroutine(this.DropHandSprite());
				}
			}
			if (damage)
			{
				this.targetEntity.Damage(2f, this.objectType);
			}
		}
	}

	// Token: 0x06000267 RID: 615 RVA: 0x000168E4 File Offset: 0x00014AE4
	private IEnumerator DropHandSprite()
	{
		Transform playerCamera = ENT_Player.playerObject.cam.transform;
		float dropTime = 1f;
		while (dropTime > 0f)
		{
			dropTime -= Time.deltaTime;
			Vector3 vector = playerCamera.TransformPoint(this.handOffset);
			this.handSpriteRenderer.transform.position = Vector3.Lerp(vector + -playerCamera.up, vector, dropTime);
			this.handSpriteRenderer.transform.rotation = playerCamera.rotation;
			yield return null;
		}
		this.handSpriteRenderer.gameObject.SetActive(false);
		yield break;
	}

	// Token: 0x06000268 RID: 616 RVA: 0x000168F4 File Offset: 0x00014AF4
	private void CalculateArms()
	{
		List<Vector3> list = new List<Vector3>();
		if (this.currentWaypoint > 0)
		{
			for (int i = 0; i <= this.currentWaypoint; i++)
			{
				list.Add(this.reachPath.vectorPath[i] + this.navOffset);
			}
		}
		if (this.lastWaypoint <= this.currentWaypoint && list.Count > this.currentWaypoint)
		{
			list.RemoveAt(this.currentWaypoint);
		}
		if (this.chasePlayerPath.Count > 0)
		{
			list.AddRange(this.chasePlayerPath);
		}
		for (int j = 0; j < list.Count; j++)
		{
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.SphereOutline(list[j], 1f, Color.magenta);
			}
		}
		int num = list.Count - 1;
		for (int k = 0; k < Mathf.Max(list.Count, Mathf.Max(this.arms.Count, 1)); k++)
		{
			if ((k >= this.arms.Count && this.arms.Count < 50) || this.arms.Count == 0)
			{
				this.arms.Add(Object.Instantiate<GameObject>(this.armAsset, base.transform).transform);
				this.armRenderers.Add(this.arms[this.arms.Count - 1].GetComponent<MeshRenderer>());
			}
			if (k == num)
			{
				this.lastArmID = k;
			}
			if (k > num && k > 0)
			{
				if (this.arms[k].gameObject.activeSelf)
				{
					this.arms[k].gameObject.SetActive(false);
				}
			}
			else
			{
				if (!this.arms[k].gameObject.activeSelf)
				{
					this.arms[k].gameObject.SetActive(true);
				}
				bool flag = list != null && list.Count > num;
				Vector3 vector;
				if (k > 0 && flag)
				{
					vector = list[k];
				}
				else
				{
					vector = base.transform.position;
				}
				Vector3 vector2;
				if (num > k && flag)
				{
					vector2 = list[k + 1];
				}
				else
				{
					vector2 = this.hand.position;
				}
				this.arms[k].position = vector;
				if (vector2 - vector != Vector3.zero)
				{
					this.arms[k].rotation = Quaternion.LookRotation(vector2 - vector, Vector3.up);
				}
				this.arms[k].localScale = new Vector3(1f, 1f, Vector3.Distance(vector2, vector));
				float num2 = Mathf.Clamp(this.arms[k].localScale.z, 0f, 1f);
				this.armRenderers[k].material.SetFloat("_Slack", num2 * 0.1f);
				this.armRenderers[k].material.SetFloat("_Wiggle", num2 * 0.1f);
			}
		}
	}

	// Token: 0x06000269 RID: 617 RVA: 0x00016C2D File Offset: 0x00014E2D
	public override bool Damage(float amount, string type)
	{
		this.DropTarget(true);
		this.retreatTime = 5f;
		this.retreating = true;
		return base.Damage(amount, type);
	}

	// Token: 0x0600026A RID: 618 RVA: 0x00016C50 File Offset: 0x00014E50
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			Draw.WireSphere(base.transform.position, this.activationDistance, Color.yellow);
		}
	}

	// Token: 0x0600026B RID: 619 RVA: 0x00016C7A File Offset: 0x00014E7A
	public void ReleaseGrapple()
	{
		Debug.Log("Releasing!");
		this.DropTarget(false);
		this.retreatTime = 5f;
		this.retreating = true;
		this.handSpriteRenderer.gameObject.SetActive(false);
	}

	// Token: 0x0600026C RID: 620 RVA: 0x00016CB0 File Offset: 0x00014EB0
	public override void OffsetEntity(float amount)
	{
		base.OffsetEntity(amount);
		for (int i = 0; i < this.chasePlayerPath.Count; i++)
		{
			List<Vector3> list = this.chasePlayerPath;
			int num = i;
			list[num] += Vector3.up * amount;
		}
		base.StartCoroutine(this.<OffsetEntity>g__WaitOneFrame|77_0());
		this.lastHandPosition += Vector3.up * amount;
	}

	// Token: 0x0600026E RID: 622 RVA: 0x00016E10 File Offset: 0x00015010
	[CompilerGenerated]
	private IEnumerator <OffsetEntity>g__WaitOneFrame|77_0()
	{
		this.currentWaypoint = 0;
		yield return null;
		this.targetPath = this.seeker.StartPath(base.transform.position, this.hand.position, new OnPathDelegate(this.OnTargetPathComplete));
		this.reachPath = this.targetPath;
		this.currentWaypoint = 0;
		this.hand.position = base.transform.position;
		yield break;
	}

	// Token: 0x04000343 RID: 835
	public Transform hand;

	// Token: 0x04000344 RID: 836
	private Rigidbody handRigidbody;

	// Token: 0x04000345 RID: 837
	public GameObject handRenderer;

	// Token: 0x04000346 RID: 838
	public Animator handAnimator;

	// Token: 0x04000347 RID: 839
	private Animator bodyAnimator;

	// Token: 0x04000348 RID: 840
	public SpriteRenderer handSpriteRenderer;

	// Token: 0x04000349 RID: 841
	public Vector3 handOffset = new Vector3(0.5f, 0.5f, 0f);

	// Token: 0x0400034A RID: 842
	private Seeker seeker;

	// Token: 0x0400034B RID: 843
	private Path reachPath;

	// Token: 0x0400034C RID: 844
	private Path targetPath;

	// Token: 0x0400034D RID: 845
	private List<Vector3> chasePlayerPath;

	// Token: 0x0400034E RID: 846
	private bool isChasingPlayer;

	// Token: 0x0400034F RID: 847
	public float activationDistance = 50f;

	// Token: 0x04000350 RID: 848
	public float handSpeed = 1f;

	// Token: 0x04000351 RID: 849
	public float outOfRangeSpeedMult = 3f;

	// Token: 0x04000352 RID: 850
	public float finalStrikeSpeedMult = 2f;

	// Token: 0x04000353 RID: 851
	public float handRetreatSpeed = 7f;

	// Token: 0x04000354 RID: 852
	public float pullForce = 2f;

	// Token: 0x04000355 RID: 853
	private bool retreating;

	// Token: 0x04000356 RID: 854
	private bool repathing;

	// Token: 0x04000357 RID: 855
	public float handbobAmplitude = 0.5f;

	// Token: 0x04000358 RID: 856
	public float handbobFrequency = 1f;

	// Token: 0x04000359 RID: 857
	private Vector3 lastTargetPosition;

	// Token: 0x0400035A RID: 858
	private bool targetVisible;

	// Token: 0x0400035B RID: 859
	private GameEntity targetEntity;

	// Token: 0x0400035C RID: 860
	private float targetDistance;

	// Token: 0x0400035D RID: 861
	private float distanceFromBody;

	// Token: 0x0400035E RID: 862
	private bool aboutToGrabTarget;

	// Token: 0x0400035F RID: 863
	private float stuckTime;

	// Token: 0x04000360 RID: 864
	private float unstickTime;

	// Token: 0x04000361 RID: 865
	private Vector3 stuckPosition;

	// Token: 0x04000362 RID: 866
	private Vector3 lastHandPosition;

	// Token: 0x04000363 RID: 867
	public float waypointSwitchDistance = 0.5f;

	// Token: 0x04000364 RID: 868
	private int currentWaypoint;

	// Token: 0x04000365 RID: 869
	private int lastWaypoint;

	// Token: 0x04000366 RID: 870
	private int targetWaypointIndex;

	// Token: 0x04000367 RID: 871
	private int repathTargetIndex = -1;

	// Token: 0x04000368 RID: 872
	private bool reachedEndOfPath;

	// Token: 0x04000369 RID: 873
	private float pathCheckTime = 1f;

	// Token: 0x0400036A RID: 874
	private bool findingPath;

	// Token: 0x0400036B RID: 875
	private bool hasGrabbedTarget;

	// Token: 0x0400036C RID: 876
	private float grabTime;

	// Token: 0x0400036D RID: 877
	private float curHandStamina = 5f;

	// Token: 0x0400036E RID: 878
	public float handStamina = 5f;

	// Token: 0x0400036F RID: 879
	private float retreatTime;

	// Token: 0x04000370 RID: 880
	private Vector3 handShake = Vector3.zero;

	// Token: 0x04000371 RID: 881
	private float shakeTime = 0.1f;

	// Token: 0x04000372 RID: 882
	public float playerStaminaDrainMult = 1f;

	// Token: 0x04000373 RID: 883
	public float playerStaminaRecoveryMult = 0.3f;

	// Token: 0x04000374 RID: 884
	public GameObject armAsset;

	// Token: 0x04000375 RID: 885
	private List<Transform> arms;

	// Token: 0x04000376 RID: 886
	private List<MeshRenderer> armRenderers;

	// Token: 0x04000377 RID: 887
	private int lastArmID;

	// Token: 0x04000378 RID: 888
	private bool targetIsCloseToBody;

	// Token: 0x04000379 RID: 889
	private Vector3 navOffset;

	// Token: 0x0400037A RID: 890
	private static bool playerIsGrabbed;

	// Token: 0x0400037B RID: 891
	private static DEN_VentThing grabberVentThing;
}
