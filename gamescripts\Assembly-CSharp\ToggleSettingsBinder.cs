﻿using System;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000116 RID: 278
public class ToggleSettingsBinder : MonoBehaviour
{
	// Token: 0x06000859 RID: 2137 RVA: 0x0003C738 File Offset: 0x0003A938
	private void Start()
	{
		this.toggle = base.GetComponent<Toggle>();
		this.PullSetting();
		this.toggle.onValueChanged.AddListener(delegate(bool value)
		{
			this.UpdateSetting();
		});
		this.RefreshInteractable();
	}

	// Token: 0x0600085A RID: 2138 RVA: 0x0003C76E File Offset: 0x0003A96E
	private void OnEnable()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.OnSettingsChange));
	}

	// Token: 0x0600085B RID: 2139 RVA: 0x0003C790 File Offset: 0x0003A990
	private void OnDisable()
	{
		SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Remove(SettingsManager.SettingsRefreshUpdate, new Action(this.OnSettingsChange));
	}

	// Token: 0x0600085C RID: 2140 RVA: 0x0003C7B4 File Offset: 0x0003A9B4
	private void PullSetting()
	{
		string setting = SettingsManager.GetSetting(this.settingName);
		this.toggle.isOn = bool.Parse(setting);
	}

	// Token: 0x0600085D RID: 2141 RVA: 0x0003C7E0 File Offset: 0x0003A9E0
	public void UpdateSetting()
	{
		SettingsManager.SetSetting(new string[]
		{
			this.settingName,
			this.toggle.isOn.ToString()
		});
		SettingsManager.RefreshSettings(this.settingName);
	}

	// Token: 0x0600085E RID: 2142 RVA: 0x0003C822 File Offset: 0x0003AA22
	public void OnSettingsChange()
	{
		this.RefreshInteractable();
	}

	// Token: 0x0600085F RID: 2143 RVA: 0x0003C82C File Offset: 0x0003AA2C
	public void RefreshInteractable()
	{
		if (this.tiedToSetting)
		{
			if (this.toggle == null)
			{
				return;
			}
			bool flag = bool.Parse(SettingsManager.GetSetting(this.tiedSetting));
			this.toggle.interactable = (this.invertTiedActive ? flag : (!flag));
		}
	}

	// Token: 0x040009BA RID: 2490
	public string settingName;

	// Token: 0x040009BB RID: 2491
	private Toggle toggle;

	// Token: 0x040009BC RID: 2492
	public bool tiedToSetting;

	// Token: 0x040009BD RID: 2493
	public bool invertTiedActive;

	// Token: 0x040009BE RID: 2494
	public string tiedSetting;
}
