﻿using System;
using UnityEngine;

namespace DarkMachine.AI
{
	// Token: 0x020001D8 RID: 472
	public class AIC_Teeth_Retreat : AIStateComponent
	{
		// Token: 0x1700000D RID: 13
		// (get) Token: 0x06000BED RID: 3053 RVA: 0x0004BBF8 File Offset: 0x00049DF8
		public override string name
		{
			get
			{
				return "retreat";
			}
		}

		// Token: 0x06000BEE RID: 3054 RVA: 0x0004BBFF File Offset: 0x00049DFF
		public override void Enter(AIGameEntity e, string[] args = null)
		{
			base.Enter(e, args);
			e.clipHandler.PlaySound("teeth:retreat");
			this.retreatTimer = this.retreatLength;
			this.teeth = e.GetComponent<DEN_Teeth>();
		}

		// Token: 0x06000BEF RID: 3055 RVA: 0x0004BC34 File Offset: 0x00049E34
		public override void Execute(AIGameEntity e, string[] args = null)
		{
			base.Execute(e, args);
			this.teeth.SetCohesionEffect(0.6f);
			e.targetPosition = e.transform.position - Vector3.up * (float)((e.target.position.y > e.transform.position.y) ? 20 : (-20));
			this.retreatTimer -= Time.deltaTime;
			if (this.retreatTimer <= 0f)
			{
				e.ChangeState("burrow");
			}
		}

		// Token: 0x04000CEE RID: 3310
		public float retreatLength = 9f;

		// Token: 0x04000CEF RID: 3311
		private float retreatTimer = 5f;

		// Token: 0x04000CF0 RID: 3312
		private DEN_Teeth teeth;
	}
}
