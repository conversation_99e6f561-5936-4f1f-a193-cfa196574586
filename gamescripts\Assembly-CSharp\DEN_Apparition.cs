﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200002E RID: 46
public class DEN_Apparition : MonoBehaviour
{
	// Token: 0x060001A4 RID: 420 RVA: 0x0000C314 File Offset: 0x0000A514
	private void OnEnable()
	{
		this.apparitionSprite.sprite = this.randomSprites[Random.Range(0, this.randomSprites.Count)];
		this.startPosition = base.transform.position;
	}

	// Token: 0x060001A5 RID: 421 RVA: 0x0000C350 File Offset: 0x0000A550
	private void Update()
	{
		Vector2 vector = new Vector2(Math<PERSON>.<PERSON><PERSON>oise(this.startPosition.y - Time.time * this.wanderSpeed, this.startPosition.x - Time.time * this.wanderSpeed), Mathf.PerlinNoise(this.startPosition.x + Time.time * this.wanderSpeed, this.startPosition.y + Time.time * this.wanderSpeed));
		vector -= Vector2.one * 0.5f;
		base.transform.position = this.startPosition + new Vector3(vector.x, 0f, vector.y) * this.wanderAmount;
	}

	// Token: 0x04000186 RID: 390
	public SpriteRenderer apparitionSprite;

	// Token: 0x04000187 RID: 391
	private Transform apparitionTransform;

	// Token: 0x04000188 RID: 392
	public List<Sprite> randomSprites;

	// Token: 0x04000189 RID: 393
	public float wanderAmount = 2f;

	// Token: 0x0400018A RID: 394
	public float wanderSpeed = 1f;

	// Token: 0x0400018B RID: 395
	private Vector3 startPosition;
}
