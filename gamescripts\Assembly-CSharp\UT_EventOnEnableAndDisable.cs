﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000166 RID: 358
public class UT_EventOnEnableAndDisable : MonoBehaviour
{
	// Token: 0x06000A10 RID: 2576 RVA: 0x00043845 File Offset: 0x00041A45
	private void OnEnable()
	{
		this.enableEvent.Invoke();
	}

	// Token: 0x06000A11 RID: 2577 RVA: 0x00043852 File Offset: 0x00041A52
	private void OnDisable()
	{
		this.disableEvent.Invoke();
	}

	// Token: 0x04000B2E RID: 2862
	public UnityEvent enableEvent;

	// Token: 0x04000B2F RID: 2863
	public UnityEvent disableEvent;
}
