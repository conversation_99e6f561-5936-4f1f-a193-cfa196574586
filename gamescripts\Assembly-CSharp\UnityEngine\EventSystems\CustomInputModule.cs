﻿using System;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace UnityEngine.EventSystems
{
	// Token: 0x020001DC RID: 476
	[AddComponentMenu("Event/Custom Input Module")]
	public class CustomInputModule : PointerInputModule
	{
		// Token: 0x06000BFD RID: 3069 RVA: 0x0004C058 File Offset: 0x0004A258
		protected CustomInputModule()
		{
		}

		// Token: 0x1700000E RID: 14
		// (get) Token: 0x06000BFE RID: 3070 RVA: 0x0004C0AD File Offset: 0x0004A2AD
		[Obsolete("Mode is no longer needed on input module as it handles both mouse and keyboard simultaneously.", false)]
		public CustomInputModule.InputMode inputMode
		{
			get
			{
				return CustomInputModule.InputMode.Mouse;
			}
		}

		// Token: 0x1700000F RID: 15
		// (get) Token: 0x06000BFF RID: 3071 RVA: 0x0004C0B0 File Offset: 0x0004A2B0
		// (set) Token: 0x06000C00 RID: 3072 RVA: 0x0004C0B8 File Offset: 0x0004A2B8
		[Obsolete("allowActivationOnMobileDevice has been deprecated. Use forceModuleActive instead (UnityUpgradable) -> forceModuleActive")]
		public bool allowActivationOnMobileDevice
		{
			get
			{
				return this.m_ForceModuleActive;
			}
			set
			{
				this.m_ForceModuleActive = value;
			}
		}

		// Token: 0x17000010 RID: 16
		// (get) Token: 0x06000C01 RID: 3073 RVA: 0x0004C0C1 File Offset: 0x0004A2C1
		// (set) Token: 0x06000C02 RID: 3074 RVA: 0x0004C0C9 File Offset: 0x0004A2C9
		public bool forceModuleActive
		{
			get
			{
				return this.m_ForceModuleActive;
			}
			set
			{
				this.m_ForceModuleActive = value;
			}
		}

		// Token: 0x17000011 RID: 17
		// (get) Token: 0x06000C03 RID: 3075 RVA: 0x0004C0D2 File Offset: 0x0004A2D2
		// (set) Token: 0x06000C04 RID: 3076 RVA: 0x0004C0DA File Offset: 0x0004A2DA
		public float inputActionsPerSecond
		{
			get
			{
				return this.m_InputActionsPerSecond;
			}
			set
			{
				this.m_InputActionsPerSecond = value;
			}
		}

		// Token: 0x17000012 RID: 18
		// (get) Token: 0x06000C05 RID: 3077 RVA: 0x0004C0E3 File Offset: 0x0004A2E3
		// (set) Token: 0x06000C06 RID: 3078 RVA: 0x0004C0EB File Offset: 0x0004A2EB
		public float repeatDelay
		{
			get
			{
				return this.m_RepeatDelay;
			}
			set
			{
				this.m_RepeatDelay = value;
			}
		}

		// Token: 0x17000013 RID: 19
		// (get) Token: 0x06000C07 RID: 3079 RVA: 0x0004C0F4 File Offset: 0x0004A2F4
		// (set) Token: 0x06000C08 RID: 3080 RVA: 0x0004C0FC File Offset: 0x0004A2FC
		public string horizontalAxis
		{
			get
			{
				return this.m_HorizontalAxis;
			}
			set
			{
				this.m_HorizontalAxis = value;
			}
		}

		// Token: 0x17000014 RID: 20
		// (get) Token: 0x06000C09 RID: 3081 RVA: 0x0004C105 File Offset: 0x0004A305
		// (set) Token: 0x06000C0A RID: 3082 RVA: 0x0004C10D File Offset: 0x0004A30D
		public string verticalAxis
		{
			get
			{
				return this.m_VerticalAxis;
			}
			set
			{
				this.m_VerticalAxis = value;
			}
		}

		// Token: 0x17000015 RID: 21
		// (get) Token: 0x06000C0B RID: 3083 RVA: 0x0004C116 File Offset: 0x0004A316
		// (set) Token: 0x06000C0C RID: 3084 RVA: 0x0004C11E File Offset: 0x0004A31E
		public string submitButton
		{
			get
			{
				return this.m_SubmitButton;
			}
			set
			{
				this.m_SubmitButton = value;
			}
		}

		// Token: 0x17000016 RID: 22
		// (get) Token: 0x06000C0D RID: 3085 RVA: 0x0004C127 File Offset: 0x0004A327
		// (set) Token: 0x06000C0E RID: 3086 RVA: 0x0004C12F File Offset: 0x0004A32F
		public string cancelButton
		{
			get
			{
				return this.m_CancelButton;
			}
			set
			{
				this.m_CancelButton = value;
			}
		}

		// Token: 0x06000C0F RID: 3087 RVA: 0x0004C138 File Offset: 0x0004A338
		private bool ShouldIgnoreEventsOnNoFocus()
		{
			OperatingSystemFamily operatingSystemFamily = SystemInfo.operatingSystemFamily;
			return operatingSystemFamily - OperatingSystemFamily.MacOSX <= 2;
		}

		// Token: 0x06000C10 RID: 3088 RVA: 0x0004C154 File Offset: 0x0004A354
		public override void UpdateModule()
		{
			if (!base.eventSystem.isFocused && this.ShouldIgnoreEventsOnNoFocus())
			{
				if (this.m_InputPointerEvent != null && this.m_InputPointerEvent.pointerDrag != null && this.m_InputPointerEvent.dragging)
				{
					this.ReleaseMouse(this.m_InputPointerEvent, this.m_InputPointerEvent.pointerCurrentRaycast.gameObject);
				}
				this.m_InputPointerEvent = null;
				return;
			}
			this.m_LastMousePosition = this.m_MousePosition;
			this.m_MousePosition = this.customPointer.anchoredPosition * this.canvasScaler.scaleFactor;
		}

		// Token: 0x06000C11 RID: 3089 RVA: 0x0004C1F4 File Offset: 0x0004A3F4
		private void ReleaseMouse(PointerEventData pointerEvent, GameObject currentOverGo)
		{
			ExecuteEvents.Execute<IPointerUpHandler>(pointerEvent.pointerPress, pointerEvent, ExecuteEvents.pointerUpHandler);
			GameObject eventHandler = ExecuteEvents.GetEventHandler<IPointerClickHandler>(currentOverGo);
			if (pointerEvent.pointerPress == eventHandler && pointerEvent.eligibleForClick)
			{
				ExecuteEvents.Execute<IPointerClickHandler>(pointerEvent.pointerPress, pointerEvent, ExecuteEvents.pointerClickHandler);
			}
			else if (pointerEvent.pointerDrag != null && pointerEvent.dragging)
			{
				ExecuteEvents.ExecuteHierarchy<IDropHandler>(currentOverGo, pointerEvent, ExecuteEvents.dropHandler);
			}
			pointerEvent.eligibleForClick = false;
			pointerEvent.pointerPress = null;
			pointerEvent.rawPointerPress = null;
			if (pointerEvent.pointerDrag != null && pointerEvent.dragging)
			{
				ExecuteEvents.Execute<IEndDragHandler>(pointerEvent.pointerDrag, pointerEvent, ExecuteEvents.endDragHandler);
			}
			pointerEvent.dragging = false;
			pointerEvent.pointerDrag = null;
			if (currentOverGo != pointerEvent.pointerEnter)
			{
				this.HandleOSPointerExitAndEnter(pointerEvent, null);
				this.HandleOSPointerExitAndEnter(pointerEvent, currentOverGo);
			}
			this.m_InputPointerEvent = pointerEvent;
		}

		// Token: 0x06000C12 RID: 3090 RVA: 0x0004C2D7 File Offset: 0x0004A4D7
		public override bool IsModuleSupported()
		{
			return this.m_ForceModuleActive || base.input.mousePresent || base.input.touchSupported;
		}

		// Token: 0x06000C13 RID: 3091 RVA: 0x0004C2FC File Offset: 0x0004A4FC
		public override bool ShouldActivateModule()
		{
			if (!base.ShouldActivateModule())
			{
				return false;
			}
			bool flag = this.m_ForceModuleActive;
			flag |= InputManager.GetButton(this.m_SubmitButton).Down;
			flag |= InputManager.GetButton(this.m_CancelButton).Down;
			flag |= !Mathf.Approximately(InputManager.GetVector("Navigate").vector.x, 0f);
			flag |= !Mathf.Approximately(InputManager.GetVector("Navigate").vector.y, 0f);
			flag |= (this.m_MousePosition - this.m_LastMousePosition).sqrMagnitude > 0f;
			flag |= base.input.GetMouseButtonDown(0);
			if (base.input.touchCount > 0)
			{
				flag = true;
			}
			return flag;
		}

		// Token: 0x06000C14 RID: 3092 RVA: 0x0004C3CC File Offset: 0x0004A5CC
		public override void ActivateModule()
		{
			if (!base.eventSystem.isFocused && this.ShouldIgnoreEventsOnNoFocus())
			{
				return;
			}
			base.ActivateModule();
			this.m_MousePosition = this.customPointer.anchoredPosition * this.canvasScaler.scaleFactor;
			this.m_LastMousePosition = this.customPointer.anchoredPosition * this.canvasScaler.scaleFactor;
			GameObject gameObject = base.eventSystem.currentSelectedGameObject;
			if (gameObject == null)
			{
				gameObject = base.eventSystem.firstSelectedGameObject;
			}
			base.eventSystem.SetSelectedGameObject(gameObject, this.GetBaseEventData());
		}

		// Token: 0x06000C15 RID: 3093 RVA: 0x0004C46A File Offset: 0x0004A66A
		public override void DeactivateModule()
		{
			base.DeactivateModule();
			base.ClearSelection();
		}

		// Token: 0x06000C16 RID: 3094 RVA: 0x0004C478 File Offset: 0x0004A678
		public override void Process()
		{
			if (!base.eventSystem.isFocused && this.ShouldIgnoreEventsOnNoFocus())
			{
				return;
			}
			bool flag = this.SendUpdateEventToSelectedObject();
			if (!this.ProcessTouchEvents() && base.input.mousePresent)
			{
				this.ProcessMouseEvent();
			}
			if (base.eventSystem.sendNavigationEvents)
			{
				if (!flag)
				{
					flag |= this.SendMoveEventToSelectedObject();
				}
				if (!flag)
				{
					this.SendSubmitEventToSelectedObject();
				}
			}
		}

		// Token: 0x06000C17 RID: 3095 RVA: 0x0004C4E0 File Offset: 0x0004A6E0
		private bool ProcessTouchEvents()
		{
			for (int i = 0; i < base.input.touchCount; i++)
			{
				Touch touch = base.input.GetTouch(i);
				if (touch.type != TouchType.Indirect)
				{
					bool flag;
					bool flag2;
					PointerEventData touchPointerEventData = base.GetTouchPointerEventData(touch, out flag, out flag2);
					if (!flag2)
					{
						this.ProcessMove(touchPointerEventData);
						this.ProcessDrag(touchPointerEventData);
					}
					else
					{
						base.RemovePointerData(touchPointerEventData);
					}
				}
			}
			return base.input.touchCount > 0;
		}

		// Token: 0x06000C18 RID: 3096 RVA: 0x0004C554 File Offset: 0x0004A754
		protected void ProcessTouchPress(PointerEventData pointerEvent, bool pressed, bool released)
		{
			GameObject gameObject = pointerEvent.pointerCurrentRaycast.gameObject;
			if (pressed)
			{
				pointerEvent.eligibleForClick = true;
				pointerEvent.delta = Vector2.zero;
				pointerEvent.dragging = false;
				pointerEvent.useDragThreshold = true;
				pointerEvent.pressPosition = pointerEvent.position;
				pointerEvent.pointerPressRaycast = pointerEvent.pointerCurrentRaycast;
				base.DeselectIfSelectionChanged(gameObject, pointerEvent);
				if (pointerEvent.pointerEnter != gameObject)
				{
					this.HandleOSPointerExitAndEnter(pointerEvent, gameObject);
					pointerEvent.pointerEnter = gameObject;
				}
				GameObject gameObject2 = ExecuteEvents.ExecuteHierarchy<IPointerDownHandler>(gameObject, pointerEvent, ExecuteEvents.pointerDownHandler);
				if (gameObject2 == null)
				{
					gameObject2 = ExecuteEvents.GetEventHandler<IPointerClickHandler>(gameObject);
				}
				float unscaledTime = Time.unscaledTime;
				if (gameObject2 == pointerEvent.lastPress)
				{
					if (unscaledTime - pointerEvent.clickTime < 0.3f)
					{
						int num = pointerEvent.clickCount + 1;
						pointerEvent.clickCount = num;
					}
					else
					{
						pointerEvent.clickCount = 1;
					}
					pointerEvent.clickTime = unscaledTime;
				}
				else
				{
					pointerEvent.clickCount = 1;
				}
				pointerEvent.pointerPress = gameObject2;
				pointerEvent.rawPointerPress = gameObject;
				pointerEvent.clickTime = unscaledTime;
				pointerEvent.pointerDrag = ExecuteEvents.GetEventHandler<IDragHandler>(gameObject);
				if (pointerEvent.pointerDrag != null)
				{
					ExecuteEvents.Execute<IInitializePotentialDragHandler>(pointerEvent.pointerDrag, pointerEvent, ExecuteEvents.initializePotentialDrag);
				}
				this.m_InputPointerEvent = pointerEvent;
			}
			if (released)
			{
				ExecuteEvents.Execute<IPointerUpHandler>(pointerEvent.pointerPress, pointerEvent, ExecuteEvents.pointerUpHandler);
				GameObject eventHandler = ExecuteEvents.GetEventHandler<IPointerClickHandler>(gameObject);
				if (pointerEvent.pointerPress == eventHandler && pointerEvent.eligibleForClick)
				{
					ExecuteEvents.Execute<IPointerClickHandler>(pointerEvent.pointerPress, pointerEvent, ExecuteEvents.pointerClickHandler);
				}
				else if (pointerEvent.pointerDrag != null && pointerEvent.dragging)
				{
					ExecuteEvents.ExecuteHierarchy<IDropHandler>(gameObject, pointerEvent, ExecuteEvents.dropHandler);
				}
				pointerEvent.eligibleForClick = false;
				pointerEvent.pointerPress = null;
				pointerEvent.rawPointerPress = null;
				if (pointerEvent.pointerDrag != null && pointerEvent.dragging)
				{
					ExecuteEvents.Execute<IEndDragHandler>(pointerEvent.pointerDrag, pointerEvent, ExecuteEvents.endDragHandler);
				}
				pointerEvent.dragging = false;
				pointerEvent.pointerDrag = null;
				ExecuteEvents.ExecuteHierarchy<IPointerExitHandler>(pointerEvent.pointerEnter, pointerEvent, ExecuteEvents.pointerExitHandler);
				pointerEvent.pointerEnter = null;
				this.m_InputPointerEvent = pointerEvent;
			}
		}

		// Token: 0x06000C19 RID: 3097 RVA: 0x0004C760 File Offset: 0x0004A960
		protected bool SendSubmitEventToSelectedObject()
		{
			if (base.eventSystem.currentSelectedGameObject == null)
			{
				return false;
			}
			BaseEventData baseEventData = this.GetBaseEventData();
			if (InputManager.GetButton(this.m_SubmitButton).Down)
			{
				ExecuteEvents.Execute<ISubmitHandler>(base.eventSystem.currentSelectedGameObject, baseEventData, ExecuteEvents.submitHandler);
			}
			if (InputManager.GetButton(this.m_CancelButton).Down)
			{
				ExecuteEvents.Execute<ICancelHandler>(base.eventSystem.currentSelectedGameObject, baseEventData, ExecuteEvents.cancelHandler);
			}
			return baseEventData.used;
		}

		// Token: 0x06000C1A RID: 3098 RVA: 0x0004C7E4 File Offset: 0x0004A9E4
		private Vector2 GetRawMoveVector()
		{
			Vector2 zero = Vector2.zero;
			zero.x = InputManager.GetVector("Navigate").vector.x;
			zero.y = InputManager.GetVector("Navigate").vector.y;
			if (InputManager.GetVector("Navigate").Down)
			{
				if (zero.x < 0f)
				{
					zero.x = -1f;
				}
				if (zero.x > 0f)
				{
					zero.x = 1f;
				}
			}
			if (InputManager.GetVector("Navigate").Down)
			{
				if (zero.y < 0f)
				{
					zero.y = -1f;
				}
				if (zero.y > 0f)
				{
					zero.y = 1f;
				}
			}
			return zero;
		}

		// Token: 0x06000C1B RID: 3099 RVA: 0x0004C8B4 File Offset: 0x0004AAB4
		protected bool SendMoveEventToSelectedObject()
		{
			float unscaledTime = Time.unscaledTime;
			Vector2 rawMoveVector = this.GetRawMoveVector();
			if (Mathf.Approximately(rawMoveVector.x, 0f) && Mathf.Approximately(rawMoveVector.y, 0f))
			{
				this.m_ConsecutiveMoveCount = 0;
				return false;
			}
			bool flag = Vector2.Dot(rawMoveVector, this.m_LastMoveVector) > 0f;
			if (flag && this.m_ConsecutiveMoveCount == 1)
			{
				if (unscaledTime <= this.m_PrevActionTime + this.m_RepeatDelay)
				{
					return false;
				}
			}
			else if (unscaledTime <= this.m_PrevActionTime + 1f / this.m_InputActionsPerSecond)
			{
				return false;
			}
			AxisEventData axisEventData = this.GetAxisEventData(rawMoveVector.x, rawMoveVector.y, 0.6f);
			if (axisEventData.moveDir != MoveDirection.None)
			{
				ExecuteEvents.Execute<IMoveHandler>(base.eventSystem.currentSelectedGameObject, axisEventData, ExecuteEvents.moveHandler);
				if (!flag)
				{
					this.m_ConsecutiveMoveCount = 0;
				}
				this.m_ConsecutiveMoveCount++;
				this.m_PrevActionTime = unscaledTime;
				this.m_LastMoveVector = rawMoveVector;
			}
			else
			{
				this.m_ConsecutiveMoveCount = 0;
			}
			return axisEventData.used;
		}

		// Token: 0x06000C1C RID: 3100 RVA: 0x0004C9B2 File Offset: 0x0004ABB2
		protected void ProcessMouseEvent()
		{
			this.ProcessMouseEvent(0);
		}

		// Token: 0x06000C1D RID: 3101 RVA: 0x0004C9BB File Offset: 0x0004ABBB
		[Obsolete("This method is no longer checked, overriding it with return true does nothing!")]
		protected virtual bool ForceAutoSelect()
		{
			return false;
		}

		// Token: 0x06000C1E RID: 3102 RVA: 0x0004C9C0 File Offset: 0x0004ABC0
		protected override PointerInputModule.MouseState GetMousePointerEventData(int id)
		{
			PointerInputModule.MouseState mouseState = new PointerInputModule.MouseState();
			PointerEventData pointerEventData;
			bool pointerData = base.GetPointerData(-1, out pointerEventData, true);
			pointerEventData.Reset();
			if (pointerData)
			{
				pointerEventData.position = this.customPointer.anchoredPosition * this.canvasScaler.scaleFactor;
			}
			Vector2 vector = this.customPointer.anchoredPosition * this.canvasScaler.scaleFactor;
			pointerEventData.delta = vector - this.lastPointerPosition;
			this.lastPointerPosition = vector;
			pointerEventData.position = vector;
			pointerEventData.scrollDelta = base.input.mouseScrollDelta;
			pointerEventData.button = PointerEventData.InputButton.Left;
			base.eventSystem.RaycastAll(pointerEventData, this.m_RaycastResultCache);
			RaycastResult raycastResult = BaseInputModule.FindFirstRaycast(this.m_RaycastResultCache);
			pointerEventData.pointerCurrentRaycast = raycastResult;
			this.m_RaycastResultCache.Clear();
			PointerEventData pointerEventData2;
			base.GetPointerData(-2, out pointerEventData2, true);
			base.CopyFromTo(pointerEventData, pointerEventData2);
			pointerEventData2.button = PointerEventData.InputButton.Right;
			PointerEventData pointerEventData3;
			base.GetPointerData(-3, out pointerEventData3, true);
			base.CopyFromTo(pointerEventData, pointerEventData3);
			pointerEventData3.button = PointerEventData.InputButton.Middle;
			mouseState.SetButtonState(PointerEventData.InputButton.Left, base.StateForMouseButton(0), pointerEventData);
			mouseState.SetButtonState(PointerEventData.InputButton.Right, base.StateForMouseButton(1), pointerEventData2);
			mouseState.SetButtonState(PointerEventData.InputButton.Middle, base.StateForMouseButton(2), pointerEventData3);
			return mouseState;
		}

		// Token: 0x06000C1F RID: 3103 RVA: 0x0004CAF0 File Offset: 0x0004ACF0
		private bool ShouldStartDrag(Vector2 pressPos, Vector2 currentPos, float threshold, bool useDragThreshold)
		{
			return !useDragThreshold || (pressPos - currentPos).sqrMagnitude >= threshold * threshold;
		}

		// Token: 0x06000C20 RID: 3104 RVA: 0x0004CB1C File Offset: 0x0004AD1C
		protected override void ProcessDrag(PointerEventData pointerEvent)
		{
			if (!pointerEvent.IsPointerMoving() || pointerEvent.pointerDrag == null)
			{
				return;
			}
			if (!pointerEvent.dragging && this.ShouldStartDrag(pointerEvent.pressPosition, pointerEvent.position, (float)base.eventSystem.pixelDragThreshold, pointerEvent.useDragThreshold))
			{
				ExecuteEvents.Execute<IBeginDragHandler>(pointerEvent.pointerDrag, pointerEvent, ExecuteEvents.beginDragHandler);
				pointerEvent.dragging = true;
			}
			if (pointerEvent.dragging)
			{
				if (pointerEvent.pointerPress != pointerEvent.pointerDrag)
				{
					ExecuteEvents.Execute<IPointerUpHandler>(pointerEvent.pointerPress, pointerEvent, ExecuteEvents.pointerUpHandler);
					pointerEvent.eligibleForClick = false;
					pointerEvent.pointerPress = null;
					pointerEvent.rawPointerPress = null;
				}
				ExecuteEvents.Execute<IDragHandler>(pointerEvent.pointerDrag, pointerEvent, ExecuteEvents.dragHandler);
			}
		}

		// Token: 0x06000C21 RID: 3105 RVA: 0x0004CBDC File Offset: 0x0004ADDC
		protected void ProcessMouseEvent(int id)
		{
			PointerInputModule.MouseState mousePointerEventData = this.GetMousePointerEventData(id);
			PointerInputModule.MouseButtonEventData eventData = mousePointerEventData.GetButtonState(PointerEventData.InputButton.Left).eventData;
			this.m_CurrentFocusedGameObject = eventData.buttonData.pointerCurrentRaycast.gameObject;
			this.ProcessMousePress(eventData);
			this.ProcessMove(eventData.buttonData);
			this.ProcessDrag(eventData.buttonData);
			this.ProcessMousePress(mousePointerEventData.GetButtonState(PointerEventData.InputButton.Right).eventData);
			this.ProcessDrag(mousePointerEventData.GetButtonState(PointerEventData.InputButton.Right).eventData.buttonData);
			this.ProcessMousePress(mousePointerEventData.GetButtonState(PointerEventData.InputButton.Middle).eventData);
			this.ProcessDrag(mousePointerEventData.GetButtonState(PointerEventData.InputButton.Middle).eventData.buttonData);
			if (!Mathf.Approximately(eventData.buttonData.scrollDelta.sqrMagnitude, 0f))
			{
				ExecuteEvents.ExecuteHierarchy<IScrollHandler>(ExecuteEvents.GetEventHandler<IScrollHandler>(eventData.buttonData.pointerCurrentRaycast.gameObject), eventData.buttonData, ExecuteEvents.scrollHandler);
			}
		}

		// Token: 0x06000C22 RID: 3106 RVA: 0x0004CCD0 File Offset: 0x0004AED0
		protected bool SendUpdateEventToSelectedObject()
		{
			if (base.eventSystem.currentSelectedGameObject == null)
			{
				return false;
			}
			BaseEventData baseEventData = this.GetBaseEventData();
			ExecuteEvents.Execute<IUpdateSelectedHandler>(base.eventSystem.currentSelectedGameObject, baseEventData, ExecuteEvents.updateSelectedHandler);
			return baseEventData.used;
		}

		// Token: 0x06000C23 RID: 3107 RVA: 0x0004CD18 File Offset: 0x0004AF18
		protected void ProcessMousePress(PointerInputModule.MouseButtonEventData data)
		{
			PointerEventData buttonData = data.buttonData;
			GameObject gameObject = buttonData.pointerCurrentRaycast.gameObject;
			if (data.PressedThisFrame())
			{
				buttonData.eligibleForClick = true;
				buttonData.delta = Vector2.zero;
				buttonData.dragging = false;
				buttonData.useDragThreshold = true;
				buttonData.pressPosition = buttonData.position;
				buttonData.pointerPressRaycast = buttonData.pointerCurrentRaycast;
				base.DeselectIfSelectionChanged(gameObject, buttonData);
				GameObject gameObject2 = ExecuteEvents.ExecuteHierarchy<IPointerDownHandler>(gameObject, buttonData, ExecuteEvents.pointerDownHandler);
				if (gameObject2 == null)
				{
					gameObject2 = ExecuteEvents.GetEventHandler<IPointerClickHandler>(gameObject);
				}
				float unscaledTime = Time.unscaledTime;
				if (gameObject2 == buttonData.lastPress)
				{
					if (unscaledTime - buttonData.clickTime < 0.3f)
					{
						PointerEventData pointerEventData = buttonData;
						int num = pointerEventData.clickCount + 1;
						pointerEventData.clickCount = num;
					}
					else
					{
						buttonData.clickCount = 1;
					}
					buttonData.clickTime = unscaledTime;
				}
				else
				{
					buttonData.clickCount = 1;
				}
				buttonData.pointerPress = gameObject2;
				buttonData.rawPointerPress = gameObject;
				buttonData.clickTime = unscaledTime;
				buttonData.pointerDrag = ExecuteEvents.GetEventHandler<IDragHandler>(gameObject);
				if (buttonData.pointerDrag != null)
				{
					ExecuteEvents.Execute<IInitializePotentialDragHandler>(buttonData.pointerDrag, buttonData, ExecuteEvents.initializePotentialDrag);
				}
				this.m_InputPointerEvent = buttonData;
			}
			if (data.ReleasedThisFrame())
			{
				this.ReleaseMouse(buttonData, gameObject);
			}
		}

		// Token: 0x06000C24 RID: 3108 RVA: 0x0004CE4D File Offset: 0x0004B04D
		protected GameObject GetCurrentFocusedGameObject()
		{
			return this.m_CurrentFocusedGameObject;
		}

		// Token: 0x06000C25 RID: 3109 RVA: 0x0004CE58 File Offset: 0x0004B058
		protected override void ProcessMove(PointerEventData pointerEvent)
		{
			GameObject gameObject = ((!OS_Manager.mouseEnabled) ? null : pointerEvent.pointerCurrentRaycast.gameObject);
			this.HandleOSPointerExitAndEnter(pointerEvent, gameObject);
		}

		// Token: 0x06000C26 RID: 3110 RVA: 0x0004CE88 File Offset: 0x0004B088
		protected void HandleOSPointerExitAndEnter(PointerEventData currentPointerData, GameObject newEnterTarget)
		{
			if (newEnterTarget == null || currentPointerData.pointerEnter == null)
			{
				for (int i = 0; i < currentPointerData.hovered.Count; i++)
				{
					ExecuteEvents.Execute<IPointerExitHandler>(currentPointerData.hovered[i], currentPointerData, ExecuteEvents.pointerExitHandler);
				}
				currentPointerData.hovered.Clear();
				if (newEnterTarget == null)
				{
					currentPointerData.pointerEnter = null;
					return;
				}
			}
			if (currentPointerData.pointerEnter == newEnterTarget && newEnterTarget)
			{
				return;
			}
			GameObject gameObject = BaseInputModule.FindCommonRoot(currentPointerData.pointerEnter, newEnterTarget);
			if (currentPointerData.pointerEnter != null)
			{
				Transform transform = currentPointerData.pointerEnter.transform;
				while (transform != null && (!(gameObject != null) || !(gameObject.transform == transform)))
				{
					ExecuteEvents.Execute<IPointerExitHandler>(transform.gameObject, currentPointerData, ExecuteEvents.pointerExitHandler);
					currentPointerData.hovered.Remove(transform.gameObject);
					transform = transform.parent;
				}
			}
			currentPointerData.pointerEnter = newEnterTarget;
			if (newEnterTarget != null)
			{
				Transform transform2 = newEnterTarget.transform;
				while (transform2 != null && transform2.gameObject != gameObject)
				{
					ExecuteEvents.Execute<IPointerEnterHandler>(transform2.gameObject, currentPointerData, ExecuteEvents.pointerEnterHandler);
					currentPointerData.hovered.Add(transform2.gameObject);
					transform2 = transform2.parent;
				}
			}
		}

		// Token: 0x04000CF3 RID: 3315
		private float m_PrevActionTime;

		// Token: 0x04000CF4 RID: 3316
		private Vector2 m_LastMoveVector;

		// Token: 0x04000CF5 RID: 3317
		private int m_ConsecutiveMoveCount;

		// Token: 0x04000CF6 RID: 3318
		private Vector2 m_LastMousePosition;

		// Token: 0x04000CF7 RID: 3319
		private Vector2 m_MousePosition;

		// Token: 0x04000CF8 RID: 3320
		private Vector2 lastPointerPosition;

		// Token: 0x04000CF9 RID: 3321
		private GameObject m_CurrentFocusedGameObject;

		// Token: 0x04000CFA RID: 3322
		private PointerEventData m_InputPointerEvent;

		// Token: 0x04000CFB RID: 3323
		public RectTransform customPointer;

		// Token: 0x04000CFC RID: 3324
		public CanvasScaler canvasScaler;

		// Token: 0x04000CFD RID: 3325
		[SerializeField]
		private string m_HorizontalAxis = "Horizontal";

		// Token: 0x04000CFE RID: 3326
		[SerializeField]
		private string m_VerticalAxis = "Vertical";

		// Token: 0x04000CFF RID: 3327
		[SerializeField]
		private string m_SubmitButton = "Submit";

		// Token: 0x04000D00 RID: 3328
		[SerializeField]
		private string m_CancelButton = "Cancel";

		// Token: 0x04000D01 RID: 3329
		[SerializeField]
		private float m_InputActionsPerSecond = 10f;

		// Token: 0x04000D02 RID: 3330
		[SerializeField]
		private float m_RepeatDelay = 0.5f;

		// Token: 0x04000D03 RID: 3331
		[SerializeField]
		[FormerlySerializedAs("m_AllowActivationOnMobileDevice")]
		private bool m_ForceModuleActive;

		// Token: 0x020002EF RID: 751
		[Obsolete("Mode is no longer needed on input module as it handles both mouse and keyboard simultaneously.", false)]
		public enum InputMode
		{
			// Token: 0x0400129A RID: 4762
			Mouse,
			// Token: 0x0400129B RID: 4763
			Buttons
		}
	}
}
