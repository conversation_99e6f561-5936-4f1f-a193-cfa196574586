﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace DarkMachine.Collections
{
	// Token: 0x020001D0 RID: 464
	public class PriorityQueue<TElement, TPriority>
	{
		// Token: 0x06000BC0 RID: 3008 RVA: 0x0004ABE3 File Offset: 0x00048DE3
		public PriorityQueue()
			: this(null)
		{
		}

		// Token: 0x06000BC1 RID: 3009 RVA: 0x0004ABEC File Offset: 0x00048DEC
		public PriorityQueue(IComparer<TPriority> comparer)
		{
			this._comparer = comparer ?? Comparer<TPriority>.Default;
			this._heap = new List<ValueTuple<TElement, TPriority>>();
		}

		// Token: 0x17000007 RID: 7
		// (get) Token: 0x06000BC2 RID: 3010 RVA: 0x0004AC0F File Offset: 0x00048E0F
		public int Count
		{
			get
			{
				return this._heap.Count;
			}
		}

		// Token: 0x06000BC3 RID: 3011 RVA: 0x0004AC1C File Offset: 0x00048E1C
		public void Enqueue(TElement element, TPriority priority)
		{
			this._heap.Add(new ValueTuple<TElement, TPriority>(element, priority));
			this.HeapifyUp(this._heap.Count - 1);
		}

		// Token: 0x06000BC4 RID: 3012 RVA: 0x0004AC44 File Offset: 0x00048E44
		public TElement Dequeue()
		{
			if (this._heap.Count == 0)
			{
				throw new InvalidOperationException("The priority queue is empty.");
			}
			TElement item = this._heap[0].Item1;
			this.Swap(0, this._heap.Count - 1);
			this._heap.RemoveAt(this._heap.Count - 1);
			if (this._heap.Count > 0)
			{
				this.HeapifyDown(0);
			}
			return item;
		}

		// Token: 0x06000BC5 RID: 3013 RVA: 0x0004ACBB File Offset: 0x00048EBB
		public TElement Peek()
		{
			if (this._heap.Count == 0)
			{
				throw new InvalidOperationException("The priority queue is empty.");
			}
			return this._heap[0].Item1;
		}

		// Token: 0x06000BC6 RID: 3014 RVA: 0x0004ACE8 File Offset: 0x00048EE8
		private void HeapifyUp(int index)
		{
			if (index == 0)
			{
				return;
			}
			int num = (index - 1) / 2;
			if (this._comparer.Compare(this._heap[index].Item2, this._heap[num].Item2) < 0)
			{
				this.Swap(index, num);
				this.HeapifyUp(num);
			}
		}

		// Token: 0x06000BC7 RID: 3015 RVA: 0x0004AD40 File Offset: 0x00048F40
		private void HeapifyDown(int index)
		{
			int num = index;
			int num2 = 2 * index + 1;
			int num3 = 2 * index + 2;
			if (num2 < this._heap.Count && this._comparer.Compare(this._heap[num2].Item2, this._heap[num].Item2) < 0)
			{
				num = num2;
			}
			if (num3 < this._heap.Count && this._comparer.Compare(this._heap[num3].Item2, this._heap[num].Item2) < 0)
			{
				num = num3;
			}
			if (num != index)
			{
				this.Swap(index, num);
				this.HeapifyDown(num);
			}
		}

		// Token: 0x06000BC8 RID: 3016 RVA: 0x0004ADF0 File Offset: 0x00048FF0
		private void Swap(int i, int j)
		{
			ValueTuple<TElement, TPriority> valueTuple = this._heap[i];
			this._heap[i] = this._heap[j];
			this._heap[j] = valueTuple;
		}

		// Token: 0x04000CCC RID: 3276
		[TupleElementNames(new string[] { "Element", "Priority" })]
		private readonly List<ValueTuple<TElement, TPriority>> _heap;

		// Token: 0x04000CCD RID: 3277
		private readonly IComparer<TPriority> _comparer;
	}
}
