﻿using System;
using UnityEngine;
using UnityEngine.Rendering.PostProcessing;

// Token: 0x020001B9 RID: 441
public sealed class PostProcessOutlineRenderer : PostProcessEffectRenderer<PostProcessOutline>
{
	// Token: 0x06000B76 RID: 2934 RVA: 0x000496E4 File Offset: 0x000478E4
	public override void Render(PostProcessRenderContext context)
	{
		PropertySheet propertySheet = context.propertySheets.Get(Shader.Find("Hidden/Roystan/Outline Post Process"));
		propertySheet.properties.SetFloat("_Scale", (float)base.settings.scale);
		propertySheet.properties.SetColor("_Color", base.settings.color);
		propertySheet.properties.SetFloat("_DepthThreshold", base.settings.depthThreshold);
		propertySheet.properties.SetFloat("_DepthNormalThreshold", base.settings.depthNormalThreshold);
		propertySheet.properties.SetFloat("_DepthNormalThresholdScale", base.settings.depthNormalThresholdScale);
		propertySheet.properties.SetFloat("_NormalThreshold", base.settings.normalThreshold);
		propertySheet.properties.SetColor("_Color", base.settings.color);
		Matrix4x4 inverse = GL.GetGPUProjectionMatrix(context.camera.projectionMatrix, true).inverse;
		propertySheet.properties.SetMatrix("_ClipToView", inverse);
		context.command.BlitFullscreenTriangle(context.source, context.destination, propertySheet, 0, false, null, false);
	}
}
