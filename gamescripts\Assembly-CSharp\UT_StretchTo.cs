﻿using System;
using UnityEngine;

// Token: 0x0200019C RID: 412
public class UT_StretchTo : MonoBehaviour
{
	// Token: 0x06000B01 RID: 2817 RVA: 0x00047716 File Offset: 0x00045916
	private void Start()
	{
	}

	// Token: 0x06000B02 RID: 2818 RVA: 0x00047718 File Offset: 0x00045918
	private void LateUpdate()
	{
		if (!this.useRaycast)
		{
			float num = Vector3.Distance(this.target.position, base.transform.position);
			base.transform.rotation = Quaternion.LookRotation(this.target.position - base.transform.position, base.transform.up);
			base.transform.localScale = new Vector3(1f, 1f, num);
			return;
		}
		RaycastHit raycastHit;
		if (Physics.Raycast(base.transform.position, base.transform.forward, out raycastHit, this.maxDistance, this.layerMask))
		{
			float num2 = Vector3.Distance(raycastHit.point, base.transform.position);
			base.transform.localScale = new Vector3(1f, 1f, num2);
			return;
		}
		base.transform.localScale = new Vector3(1f, 1f, this.maxDistance);
	}

	// Token: 0x04000C06 RID: 3078
	public bool useRaycast;

	// Token: 0x04000C07 RID: 3079
	public LayerMask layerMask;

	// Token: 0x04000C08 RID: 3080
	public float maxDistance;

	// Token: 0x04000C09 RID: 3081
	public Transform target;
}
