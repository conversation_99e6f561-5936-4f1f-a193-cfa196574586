﻿using System;
using UnityEngine;

// Token: 0x02000030 RID: 48
public class DEN_Bloodbug : Denizen
{
	// Token: 0x060001B1 RID: 433 RVA: 0x0000CCA0 File Offset: 0x0000AEA0
	public override void Start()
	{
		base.Start();
		this.bloodbugTarget = new DEN_Bloodbug.Target();
		this.rigid = base.GetComponent<Rigidbody>();
		this.targetPosition = this.FindWanderSpot();
		this.rigid.AddForce(base.transform.forward);
		this.noiseOffsetX = Random.Range(0f, 100f);
		this.noiseOffsetY = Random.Range(0f, 100f);
		this.noiseOffsetZ = Random.Range(0f, 100f);
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
		this.flyAudio = base.GetComponent<AudioSource>();
		this.restTimer = (float)Random.Range(5, 15);
		this.flyAudioTarget = this.flyAudio.volume;
		if (this.alwaysTargetPlayer)
		{
			this.bloodbugTarget.transform = CL_GameManager.gMan.localPlayer.transform;
			this.bloodbugTarget.tagger = CL_GameManager.gMan.localPlayer.GetTagger();
			this.bloodbugTarget.entity = CL_GameManager.gMan.localPlayer;
			this.bloodbugTarget.isVisible = true;
		}
	}

	// Token: 0x060001B2 RID: 434 RVA: 0x0000CDC0 File Offset: 0x0000AFC0
	public override void FixedUpdate()
	{
		base.FixedUpdate();
		if (this.alwaysTargetPlayer && this.bloodbugTarget.transform == null)
		{
			this.bloodbugTarget.transform = CL_GameManager.gMan.localPlayer.transform;
			this.bloodbugTarget.tagger = CL_GameManager.gMan.localPlayer.GetTagger();
			this.bloodbugTarget.entity = CL_GameManager.gMan.localPlayer;
			this.bloodbugTarget.isVisible = true;
			this.bloodbugTarget.lastSightedTimer = 1000f;
		}
		if (!this.dead)
		{
			this.AI();
			this.Movement();
			return;
		}
		if (this.flyAudio.volume > 0.1f && this.flyAudio.isPlaying)
		{
			this.flyAudio.volume = Mathf.Lerp(this.flyAudio.volume, 0f, Time.fixedDeltaTime * 5f);
		}
		else if (this.flyAudio.isPlaying)
		{
			this.flyAudio.Stop();
		}
		if (this.rigid.velocity.magnitude > 1f)
		{
			base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(this.rigid.velocity.normalized, base.transform.forward), Time.fixedDeltaTime * 10f);
			return;
		}
		base.transform.rotation = Quaternion.LookRotation(Vector3.forward);
	}

	// Token: 0x060001B3 RID: 435 RVA: 0x0000CF48 File Offset: 0x0000B148
	public override void Update()
	{
		base.Update();
		if (CL_UIManager.debug)
		{
			CL_DebugView.draw.Cross(this.targetPosition, 0.5f, Color.green);
		}
	}

	// Token: 0x060001B4 RID: 436 RVA: 0x0000CF78 File Offset: 0x0000B178
	private void AI()
	{
		float num = 0f;
		if (this.chargeCooldownTime > 0f)
		{
			this.chargeCooldownTime -= Time.fixedDeltaTime;
		}
		if (this.bloodbugTarget.transform == null)
		{
			if (this.aiState != DEN_Bloodbug.AIStates.resting)
			{
				this.aiState = DEN_Bloodbug.AIStates.wandering;
			}
		}
		else
		{
			num = Vector3.Distance(base.transform.position, this.bloodbugTarget.transform.position);
			if (!this.bloodbugTarget.isVisible)
			{
				this.bloodbugTarget.lastSightedTimer -= Time.fixedDeltaTime;
			}
			else
			{
				this.bloodbugTarget.lastSightedTimer = this.loseTargetTimer;
			}
			if (this.bloodbugTarget.lastSightedTimer <= 0f)
			{
				this.bloodbugTarget.Clear();
				this.aiState = DEN_Bloodbug.AIStates.wandering;
			}
		}
		switch (this.aiState)
		{
		case DEN_Bloodbug.AIStates.wandering:
			this.moveState = DEN_Bloodbug.MovementState.flying;
			if (Vector3.Distance(base.transform.position, this.targetPosition) < 1f)
			{
				if (this.hoverTime <= 0f)
				{
					this.targetPosition = this.FindWanderSpot();
					this.hoverTime = Random.Range(this.hoverTimeMin, this.hoverTimeMax);
				}
				else
				{
					this.hoverTime -= Time.deltaTime;
				}
			}
			this.restTimer -= Time.fixedDeltaTime;
			if (this.restTimer <= 0f)
			{
				this.aiState = DEN_Bloodbug.AIStates.resting;
				this.restTimer = (float)Random.Range(10, 30);
				this.foundRestSpot = false;
			}
			if (this.alwaysTargetPlayer)
			{
				this.aiState = DEN_Bloodbug.AIStates.hunting;
			}
			break;
		case DEN_Bloodbug.AIStates.resting:
			if (!this.foundRestSpot)
			{
				this.targetPosition = this.FindRestSpot();
			}
			if (this.foundRestSpot)
			{
				this.restTimer -= Time.fixedDeltaTime;
				if (this.restTimer <= 0f)
				{
					this.moveState = DEN_Bloodbug.MovementState.flying;
					this.aiState = DEN_Bloodbug.AIStates.wandering;
					this.targetPosition = this.FindWanderSpot();
					this.restTimer = (float)Random.Range(2, 10);
				}
			}
			break;
		case DEN_Bloodbug.AIStates.hunting:
			this.moveState = DEN_Bloodbug.MovementState.flying;
			if (this.bloodbugTarget.isVisible)
			{
				this.targetPosition = this.bloodbugTarget.transform.position + Vector3.up * 0.5f - (this.bloodbugTarget.transform.position + Vector3.up * 0.5f - base.transform.position).normalized * 1f;
				if (num < this.chargeDistance && this.chargeCooldownTime <= 0f)
				{
					this.aiState = DEN_Bloodbug.AIStates.charging;
					this.animator.SetBool("charging", true);
					this.clipHandler.PlaySound("bloodbug:chargewindup");
				}
			}
			else
			{
				this.targetPosition = this.bloodbugTarget.lastSighted;
			}
			break;
		case DEN_Bloodbug.AIStates.charging:
			this.targetPosition = this.bloodbugTarget.transform.position + Vector3.up * 0.5f - (this.bloodbugTarget.transform.position + Vector3.up * 0.5f - base.transform.position).normalized * 1f;
			this.chargeTime += Time.fixedDeltaTime;
			if (this.chargeTime > this.chargeWindup)
			{
				this.moveState = DEN_Bloodbug.MovementState.charging;
				this.rigid.AddForce((this.bloodbugTarget.transform.position - base.transform.position).normalized * 50f);
				if (!this.hasCharged)
				{
					this.hasCharged = true;
					this.clipHandler.PlaySound("bloodbug:charge");
				}
				if (this.chargeTime > this.chargeWindup + 1f)
				{
					this.moveState = DEN_Bloodbug.MovementState.flying;
					this.aiState = DEN_Bloodbug.AIStates.hunting;
					this.chargeCooldownTime = this.chargeCooldown;
					this.chargeTime = 0f;
					this.hasCharged = false;
					this.animator.SetBool("charging", false);
				}
			}
			break;
		}
		if (base.IsTickFrame())
		{
			if (this.alwaysTargetPlayer)
			{
				this.bloodbugTarget.isVisible = true;
				this.bloodbugTarget.lastSighted = this.bloodbugTarget.transform.position;
				return;
			}
			RaycastHit raycastHit;
			if (Physics.Raycast(base.transform.position, -Vector3.up, out raycastHit, 3f, this.flyMask))
			{
				this.rigid.AddForce(Vector3.up * 50f);
			}
			if (this.bloodbugTarget.transform == null)
			{
				this.FindTarget();
				return;
			}
			if (base.CanSeeTarget(this.bloodbugTarget.transform, this.sightDistance) && !CL_GameManager.noTarget)
			{
				this.bloodbugTarget.isVisible = true;
				this.bloodbugTarget.lastSighted = this.bloodbugTarget.transform.position;
				return;
			}
			this.bloodbugTarget.isVisible = false;
		}
	}

	// Token: 0x060001B5 RID: 437 RVA: 0x0000D4C8 File Offset: 0x0000B6C8
	private void Movement()
	{
		switch (this.moveState)
		{
		case DEN_Bloodbug.MovementState.flying:
			this.Fly();
			break;
		case DEN_Bloodbug.MovementState.landed:
			this.Crawl();
			break;
		case DEN_Bloodbug.MovementState.charging:
			this.Charge();
			break;
		}
		this.rigid.AddForce(this.targetMoveVector);
		this.ApplyBankingRotation();
	}

	// Token: 0x060001B6 RID: 438 RVA: 0x0000D520 File Offset: 0x0000B720
	private void Crawl()
	{
		if (!this.grounded)
		{
			this.Fly();
			return;
		}
		this.flyAudio.volume = Mathf.Lerp(this.flyAudio.volume, 0f, Time.fixedDeltaTime * 3f);
		this.moveState = DEN_Bloodbug.MovementState.landed;
		this.animator.SetBool("grounded", true);
		if (!this.rigid.isKinematic)
		{
			this.rigid.velocity = this.targetPosition - base.transform.position;
		}
		if (this.rigid.isKinematic)
		{
			base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(this.facingDir, this.wallNormal), Time.fixedDeltaTime * 5f);
		}
		if (this.collision == null)
		{
			return;
		}
		if (this.collision.contactCount > 0)
		{
			this.rigid.useGravity = false;
			if (!this.rigid.isKinematic)
			{
				this.rigid.velocity = Vector3.zero;
			}
			this.wallNormal = (this.targetPosition - base.transform.position).normalized;
			RaycastHit raycastHit;
			if (Physics.Raycast(base.transform.position, this.targetPosition - base.transform.position, out raycastHit, 5f, this.flyMask))
			{
				this.wallNormal = raycastHit.normal;
			}
			float num = Vector3.Dot(this.landDir, this.wallNormal);
			if ((double)num < -0.8 || num > 0.1f)
			{
				this.landDir = Vector3.ProjectOnPlane(Random.onUnitSphere, this.wallNormal).normalized;
			}
			if (this.rigid.velocity.magnitude > 0.1f)
			{
				this.facingDir = Vector3.ProjectOnPlane(this.landDir, this.wallNormal).normalized;
			}
			else
			{
				this.facingDir = Vector3.ProjectOnPlane(this.landDir, this.wallNormal).normalized;
				this.rigid.isKinematic = true;
			}
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.Arrow(base.transform.position, base.transform.position + this.facingDir, this.wallNormal, 0.5f, Color.yellow);
				CL_DebugView.draw.Arrow(base.transform.position, base.transform.position + this.wallNormal, this.wallNormal, 0.5f, Color.red);
			}
			base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(this.facingDir, this.wallNormal), Time.fixedDeltaTime * 5f);
		}
	}

	// Token: 0x060001B7 RID: 439 RVA: 0x0000D820 File Offset: 0x0000BA20
	private void Fly()
	{
		this.rigid.useGravity = true;
		this.rigid.isKinematic = false;
		this.animator.SetBool("grounded", false);
		this.flyAudio.volume = Mathf.Lerp(this.flyAudio.volume, this.flyAudioTarget, Time.fixedDeltaTime * 3f);
		if (this.moveState != DEN_Bloodbug.MovementState.charging)
		{
			this.moveState = DEN_Bloodbug.MovementState.flying;
		}
		DEN_Bloodbug.AIStates aistates = this.aiState;
		Vector3 vector = new Vector3(Mathf.PerlinNoise(Time.time * this.noiseFrequency + this.noiseOffsetX, 0f) - 0.5f, Mathf.PerlinNoise(Time.time * this.noiseFrequency + this.noiseOffsetY, 0f) - 0.5f, Mathf.PerlinNoise(Time.time * this.noiseFrequency + this.noiseOffsetZ, 0f) - 0.5f);
		vector *= this.noiseIntensity;
		Vector3 vector2 = this.targetPosition - base.transform.position;
		Vector3 vector3 = vector2;
		this.positionErrorSum += vector2 * Time.fixedDeltaTime;
		Vector3 vector4 = this.positionErrorSum;
		Vector3 vector5 = (vector2 - this.lastPositionError) / Time.fixedDeltaTime;
		Vector3 vector6 = this.pGain * vector3 + this.iGain * vector4 + this.dGain * vector5;
		vector6 = Vector3.ClampMagnitude(vector6 * this.flySpeed, this.flySpeedClamp);
		this.rigid.AddForce(vector6 + vector);
		if (!this.bloodbugTarget.isVisible)
		{
			this.lookVector = Vector3.Scale((this.rigid.velocity.normalized + vector2.normalized).normalized, new Vector3(1f, 0.2f, 1f));
		}
		else if (this.aiState == DEN_Bloodbug.AIStates.charging)
		{
			this.lookVector = this.bloodbugTarget.transform.position - base.transform.position;
		}
		else
		{
			this.lookVector = this.targetPosition - base.transform.position;
		}
		this.lastPositionError = vector2;
	}

	// Token: 0x060001B8 RID: 440 RVA: 0x0000DA75 File Offset: 0x0000BC75
	private void Charge()
	{
		this.Fly();
	}

	// Token: 0x060001B9 RID: 441 RVA: 0x0000DA80 File Offset: 0x0000BC80
	private void ApplyBankingRotation()
	{
		Vector3 normalized = this.lookVector.normalized;
		normalized.y = 0f;
		float num = Mathf.Clamp(Vector3.SignedAngle(base.transform.forward, normalized, Vector3.up), -this.maxBankAngle, this.maxBankAngle);
		Quaternion quaternion = Quaternion.Euler(0f, 0f, -num);
		Quaternion quaternion2 = Quaternion.LookRotation(normalized) * quaternion;
		base.transform.rotation = Quaternion.Slerp(base.transform.rotation, quaternion2, Time.deltaTime * this.rotationSpeed);
	}

	// Token: 0x060001BA RID: 442 RVA: 0x0000DB18 File Offset: 0x0000BD18
	private Vector3 FindRestSpot()
	{
		Vector3 onUnitSphere = Random.onUnitSphere;
		RaycastHit raycastHit;
		if (Physics.Raycast(base.transform.position, onUnitSphere, out raycastHit, 10f, this.flyMask))
		{
			Vector3 point = raycastHit.point;
			this.foundRestSpot = true;
			return point;
		}
		this.moveState = DEN_Bloodbug.MovementState.flying;
		this.foundRestSpot = false;
		return this.FindWanderSpot();
	}

	// Token: 0x060001BB RID: 443 RVA: 0x0000DB74 File Offset: 0x0000BD74
	private Vector3 FindWanderSpot()
	{
		Vector3 vector = Random.onUnitSphere * Random.Range(1f, 10f);
		RaycastHit raycastHit;
		if (Physics.Raycast(base.transform.position, vector, out raycastHit, vector.magnitude, this.flyMask))
		{
			Vector3 vector2 = raycastHit.point + raycastHit.normal * 2f;
			if (Physics.Linecast(base.transform.position, vector2, this.flyMask))
			{
				vector2 = base.transform.position + vector.normalized * raycastHit.distance * 0.2f;
			}
			return vector2;
		}
		return base.transform.position + vector;
	}

	// Token: 0x060001BC RID: 444 RVA: 0x0000DC40 File Offset: 0x0000BE40
	private void FindTarget()
	{
		if (CL_GameManager.noTarget)
		{
			return;
		}
		foreach (Collider collider in Physics.OverlapSphere(base.transform.position, this.sightDistance, this.sight.sightMask))
		{
			ObjectTagger objectTagger;
			if (ObjectTagger.TagCheck(collider.gameObject, this.targetTags, out objectTagger) && base.CanSeeTarget(collider.transform, this.sightDistance))
			{
				this.bloodbugTarget.transform = collider.transform;
				this.bloodbugTarget.tagger = objectTagger;
				this.bloodbugTarget.entity = collider.GetComponent<GameEntity>();
				this.bloodbugTarget.isVisible = true;
				this.aiState = DEN_Bloodbug.AIStates.hunting;
				return;
			}
		}
	}

	// Token: 0x060001BD RID: 445 RVA: 0x0000DCF9 File Offset: 0x0000BEF9
	private void OnCollisionStay(Collision other)
	{
		this.collision = other;
	}

	// Token: 0x060001BE RID: 446 RVA: 0x0000DD04 File Offset: 0x0000BF04
	private void OnCollisionEnter(Collision other)
	{
		if (this.moveState == DEN_Bloodbug.MovementState.charging)
		{
			ObjectTagger component = other.gameObject.GetComponent<ObjectTagger>();
			if (component != null && component.HasTag("Entity"))
			{
				GameEntity component2 = other.gameObject.GetComponent<GameEntity>();
				float num = 1f;
				float num2 = 1f;
				if (CL_GameManager.IsHardmode())
				{
					num = 1.7f;
					num2 = 2f;
				}
				component2.AddForce((Vector3.Scale((other.transform.position - base.transform.position).normalized, new Vector3(1f, 0f, 1f)) + Vector3.up * 0.5f).normalized * this.chargeHitForce * num);
				component2.Damage(num2, this.objectType);
			}
			this.animator.SetTrigger("chargehit");
			this.animator.SetBool("charging", false);
			this.moveState = DEN_Bloodbug.MovementState.flying;
			this.aiState = DEN_Bloodbug.AIStates.hunting;
			if (CL_GameManager.IsHardmode())
			{
				this.chargeCooldownTime = this.chargeCooldown * 0.75f;
			}
			else
			{
				this.chargeCooldownTime = this.chargeCooldown;
			}
			this.chargeTime = 0f;
			this.hasCharged = false;
		}
		else if (this.aiState == DEN_Bloodbug.AIStates.resting)
		{
			this.moveState = DEN_Bloodbug.MovementState.landed;
			this.grounded = true;
			this.landDir = base.transform.forward;
			this.animator.SetBool("grounded", true);
			this.collision = other;
		}
		if (this.dead)
		{
			this.grounded = true;
			this.animator.SetBool("grounded", true);
		}
	}

	// Token: 0x060001BF RID: 447 RVA: 0x0000DEB5 File Offset: 0x0000C0B5
	private void OnCollisionExit(Collision other)
	{
		if (this.dead)
		{
			this.grounded = false;
			this.animator.SetBool("grounded", false);
		}
	}

	// Token: 0x060001C0 RID: 448 RVA: 0x0000DED7 File Offset: 0x0000C0D7
	public override void AddForce(Vector3 v)
	{
		this.rigid.AddForce(v, ForceMode.VelocityChange);
		base.AddForce(v);
	}

	// Token: 0x060001C1 RID: 449 RVA: 0x0000DEED File Offset: 0x0000C0ED
	public override bool Damage(float amount, string type)
	{
		if (!this.dead)
		{
			this.clipHandler.PlaySound("bloodbug:hurt");
			this.animator.SetTrigger("hurt");
		}
		return base.Damage(amount, type);
	}

	// Token: 0x060001C2 RID: 450 RVA: 0x0000DF20 File Offset: 0x0000C120
	public override void Kill(string type = "")
	{
		if (this.dead)
		{
			return;
		}
		this.clipHandler.PlaySound("bloodbug:die");
		this.animator.SetBool("dead", true);
		this.animator.SetLayerWeight(1, 1f);
		base.gameObject.layer = LayerMask.NameToLayer("CreaturePassthrough");
		this.dead = true;
		if (this.destroyObject)
		{
			Object.Instantiate<GameObject>(this.destroyObject, base.transform.position, base.transform.rotation, base.transform.parent);
		}
		this.health = 0f;
		base.GetComponent<CapsuleCollider>().radius = 0.1f;
		CL_GameTracker.AddEventToSession(CL_GameTracker.CreateEvent(base.name, base.gameObject.GetInstanceID().ToString(), this.objectType, CL_GameTracker.Session.Tick.Event.EventType.kill, base.transform.position));
	}

	// Token: 0x040001AD RID: 429
	[Header("Bloodbug")]
	public LayerMask flyMask;

	// Token: 0x040001AE RID: 430
	public float flySpeed = 1f;

	// Token: 0x040001AF RID: 431
	public float flySpeedClamp = 1f;

	// Token: 0x040001B0 RID: 432
	public float crawlSpeed = 1f;

	// Token: 0x040001B1 RID: 433
	public float rotationSpeed = 2f;

	// Token: 0x040001B2 RID: 434
	public float maxBankAngle = 45f;

	// Token: 0x040001B3 RID: 435
	public float hoverTimeMin = 1f;

	// Token: 0x040001B4 RID: 436
	public float hoverTimeMax = 2f;

	// Token: 0x040001B5 RID: 437
	private float hoverTime;

	// Token: 0x040001B6 RID: 438
	public float chargeHitForce = 2f;

	// Token: 0x040001B7 RID: 439
	[Header("AI")]
	public DEN_Bloodbug.AIStates aiState = DEN_Bloodbug.AIStates.wandering;

	// Token: 0x040001B8 RID: 440
	public DEN_Bloodbug.MovementState moveState;

	// Token: 0x040001B9 RID: 441
	public float sightDistance;

	// Token: 0x040001BA RID: 442
	public float chargeDistance;

	// Token: 0x040001BB RID: 443
	public float loseTargetTimer = 5f;

	// Token: 0x040001BC RID: 444
	private float chargeTime;

	// Token: 0x040001BD RID: 445
	public float chargeWindup = 3f;

	// Token: 0x040001BE RID: 446
	public float chargeCooldown = 10f;

	// Token: 0x040001BF RID: 447
	private float chargeCooldownTime;

	// Token: 0x040001C0 RID: 448
	private float restTimer;

	// Token: 0x040001C1 RID: 449
	private bool foundRestSpot;

	// Token: 0x040001C2 RID: 450
	private bool hasCharged;

	// Token: 0x040001C3 RID: 451
	private AudioSource flyAudio;

	// Token: 0x040001C4 RID: 452
	private float flyAudioTarget;

	// Token: 0x040001C5 RID: 453
	private Collision collision;

	// Token: 0x040001C6 RID: 454
	public bool alwaysTargetPlayer;

	// Token: 0x040001C7 RID: 455
	private DEN_Bloodbug.Target bloodbugTarget;

	// Token: 0x040001C8 RID: 456
	private Vector3 targetMoveVector;

	// Token: 0x040001C9 RID: 457
	public string[] targetTags;

	// Token: 0x040001CA RID: 458
	private Vector3 lookVector;

	// Token: 0x040001CB RID: 459
	private RaycastHit searchRaycast;

	// Token: 0x040001CC RID: 460
	private bool grounded;

	// Token: 0x040001CD RID: 461
	private Vector3 wallNormal;

	// Token: 0x040001CE RID: 462
	private Vector3 facingDir;

	// Token: 0x040001CF RID: 463
	[Header("Physics")]
	public float pGain = 1f;

	// Token: 0x040001D0 RID: 464
	public float iGain = 0.1f;

	// Token: 0x040001D1 RID: 465
	public float dGain = 0.5f;

	// Token: 0x040001D2 RID: 466
	private Rigidbody rigid;

	// Token: 0x040001D3 RID: 467
	private Vector3 positionErrorSum = Vector3.zero;

	// Token: 0x040001D4 RID: 468
	private Vector3 lastPositionError = Vector3.zero;

	// Token: 0x040001D5 RID: 469
	public float noiseIntensity = 0.5f;

	// Token: 0x040001D6 RID: 470
	public float noiseFrequency = 1f;

	// Token: 0x040001D7 RID: 471
	private float noiseOffsetX;

	// Token: 0x040001D8 RID: 472
	private float noiseOffsetY;

	// Token: 0x040001D9 RID: 473
	private float noiseOffsetZ;

	// Token: 0x040001DA RID: 474
	private Vector3 landDir;

	// Token: 0x040001DB RID: 475
	[Header("Visuals")]
	public Animator animator;

	// Token: 0x02000212 RID: 530
	public enum AIStates
	{
		// Token: 0x04000E07 RID: 3591
		searching,
		// Token: 0x04000E08 RID: 3592
		wandering,
		// Token: 0x04000E09 RID: 3593
		resting,
		// Token: 0x04000E0A RID: 3594
		hunting,
		// Token: 0x04000E0B RID: 3595
		charging
	}

	// Token: 0x02000213 RID: 531
	public enum MovementState
	{
		// Token: 0x04000E0D RID: 3597
		flying,
		// Token: 0x04000E0E RID: 3598
		landed,
		// Token: 0x04000E0F RID: 3599
		charging
	}

	// Token: 0x02000214 RID: 532
	public class Target
	{
		// Token: 0x06000CF8 RID: 3320 RVA: 0x00050A63 File Offset: 0x0004EC63
		public void Clear()
		{
			this.transform = null;
			this.tagger = null;
			this.entity = null;
			this.isVisible = false;
		}

		// Token: 0x04000E10 RID: 3600
		public Transform transform;

		// Token: 0x04000E11 RID: 3601
		public ObjectTagger tagger;

		// Token: 0x04000E12 RID: 3602
		public GameEntity entity;

		// Token: 0x04000E13 RID: 3603
		public Vector3 lastSighted;

		// Token: 0x04000E14 RID: 3604
		public bool isVisible;

		// Token: 0x04000E15 RID: 3605
		public float lastSightedTimer;
	}
}
