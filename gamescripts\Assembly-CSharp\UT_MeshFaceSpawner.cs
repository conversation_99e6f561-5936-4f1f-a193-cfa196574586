﻿using System;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x020001AD RID: 429
public class UT_MeshFaceSpawner : MonoBehaviourGizmos
{
	// Token: 0x06000B4A RID: 2890 RVA: 0x000484FB File Offset: 0x000466FB
	private void Start()
	{
		if (this.spawnOnStart)
		{
			this.Spawn();
		}
	}

	// Token: 0x06000B4B RID: 2891 RVA: 0x0004850C File Offset: 0x0004670C
	public void Spawn()
	{
		if (this.useSeed)
		{
			int num = this.seed;
			this.seed = num + 1;
			Random.InitState(num);
		}
		int num2 = Random.Range(this.minSpawnAmount, this.maxSpawnAmount);
		Transform parent;
		if (this.parentObject != null)
		{
			if (this.checkTargetParentForSpawnReduction)
			{
				int num3 = this.parentObject.childCount;
				for (int i = num3 - 1; i >= 0; i--)
				{
					Transform child = this.parentObject.GetChild(i);
					if (!child.gameObject.activeSelf)
					{
						Object.Destroy(child.gameObject);
						num3--;
					}
				}
				num2 = Mathf.Min(num2, num2 - num3);
			}
			parent = this.parentObject;
		}
		else
		{
			parent = base.transform.parent;
		}
		if (this.useSeed)
		{
			int num = this.seed;
			this.seed = num + 1;
			Random.InitState(num);
		}
		num2 = Mathf.Max(num2, 0);
		if (num2 == 0)
		{
			return;
		}
		for (int j = 0; j < num2; j++)
		{
			if (this.spawnTable != null)
			{
				Object.Instantiate<GameObject>(this.spawnTable.GetRandomSpawnObject().GetRandomPrefab(), RandomMeshPointSampler.SamplePointWorld(base.GetComponent<MeshFilter>().sharedMesh, base.transform), base.transform.rotation, parent);
			}
			else
			{
				Object.Instantiate<GameObject>(this.spawnObjects[Random.Range(0, this.spawnObjects.Count)], RandomMeshPointSampler.SamplePointWorld(base.GetComponent<MeshFilter>().sharedMesh, base.transform), base.transform.rotation, parent);
			}
		}
	}

	// Token: 0x06000B4C RID: 2892 RVA: 0x00048698 File Offset: 0x00046898
	public override void DrawGizmos()
	{
		if (this.testSamples == null)
		{
			this.testSamples = new List<Vector3>();
		}
		if (GizmoContext.InSelection(this))
		{
			if (this.testSamples.Count != this.maxSpawnAmount)
			{
				this.testSamples.Clear();
				for (int i = 0; i < this.maxSpawnAmount; i++)
				{
					this.testSamples.Add(RandomMeshPointSampler.SamplePointWorld(base.GetComponent<MeshFilter>().sharedMesh, base.transform));
				}
			}
			for (int j = 0; j < this.maxSpawnAmount; j++)
			{
				Draw.SphereOutline(this.testSamples[j], 0.2f, Color.yellow);
			}
		}
	}

	// Token: 0x04000C36 RID: 3126
	public SpawnTable spawnTable;

	// Token: 0x04000C37 RID: 3127
	public List<GameObject> spawnObjects;

	// Token: 0x04000C38 RID: 3128
	public int minSpawnAmount = 1;

	// Token: 0x04000C39 RID: 3129
	public int maxSpawnAmount = 5;

	// Token: 0x04000C3A RID: 3130
	public bool spawnOnStart;

	// Token: 0x04000C3B RID: 3131
	public Transform parentObject;

	// Token: 0x04000C3C RID: 3132
	public bool checkTargetParentForSpawnReduction;

	// Token: 0x04000C3D RID: 3133
	private List<Vector3> testSamples;

	// Token: 0x04000C3E RID: 3134
	public bool useSeed;

	// Token: 0x04000C3F RID: 3135
	public int seed;
}
