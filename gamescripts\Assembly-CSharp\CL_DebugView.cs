﻿using System;
using Drawing;
using UnityEngine;

// Token: 0x020000B3 RID: 179
public class CL_DebugView : MonoBehaviour
{
	// Token: 0x060005DD RID: 1501 RVA: 0x000310EC File Offset: 0x0002F2EC
	private void Awake()
	{
		if (CL_DebugView.instance == null)
		{
			CL_DebugView.instance = this;
			CL_DebugView.draw = DrawingManager.GetBuilder(true);
			return;
		}
		Object.Destroy(this);
	}

	// Token: 0x060005DE RID: 1502 RVA: 0x00031113 File Offset: 0x0002F313
	private void Update()
	{
	}

	// Token: 0x060005DF RID: 1503 RVA: 0x00031115 File Offset: 0x0002F315
	private void LateUpdate()
	{
		CL_DebugView.draw.Dispose();
		CL_DebugView.draw = DrawingManager.GetBuilder(true);
		CL_DebugView.draw.cameraTargets = new Camera[] { Camera.main };
	}

	// Token: 0x060005E0 RID: 1504 RVA: 0x00031144 File Offset: 0x0002F344
	public static void SetCameraTarget(Camera camera)
	{
		CL_DebugView.draw.cameraTargets = new Camera[] { camera };
	}

	// Token: 0x0400078B RID: 1931
	public static bool initialized;

	// Token: 0x0400078C RID: 1932
	public static CL_DebugView instance;

	// Token: 0x0400078D RID: 1933
	public static CommandBuilder draw;
}
