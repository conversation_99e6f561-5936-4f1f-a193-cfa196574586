﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x020000F8 RID: 248
public class OS_File : Selectable, ISelectHandler, IEventSystemHandler, IPointerClickHandler, IDragHandler, IPointerEnterHandler, IPointerExitHandler, IBeginDragHandler, IEndDragHandler, OSPointerClickHandler, ISubmitHandler, IDeselectHandler
{
	// Token: 0x0600077C RID: 1916 RVA: 0x00038DE8 File Offset: 0x00036FE8
	private void Update()
	{
		if (this.open)
		{
			this.currentColor = Color.Lerp(this.currentColor, this.targetColor, Time.deltaTime * 15f);
		}
		else if (this.selected)
		{
			this.currentColor = Color.Lerp(this.currentColor, Color.red, Time.deltaTime * 15f);
		}
		else
		{
			this.currentColor = Color.Lerp(this.currentColor, Color.white, Time.deltaTime * 15f);
		}
		if (this.sprite)
		{
			this.sprite.color = this.currentColor;
		}
		ColorBlock colors = this.nameText.colors;
		colors.normalColor = this.currentColor;
		this.nameText.colors = colors;
		this.cTime += Time.deltaTime;
	}

	// Token: 0x0600077D RID: 1917 RVA: 0x00038EC4 File Offset: 0x000370C4
	public void Initialize(OS_Filesystem.FileInfo info, OS_Folder folder, OS_Manager osMan)
	{
		if (osMan == null)
		{
			this.os = OS_Manager.activeComputer;
		}
		else
		{
			this.os = osMan;
		}
		if (!info.hasInitialized && !info.useCustomFiletype)
		{
			Debug.Log(info.type.ToString());
			OS_Filesystem.FileInfo defaultFiletype = this.os.GetFilesystem().GetDefaultFiletype(info.type.ToString());
			if (info.icon == null)
			{
				info.icon = defaultFiletype.icon;
				info.openIcon = defaultFiletype.openIcon;
			}
			info.windowAsset = defaultFiletype.windowAsset;
		}
		this.sprite = base.GetComponent<Image>();
		this.currentContainer = base.transform.parent.GetComponent<RectTransform>();
		this.cTime = 1f;
		this.fileInfo = info;
		if (!this.fileInfo.useWorldPosition)
		{
			base.GetComponent<RectTransform>().anchoredPosition = this.fileInfo.position;
		}
		else
		{
			this.fileInfo.useWorldPosition = false;
			this.fileInfo.position = base.GetComponent<RectTransform>().anchoredPosition;
		}
		this.fileInfo.fileObject = this;
		this.itemName = this.fileInfo.name;
		base.transform.name = this.fileInfo.name + "_File";
		this.nameText.text = this.itemName;
		this.nameTextBackplate.sizeDelta = new Vector2(this.nameText.preferredWidth + 4f, 14f);
		this.sprite.sprite = info.icon;
		this.parent = base.transform.parent;
		this.parentFolder = folder;
		if (folder != null && folder.file != null)
		{
			this.fileInfo.parent = folder.file.fileInfo;
		}
		base.transform.localScale = Vector3.one;
		this.id = Guid.NewGuid().ToString();
		if (!info.hasInitialized)
		{
			if (info.openOnInitialize)
			{
				this.OpenFile();
			}
			info.hasInitialized = true;
		}
	}

	// Token: 0x0600077E RID: 1918 RVA: 0x000390F4 File Offset: 0x000372F4
	public void OpenFile()
	{
		if (this.open)
		{
			return;
		}
		this.sprite.sprite = this.fileInfo.openIcon;
		this.targetColor = Color.grey;
		this.open = true;
		this.selected = true;
		if (this.fileInfo.windowAsset)
		{
			if (this.fileInfo.targetWindow == null)
			{
				this.fileInfo.targetWindow = this.os.CreateAppWindow(this.fileInfo.windowAsset, this.id);
				if (this.fileInfo.type == OS_Filesystem.FileInfo.fileType.folder)
				{
					Debug.Log(this.fileInfo.targetWindow);
					Debug.Log(this.fileInfo.targetWindow.GetComponent<OS_Folder>());
					Debug.Log(this.fileInfo);
					Debug.Log(this.fileInfo.children);
					this.fileInfo.targetWindow.GetComponent<OS_Folder>().CreateFiles(this.fileInfo.children);
					this.fileInfo.targetWindow.transform.position = this.parentFolder.transform.position + new Vector2(15f, 15f);
				}
			}
			else
			{
				this.fileInfo.targetWindow.gameObject.SetActive(true);
				this.fileInfo.targetWindow.SetWindowName(this.fileInfo.name);
			}
			this.fileInfo.targetWindow.OpenApp(this);
			this.fileInfo.targetWindow.file = this;
			this.fileInfo.targetWindow.transform.SetAsLastSibling();
		}
		OS_Manager.soundPlayer.PlaySound("os:click");
	}

	// Token: 0x0600077F RID: 1919 RVA: 0x000392B8 File Offset: 0x000374B8
	public void CloseFile()
	{
		this.open = false;
		this.fileInfo.open = false;
		this.targetColor = Color.white;
		this.selected = false;
		this.sprite.sprite = this.fileInfo.icon;
	}

	// Token: 0x06000780 RID: 1920 RVA: 0x000392F8 File Offset: 0x000374F8
	internal void CloseFileRecursive()
	{
		if (this.fileInfo.children == null)
		{
			return;
		}
		foreach (OS_Filesystem.FileInfo fileInfo in this.fileInfo.children)
		{
			if (fileInfo != null && fileInfo.fileObject != null)
			{
				fileInfo.fileObject.CloseFileRecursive();
			}
		}
		if (this.open)
		{
			this.CloseFile();
		}
	}

	// Token: 0x06000781 RID: 1921 RVA: 0x00039384 File Offset: 0x00037584
	public void MoveIntoFolder(OS_Folder folder)
	{
		base.transform.SetParent(folder.contents.transform);
		this.parent = base.transform.parent;
		this.fileInfo.position = base.GetComponent<RectTransform>().anchoredPosition;
		this.fileInfo.location = folder.location + folder.windowName + "/";
		this.parentFolder.RemoveFile(this.fileInfo);
		folder.AddFile(this.fileInfo);
		this.parentFolder = folder;
		if (folder != null && folder.file != null)
		{
			this.fileInfo.parent = folder.file.fileInfo;
		}
	}

	// Token: 0x06000782 RID: 1922 RVA: 0x00039440 File Offset: 0x00037640
	public void RemoveFromFolder()
	{
	}

	// Token: 0x06000783 RID: 1923 RVA: 0x00039442 File Offset: 0x00037642
	public void DeleteFile()
	{
		if (this.fileInfo.open || this.open)
		{
			this.CloseFile();
		}
		this.fileInfo.parent.DeleteChild(this.fileInfo);
	}

	// Token: 0x06000784 RID: 1924 RVA: 0x00039475 File Offset: 0x00037675
	public void OSPointerClick(PointerEventData eventData)
	{
	}

	// Token: 0x06000785 RID: 1925 RVA: 0x00039477 File Offset: 0x00037677
	public override void OnSelect(BaseEventData eventData)
	{
		this.selected = true;
		base.OnSelect(eventData);
	}

	// Token: 0x06000786 RID: 1926 RVA: 0x00039487 File Offset: 0x00037687
	public override void OnDeselect(BaseEventData eventData)
	{
		this.selected = false;
		base.OnDeselect(eventData);
	}

	// Token: 0x06000787 RID: 1927 RVA: 0x00039497 File Offset: 0x00037697
	public void OnSubmit(BaseEventData eventData)
	{
		this.OpenFile();
	}

	// Token: 0x06000788 RID: 1928 RVA: 0x000394A0 File Offset: 0x000376A0
	public void OnPointerClick(PointerEventData eventData)
	{
		if (eventData.button == PointerEventData.InputButton.Left)
		{
			this.sprite.color = Color.grey;
			if (this.cTime <= this.dClickTime)
			{
				this.OpenFile();
			}
			this.cTime = 0f;
		}
		OS_Manager.soundPlayer.PlaySound("os:click");
	}

	// Token: 0x06000789 RID: 1929 RVA: 0x000394F4 File Offset: 0x000376F4
	public void OnDrag(PointerEventData eventData)
	{
		if (this.canDrag && !this.open && this.dragging && !this.fileInfo.open)
		{
			base.transform.position = OS_Manager.mousePosition + this.offset;
			this.fileInfo.position = base.GetComponent<RectTransform>().anchoredPosition;
			if (RaycastUtilities.PointerIsOverUI(eventData.position, "Folder", "OS"))
			{
				OS_Folder component = RaycastUtilities.UIRaycast(eventData.position, "Folder", "OS").transform.GetComponent<OS_Folder>();
				if (component != null && (component.file == null || !this.IsParent(this.fileInfo, component.file.fileInfo)))
				{
					this.MoveIntoFolder(component);
				}
			}
		}
	}

	// Token: 0x0600078A RID: 1930 RVA: 0x000395DC File Offset: 0x000377DC
	public void OnBeginDrag(PointerEventData eventData)
	{
		this.offset = base.transform.position - OS_Manager.mousePosition;
		base.transform.SetAsLastSibling();
		OS_Manager.soundPlayer.PlaySound("os:drag-start");
		this.dragging = true;
	}

	// Token: 0x0600078B RID: 1931 RVA: 0x0003962C File Offset: 0x0003782C
	public void OnEndDrag(PointerEventData eventData)
	{
		if (base.GetComponent<RectTransform>().anchoredPosition.y > -20f)
		{
			Vector2 vector = base.GetComponent<RectTransform>().anchoredPosition;
			vector = new Vector3(vector.x, -20f);
			this.fileInfo.position = vector;
			base.GetComponent<RectTransform>().anchoredPosition = vector;
		}
		this.dragging = false;
		OS_Manager.soundPlayer.PlaySound("os:drag-end");
	}

	// Token: 0x0600078C RID: 1932 RVA: 0x000396A0 File Offset: 0x000378A0
	public void SetText(string s)
	{
		this.fileInfo.name = s;
		this.itemName = this.fileInfo.name;
		base.transform.name = this.fileInfo.name + "_File";
		this.nameTextBackplate.sizeDelta = new Vector2(this.nameText.textComponent.preferredWidth + 4f, 14f);
	}

	// Token: 0x0600078D RID: 1933 RVA: 0x00039715 File Offset: 0x00037915
	public void ResetBackerWidth()
	{
		this.nameTextBackplate.sizeDelta = new Vector2(this.nameText.preferredWidth + 4f, 14f);
	}

	// Token: 0x0600078E RID: 1934 RVA: 0x00039740 File Offset: 0x00037940
	public bool IsDescendant(OS_Filesystem.FileInfo target, List<OS_Filesystem.FileInfo> children)
	{
		if (children == null || children.Count == 0)
		{
			return false;
		}
		foreach (OS_Filesystem.FileInfo fileInfo in children)
		{
			if (fileInfo == target)
			{
				return true;
			}
			if (fileInfo.children != null && fileInfo.children.Count > 0 && this.IsDescendant(target, fileInfo.children))
			{
				return true;
			}
		}
		return false;
	}

	// Token: 0x0600078F RID: 1935 RVA: 0x000397C8 File Offset: 0x000379C8
	public bool IsParent(OS_Filesystem.FileInfo targetParent, OS_Filesystem.FileInfo targetChild)
	{
		bool flag = false;
		int num = 0;
		if (targetChild.parent == null)
		{
			return false;
		}
		OS_Filesystem.FileInfo fileInfo = targetChild.parent;
		while (!flag && num < 100)
		{
			num++;
			if (targetParent == fileInfo)
			{
				return true;
			}
			if (fileInfo.parent == null)
			{
				return false;
			}
			fileInfo = fileInfo.parent;
		}
		return false;
	}

	// Token: 0x040008F2 RID: 2290
	private OS_Manager os;

	// Token: 0x040008F3 RID: 2291
	public string itemName;

	// Token: 0x040008F4 RID: 2292
	public OS_Filesystem.FileInfo fileInfo;

	// Token: 0x040008F5 RID: 2293
	public bool canDrag = true;

	// Token: 0x040008F6 RID: 2294
	private bool dragging;

	// Token: 0x040008F7 RID: 2295
	public bool selectable = true;

	// Token: 0x040008F8 RID: 2296
	private bool selected;

	// Token: 0x040008F9 RID: 2297
	public bool open;

	// Token: 0x040008FA RID: 2298
	private RectTransform currentContainer;

	// Token: 0x040008FB RID: 2299
	private Image sprite;

	// Token: 0x040008FC RID: 2300
	private Color targetColor = Color.white;

	// Token: 0x040008FD RID: 2301
	private Color currentColor = Color.white;

	// Token: 0x040008FE RID: 2302
	public InputField nameText;

	// Token: 0x040008FF RID: 2303
	public RectTransform nameTextBackplate;

	// Token: 0x04000900 RID: 2304
	private Transform parent;

	// Token: 0x04000901 RID: 2305
	private OS_Folder parentFolder;

	// Token: 0x04000902 RID: 2306
	public float dClickTime = 0.5f;

	// Token: 0x04000903 RID: 2307
	private float cTime;

	// Token: 0x04000904 RID: 2308
	private Vector2 offset;

	// Token: 0x04000905 RID: 2309
	private string id;
}
