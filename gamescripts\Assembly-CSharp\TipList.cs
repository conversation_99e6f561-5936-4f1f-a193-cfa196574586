﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x020000C5 RID: 197
[CreateAssetMenu(fileName = "New Tip List", menuName = "White Knuckle/Other/Tip List")]
public class TipList : ScriptableObject
{
	// Token: 0x06000677 RID: 1655 RVA: 0x00034917 File Offset: 0x00032B17
	public string GetTip()
	{
		return this.tips[Random.Range(0, this.tips.Count)];
	}

	// Token: 0x040007EC RID: 2028
	public List<string> tips;
}
