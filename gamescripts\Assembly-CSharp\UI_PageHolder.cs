﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

// Token: 0x02000132 RID: 306
public class UI_PageHolder : MonoBehaviour
{
	// Token: 0x060008FA RID: 2298 RVA: 0x0003EE38 File Offset: 0x0003D038
	private void Start()
	{
		this.UpdatePage();
	}

	// Token: 0x060008FB RID: 2299 RVA: 0x0003EE40 File Offset: 0x0003D040
	public void UpdatePage()
	{
		for (int i = 0; i < this.pages.Count; i++)
		{
			if (this.currentPage == i)
			{
				this.pages[i].gameObject.SetActive(true);
			}
			else
			{
				this.pages[i].gameObject.SetActive(false);
			}
		}
		if (this.pageTitle != null)
		{
			this.pageTitle.text = this.pages[this.currentPage].title + string.Format(" ({0}/{1})", this.currentPage + 1, this.pages.Count);
		}
	}

	// Token: 0x060008FC RID: 2300 RVA: 0x0003EEF7 File Offset: 0x0003D0F7
	public void NextPage()
	{
		this.currentPage++;
		if (this.currentPage >= this.pages.Count)
		{
			this.currentPage = 0;
		}
		this.UpdatePage();
	}

	// Token: 0x060008FD RID: 2301 RVA: 0x0003EF27 File Offset: 0x0003D127
	public void PreviousPage()
	{
		this.currentPage--;
		if (this.currentPage < 0)
		{
			this.currentPage = this.pages.Count - 1;
		}
		this.UpdatePage();
	}

	// Token: 0x04000A47 RID: 2631
	public int currentPage;

	// Token: 0x04000A48 RID: 2632
	public List<UI_Page> pages;

	// Token: 0x04000A49 RID: 2633
	public TMP_Text pageTitle;
}
