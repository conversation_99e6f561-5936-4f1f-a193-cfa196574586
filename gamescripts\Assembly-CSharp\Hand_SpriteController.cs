using System;
using UnityEngine;

// Token: 0x02000092 RID: 146
public class Hand_SpriteController : MonoBehaviour
{
	// Token: 0x060004E1 RID: 1249 RVA: 0x0002A36C File Offset: 0x0002856C
	private void OnEnable()
	{
		this.sprite = base.gameObject.GetComponent<SpriteRenderer>();
	}

	// Token: 0x060004E2 RID: 1250 RVA: 0x0002A37F File Offset: 0x0002857F
	private void Update()
	{
	}

	// Token: 0x060004E3 RID: 1251 RVA: 0x0002A381 File Offset: 0x00028581
	public void SetColor(Color c)
	{
		if (this.sprite == null)
		{
			return;
		}
		this.sprite.color = c;
	}

	// Token: 0x04000673 RID: 1651
	private SpriteRenderer sprite;

	// Token: 0x04000674 RID: 1652
	public string spriteName;
}
