﻿using System;
using UnityEngine;
using UnityEngine.Rendering.PostProcessing;

// Token: 0x020001BC RID: 444
public sealed class PostProcessGammaRenderer : PostProcessEffectRenderer<PostProcessGamma>
{
	// Token: 0x06000B7C RID: 2940 RVA: 0x0004986C File Offset: 0x00047A6C
	public override void Render(PostProcessRenderContext context)
	{
		PropertySheet propertySheet = context.propertySheets.Get(Shader.Find("Hidden/Dark Machine/Gamma Post Process"));
		propertySheet.properties.SetFloat("_Amount", base.settings.amount);
		Matrix4x4 inverse = GL.GetGPUProjectionMatrix(context.camera.projectionMatrix, true).inverse;
		propertySheet.properties.SetMatrix("_ClipToView", inverse);
		context.command.BlitFullscreenTriangle(context.source, context.destination, propertySheet, 0, false, null, false);
	}
}
