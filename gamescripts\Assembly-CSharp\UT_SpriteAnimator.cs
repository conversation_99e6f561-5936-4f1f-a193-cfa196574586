﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000199 RID: 409
public class UT_SpriteAnimator : MonoBehaviour
{
	// Token: 0x06000AF4 RID: 2804 RVA: 0x0004730E File Offset: 0x0004550E
	private void Start()
	{
		this.spriteRenderer = base.GetComponent<SpriteRenderer>();
		this.SetAnimation(this.startAnimationName);
	}

	// Token: 0x06000AF5 RID: 2805 RVA: 0x00047328 File Offset: 0x00045528
	private void Update()
	{
		if (this.currentAnimation != null)
		{
			this.PlayAnimation();
		}
	}

	// Token: 0x06000AF6 RID: 2806 RVA: 0x00047338 File Offset: 0x00045538
	private void PlayAnimation()
	{
		int num = Mathf.FloorToInt(Mathf.Repeat(Time.time * this.currentAnimation.animationSpeed, (float)this.currentAnimation.sprites.Count));
		Sprite sprite = this.currentAnimation.sprites[num];
		this.spriteRenderer.sprite = sprite;
	}

	// Token: 0x06000AF7 RID: 2807 RVA: 0x00047390 File Offset: 0x00045590
	public void SetAnimation(string s)
	{
		foreach (UT_SpriteAnimator.SpriteAnimation spriteAnimation in this.animations)
		{
			if (spriteAnimation.name == s)
			{
				this.currentAnimation = spriteAnimation;
				break;
			}
		}
	}

	// Token: 0x04000BF4 RID: 3060
	public List<UT_SpriteAnimator.SpriteAnimation> animations;

	// Token: 0x04000BF5 RID: 3061
	private SpriteRenderer spriteRenderer;

	// Token: 0x04000BF6 RID: 3062
	public string startAnimationName;

	// Token: 0x04000BF7 RID: 3063
	private UT_SpriteAnimator.SpriteAnimation currentAnimation;

	// Token: 0x020002E2 RID: 738
	[Serializable]
	public class SpriteAnimation
	{
		// Token: 0x04001261 RID: 4705
		public string name;

		// Token: 0x04001262 RID: 4706
		public List<Sprite> sprites;

		// Token: 0x04001263 RID: 4707
		public float animationSpeed = 1f;
	}
}
