﻿using System;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x02000186 RID: 390
public class UT_Portal : MonoBehaviourGizmos
{
	// Token: 0x06000AB3 RID: 2739 RVA: 0x00046504 File Offset: 0x00044704
	private void Start()
	{
		this.updateTick = Random.Range(0, 20);
		this.currentBounds = this.bounds;
	}

	// Token: 0x06000AB4 RID: 2740 RVA: 0x00046520 File Offset: 0x00044720
	private void Update()
	{
		this.currentBounds.center = base.transform.position;
		this.updateTick--;
		if (this.updateTick <= 0)
		{
			bool flag = this.currentBounds.Contains(ENT_Player.GetPlayer().transform.position);
			this.updateTick = 10;
			if (flag)
			{
				foreach (GameObject gameObject in this.internalObjects)
				{
					if (!gameObject.activeSelf)
					{
						gameObject.SetActive(true);
					}
				}
				using (List<GameObject>.Enumerator enumerator = this.externalObjects.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						GameObject gameObject2 = enumerator.Current;
						if (gameObject2.activeSelf)
						{
							gameObject2.SetActive(false);
						}
					}
					return;
				}
			}
			foreach (GameObject gameObject3 in this.internalObjects)
			{
				if (gameObject3.activeSelf)
				{
					gameObject3.SetActive(false);
				}
			}
			foreach (GameObject gameObject4 in this.externalObjects)
			{
				if (!gameObject4.activeSelf)
				{
					gameObject4.SetActive(true);
				}
			}
		}
	}

	// Token: 0x06000AB5 RID: 2741 RVA: 0x000466B4 File Offset: 0x000448B4
	public override void DrawGizmos()
	{
		base.DrawGizmos();
		GizmoContext.InSelection(this);
		this.bounds.center = base.transform.position;
		Draw.WireBox(this.bounds, Color.white);
		foreach (UT_Portal ut_Portal in this.connectedPortals)
		{
			Draw.Line(ut_Portal.transform.position, base.transform.position);
		}
	}

	// Token: 0x04000BB4 RID: 2996
	public Bounds bounds;

	// Token: 0x04000BB5 RID: 2997
	private Bounds currentBounds;

	// Token: 0x04000BB6 RID: 2998
	public List<GameObject> internalObjects;

	// Token: 0x04000BB7 RID: 2999
	public List<GameObject> externalObjects;

	// Token: 0x04000BB8 RID: 3000
	public List<UT_Portal> connectedPortals;

	// Token: 0x04000BB9 RID: 3001
	private int updateTick;
}
