﻿using System;
using UnityEngine;

// Token: 0x02000027 RID: 39
[ExecuteInEditMode]
public class FX_CameraShaderController : MonoBehaviour
{
	// Token: 0x06000179 RID: 377 RVA: 0x0000B93C File Offset: 0x00009B3C
	private void Start()
	{
		if (this.cam == null)
		{
			this.cam = base.gameObject.GetComponent<Camera>();
		}
		this.RefreshDepthTexture();
		if (Application.isPlaying)
		{
			SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Combine(SettingsManager.SettingsRefreshUpdate, new Action(this.RefreshDepthTexture));
		}
	}

	// Token: 0x0600017A RID: 378 RVA: 0x0000B995 File Offset: 0x00009B95
	private void OnDestroy()
	{
		if (Application.isPlaying)
		{
			SettingsManager.SettingsRefreshUpdate = (Action)Delegate.Remove(SettingsManager.SettingsRefreshUpdate, new Action(this.RefreshDepthTexture));
		}
	}

	// Token: 0x0600017B RID: 379 RVA: 0x0000B9BE File Offset: 0x00009BBE
	private void OnValidate()
	{
		if (this.cam == null)
		{
			this.cam = base.gameObject.GetComponent<Camera>();
		}
		this.RefreshDepthTexture();
	}

	// Token: 0x0600017C RID: 380 RVA: 0x0000B9E8 File Offset: 0x00009BE8
	private void RefreshDepthTexture()
	{
		if (Application.isPlaying && SettingsManager.settings != null && SettingsManager.settings.debugCamDepthDisable)
		{
			this.cam.depthTextureMode = DepthTextureMode.None;
			return;
		}
		if (this.useDepthTexture)
		{
			this.cam.depthTextureMode = DepthTextureMode.Depth;
			return;
		}
		this.cam.depthTextureMode = DepthTextureMode.None;
	}

	// Token: 0x0600017D RID: 381 RVA: 0x0000BA3D File Offset: 0x00009C3D
	private bool IsUsingDepthTexture()
	{
		if (this.cam == null)
		{
			this.cam = base.gameObject.GetComponent<Camera>();
		}
		return this.cam.depthTextureMode == DepthTextureMode.Depth;
	}

	// Token: 0x0600017E RID: 382 RVA: 0x0000BA6C File Offset: 0x00009C6C
	private void OnPreRender()
	{
		if (this.useCameraFX)
		{
			FXManager.CameraFX(this.cam);
		}
	}

	// Token: 0x0400015D RID: 349
	private Camera cam;

	// Token: 0x0400015E RID: 350
	public bool useCameraFX;

	// Token: 0x0400015F RID: 351
	public bool useDepthTexture;
}
