﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000083 RID: 131
public class CL_Handhold : MonoBehaviour, Clickable
{
	// Token: 0x0600046D RID: 1133 RVA: 0x00027029 File Offset: 0x00025229
	private void Start()
	{
		this.Initialize();
	}

	// Token: 0x0600046E RID: 1134 RVA: 0x00027034 File Offset: 0x00025234
	public void Initialize()
	{
		if (this.initialized)
		{
			return;
		}
		this.initialized = true;
		this.softParent = base.GetComponent<UT_SoftParent>();
		if (this.handholdRenderer == null)
		{
			this.handholdRenderer = base.GetComponent<Renderer>();
		}
		if (this.handholdRenderer != null)
		{
			this.handholdRenderer.material = FXManager.GetSharedHandholdMaterial(this.handholdRenderer.material);
		}
	}

	// Token: 0x0600046F RID: 1135 RVA: 0x000270A0 File Offset: 0x000252A0
	internal virtual void Update()
	{
		if (!this.holding && this.hands.Count > 0)
		{
			this.holding = true;
			this.player = this.hands[0].GetPlayer();
		}
	}

	// Token: 0x06000470 RID: 1136 RVA: 0x000270D6 File Offset: 0x000252D6
	public void Reset()
	{
	}

	// Token: 0x06000471 RID: 1137 RVA: 0x000270D8 File Offset: 0x000252D8
	public bool CanInteract(ENT_Player p)
	{
		return this.active;
	}

	// Token: 0x06000472 RID: 1138 RVA: 0x000270E0 File Offset: 0x000252E0
	internal virtual void Stop()
	{
		this.holding = false;
		this.stopEvent.Invoke();
	}

	// Token: 0x06000473 RID: 1139 RVA: 0x000270F4 File Offset: 0x000252F4
	public virtual void Interact(ENT_Player p, ENT_Player.Hand hand)
	{
		this.activeEvent.Invoke();
		this.holding = true;
		this.player = p;
		this.inUse = true;
		if (!this.hands.Contains(hand))
		{
			this.hands.Add(hand);
		}
	}

	// Token: 0x06000474 RID: 1140 RVA: 0x00027130 File Offset: 0x00025330
	public virtual void StopInteract(ENT_Player p, ENT_Player.Hand dropHand, string s = "")
	{
		this.LetGo(dropHand);
		if (this.hands.Count == 0)
		{
			this.player = null;
			this.Stop();
		}
	}

	// Token: 0x06000475 RID: 1141 RVA: 0x00027153 File Offset: 0x00025353
	public ObjectTagger GetTagger()
	{
		return base.gameObject.GetComponent<ObjectTagger>();
	}

	// Token: 0x06000476 RID: 1142 RVA: 0x00027160 File Offset: 0x00025360
	public GameObject GetGameObject()
	{
		return base.gameObject;
	}

	// Token: 0x06000477 RID: 1143 RVA: 0x00027168 File Offset: 0x00025368
	public virtual void HammerIn(float amount = 0.35f)
	{
		if (this.secure)
		{
			return;
		}
		this.hammerEvent.Invoke();
		this.skipFixedUpdate = true;
		this.secureAmount += amount;
		float num = -0.45f * amount;
		if (this.offsetAmount + num < -this.secureMoveMax)
		{
			num = -this.secureMoveMax - this.offsetAmount;
		}
		if (this.softParent)
		{
			this.softParent.SoftMove(base.transform.forward * num);
			this.offsetAmount += num;
		}
		else
		{
			base.transform.position += base.transform.forward * num;
			this.offsetAmount += num;
		}
		if (this.secureAmount >= 1f)
		{
			UT_Highlight component = base.GetComponent<UT_Highlight>();
			if (component != null)
			{
				component.active = false;
			}
			this.secure = true;
		}
	}

	// Token: 0x06000478 RID: 1144 RVA: 0x0002725F File Offset: 0x0002545F
	public void LetGo(ENT_Player.Hand dropHand = null)
	{
		if (dropHand != null)
		{
			this.hands.Remove(dropHand);
		}
		else
		{
			this.hands.Clear();
		}
		if (this.hands.Count == 0)
		{
			this.inUse = false;
		}
	}

	// Token: 0x06000479 RID: 1145 RVA: 0x00027294 File Offset: 0x00025494
	private void FixedUpdate()
	{
		if (!this.secure && this.inUse && !this.skipFixedUpdate)
		{
			if (this.secureAmount < 0f)
			{
				Object.Instantiate<GameObject>(this.breakObject, base.transform.position, base.transform.rotation);
				while (this.hands.Count > 0)
				{
					this.hands[0].DropHand(false);
				}
				UT_AudioClipHandler component = base.GetComponent<UT_AudioClipHandler>();
				if (component != null)
				{
					component.GetGroup("piton").PlayAudioSetAtPoint("break", base.transform.position);
				}
				this.LetGo(null);
				Object.Destroy(base.gameObject);
			}
			if (this.softParent)
			{
				this.softParent.SoftMove(base.transform.forward * Time.fixedDeltaTime * 0.05f);
			}
			else
			{
				base.transform.position += base.transform.forward * Time.fixedDeltaTime * 0.05f;
			}
			this.secureAmount -= Time.deltaTime * 0.115f;
			this.offsetAmount += Time.fixedDeltaTime * 0.05f;
		}
		this.skipFixedUpdate = false;
	}

	// Token: 0x0600047A RID: 1146 RVA: 0x000273FD File Offset: 0x000255FD
	public bool GetHolding()
	{
		return this.holding;
	}

	// Token: 0x0600047B RID: 1147 RVA: 0x00027405 File Offset: 0x00025605
	public bool GetInUse()
	{
		return this.inUse;
	}

	// Token: 0x0600047C RID: 1148 RVA: 0x0002740D File Offset: 0x0002560D
	public ENT_Player GetPlayer()
	{
		return this.player;
	}

	// Token: 0x0600047D RID: 1149 RVA: 0x00027415 File Offset: 0x00025615
	public virtual float GetClimbMult()
	{
		return this.climbMult;
	}

	// Token: 0x0600047E RID: 1150 RVA: 0x0002741D File Offset: 0x0002561D
	public void SetSecure(bool b)
	{
		this.secure = b;
	}

	// Token: 0x040005D4 RID: 1492
	public bool active = true;

	// Token: 0x040005D5 RID: 1493
	internal List<ENT_Player.Hand> hands = new List<ENT_Player.Hand>();

	// Token: 0x040005D6 RID: 1494
	private ENT_Player player;

	// Token: 0x040005D7 RID: 1495
	private bool holding;

	// Token: 0x040005D8 RID: 1496
	private bool inUse;

	// Token: 0x040005D9 RID: 1497
	public UnityEvent activeEvent;

	// Token: 0x040005DA RID: 1498
	public UnityEvent stopEvent;

	// Token: 0x040005DB RID: 1499
	public bool canHammer;

	// Token: 0x040005DC RID: 1500
	public bool secure = true;

	// Token: 0x040005DD RID: 1501
	public float secureAmount = 1f;

	// Token: 0x040005DE RID: 1502
	public float secureMoveMax = 0.45f;

	// Token: 0x040005DF RID: 1503
	public GameObject breakObject;

	// Token: 0x040005E0 RID: 1504
	public UnityEvent hammerEvent;

	// Token: 0x040005E1 RID: 1505
	public float dragMult = 10f;

	// Token: 0x040005E2 RID: 1506
	public float velocityMult = 1f;

	// Token: 0x040005E3 RID: 1507
	public float climbMult = 1f;

	// Token: 0x040005E4 RID: 1508
	public float climbGrav;

	// Token: 0x040005E5 RID: 1509
	public float strainRate = 1f;

	// Token: 0x040005E6 RID: 1510
	private float offsetAmount;

	// Token: 0x040005E7 RID: 1511
	private UT_SoftParent softParent;

	// Token: 0x040005E8 RID: 1512
	public Renderer handholdRenderer;

	// Token: 0x040005E9 RID: 1513
	private bool initialized;

	// Token: 0x040005EA RID: 1514
	private bool skipFixedUpdate;
}
