﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200010F RID: 271
[Serializable]
public class SessionEventModule_FlashVignette : SessionEventModule
{
	// Token: 0x06000831 RID: 2097 RVA: 0x0003B8CC File Offset: 0x00039ACC
	public override void Initialize(SessionEvent s)
	{
		base.Initialize(s);
		this.Activate();
	}

	// Token: 0x06000832 RID: 2098 RVA: 0x0003B8DB File Offset: 0x00039ADB
	public override void Activate()
	{
		base.Activate();
		this.Play();
	}

	// Token: 0x06000833 RID: 2099 RVA: 0x0003B8EC File Offset: 0x00039AEC
	public void Play()
	{
		if (this.useCustomSprite)
		{
			CL_UIManager.instance.SetVignetteImage(this.vignetteID, this.customSprites[Random.Range(0, this.customSprites.Count)]);
		}
		if (this.useCustomColor)
		{
			CL_UIManager.instance.SetVignetteColor(this.vignetteID, this.customColor);
		}
		CL_UIManager.instance.FlashVignette(this.vignetteID);
	}

	// Token: 0x0400099D RID: 2461
	public string vignetteID;

	// Token: 0x0400099E RID: 2462
	public bool useCustomSprite;

	// Token: 0x0400099F RID: 2463
	public List<Sprite> customSprites;

	// Token: 0x040009A0 RID: 2464
	public bool useCustomColor;

	// Token: 0x040009A1 RID: 2465
	public Color customColor = Color.white;
}
