﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000035 RID: 53
public class DEN_Gasbag : Denizen
{
	// Token: 0x06000200 RID: 512 RVA: 0x0001091C File Offset: 0x0000EB1C
	public override void Start()
	{
		base.Start();
		this.barkTime = Random.Range(this.barkRate, this.barkRate * 2f);
		this.rigid = base.GetComponent<Rigidbody>();
		this.rigid.useGravity = false;
		this.meshFilter = base.GetComponent<MeshFilter>();
		this.meshFilter.mesh = this.meshOptions[Random.Range(0, this.meshOptions.Count)];
		this.flock = new List<DEN_Gasbag>();
		base.transform.localScale *= Random.Range(this.minSize, this.maxSize);
		this.wanderOffset = Random.Range(0f, 100f);
		Collider[] array = Physics.OverlapSphere(base.transform.position, 10f, this.flockLayermask);
		this.startHeight = base.transform.position.y;
		if (array.Length != 0)
		{
			Collider[] array2 = array;
			for (int i = 0; i < array2.Length; i++)
			{
				DEN_Gasbag component = array2[i].GetComponent<DEN_Gasbag>();
				if (component != null && component != this)
				{
					this.flock.Add(component);
				}
			}
		}
		this.clipHandler = base.GetComponent<UT_AudioClipHandler>();
	}

	// Token: 0x06000201 RID: 513 RVA: 0x00010A5C File Offset: 0x0000EC5C
	public override void FixedUpdate()
	{
		base.FixedUpdate();
		this.wanderDirection.x = this.wanderDirection.x + (Mathf.PerlinNoise(Time.time - base.transform.position.x + this.wanderOffset, Time.time + base.transform.position.x + this.wanderOffset) - 0.5f) * Time.fixedDeltaTime * this.wanderDrift;
		this.wanderDirection.y = this.wanderDirection.y + (Mathf.PerlinNoise(Time.time - base.transform.position.y + this.wanderOffset, Time.time + base.transform.position.y + this.wanderOffset) - 0.5f) * Time.fixedDeltaTime * this.wanderDrift;
		this.wanderDirection.z = this.wanderDirection.z + (Mathf.PerlinNoise(Time.time - base.transform.position.z + this.wanderOffset, Time.time + base.transform.position.z + this.wanderOffset) - 0.5f) * Time.fixedDeltaTime * this.wanderDrift;
		this.wanderDirection = this.wanderDirection.normalized;
		this.wanderDirection += this.GetFlockingVector() * 0.5f * this.flockCohesion;
		if (CL_UIManager.debug)
		{
			CL_DebugView.draw.Arrow(base.transform.position, base.transform.position + this.wanderDirection * 2f);
		}
		if (this.target != null && !CL_GameManager.noTarget)
		{
			this.wanderDirection += (this.target.position - base.transform.position).normalized;
		}
		else
		{
			this.wanderDirection.y = this.wanderDirection.y + (this.startHeight - base.transform.position.y) * 0.1f;
		}
		this.rigid.AddForce(this.wanderDirection * Time.fixedDeltaTime * this.speed);
		base.transform.rotation = Quaternion.Lerp(base.transform.rotation, Quaternion.LookRotation(Vector3.Scale(this.rigid.velocity.normalized, new Vector3(1f, 0.01f, 1f))), Time.fixedDeltaTime * 1.2f);
		if (base.IsTickFrame())
		{
			this.ExplodeScan();
			this.ScanForTarget();
		}
		this.barkTime -= Time.fixedDeltaTime;
		if (this.barkTime < 0f)
		{
			this.barkTime = Random.Range(this.barkRate, this.barkRate * 2f);
			this.clipHandler.PlaySound("gasbag:grunt");
		}
	}

	// Token: 0x06000202 RID: 514 RVA: 0x00010D6C File Offset: 0x0000EF6C
	public Vector3 GetFlockingVector()
	{
		Vector3 vector = Vector3.zero;
		foreach (DEN_Gasbag den_Gasbag in this.flock)
		{
			vector += (den_Gasbag.transform.position - base.transform.position).normalized / (float)this.flock.Count;
		}
		return vector;
	}

	// Token: 0x06000203 RID: 515 RVA: 0x00010DFC File Offset: 0x0000EFFC
	public void ScanForTarget()
	{
		if (CL_GameManager.noTarget)
		{
			return;
		}
		if (this.target != null)
		{
			if (Vector3.Distance(base.transform.position, this.target.position) > this.sightDistance * 1.5f)
			{
				this.target = null;
			}
			return;
		}
		foreach (Collider collider in Physics.OverlapSphere(base.transform.position, this.sightDistance))
		{
			ObjectTagger component = collider.gameObject.GetComponent<ObjectTagger>();
			if (component != null && component.HasTagInList(this.explodeTags))
			{
				this.target = collider.transform;
				this.clipHandler.PlaySound("gasbag:alert");
				return;
			}
		}
	}

	// Token: 0x06000204 RID: 516 RVA: 0x00010EBC File Offset: 0x0000F0BC
	public void ExplodeScan()
	{
		if (this.dead)
		{
			return;
		}
		foreach (Collider collider in Physics.OverlapSphere(base.transform.position, this.explodeDetectRadius))
		{
			ObjectTagger component = collider.gameObject.GetComponent<ObjectTagger>();
			if (component != null && component.HasTagInList(this.explodeTags))
			{
				float num = Vector3.Angle(collider.transform.position - base.transform.position, Vector3.up);
				this.Kill("");
				if (component.HasTag("Entity"))
				{
					GameEntity component2 = collider.GetComponent<GameEntity>();
					Vector3 vector = (collider.transform.position - base.transform.position).normalized;
					if (num < 70f)
					{
						vector = Vector3.up * 1.4f;
						if (component.HasTag("Player"))
						{
							CL_AchievementManager.SetAchievementValue("ACH_GASBAGJUMP", true);
							component2.ClampVelocityToAxis(Vector3.up);
						}
					}
					component2.AddForce(vector * this.explodeForce);
					component2.Damage(this.explodeDamage, this.objectType);
				}
				this.dead = true;
				return;
			}
		}
	}

	// Token: 0x06000205 RID: 517 RVA: 0x00011008 File Offset: 0x0000F208
	private void OnCollisionEnter(Collision other)
	{
		this.wanderDirection = other.contacts[0].normal;
	}

	// Token: 0x06000206 RID: 518 RVA: 0x00011021 File Offset: 0x0000F221
	public override void Kill(string type = "")
	{
		this.clipHandler.PlaySound("gasbag:death");
		base.Kill("");
	}

	// Token: 0x06000207 RID: 519 RVA: 0x0001103E File Offset: 0x0000F23E
	public override void AddForce(Vector3 v)
	{
		this.rigid.AddForce(v);
		base.AddForce(v);
	}

	// Token: 0x06000208 RID: 520 RVA: 0x00011053 File Offset: 0x0000F253
	public override bool Damage(float amount, string type)
	{
		this.clipHandler.PlaySound("gasbag:hurt");
		return base.Damage(amount, type);
	}

	// Token: 0x06000209 RID: 521 RVA: 0x0001106D File Offset: 0x0000F26D
	public override void OffsetEntity(float amount)
	{
		base.OffsetEntity(amount);
		this.startHeight += amount;
	}

	// Token: 0x04000247 RID: 583
	[Header("Movement")]
	public float speed = 1f;

	// Token: 0x04000248 RID: 584
	private Rigidbody rigid;

	// Token: 0x04000249 RID: 585
	private Vector3 wanderDirection;

	// Token: 0x0400024A RID: 586
	public float wanderDrift = 100f;

	// Token: 0x0400024B RID: 587
	public float flockCohesion = 0.5f;

	// Token: 0x0400024C RID: 588
	private List<DEN_Gasbag> flock;

	// Token: 0x0400024D RID: 589
	public LayerMask flockLayermask;

	// Token: 0x0400024E RID: 590
	[Header("Visuals")]
	public float minSize = 1f;

	// Token: 0x0400024F RID: 591
	[Header("Visuals")]
	public float maxSize = 1f;

	// Token: 0x04000250 RID: 592
	private MeshFilter meshFilter;

	// Token: 0x04000251 RID: 593
	public List<Mesh> meshOptions;

	// Token: 0x04000252 RID: 594
	[Header("Explosion")]
	public string[] explodeTags;

	// Token: 0x04000253 RID: 595
	public float explodeDamage = 1f;

	// Token: 0x04000254 RID: 596
	private float wanderOffset;

	// Token: 0x04000255 RID: 597
	public float explodeDetectRadius = 3f;

	// Token: 0x04000256 RID: 598
	public float explodeForce = 2f;

	// Token: 0x04000257 RID: 599
	[Header("AI")]
	public float sightDistance = 6f;

	// Token: 0x04000258 RID: 600
	private float startHeight;

	// Token: 0x04000259 RID: 601
	[Header("Audio")]
	public float barkRate = 5f;

	// Token: 0x0400025A RID: 602
	private float barkTime = 10f;
}
