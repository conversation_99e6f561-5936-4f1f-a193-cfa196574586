﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x020000CC RID: 204
public class UT_EventController : MonoBehaviour
{
	// Token: 0x060006A7 RID: 1703 RVA: 0x0003586C File Offset: 0x00033A6C
	private void Awake()
	{
		this.eventDictionary = new Dictionary<string, UT_EventController.EventContainer>();
		foreach (UT_EventController.EventContainer eventContainer in this.events)
		{
			this.eventDictionary.Add(eventContainer.name, eventContainer);
		}
	}

	// Token: 0x060006A8 RID: 1704 RVA: 0x000358D8 File Offset: 0x00033AD8
	public void PlayEvent(string eventName)
	{
		if (this.eventDictionary.ContainsKey(eventName) && !this.eventDictionary[eventName].Play())
		{
			string falseEventName = this.eventDictionary[eventName].falseEventName;
			if (falseEventName != "")
			{
				this.eventDictionary[falseEventName].Play();
			}
		}
	}

	// Token: 0x060006A9 RID: 1705 RVA: 0x00035938 File Offset: 0x00033B38
	public void PlayAllEvents()
	{
		foreach (UT_EventController.EventContainer eventContainer in this.events)
		{
			eventContainer.Play();
		}
	}

	// Token: 0x060006AA RID: 1706 RVA: 0x0003598C File Offset: 0x00033B8C
	public void ResetEventCounter(string eventName)
	{
		if (this.eventDictionary.ContainsKey(eventName))
		{
			this.eventDictionary[eventName].ResetPlays();
		}
	}

	// Token: 0x060006AB RID: 1707 RVA: 0x000359B0 File Offset: 0x00033BB0
	public void ResetAllEventCounters()
	{
		foreach (UT_EventController.EventContainer eventContainer in this.events)
		{
			eventContainer.ResetPlays();
		}
	}

	// Token: 0x0400082C RID: 2092
	public List<UT_EventController.EventContainer> events = new List<UT_EventController.EventContainer>();

	// Token: 0x0400082D RID: 2093
	private Dictionary<string, UT_EventController.EventContainer> eventDictionary;

	// Token: 0x02000290 RID: 656
	[Serializable]
	public class EventContainer
	{
		// Token: 0x06000E5A RID: 3674 RVA: 0x00056FA4 File Offset: 0x000551A4
		public bool Play()
		{
			if (this.useSpawnSettings && !this.spawnSettings.RandomCheck())
			{
				return false;
			}
			if (this.maxPlays > 0)
			{
				if (this.currentPlays >= this.maxPlays)
				{
					return false;
				}
				this.currentPlays++;
			}
			this.playEvents.Invoke();
			return true;
		}

		// Token: 0x06000E5B RID: 3675 RVA: 0x00056FFB File Offset: 0x000551FB
		public void ResetPlays()
		{
			this.currentPlays = 0;
		}

		// Token: 0x04001096 RID: 4246
		public string name;

		// Token: 0x04001097 RID: 4247
		public UnityEvent playEvents;

		// Token: 0x04001098 RID: 4248
		public int maxPlays;

		// Token: 0x04001099 RID: 4249
		private int currentPlays;

		// Token: 0x0400109A RID: 4250
		public bool useSpawnSettings;

		// Token: 0x0400109B RID: 4251
		public SpawnTable.SpawnSettings spawnSettings;

		// Token: 0x0400109C RID: 4252
		public string falseEventName;
	}
}
