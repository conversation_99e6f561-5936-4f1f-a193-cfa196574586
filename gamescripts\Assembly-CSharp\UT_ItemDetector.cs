﻿using System;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200017A RID: 378
public class UT_ItemDetector : MonoBehaviour
{
	// Token: 0x06000A79 RID: 2681 RVA: 0x00044C30 File Offset: 0x00042E30
	private void Update()
	{
		if (!this.active)
		{
			return;
		}
		if (this.runContinuouslyUntilFound && !this.hasFoundItem)
		{
			this.checkTime -= Time.deltaTime;
			if (this.checkTime <= 0f)
			{
				this.Search();
				this.checkTime = 1f;
			}
		}
	}

	// Token: 0x06000A7A RID: 2682 RVA: 0x00044C86 File Offset: 0x00042E86
	public void Search()
	{
		if (Inventory.instance.HasItemWithTag(this.searchTag, this.checkInHand))
		{
			this.searchSuccess.Invoke();
			this.hasFoundItem = true;
			return;
		}
		this.searchFail.Invoke();
	}

	// Token: 0x06000A7B RID: 2683 RVA: 0x00044CBE File Offset: 0x00042EBE
	public void SetActive(bool b)
	{
		this.active = b;
	}

	// Token: 0x04000B7C RID: 2940
	public string searchTag;

	// Token: 0x04000B7D RID: 2941
	public bool active = true;

	// Token: 0x04000B7E RID: 2942
	public UnityEvent searchSuccess;

	// Token: 0x04000B7F RID: 2943
	public UnityEvent searchFail;

	// Token: 0x04000B80 RID: 2944
	public bool runContinuouslyUntilFound;

	// Token: 0x04000B81 RID: 2945
	public bool checkInHand;

	// Token: 0x04000B82 RID: 2946
	private float checkTime;

	// Token: 0x04000B83 RID: 2947
	private bool hasFoundItem;
}
