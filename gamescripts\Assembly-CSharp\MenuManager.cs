﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

// Token: 0x020000BE RID: 190
public class MenuManager : MonoBehaviour
{
	// Token: 0x06000644 RID: 1604 RVA: 0x00033724 File Offset: 0x00031924
	private void Awake()
	{
		CL_GameManager.gMan.isPaused = false;
		CL_GameManager.showCursor = true;
		Time.timeScale = 1f;
		AudioListener.pause = false;
		Cursor.lockState = CursorLockMode.None;
		Cursor.visible = true;
		this.SelectMenuBackground();
	}

	// Token: 0x06000645 RID: 1605 RVA: 0x0003375C File Offset: 0x0003195C
	private void Start()
	{
		if (StatManager.showUpgradeDialogue)
		{
			this.saveUpgradeMenu.Initialize(null, this.menu);
			Debug.Log("Showing Upgrade Dialogue!");
			this.menu.DisableMenus(null);
			this.saveUpgradeMenu.Open();
			StatManager.showUpgradeDialogue = false;
		}
		ChallengeMode.CheckChallengeAchievements();
	}

	// Token: 0x06000646 RID: 1606 RVA: 0x000337AE File Offset: 0x000319AE
	private void Update()
	{
		if (!this.seedWindow.activeSelf && Input.GetKey(KeyCode.LeftShift) && Input.GetKey(KeyCode.S) && Input.GetKey(KeyCode.LeftControl))
		{
			this.seedWindow.SetActive(true);
		}
	}

	// Token: 0x06000647 RID: 1607 RVA: 0x000337EC File Offset: 0x000319EC
	public void SelectMenuBackground()
	{
		if (!StatManager.initialized)
		{
			return;
		}
		List<GameObject> list = new List<GameObject>();
		foreach (MenuManager.BackgroundScene backgroundScene in this.backgroundScenes)
		{
			if (backgroundScene.unlockFlagID == "" || StatManager.saveData.GetFlagState(backgroundScene.unlockFlagID))
			{
				list.Add(backgroundScene.asset);
			}
		}
		Object.Instantiate<GameObject>(list[Random.Range(0, list.Count)], base.transform);
	}

	// Token: 0x06000648 RID: 1608 RVA: 0x00033894 File Offset: 0x00031A94
	public void LoadScene(string s)
	{
		SceneManager.LoadScene(s);
	}

	// Token: 0x06000649 RID: 1609 RVA: 0x0003389C File Offset: 0x00031A9C
	public void Quit()
	{
		SteamManager.Shutdown();
		Application.Quit();
	}

	// Token: 0x040007C6 RID: 1990
	public List<MenuManager.BackgroundScene> backgroundScenes;

	// Token: 0x040007C7 RID: 1991
	public UI_Menu menu;

	// Token: 0x040007C8 RID: 1992
	public GameObject seedWindow;

	// Token: 0x040007C9 RID: 1993
	public UI_MenuScreen saveUpgradeMenu;

	// Token: 0x02000284 RID: 644
	[Serializable]
	public class BackgroundScene
	{
		// Token: 0x0400105B RID: 4187
		public string name;

		// Token: 0x0400105C RID: 4188
		public string unlockFlagID;

		// Token: 0x0400105D RID: 4189
		public GameObject asset;
	}
}
