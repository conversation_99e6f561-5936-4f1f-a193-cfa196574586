﻿using System;
using UnityEngine;

namespace DitzelGames.FastIK
{
	// Token: 0x020001E0 RID: 480
	public class FastIKLook : MonoBehaviour
	{
		// Token: 0x06000C54 RID: 3156 RVA: 0x0004E168 File Offset: 0x0004C368
		private void Awake()
		{
			if (this.Target == null)
			{
				return;
			}
			this.StartDirection = this.Target.position - base.transform.position;
			this.StartRotation = base.transform.rotation;
		}

		// Token: 0x06000C55 RID: 3157 RVA: 0x0004E1B8 File Offset: 0x0004C3B8
		private void Update()
		{
			if (this.Target == null)
			{
				return;
			}
			base.transform.rotation = Quaternion.FromToRotation(this.StartDirection, this.Target.position - base.transform.position) * this.StartRotation;
		}

		// Token: 0x04000D24 RID: 3364
		public Transform Target;

		// Token: 0x04000D25 RID: 3365
		protected Vector3 StartDirection;

		// Token: 0x04000D26 RID: 3366
		protected Quaternion StartRotation;
	}
}
