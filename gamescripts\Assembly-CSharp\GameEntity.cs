﻿using System;
using System.Collections.Generic;
using Drawing;
using UnityEngine;

// Token: 0x02000041 RID: 65
[RequireComponent(typeof(ObjectTagger))]
public class GameEntity : MonoBehaviourGizmos, Damageable
{
	// Token: 0x06000273 RID: 627 RVA: 0x00016E6C File Offset: 0x0001506C
	private void OnEnable()
	{
		if (GameEntity.entities == null)
		{
			GameEntity.entities = new List<GameEntity>();
		}
		GameEntity.entities.Add(this);
	}

	// Token: 0x06000274 RID: 628 RVA: 0x00016E8A File Offset: 0x0001508A
	private void OnDisable()
	{
		GameEntity.entities.Remove(this);
	}

	// Token: 0x06000275 RID: 629 RVA: 0x00016E98 File Offset: 0x00015098
	public virtual void Start()
	{
		this.tagger = base.GetComponent<ObjectTagger>();
		CL_GameTracker.AddEventToSession(CL_GameTracker.CreateEvent(base.name, base.gameObject.GetInstanceID().ToString(), this.objectType, CL_GameTracker.Session.Tick.Event.EventType.spawn, base.transform.position));
		CL_GameTracker.EventUpdate = (Action)Delegate.Combine(CL_GameTracker.EventUpdate, new Action(this.SessionUpdate));
		this.lastPosition = base.transform.position;
	}

	// Token: 0x06000276 RID: 630 RVA: 0x00016F18 File Offset: 0x00015118
	private void OnDestroy()
	{
		if (CL_GameTracker.session != null)
		{
			CL_GameTracker.AddEventToSession(CL_GameTracker.CreateEvent(base.name, base.gameObject.GetInstanceID().ToString(), this.objectType, CL_GameTracker.Session.Tick.Event.EventType.destroy, base.transform.position));
			CL_GameTracker.EventUpdate = (Action)Delegate.Remove(CL_GameTracker.EventUpdate, new Action(this.SessionUpdate));
		}
	}

	// Token: 0x06000277 RID: 631 RVA: 0x00016F82 File Offset: 0x00015182
	public virtual bool Damage(float amount, string type)
	{
		if (this.dead)
		{
			return false;
		}
		this.health -= amount;
		if (this.health <= 0f)
		{
			this.Kill(type);
			return true;
		}
		return false;
	}

	// Token: 0x06000278 RID: 632 RVA: 0x00016FB3 File Offset: 0x000151B3
	public virtual void Teleport(Vector3 pos)
	{
		base.transform.position = pos;
	}

	// Token: 0x06000279 RID: 633 RVA: 0x00016FC1 File Offset: 0x000151C1
	public virtual void Teleport(Vector3 pos, Quaternion rot, bool keepVelocity = false)
	{
		this.Teleport(pos);
		base.transform.rotation = rot;
	}

	// Token: 0x0600027A RID: 634 RVA: 0x00016FD8 File Offset: 0x000151D8
	public virtual void Kill(string type = "")
	{
		this.dead = true;
		if (this.destroyObject)
		{
			Object.Instantiate<GameObject>(this.destroyObject, base.transform.position, base.transform.rotation, base.transform.parent);
		}
		this.health = 0f;
	}

	// Token: 0x0600027B RID: 635 RVA: 0x00017031 File Offset: 0x00015231
	public virtual bool IsDead()
	{
		return this.dead;
	}

	// Token: 0x0600027C RID: 636 RVA: 0x00017039 File Offset: 0x00015239
	public virtual void AddForce(Vector3 v)
	{
	}

	// Token: 0x0600027D RID: 637 RVA: 0x0001703B File Offset: 0x0001523B
	public virtual void AddForceAtPosition(Vector3 v, Vector3 p)
	{
		this.AddForce(v);
	}

	// Token: 0x0600027E RID: 638 RVA: 0x00017044 File Offset: 0x00015244
	public virtual void AddForce(Vector3 v, float maxVel)
	{
		this.AddForce(v);
	}

	// Token: 0x0600027F RID: 639 RVA: 0x0001704D File Offset: 0x0001524D
	public virtual void TonguePull(Vector3 v)
	{
	}

	// Token: 0x06000280 RID: 640 RVA: 0x0001704F File Offset: 0x0001524F
	public virtual void AddAcceleration(Vector3 v)
	{
	}

	// Token: 0x06000281 RID: 641 RVA: 0x00017051 File Offset: 0x00015251
	public virtual ObjectTagger GetTagger()
	{
		return this.tagger;
	}

	// Token: 0x06000282 RID: 642 RVA: 0x0001705C File Offset: 0x0001525C
	public virtual void SessionUpdate()
	{
		if (Vector3.Distance(base.transform.position, this.lastPosition) > this.movementRecordDelta)
		{
			this.lastPosition = base.transform.position;
			CL_GameTracker.AddEventToSession(CL_GameTracker.CreateEvent(base.name, base.gameObject.GetInstanceID().ToString(), this.objectType, CL_GameTracker.Session.Tick.Event.EventType.move, base.transform.position));
		}
	}

	// Token: 0x06000283 RID: 643 RVA: 0x000170CD File Offset: 0x000152CD
	public GameObject GetGameObject()
	{
		return base.gameObject;
	}

	// Token: 0x06000284 RID: 644 RVA: 0x000170D5 File Offset: 0x000152D5
	internal virtual void ClampVelocityToAxis(Vector3 up)
	{
		throw new NotImplementedException();
	}

	// Token: 0x06000285 RID: 645 RVA: 0x000170DC File Offset: 0x000152DC
	public void LoadEntitySaveData(GameEntity.BaseEntitySaveData entitySaveData)
	{
		SaveableObject component = base.GetComponent<SaveableObject>();
		if (component != null)
		{
			component.SetSaveInfo(JsonUtility.FromJson<SaveableInfo>(entitySaveData.entityData));
		}
		UT_SpawnChance component2 = base.GetComponent<UT_SpawnChance>();
		if (component2 != null)
		{
			component2.enabled = false;
		}
	}

	// Token: 0x06000286 RID: 646 RVA: 0x0001711C File Offset: 0x0001531C
	public virtual GameEntity.BaseEntitySaveData GetEntitySaveData()
	{
		GameEntity.BaseEntitySaveData baseEntitySaveData = new GameEntity.BaseEntitySaveData();
		baseEntitySaveData.entityID = this.entityPrefabID;
		baseEntitySaveData.position = base.transform.localPosition;
		baseEntitySaveData.rotation = base.transform.localRotation;
		SaveableObject component = base.GetComponent<SaveableObject>();
		if (component != null)
		{
			baseEntitySaveData.entityData = JsonUtility.ToJson(component.GetSaveInfo());
		}
		return baseEntitySaveData;
	}

	// Token: 0x06000287 RID: 647 RVA: 0x0001717C File Offset: 0x0001537C
	public static void OffsetEntities(float amount)
	{
		for (int i = GameEntity.entities.Count - 1; i >= 0; i--)
		{
			if (GameEntity.entities[i] == null)
			{
				GameEntity.entities.RemoveAt(i);
			}
		}
		if (GameEntity.entities != null)
		{
			foreach (GameEntity gameEntity in GameEntity.entities)
			{
				gameEntity.OffsetEntity(amount);
			}
		}
	}

	// Token: 0x06000288 RID: 648 RVA: 0x00017208 File Offset: 0x00015408
	public virtual void OffsetEntity(float amount)
	{
		if (base.transform != null && base.transform.parent == null)
		{
			base.transform.position += Vector3.up * amount;
		}
	}

	// Token: 0x0400037D RID: 893
	public static List<GameEntity> entities = new List<GameEntity>();

	// Token: 0x0400037E RID: 894
	public string objectType = "entity";

	// Token: 0x0400037F RID: 895
	public string entityPrefabID = "entity";

	// Token: 0x04000380 RID: 896
	public bool canSave = true;

	// Token: 0x04000381 RID: 897
	public float health = 10f;

	// Token: 0x04000382 RID: 898
	public float maxHealth = 10f;

	// Token: 0x04000383 RID: 899
	[HideInInspector]
	public bool dead;

	// Token: 0x04000384 RID: 900
	public GameObject destroyObject;

	// Token: 0x04000385 RID: 901
	public bool spawnWithBounds = true;

	// Token: 0x04000386 RID: 902
	public float movementRecordDelta = 0.5f;

	// Token: 0x04000387 RID: 903
	private Vector3 lastPosition = Vector3.zero;

	// Token: 0x04000388 RID: 904
	private ObjectTagger tagger;

	// Token: 0x02000229 RID: 553
	[Serializable]
	public class BaseEntitySaveData
	{
		// Token: 0x04000E89 RID: 3721
		public string entityID;

		// Token: 0x04000E8A RID: 3722
		public Vector3 position;

		// Token: 0x04000E8B RID: 3723
		public Quaternion rotation;

		// Token: 0x04000E8C RID: 3724
		public string entityData;

		// Token: 0x04000E8D RID: 3725
		public SaveableInfo saveInfo;
	}
}
