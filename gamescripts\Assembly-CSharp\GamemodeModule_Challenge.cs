﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using UnityEngine;

// Token: 0x020000A2 RID: 162
[Serializable]
public class GamemodeModule_Challenge : GamemodeModule
{
	// Token: 0x0600053B RID: 1339 RVA: 0x0002BDB1 File Offset: 0x00029FB1
	public override void Initialize(M_Gamemode gamemode)
	{
		base.Initialize(gamemode);
		this.addedScore = 0f;
		this.bonusScore = 0f;
		GamemodeModule_Challenge.instance = this;
	}

	// Token: 0x0600053C RID: 1340 RVA: 0x0002BDD8 File Offset: 0x00029FD8
	public override void OnFinish(bool hasFinished)
	{
		float score = this.GetScore(hasFinished);
		if (hasFinished)
		{
			GamemodeModule_Challenge.<>c__DisplayClass12_0 CS$<>8__locals1 = new GamemodeModule_Challenge.<>c__DisplayClass12_0();
			CS$<>8__locals1.currentMedal = this.GetMedalFromScore(score);
			StatManager.instance.GetScoreScreen(hasFinished).SetMedalInfo(CS$<>8__locals1.currentMedal.medalTitle + this.medalAppend, CS$<>8__locals1.currentMedal.medalSprite, CS$<>8__locals1.currentMedal.textColor);
			int num = this.medals.IndexOf(CS$<>8__locals1.currentMedal) + 1;
			Debug.Log("Got Medal: " + num.ToString());
			StatManager.sessionStats.UpdateStatistic("best-medal", num, StatManager.Statistic.DataType.Int, StatManager.Statistic.ModType.Max, StatManager.Statistic.DisplayType.Default, StatManager.Statistic.ModType.Max);
			CL_GameManager.gMan.StartCoroutine(CS$<>8__locals1.<OnFinish>g__WaitToPlayAudio|1());
			return;
		}
		CL_GameManager.gMan.StartCoroutine(this.<OnFinish>g__WaitToPlayAudio|12_0());
	}

	// Token: 0x0600053D RID: 1341 RVA: 0x0002BEA8 File Offset: 0x0002A0A8
	public override float GetScore(bool hasFinished = false)
	{
		float num;
		if (this.scoreUsesAscent)
		{
			num = CL_GameManager.gMan.GetPlayerAscent() * CL_GameManager.gMan.GetPlayerAscentRate();
		}
		else
		{
			num = (this.addedScore + this.startingScore) / CL_GameManager.gMan.GetGameTime() + this.bonusScore;
			Debug.Log(CL_GameManager.gMan.GetGameTime());
			Debug.Log("Score Check = " + num.ToString());
			if (!hasFinished)
			{
				num = 0f;
			}
		}
		return (float)Math.Round((double)num, 2, MidpointRounding.AwayFromZero);
	}

	// Token: 0x0600053E RID: 1342 RVA: 0x0002BF3C File Offset: 0x0002A13C
	public GamemodeModule_Challenge.Medal GetMedalFromScore(float score)
	{
		GamemodeModule_Challenge.Medal medal = this.medals[0];
		foreach (GamemodeModule_Challenge.Medal medal2 in this.medals)
		{
			if (score >= medal2.scoreRequirement)
			{
				medal = medal2;
			}
		}
		return medal;
	}

	// Token: 0x0600053F RID: 1343 RVA: 0x0002BFA4 File Offset: 0x0002A1A4
	public int GetMedalRankFromScore(float score)
	{
		int num = 0;
		for (int i = 0; i < this.medals.Count; i++)
		{
			if (score >= this.medals[i].scoreRequirement)
			{
				num = i + 1;
			}
		}
		return num;
	}

	// Token: 0x06000540 RID: 1344 RVA: 0x0002BFE2 File Offset: 0x0002A1E2
	public static void AddScore(float f)
	{
		if (GamemodeModule_Challenge.instance != null)
		{
			GamemodeModule_Challenge.instance.addedScore += f;
		}
	}

	// Token: 0x06000541 RID: 1345 RVA: 0x0002BFFD File Offset: 0x0002A1FD
	public static void AddBonusScore(float f)
	{
		if (GamemodeModule_Challenge.instance != null)
		{
			GamemodeModule_Challenge.instance.bonusScore += f;
		}
	}

	// Token: 0x06000543 RID: 1347 RVA: 0x0002C036 File Offset: 0x0002A236
	[CompilerGenerated]
	private IEnumerator <OnFinish>g__WaitToPlayAudio|12_0()
	{
		yield return new WaitForSecondsRealtime(0.5f);
		if (this.failureMedal != null)
		{
			AudioManager.PlayUISound(this.failureMedal.audioClip, 1f, 1f);
		}
		yield break;
	}

	// Token: 0x040006C2 RID: 1730
	public List<GamemodeModule_Challenge.Medal> medals;

	// Token: 0x040006C3 RID: 1731
	public GamemodeModule_Challenge.Medal failureMedal;

	// Token: 0x040006C4 RID: 1732
	public float winScoreMultiplier = 5f;

	// Token: 0x040006C5 RID: 1733
	public string medalAppend;

	// Token: 0x040006C6 RID: 1734
	public bool useTime;

	// Token: 0x040006C7 RID: 1735
	public bool scoreUsesAscent;

	// Token: 0x040006C8 RID: 1736
	private float addedScore;

	// Token: 0x040006C9 RID: 1737
	private float bonusScore;

	// Token: 0x040006CA RID: 1738
	public float startingScore = 100f;

	// Token: 0x040006CB RID: 1739
	private static GamemodeModule_Challenge instance;

	// Token: 0x0200025D RID: 605
	[Serializable]
	public class Medal
	{
		// Token: 0x04000F89 RID: 3977
		public string medalTitle;

		// Token: 0x04000F8A RID: 3978
		public Sprite medalSprite;

		// Token: 0x04000F8B RID: 3979
		public float scoreRequirement;

		// Token: 0x04000F8C RID: 3980
		public Color textColor = Color.white;

		// Token: 0x04000F8D RID: 3981
		public AudioClip audioClip;
	}
}
