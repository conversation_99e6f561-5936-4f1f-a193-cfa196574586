﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000167 RID: 359
public class UT_Explosion : MonoBehaviour
{
	// Token: 0x06000A13 RID: 2579 RVA: 0x00043868 File Offset: 0x00041A68
	private void Start()
	{
		Collider[] array = Physics.OverlapSphere(base.transform.position, this.maxDistance, this.hitMask);
		if (array.Length != 0)
		{
			for (int i = 0; i < array.Length; i++)
			{
				ObjectTagger component = array[i].GetComponent<ObjectTagger>();
				GameEntity component2 = array[i].GetComponent<GameEntity>();
				float num = Vector3.Distance(base.transform.position, array[i].transform.position);
				if (component != null)
				{
					foreach (UT_Explosion.ExplosionEffect explosionEffect in this.effects)
					{
						if (component.HasTagInList(explosionEffect.hitTags.ToArray()))
						{
							if (num > explosionEffect.distance)
							{
								break;
							}
							if (component2 != null)
							{
								component2.Damage(explosionEffect.damage * this.damageMult, this.damageType);
								float num2 = explosionEffect.forceCurve.Evaluate(num / explosionEffect.distance);
								component2.AddForce((component2.transform.position - base.transform.position).normalized * (explosionEffect.force * num2));
								break;
							}
						}
					}
				}
			}
		}
	}

	// Token: 0x04000B30 RID: 2864
	public LayerMask hitMask;

	// Token: 0x04000B31 RID: 2865
	public float damageMult = 1f;

	// Token: 0x04000B32 RID: 2866
	public float maxDistance = 5f;

	// Token: 0x04000B33 RID: 2867
	public List<UT_Explosion.ExplosionEffect> effects;

	// Token: 0x04000B34 RID: 2868
	public string damageType = "explosion";

	// Token: 0x020002D9 RID: 729
	[Serializable]
	public class ExplosionEffect
	{
		// Token: 0x0400123A RID: 4666
		public List<string> hitTags;

		// Token: 0x0400123B RID: 4667
		public float distance;

		// Token: 0x0400123C RID: 4668
		public float damage;

		// Token: 0x0400123D RID: 4669
		public float force = 1f;

		// Token: 0x0400123E RID: 4670
		public AnimationCurve forceCurve;
	}
}
