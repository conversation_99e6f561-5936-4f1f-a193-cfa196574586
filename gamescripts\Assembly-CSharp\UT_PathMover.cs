﻿using System;
using System.Collections.Generic;
using Drawing;
using EditorCools;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000183 RID: 387
[ExecuteInEditMode]
public class UT_PathMover : MonoBehaviourGizmos
{
	// Token: 0x06000A9F RID: 2719 RVA: 0x000458D8 File Offset: 0x00043AD8
	private void Awake()
	{
		this.moveObject.position = this.pathPoints[0].node.position;
		this.moveObject.rotation = this.pathPoints[0].node.rotation;
		this.segmentStartRotation = this.pathPoints[0].node.rotation;
		float num = 0f;
		for (int i = 0; i < this.pathPoints.Count; i++)
		{
			UT_PathMover.PathPoint pathPoint = this.pathPoints[i];
			UT_PathMover.PathPoint pathPoint2 = this.pathPoints[Mathf.FloorToInt(Mathf.Repeat((float)(i + 1), (float)this.pathPoints.Count))];
			float num2 = this.GetSegmentDistance(pathPoint, pathPoint2) / (pathPoint.speed * this.speed) + pathPoint.waitTime;
			num += num2;
		}
		float num3 = Mathf.Repeat(this.offset, num);
		float num4 = 0f;
		for (int j = 0; j < this.pathPoints.Count; j++)
		{
			UT_PathMover.PathPoint pathPoint3 = this.pathPoints[j];
			UT_PathMover.PathPoint pathPoint4 = this.pathPoints[Mathf.FloorToInt(Mathf.Repeat((float)(j + 1), (float)this.pathPoints.Count))];
			float num5 = this.GetSegmentDistance(pathPoint3, pathPoint4) / (pathPoint3.speed * this.speed);
			float num6 = num5 + pathPoint3.waitTime;
			num4 += num6;
			if (num3 < num6)
			{
				float num7 = num4 - num3;
				if (num7 < num5)
				{
					this.currentLerpTime = num7 / num5;
					this.currentPointIndex = j;
				}
				else
				{
					this.currentLerpTime = 0f;
					this.currentWaitTime = (num7 - num5) / pathPoint3.waitTime;
				}
				float num8;
				if (this.pathPoints[this.currentPointIndex].useCustomCurve)
				{
					num8 = this.pathPoints[this.currentPointIndex].movementCurve.Evaluate(this.currentLerpTime);
				}
				else
				{
					num8 = this.movementCurve.Evaluate(this.currentLerpTime);
				}
				this.moveObject.position = Vector3.Lerp(pathPoint3.node.position, pathPoint4.node.position, num8);
				this.moveObject.rotation = pathPoint3.node.rotation;
				break;
			}
		}
		this.currentWaitTime += this.startWait;
		Transform node = this.pathPoints[this.currentPointIndex].node;
		this.currentSegmentDistance = Vector3.Distance(this.moveObject.position, node.position);
		this.segmentStartPosition = this.moveObject.position;
		this.segmentStartRotation = this.moveObject.rotation;
		if (this.aud == null)
		{
			this.aud = base.GetComponent<AudioSource>();
		}
	}

	// Token: 0x06000AA0 RID: 2720 RVA: 0x00045BBA File Offset: 0x00043DBA
	private void OnEnable()
	{
		DarkMachineFunctions.offsetAction = (Action<float>)Delegate.Combine(DarkMachineFunctions.offsetAction, new Action<float>(this.WorldOffset));
	}

	// Token: 0x06000AA1 RID: 2721 RVA: 0x00045BDC File Offset: 0x00043DDC
	private void OnDisable()
	{
		DarkMachineFunctions.offsetAction = (Action<float>)Delegate.Remove(DarkMachineFunctions.offsetAction, new Action<float>(this.WorldOffset));
	}

	// Token: 0x06000AA2 RID: 2722 RVA: 0x00045BFE File Offset: 0x00043DFE
	private void Update()
	{
		if (this.pathPoints == null || this.pathPoints.Count == 0)
		{
			Debug.LogWarning("Path points not set or empty.");
			return;
		}
		if (!this.active)
		{
			return;
		}
		if (Application.isPlaying)
		{
			this.MoveAlongPath();
		}
	}

	// Token: 0x06000AA3 RID: 2723 RVA: 0x00045C38 File Offset: 0x00043E38
	[Button("Generate Path Points", null, 0f)]
	private void GeneratePathPoints()
	{
		this.pathPoints = new List<UT_PathMover.PathPoint>();
		foreach (object obj in base.transform)
		{
			Transform transform = (Transform)obj;
			if (transform.name.Contains("pathpoint-node"))
			{
				UT_PathMover.PathPoint pathPoint = new UT_PathMover.PathPoint();
				pathPoint.node = transform;
				pathPoint.speed = 1f;
				pathPoint.waitTime = 0f;
				this.pathPoints.Add(pathPoint);
			}
		}
		if (this.pathPoints.Count == 0)
		{
			Debug.LogWarning("No path points found with 'pathpoint-node' in their name.");
		}
	}

	// Token: 0x06000AA4 RID: 2724 RVA: 0x00045CF0 File Offset: 0x00043EF0
	private void MoveAlongPath()
	{
		if (this.currentWaitTime >= 0f)
		{
			this.currentWaitTime -= Time.deltaTime;
			return;
		}
		if (!this.hasPlayedStartSound)
		{
			if (this.aud != null && this.pathPoints[this.currentPointIndex].endSound != null)
			{
				this.aud.clip = this.pathPoints[this.currentPointIndex].startSound;
				this.aud.Play();
			}
			this.hasPlayedStartSound = true;
			this.pathPoints[this.currentPointIndex].startEvent.Invoke();
		}
		Transform node = this.pathPoints[this.currentPointIndex].node;
		float num = this.speed * Time.deltaTime * this.pathPoints[this.currentPointIndex].speed;
		this.currentLerpTime += num / this.currentSegmentDistance;
		float num2;
		if (this.pathPoints[this.currentPointIndex].useCustomCurve)
		{
			num2 = this.pathPoints[this.currentPointIndex].movementCurve.Evaluate(this.currentLerpTime);
		}
		else
		{
			num2 = this.movementCurve.Evaluate(this.currentLerpTime);
		}
		if (!float.IsNaN(num2) && node != null)
		{
			this.moveObject.position = Vector3.Lerp(this.segmentStartPosition, node.position, num2);
			Quaternion rotation = node.rotation;
			this.moveObject.rotation = Quaternion.Lerp(this.segmentStartRotation, rotation, num2);
		}
		if (this.currentLerpTime >= 1f)
		{
			this.ArriveAtNode();
		}
	}

	// Token: 0x06000AA5 RID: 2725 RVA: 0x00045EA4 File Offset: 0x000440A4
	private void ArriveAtNode()
	{
		this.currentWaitTime = this.pathPoints[this.currentPointIndex].waitTime;
		this.pathPoints[this.currentPointIndex].endEvent.Invoke();
		if (this.pathPoints[this.currentPointIndex].stopOnArrival)
		{
			this.active = false;
		}
		if (this.aud != null && this.pathPoints[this.currentPointIndex].endSound != null)
		{
			this.aud.clip = this.pathPoints[this.currentPointIndex].endSound;
			this.aud.Play();
		}
		this.hasPlayedStartSound = false;
		this.currentLerpTime = 0f;
		this.segmentStartPosition = this.moveObject.position;
		this.segmentStartRotation = this.moveObject.rotation;
		if (this.loop)
		{
			this.currentPointIndex = (this.currentPointIndex + 1) % this.pathPoints.Count;
		}
		else if (this.isMovingForward)
		{
			if (this.currentPointIndex < this.pathPoints.Count - 1)
			{
				this.currentPointIndex++;
			}
			else
			{
				this.isMovingForward = false;
				this.currentPointIndex--;
			}
		}
		else if (this.currentPointIndex > 0)
		{
			this.currentPointIndex--;
		}
		else
		{
			this.isMovingForward = true;
			this.currentPointIndex++;
		}
		Transform node = this.pathPoints[this.currentPointIndex].node;
		this.currentSegmentDistance = Vector3.Distance(this.moveObject.position, node.position);
	}

	// Token: 0x06000AA6 RID: 2726 RVA: 0x0004605C File Offset: 0x0004425C
	private float GetSegmentDistance(UT_PathMover.PathPoint p1, UT_PathMover.PathPoint p2)
	{
		return Vector3.Distance(p1.node.position, p2.node.position);
	}

	// Token: 0x06000AA7 RID: 2727 RVA: 0x0004607C File Offset: 0x0004427C
	public override void DrawGizmos()
	{
		if (this.pathPoints.Count == 0)
		{
			return;
		}
		Draw.Arrow(this.moveObject.position, this.moveObject.position + this.moveObject.forward, Vector3.up, 0.1f, Color.cyan);
		Draw.Arrow(this.moveObject.position, this.moveObject.position + this.moveObject.up, Vector3.up, 0.1f, Color.green);
		Draw.Label2D(base.transform.position + Vector3.up * 0.5f, "OFFSET: " + this.offset.ToString() + "\n WAIT: " + this.startWait.ToString(), Color.yellow);
		for (int i = 0; i < this.pathPoints.Count; i++)
		{
			Draw.Arrow(this.pathPoints[i].node.position, this.pathPoints[i].node.position + this.pathPoints[i].node.forward, Vector3.up, 0.1f, Color.blue);
			Draw.Arrow(this.pathPoints[i].node.position, this.pathPoints[i].node.position + this.pathPoints[i].node.up, Vector3.up, 0.1f, Color.green);
			Draw.Label2D(this.pathPoints[i].node.position + Vector3.up * 0.25f, string.Concat(new string[]
			{
				this.pathPoints[i].node.name,
				"\nS:",
				this.pathPoints[i].speed.ToString(),
				" W:",
				this.pathPoints[i].waitTime.ToString()
			}), 14f);
			if (this.pathPoints.Count > i + 1)
			{
				Draw.Arrow(this.pathPoints[i].node.position, this.pathPoints[i + 1].node.position, Vector3.up, this.speed * 0.1f, Color.magenta);
			}
			else if (this.loop)
			{
				Draw.Arrow(this.pathPoints[i].node.position, this.pathPoints[0].node.position, Vector3.up, this.speed * 0.1f, Color.magenta);
			}
		}
	}

	// Token: 0x06000AA8 RID: 2728 RVA: 0x000463D3 File Offset: 0x000445D3
	public void StartMove()
	{
		this.active = true;
	}

	// Token: 0x06000AA9 RID: 2729 RVA: 0x000463DC File Offset: 0x000445DC
	public void StopMove()
	{
		this.active = false;
	}

	// Token: 0x06000AAA RID: 2730 RVA: 0x000463E5 File Offset: 0x000445E5
	public void WorldOffset(float f)
	{
		this.segmentStartPosition += Vector3.up * f;
	}

	// Token: 0x04000B9B RID: 2971
	public Transform moveObject;

	// Token: 0x04000B9C RID: 2972
	public float speed = 5f;

	// Token: 0x04000B9D RID: 2973
	public float offset;

	// Token: 0x04000B9E RID: 2974
	public float startWait;

	// Token: 0x04000B9F RID: 2975
	public bool active = true;

	// Token: 0x04000BA0 RID: 2976
	public bool loop;

	// Token: 0x04000BA1 RID: 2977
	public AnimationCurve movementCurve = AnimationCurve.Linear(0f, 0f, 1f, 1f);

	// Token: 0x04000BA2 RID: 2978
	public List<UT_PathMover.PathPoint> pathPoints;

	// Token: 0x04000BA3 RID: 2979
	private int currentPointIndex;

	// Token: 0x04000BA4 RID: 2980
	private bool isMovingForward = true;

	// Token: 0x04000BA5 RID: 2981
	private float currentLerpTime;

	// Token: 0x04000BA6 RID: 2982
	private float currentSegmentDistance;

	// Token: 0x04000BA7 RID: 2983
	private float currentWaitTime;

	// Token: 0x04000BA8 RID: 2984
	private Vector3 segmentStartPosition;

	// Token: 0x04000BA9 RID: 2985
	private Quaternion segmentStartRotation;

	// Token: 0x04000BAA RID: 2986
	public AudioSource aud;

	// Token: 0x04000BAB RID: 2987
	private bool hasPlayedStartSound;

	// Token: 0x020002E0 RID: 736
	[Serializable]
	public class PathPoint
	{
		// Token: 0x04001254 RID: 4692
		public Transform node;

		// Token: 0x04001255 RID: 4693
		public float speed = 1f;

		// Token: 0x04001256 RID: 4694
		public float waitTime;

		// Token: 0x04001257 RID: 4695
		public bool stopOnArrival;

		// Token: 0x04001258 RID: 4696
		public bool useCustomCurve;

		// Token: 0x04001259 RID: 4697
		public AnimationCurve movementCurve;

		// Token: 0x0400125A RID: 4698
		public AudioClip startSound;

		// Token: 0x0400125B RID: 4699
		public AudioClip endSound;

		// Token: 0x0400125C RID: 4700
		public UnityEvent startEvent;

		// Token: 0x0400125D RID: 4701
		public UnityEvent endEvent;
	}
}
