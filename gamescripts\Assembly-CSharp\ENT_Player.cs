﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000043 RID: 67
[DefaultExecutionOrder(-10)]
public class ENT_Player : GameEntity
{
	// Token: 0x06000290 RID: 656 RVA: 0x000173EC File Offset: 0x000155EC
	private void Awake()
	{
		ENT_Player.playerObject = this;
		this.curBuffs = new ENT_Player.PlayerBuff();
		this.curBuffs.Initialize();
		if (this.clipHandler != null)
		{
			this.clipHandler.Initialize();
			this.clipHandler.GetGroup("fatigue").GetSet("level").Play(0f, null);
		}
		ENT_Player.GetInventory();
	}

	// Token: 0x06000291 RID: 657 RVA: 0x0001745C File Offset: 0x0001565C
	public override void Start()
	{
		base.Start();
		this.softParent = new SoftParent();
		this.softParent.Initialize(base.transform, null);
		this.softParent.lockAngles = new Vector3(0f, 1f, 0f);
		Cursor.lockState = CursorLockMode.Locked;
		this.gMan = GameObject.Find("GameManager").GetComponent<CL_GameManager>();
		this.audioChannels = new List<AudioSource> { base.GetComponent<AudioSource>() };
		for (int i = 0; i < 5; i++)
		{
			this.audioChannels.Add(base.gameObject.AddComponent<AudioSource>());
			this.audioChannels[this.audioChannels.Count - 1].volume = 0.5f;
			this.audioChannels[i].outputAudioMixerGroup = AudioManager.instance.gameMixer;
		}
		this.curFootstep = this.footsteps[0];
		foreach (ENT_Player.Hand hand in this.hands)
		{
			hand.gripStrength = this.gripStrengthTimer;
			hand.Initialize(this);
		}
		this.uiSpriteDict = new Dictionary<string, ENT_Player.InteractionSprite>();
		foreach (ENT_Player.InteractionSprite interactionSprite in this.interactionSprites)
		{
			this.uiSpriteDict.Add(interactionSprite.name, interactionSprite);
		}
		this.CreateCommands();
		CL_DebugView.SetCameraTarget(this.cam);
	}

	// Token: 0x06000292 RID: 658 RVA: 0x000175E8 File Offset: 0x000157E8
	private void FixedUpdate()
	{
		if (!this.IsLocked())
		{
			this.curFOV = Mathf.Clamp(this.curFOV + this.curBuffs.GetBuff("addFOV"), 60f, 140f);
			this.smoothedFOV = Mathf.Lerp(this.smoothedFOV, this.curFOV, Time.deltaTime * 5f);
			this.curFOV = SettingsManager.settings.playerFOV;
			this.cam.fieldOfView = this.smoothedFOV;
		}
		if (this.IsMoveLocked())
		{
			this.previousFixedCameraPosition = this.currentFixedCameraPosition;
			this.currentFixedCameraPosition = this.mainCamTarget.position;
			this.lastFixedPlayerPosition = base.transform.position;
			if (this.softParent.IsParented())
			{
				this.lastFixedPlayerParentedPosition = this.softParent.GetParentPosition();
			}
			return;
		}
		if (this.isFrozen())
		{
			return;
		}
		ENT_Player.Hand[] array = this.hands;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].FixedUpdate();
		}
		this.Movement();
		this.lastFixedPlayerPosition = base.transform.position;
		if (this.softParent.IsParented())
		{
			this.lastFixedPlayerParentedPosition = this.softParent.GetParentPosition();
		}
		this.previousFixedCameraPosition = this.currentFixedCameraPosition;
		this.currentFixedCameraPosition = this.mainCamTarget.position;
		this.gMan.uiMan.SetVignette("hurt", 1f - this.health / this.maxHealth);
	}

	// Token: 0x06000293 RID: 659 RVA: 0x00017762 File Offset: 0x00015962
	private void Update()
	{
	}

	// Token: 0x06000294 RID: 660 RVA: 0x00017764 File Offset: 0x00015964
	private void LateUpdate()
	{
		this.softParentHasUpdatedPosition = false;
		for (int i = this.perks.Count - 1; i >= 0; i--)
		{
			this.perks[i].Update();
		}
		if (this.gMan.isPaused)
		{
			this.windAudio.volume = Mathf.Lerp(this.windAudio.volume, 0f, Time.unscaledDeltaTime * 8f);
			return;
		}
		if (SettingsManager.settings.toggleCrouch)
		{
			if (InputManager.GetButton("Crouch").Down)
			{
				this.crouchPressed = !this.crouchPressed;
			}
		}
		else
		{
			this.crouchPressed = InputManager.GetButton("Crouch").Pressed;
		}
		if (!this.gMan.lockPlayer || !this.gMan.freecam)
		{
			if (Input.GetKeyDown(KeyCode.F2))
			{
				this.hideInv = !this.hideInv;
				this.inventoryCam.enabled = this.hideInv;
				this.gMan.uiMan.canvas.gameObject.SetActive(this.hideInv);
			}
			if (Input.GetKeyDown(KeyCode.F4))
			{
				this.hideUI = !this.hideUI;
				this.gMan.uiMan.canvas.gameObject.SetActive(this.hideUI);
			}
		}
		if (!this.isFrozen() && !this.gMan.lockPlayerInput)
		{
			this.LandingAudio();
			if (this.hitTime > 0f)
			{
				this.hitTime -= Time.deltaTime;
				DebugMenu.UpdateDebugText("hittime", "HITTIME: " + this.hitTime.ToString());
			}
			else
			{
				this.hitNormal = Vector3.zero;
				this.hasHit = false;
				this.climbDir = Vector3.up;
				this.jumpDir = Vector3.up;
			}
			this.sprintFOV = SettingsManager.settings.playerFOV + 15f;
			if (!this.camLocked)
			{
				this.MouseLook();
			}
			this.Actions();
			if (!this.dead)
			{
				this.health = Mathf.Clamp(this.health + Time.deltaTime * 0.15f, 0f, this.maxHealth);
			}
			if (this.tick <= 0f)
			{
				this.tick = 0.15f;
				this.SlowTick();
			}
			this.tick -= Time.deltaTime;
			this.curBuffs.UpdateBuffs();
			this.ApplyBuffEffects();
			this.PoisonCheck();
			if (this.grabAnywhereHit[0] != null)
			{
				this.grabAnywhereFakeHandholds[0].transform.position = this.grabAnywhereHit[0].TransformPoint(this.grabAnywherePosition[0]);
				this.grabAnywhereFakeHandholds[0].transform.rotation = quaternion.identity;
			}
			if (this.grabAnywhereHit[1] != null)
			{
				this.grabAnywhereFakeHandholds[1].transform.position = this.grabAnywhereHit[1].TransformPoint(this.grabAnywherePosition[1]);
				this.grabAnywhereFakeHandholds[1].transform.rotation = quaternion.identity;
			}
			this.lastVel = this.cCon.velocity;
			if (this.cCon.velocity.y < -2f && !this.dead && !this.gMan.IsReviving() && !this.IsHanging())
			{
				if (!this.falling)
				{
					this.fallStartHeight = this.gMan.GetPlayerCorrectedHeight();
					this.falling = true;
				}
				float num = Mathf.Max(this.fallStartHeight - this.gMan.GetPlayerCorrectedHeight(), 0f);
				this.curFallDistance = num;
				DebugMenu.UpdateDebugText("falldistance", "Fall Distance: " + num.ToString());
				if (num > 50f && !this.noclip)
				{
					if (this.fallHurtTime > 0f)
					{
						this.fallHurtTime -= Time.deltaTime;
					}
					else
					{
						this.fallHurtTime = 1f;
						this.Damage(0.5f, "falling");
					}
					this.camRoot.ShakeCamera(0.1f * Time.deltaTime);
				}
				else
				{
					this.fallHurtTime = 0f;
				}
				if (Mathf.Abs(this.gMan.GetPlayerCorrectedHeight() - this.fallStartHeight) > 75f)
				{
					CL_AchievementManager.SetAchievementValue("ACH_LONGFALL", true);
				}
			}
			else
			{
				if (this.falling)
				{
					float num2 = Mathf.Max(this.fallStartHeight - this.gMan.GetPlayerCorrectedHeight(), 0f);
					if (num2 > 50f)
					{
						this.Damage(Mathf.Ceil((num2 - 60f) / 15f) + 1f, "falling");
					}
				}
				DebugMenu.UpdateDebugText("falldistance", "Fall Distance: 0");
				this.falling = false;
				this.curFallDistance = 0f;
			}
			this.timeSinceLastHit += Time.deltaTime;
			DebugMenu.UpdateDebugText("lasthittime", "<color=red>Time Since Last Hit: " + this.timeSinceLastHit.ToString());
		}
		this.SoftParentWait();
	}

	// Token: 0x06000295 RID: 661 RVA: 0x00017CA4 File Offset: 0x00015EA4
	private void SlowTick()
	{
		this.curFootstep = this.GetFootstepMaterial();
		this.AchievementCheck();
		if (this.grapplers != null && this.grapplers.Count > 0)
		{
			for (int i = this.grapplers.Count - 1; i >= 0; i--)
			{
			}
		}
	}

	// Token: 0x06000296 RID: 662 RVA: 0x00017CF1 File Offset: 0x00015EF1
	private void SoftParentWait()
	{
		base.StartCoroutine(this.<SoftParentWait>g__WaitFunc|155_0());
	}

	// Token: 0x06000297 RID: 663 RVA: 0x00017D00 File Offset: 0x00015F00
	private void Movement()
	{
		float num = (this.grappled ? 0.25f : 1f);
		this.timeSinceJumpBoost += Time.fixedDeltaTime;
		if (this.isGrounded && !this.cCon.isGrounded && this.vel.y < 0f && !this.isSliding)
		{
			this.vel.y = 0f;
		}
		bool flag = this.crouching && this.curBuffs.GetBuff("addJumpBoost") > 0f && this.cCon.isGrounded && this.GetLowestGripStrength() > 2f;
		if (flag && !this.isGrounded)
		{
			this.clipHandler.GetGroup("perk").GetSet("jumpboost-prime").Play(1f, this.audioChannels[2]);
		}
		this.isGrounded = this.cCon.isGrounded;
		this.inputAxis = new Vector3(InputManager.GetVector("Move").vector.x, InputManager.GetVector("Move").vector.y, 0f);
		if (this.IsMoveLocked())
		{
			this.inputAxis = Vector2.zero;
		}
		this.moveAxis = Vector3.Lerp(this.moveAxis, ((this.playerState == ENT_Player.PlayerState.hanging) ? Vector3.zero : (base.transform.forward * this.inputAxis.y)) + base.transform.right * this.inputAxis.x, Time.fixedDeltaTime * 100f * this.curFootstep.moveLerp);
		this.moveAxis = Vector3.ClampMagnitude(this.moveAxis, 1f);
		this.jumpDir = Vector3.up;
		float num2 = this.speed;
		float num3 = this.gravity * this.gravityMult * Mathf.Max(1f + this.curBuffs.GetBuff("addGravity"), 0.1f);
		if (!this.hasGravity)
		{
			num3 = 0f;
		}
		this.playerState = ENT_Player.PlayerState.airborne;
		float num4 = this.dragCoefficient;
		bool flag2 = false;
		for (int i = 0; i < this.hands.Length; i++)
		{
			if (this.hands[i].interactState == ENT_Player.InteractType.hanging)
			{
				flag2 = true;
			}
		}
		if (flag2)
		{
			this.playerState = ENT_Player.PlayerState.hanging;
		}
		if (!this.cCon.isGrounded)
		{
			this.isGrounded = false;
			if (!this.IsHanging() && this.hitTime <= 0f)
			{
				this.hitNormal = Vector3.up;
			}
		}
		else if (this.extraJumpsRemaining < this.extraJumps + Mathf.CeilToInt(this.curBuffs.GetBuff("addExtraJumps")))
		{
			this.extraJumpsRemaining = this.extraJumps + Mathf.CeilToInt(this.curBuffs.GetBuff("addExtraJumps"));
			this.clipHandler.GetGroup("perk").GetSet("doublejump-recharge").Play(1f, null);
		}
		float num5 = Vector3.Angle(Vector3.up, this.hitNormal);
		bool flag3 = num5 >= this.slopeLimit;
		this.isSliding = flag3;
		if (this.isSliding)
		{
			Vector3 vector = -ENT_Player.MostYFacingPerpendicular(this.hitNormal);
			Vector3 vector2 = Vector3.zero;
			vector2 += this.hitNormal * Time.fixedDeltaTime * this.slideSpeed * (num5 / 90f);
			num2 *= 0.5f;
			this.jumpDir = Vector3.up;
			float num6 = Vector3.Angle(this.hitNormal, Vector3.up);
			if (num6 < 90f && num6 > 20f)
			{
				this.jumpDir = Vector3.Lerp(this.hitNormal, Vector3.up, 0.9f).normalized;
			}
			else if (num6 > 90f && num6 < 140f && flag2)
			{
				this.jumpDir = -vector + this.hitNormal * 0.1f;
			}
			else
			{
				this.jumpDir = Vector3.up;
			}
			this.vel += vector2 * Time.fixedDeltaTime;
			DebugMenu.UpdateDebugText("player-slideBool", "Sliding");
		}
		else
		{
			DebugMenu.UpdateDebugText("player-slideBool", "Not Sliding");
		}
		DebugMenu.UpdateDebugText("player-grounded", "Grounded: " + this.cCon.isGrounded.ToString());
		if (this.isGrounded && this.playerState != ENT_Player.PlayerState.hanging)
		{
			this.playerState = ENT_Player.PlayerState.grounded;
			this.timeSinceGrounded = 0f;
		}
		else if (this.isGrounded && this.playerState == ENT_Player.PlayerState.hanging)
		{
			this.timeSinceGrounded = 0f;
		}
		else
		{
			this.timeSinceGrounded += Time.fixedDeltaTime;
		}
		base.transform.position + this.cCon.center + Vector3.up * -this.cCon.height * 0.5f + Vector3.up * this.cCon.height;
		RaycastHit raycastHit;
		if (Physics.SphereCast(base.transform.position, this.cCon.radius * 1.1f, -base.transform.up, out raycastHit, this.cCon.height, this.jumpMask) && !this.noclip)
		{
			if (raycastHit.collider.tag == "Platform" || raycastHit.collider.tag == "World")
			{
				DebugMenu.UpdateDebugText("playerparent", "Player Parent: " + raycastHit.collider.name);
				this.softParent.Parent(raycastHit.transform);
			}
			else
			{
				DebugMenu.UpdateDebugText("playerparent", "Player Parent: N/A");
				this.<Movement>g__Unparent|156_0();
			}
		}
		else
		{
			DebugMenu.UpdateDebugText("playerparent", "Player Parent: N/A");
			this.<Movement>g__Unparent|156_0();
		}
		if (this.noclip && !this.gMan.freecam)
		{
			this.falling = false;
			this.softParent.Unparent();
			this.vel = Vector3.zero;
			if (!this.gMan.lockPlayerInput)
			{
				this.vel.y = (float)((((InputManager.GetButton("Jump").Pressed && (!this.gMan.lockPlayer || !this.gMan.lockPlayerInput)) ? 1 : 0) - (((Input.GetKey(KeyCode.F) || InputManager.GetButton("Crouch").Pressed) && !this.gMan.lockPlayer) ? 1 : 0)) * 10);
				this.cCon.enabled = false;
				if (InputManager.GetButton("Sprint").Pressed)
				{
					this.moveAxis *= 4f;
					this.vel.y = this.vel.y * 3f;
				}
				base.transform.position += (this.moveAxis * 6f + this.vel) * Time.fixedDeltaTime * 2f;
			}
			num2 = this.sprintSpeed;
			this.cam.fieldOfView = SettingsManager.settings.playerFOV;
			this.curFOV = SettingsManager.settings.playerFOV;
			this.windAudio.volume = 0f;
			DebugMenu.UpdateDebugText("player-position", "Current Player Position: " + base.transform.position.ToString());
			DebugMenu.UpdateDebugText("player-controller-velocity", "CVEL: " + this.cCon.velocity.ToString());
			DebugMenu.UpdateDebugText("player-drag", "Current Player Drag: " + num4.ToString());
			return;
		}
		if (!this.cCon.enabled)
		{
			this.cCon.enabled = true;
		}
		if (this.IsSprintHeld())
		{
			this.sprinting = true;
		}
		else
		{
			this.sprinting = false;
		}
		if ((this.crouchPressed || this.grappled) && !this.gMan.lockPlayer && !this.gMan.lockPlayerInput && !this.gMan.freecam && !this.crouching)
		{
			this.crouching = true;
			this.sprinting = false;
			base.StopCoroutine("ChangeHeight");
			base.StartCoroutine("ChangeHeight", this.crouchHeight);
			flag = this.crouching && this.curBuffs.GetBuff("addJumpBoost") > 0f && this.cCon.isGrounded && this.GetLowestGripStrength() > 2f;
			if (flag && this.isGrounded && this.timeSinceJumpBoost > 0.3f)
			{
				this.clipHandler.GetGroup("perk").GetSet("jumpboost-prime").Play(1f, this.audioChannels[2]);
			}
		}
		if (this.crouching && !this.crouchPressed && !this.grappled && this.CanUncrouch())
		{
			this.crouching = false;
			base.StopCoroutine("ChangeHeight");
			base.StartCoroutine("ChangeHeight", this.standingHeight);
		}
		foreach (ENT_Player.Hand hand in this.hands)
		{
			if (hand.interactState == ENT_Player.InteractType.grab && hand.grabTarget != null)
			{
				float num7 = Vector3.Distance(base.transform.position, hand.grabTarget.transform.position);
				float num8 = hand.grabTarget.holdDistance * 0.5f;
				if (num7 < num8)
				{
					Vector3 vector3 = base.transform.position - hand.grabTarget.transform.position;
					vector3.y = 0f;
					vector3 = vector3.normalized;
					this.vel += vector3 * -(num7 - num8) * Time.fixedDeltaTime * 0.5f;
				}
			}
		}
		if (!this.grappled)
		{
			this.vel.y = this.vel.y + num3 * Time.fixedDeltaTime;
		}
		else
		{
			this.vel.y = this.vel.y + num3 * Time.fixedDeltaTime * 0.8f;
		}
		if (this.isGrounded)
		{
			if (!this.isSliding)
			{
				this.vel.y = Mathf.Max(this.vel.y, -0.05f);
				if (this.moveAxis.magnitude > Mathf.Epsilon)
				{
					num4 *= 15f;
				}
				else
				{
					num4 *= this.frictionMultiplier * this.curFootstep.friction;
				}
			}
		}
		else
		{
			num2 *= 0.3f;
			if (this.IsHanging() && this.hitTime > 0f)
			{
				if (this.sprinting)
				{
					num4 *= this.wallfrictionMultiplier;
				}
				else if (this.crouchPressed && !this.grappled)
				{
					num4 *= this.wallfrictionMultiplier * 30f;
					this.vel = Vector3.Lerp(this.vel, Vector3.zero, Time.deltaTime * 5f);
				}
				else
				{
					num4 *= this.wallfrictionMultiplier * 3f;
				}
			}
		}
		if (!this.IsMoveLocked())
		{
			if (this.sprinting && this.isGrounded && !this.isSliding)
			{
				num2 = this.sprintSpeed;
				this.camSway = Mathf.Sin(Time.time * 6f) * 0.6f;
				if (!SettingsManager.settings.disableSprintFov)
				{
					this.curFOV = this.sprintFOV;
				}
				this.invRoot.transform.localPosition = Vector3.Lerp(this.invRoot.transform.localPosition, new Vector3(0f, Mathf.Sin(Time.time * this.headbobFrequency) * this.headbob, 0f), Time.fixedDeltaTime * 5f);
			}
			else if (this.crouching && !this.isSliding)
			{
				num2 = this.crouchSpeed;
				this.curFOV = SettingsManager.settings.playerFOV;
			}
			else
			{
				this.curFOV = SettingsManager.settings.playerFOV;
				this.invRoot.transform.localPosition = Vector3.Lerp(this.invRoot.transform.localPosition, new Vector3(0f, Mathf.Sin(Time.time * this.headbobFrequency * 0.2f) * this.headbob * 0.2f, 0f), Time.fixedDeltaTime * 5f);
			}
		}
		this.FootstepAudio(this.moveAxis, num2);
		for (int k = 0; k < this.hands.Length; k++)
		{
			if (this.hands[k].grabTarget)
			{
				float num9 = 0f;
				if (this.hands[k].grabTarget.stuck)
				{
					num9 = this.hands[k].grabTarget.stuckStrength * 0.1f;
				}
				num2 = Mathf.Clamp(num2 - this.hands[k].grabTarget.GetRigidbody().mass * 0.1f - num9, 0f, num2);
			}
		}
		Vector3 vector4 = this.Hang(ref this.moveAxis, ref num2, ref num4);
		num2 *= this.inventory.GetEncumberance();
		if (this.gMan.lockPlayer || this.gMan.freecam || this.gMan.lockPlayerInput)
		{
			this.moveAxis = Vector3.zero;
		}
		float num10 = num * this.curBuffs.GetBuff("addSpeed") + 1f;
		if (this.IsHanging() && !this.isGrounded)
		{
			num10 *= num * this.curBuffs.GetBuff("addHangSpeed") + 1f;
		}
		this.vel += (this.moveAxis * (num2 * num10 * this.curFootstep.moveSpeed) + vector4 * 1.5f) * Time.fixedDeltaTime * 0.15f;
		float num11 = num4 * this.vel.sqrMagnitude;
		Vector3 vector5 = -this.vel.normalized * num11;
		this.vel += Vector3.ClampMagnitude(vector5 * Time.fixedDeltaTime, this.vel.magnitude / 2f);
		if ((this.timeSinceGrounded < this.coyoteTime || this.playerState == ENT_Player.PlayerState.hanging || this.extraJumpsRemaining > 0) && !this.gMan.lockPlayer && !this.gMan.freecam && !this.gMan.lockPlayerInput)
		{
			if (InputManager.GetButton("Jump").Pressed && !this.hasJumped)
			{
				bool flag4 = false;
				bool flag5 = false;
				this.vel.y = 0f;
				if (this.timeSinceGrounded >= this.coyoteTime && this.playerState != ENT_Player.PlayerState.hanging)
				{
					this.extraJumpsRemaining--;
					flag4 = true;
					this.AddGripStrength(this.curBuffs.GetBuff("addStaminaAfterExtraJump"), false);
				}
				else
				{
					this.AddGripStrength(this.curBuffs.GetBuff("addStaminaAfterJump"), false);
					if (flag && this.isGrounded)
					{
						this.timeSinceJumpBoost = 0f;
						this.AddGripStrength(this.curBuffs.GetBuff("addStaminaAfterJumpBoost"), false);
						this.camRoot.ShakeCamera(0.06f);
						if (this.moveAxis.magnitude > 0.3f)
						{
							this.vel += this.moveAxis * this.curBuffs.GetBuff("addJumpBoost") + Vector3.up * 0.02f;
						}
						else
						{
							this.vel += Vector3.up * this.curBuffs.GetBuff("addJumpBoost") * 0.55f;
						}
						flag5 = true;
					}
				}
				this.timeSinceGrounded = this.coyoteTime;
				this.hasJumped = true;
				if (Vector3.Dot(this.climbDir, Vector3.up) > 0f)
				{
					this.climbDir = Vector3.up;
				}
				Vector3 vector6 = (this.IsHanging() ? this.climbDir : this.jumpDir).normalized * (this.jumpHeight * (1f + this.curBuffs.GetBuff("addJump"))) * -this.gravity * Mathf.Min(this.inventory.GetEncumberance() * 2f, 1f);
				this.vel += vector6;
				this.wishVel = vector6;
				if (flag4)
				{
					this.clipHandler.GetGroup("perk").GetSet("doublejump").Play(1f, null);
				}
				else if (flag5)
				{
					this.clipHandler.GetGroup("perk").GetSet("jumpboost").Play(1f, null);
				}
				else if (SettingsManager.settings.voice == 1)
				{
					this.clipHandler.GetGroup("movement").GetSet("jump").Play(1f, this.audioChannels[2]);
				}
				else
				{
					this.clipHandler.GetGroup("movement").GetSet("fem-jump").Play(1f, this.audioChannels[2]);
				}
				if (this.playerState == ENT_Player.PlayerState.hanging)
				{
					foreach (ENT_Player.Hand hand2 in this.hands)
					{
						if (hand2.interactState == ENT_Player.InteractType.hanging)
						{
							this.StopInteraction(hand2.id, "jump", true);
							hand2.gripStrength -= this.climbJumpDrain / (1f + this.curBuffs.GetBuff("addGripStrength"));
						}
					}
				}
			}
			else if (!InputManager.GetButton("Jump").Pressed)
			{
				this.hasJumped = false;
			}
		}
		this.verticalSlide = false;
		float num12 = Vector3.Dot(this.vel.normalized, this.hitNormal);
		Action<string, string> updateDebugText = DebugMenu.UpdateDebugText;
		string text = "hitnormal";
		string text2 = "HIT NORMAL: ";
		Vector3 vector7 = this.hitNormal;
		updateDebugText(text, text2 + vector7.ToString());
		if (this.cConHit != null)
		{
			DebugMenu.UpdateDebugText("cconhitnormal", "CCON HIT NORMAL: " + this.cConHit.normal.ToString());
		}
		if (this.hitTime >= 0f && num12 <= 0f && num5 >= 85f)
		{
			this.vel = Vector3.ProjectOnPlane(this.vel, this.hitNormal);
			this.verticalSlide = true;
		}
		DebugMenu.UpdateDebugText("verticalSlide", "Vertical Slide: " + this.verticalSlide.ToString());
		DebugMenu.UpdateDebugText("hitdot", "Slide Dot: " + num12.ToString());
		DebugMenu.UpdateDebugText("slideangle", "Slide Angle: " + num5.ToString());
		this.cCon.Move(this.vel * Time.fixedDeltaTime * 175f);
		if (!this.verticalSlide && this.isGrounded)
		{
			Vector3 vector8 = this.cCon.velocity * Time.fixedDeltaTime;
			this.vel.x = Mathf.Clamp(this.vel.x, -Mathf.Abs(vector8.x), Mathf.Abs(vector8.x));
			this.vel.z = Mathf.Clamp(this.vel.z, -Mathf.Abs(vector8.z), Mathf.Abs(vector8.z));
			this.vel.y = Mathf.Clamp(this.vel.y, -Mathf.Abs(vector8.y), Mathf.Abs(vector8.y));
		}
		DebugMenu.UpdateDebugText("player-position", "Current Player Position: " + base.transform.position.ToString());
		DebugMenu.UpdateDebugText("player-controller-velocity", "CVEL: " + this.cCon.velocity.ToString());
		DebugMenu.UpdateDebugText("player-drag", "Current Player Drag: " + num4.ToString());
		if (CL_UIManager.debug && this.cConHit != null)
		{
			CL_DebugView.draw.Circle(this.cConHit.point, this.cConHit.normal, 0.3f);
			CL_DebugView.draw.Arrow(this.cConHit.point, this.cConHit.point + this.cConHit.normal, Color.gray);
			CL_DebugView.draw.Arrow(this.cConHit.point, this.cConHit.point + this.jumpDir, Color.blue);
			CL_DebugView.draw.Arrow(base.transform.position + base.transform.forward, base.transform.position + base.transform.forward + this.hitNormal, Color.blue);
			CL_DebugView.draw.Arrow(base.transform.position + base.transform.forward, base.transform.position + base.transform.forward + this.cConHit.normal, Color.green);
			if (this.IsHanging())
			{
				CL_DebugView.draw.Arrow(this.cConHit.point, this.cConHit.point + this.climbDir, Color.red);
			}
		}
		if (this.hitTime <= 0f)
		{
			this.isSliding = false;
		}
		this.WindSound();
	}

	// Token: 0x06000298 RID: 664 RVA: 0x00019474 File Offset: 0x00017674
	private void WindSound()
	{
		if (this.dead)
		{
			return;
		}
		this.windAudio.volume = Mathf.Lerp(this.windAudio.volume, Mathf.Max((-this.cCon.velocity.y - 2f) * this.windAudioVelocityMultiplier, 0f), Time.fixedDeltaTime * 5f);
		if (this.cCon.velocity.y < -2f && !this.fallingAudio)
		{
			this.fallingAudio = true;
			this.windAudio.Play();
			return;
		}
		if (this.fallingAudio && this.isGrounded && !this.isSliding)
		{
			this.fallingAudio = false;
		}
	}

	// Token: 0x06000299 RID: 665 RVA: 0x0001952C File Offset: 0x0001772C
	private Vector3 Hang(ref Vector3 moveAxis, ref float curSpeed, ref float curDrag)
	{
		float num = (this.grappled ? 0.25f : 1f);
		Vector3 vector = Vector3.zero;
		if (this.playerState == ENT_Player.PlayerState.hanging)
		{
			curSpeed = this.speed;
			float num2 = 1.2f;
			float num3 = 0f;
			if (!this.isGrounded)
			{
				this.climbDir = (this.hasHit ? ENT_Player.MostYFacingPerpendicular(this.hitNormal) : Vector3.up);
				if (this.climbDir != Vector3.up)
				{
					this.climbDir += this.hitNormal * 0.1f;
				}
				DebugMenu.UpdateDebugText("player-climbBool", "Player is Climbing against Surface");
			}
			else
			{
				this.climbDir = Vector3.up;
				DebugMenu.UpdateDebugText("player-climbBool", "Player is NOT Climbing against Surface");
			}
			RaycastHit raycastHit;
			if (Physics.Raycast(this.cam.transform.position, base.transform.forward, out raycastHit, this.cCon.radius * 2.5f, this.jumpMask) && this.IsHanging())
			{
				moveAxis = base.transform.right * this.inputAxis.x * 0.5f + this.inputAxis.y * Vector3.Lerp(this.cam.transform.forward, -this.climbDir, 0.3f).normalized;
			}
			else
			{
				moveAxis = base.transform.right * this.inputAxis.x * 0.5f + this.inputAxis.y * (this.cam.transform.forward + this.climbDir);
			}
			Vector3 vector2 = Vector3.zero;
			float num4 = this.interactDistance * (1f + this.curBuffs.GetBuff("addReach"));
			for (int i = 0; i < this.hands.Length; i++)
			{
				if (this.hands[i].interactState == ENT_Player.InteractType.hanging && !ENT_Player.IsNullOrDestroyed(this.hands[i].currentInteract))
				{
					Transform holdTarget = this.hands[i].holdTarget;
					Vector3 vector3 = this.hands[i].currentInteract.GetGameObject().transform.TransformPoint(this.hands[i].holdPosition) - base.transform.position;
					float magnitude = vector3.magnitude;
					if (magnitude <= num2)
					{
						vector3 *= 0f;
					}
					else
					{
						vector3 = vector3.normalized * (magnitude - num2);
					}
					if (magnitude > num4 * 1.25f)
					{
						vector3 *= 4f;
						if (magnitude > num4 * 2f)
						{
							this.StopInteraction(i, "", true);
						}
					}
					vector += vector3;
					if (this.hitNormal != Vector3.zero)
					{
						vector += this.hitNormal * 0.09f;
					}
					float num5 = Vector3.Dot(this.cam.transform.forward, Vector3.up);
					if (num5 >= 0f)
					{
						num3 = Mathf.Clamp(num3 + this.inputAxis.y * 0.015f, 0f, 0.02f);
						num3 *= holdTarget.GetComponent<CL_Handhold>().GetClimbMult();
						if (num5 > 0.5f)
						{
							num3 *= 1f + num * this.curBuffs.GetBuff("addClimb");
						}
					}
					else if (num5 < 0f)
					{
						float num6 = Mathf.Abs(num5);
						num3 = Mathf.Clamp(num3 + Mathf.Lerp(this.inputAxis.y, -this.inputAxis.y, num6) * 0.015f, 0f, 0.02f);
						num3 *= holdTarget.GetComponent<CL_Handhold>().GetClimbMult();
					}
					this.vel.x = this.vel.x + this.climbDir.x * num3 * Time.fixedDeltaTime;
					this.vel.y = Mathf.Lerp(this.vel.y, num3 * this.climbDir.y, Time.fixedDeltaTime * 8f * Mathf.Clamp(this.hands[i].holdTime, 0f, 2f));
					this.vel.z = this.vel.z + this.climbDir.z * num3 * Time.fixedDeltaTime;
					curDrag = Mathf.Max(curDrag, this.dragCoefficient * this.hands[i].dragMult);
				}
				if (moveAxis.magnitude < 0.05f && this.crouchPressed)
				{
					this.vel.y = 0f;
					this.vel = Vector3.Lerp(this.vel, Vector3.zero, Time.deltaTime * 5f);
				}
				vector2 = Vector3.ClampMagnitude(vector2 + this.hands[i].GetVelocity(), Mathf.Max(vector2.magnitude, this.hands[i].GetVelocity().magnitude));
			}
			if (this.timeSinceGrounded > 0.1f)
			{
				vector += vector2 * -0.06f;
			}
			curSpeed *= 1f + this.curBuffs.GetBuff("addClimb");
		}
		else
		{
			DebugMenu.UpdateDebugText("player-climbBool", "Player is NOT Climbing");
		}
		return vector;
	}

	// Token: 0x0600029A RID: 666 RVA: 0x00019AC8 File Offset: 0x00017CC8
	private void LandingAudio()
	{
		if (this.IsHanging() && this.timeSinceGrounded > 0.2f && this.cCon.isGrounded)
		{
			this.clipHandler.GetGroup("movement").GetSet("mantle").Play(1f, this.audioChannels[1]);
			return;
		}
		if (this.wasGrounded != this.cCon.isGrounded && !this.isSliding)
		{
			this.curFootstep = this.GetFootstepMaterial();
			if (this.cCon.isGrounded && this.lastVel.y < -this.softLandCutoff && (this.timeSinceGrounded > 0.2f || this.IsHanging()) && !this.dead)
			{
				if (this.curFootstep.useLandingAudio)
				{
					this.clipHandler.GetGroup("movement").GetSet("land-heavy").PlaySound(this.curFootstep.landingAudioVolume, this.curFootstep.landingAudio[global::UnityEngine.Random.Range(0, this.curFootstep.landingAudio.Count)]);
					if (this.curFootstep.landingEffect != null)
					{
						if (!this.playerEffects.ContainsKey(this.curFootstep.landingEffect.name))
						{
							this.playerEffects.Add(this.curFootstep.landingEffect.name, Object.Instantiate<ParticleSystem>(this.curFootstep.landingEffect, this.curFootstepRaycast.point, Quaternion.identity, this.effectsRoot));
						}
						ParticleSystem.EmitParams emitParams = default(ParticleSystem.EmitParams);
						emitParams.position = this.curFootstepRaycast.point;
						emitParams.applyShapeToPosition = true;
						this.playerEffects[this.curFootstep.landingEffect.name].Emit(emitParams, 20);
					}
				}
				else if (this.lastVel.y < -this.heavyLandCutoff)
				{
					if (SettingsManager.settings.voice == 1)
					{
						this.clipHandler.GetGroup("movement").GetSet("land-heavy").Play(1f, this.audioChannels[1]);
					}
					else
					{
						this.clipHandler.GetGroup("movement").GetSet("fem-land-heavy").Play(1f, this.audioChannels[1]);
					}
					CL_CameraControl.Shake(0.05f);
				}
				else if (this.lastVel.y < -this.mediumLandCutoff)
				{
					this.clipHandler.GetGroup("movement").GetSet("land-medium").Play(1f, this.audioChannels[1]);
					CL_CameraControl.Shake(0.01f);
				}
				else
				{
					this.clipHandler.GetGroup("movement").GetSet("land-soft").Play(1f, this.audioChannels[1]);
				}
			}
			this.wasGrounded = this.cCon.isGrounded;
		}
	}

	// Token: 0x0600029B RID: 667 RVA: 0x00019DF0 File Offset: 0x00017FF0
	private void MouseLook()
	{
		if (this.softParent.IsParented())
		{
			base.transform.rotation *= this.softParent.GetRotationalDifference();
		}
		if (!this.IsMoveLocked() || !this.IsInputLocked())
		{
			this.InterpolateCamera();
		}
		if (this.IsLocked())
		{
			return;
		}
		float num = this.camSpeed * (SettingsManager.settings.mouseSensitivity * 2f);
		float num2 = -InputManager.GetLookVector().y;
		if (SettingsManager.settings.invertY)
		{
			num2 = -num2;
		}
		float num3 = this.camMaxY;
		if (this.hands[0].interactState == ENT_Player.InteractType.grab || this.hands[1].interactState == ENT_Player.InteractType.grab)
		{
			num3 -= 20f;
		}
		this.camY = Mathf.Clamp(this.camY + num2 * num, this.camMinY, num3);
		Vector3 vector = new Vector3(this.camY, InputManager.GetLookVector().x * num, this.camSway);
		Action<string, string> updateDebugText = DebugMenu.UpdateDebugText;
		string text = "rotationVelocity";
		string text2 = "Rotation Velocity: ";
		Vector3 vector2 = this.cameraRotationVelocity;
		updateDebugText(text, text2 + vector2.ToString());
		vector += this.cameraRotationVelocity * Time.deltaTime;
		this.camY = vector.x;
		base.transform.rotation *= Quaternion.Euler(0f, vector.y, 0f);
		this.camTransform.localRotation = Quaternion.Euler(vector.x, 0f, vector.z);
		this.cameraRotationVelocity = Vector3.Lerp(this.cameraRotationVelocity, Vector3.zero, Time.deltaTime * 3f);
		if (!this.IsMoveLocked())
		{
			if (this.timeSinceGrounded < 0.2f)
			{
				this.bobTime += Time.deltaTime * Mathf.Clamp(this.cCon.velocity.magnitude * 0.5f, 0f, 1f);
				Vector3 vector3 = new Vector3(Mathf.Sin(this.bobTime * 6f) * 0.5f, Mathf.Cos(this.bobTime * 12f), 0f) * 0.03f * Mathf.Min(this.cCon.velocity.magnitude, 1f);
				this.camTransform.localPosition = vector3;
			}
			else
			{
				this.camTransform.localPosition = Vector3.Lerp(this.camTransform.localPosition, Vector3.zero, Time.deltaTime * 4f);
			}
		}
		else
		{
			this.camTransform.localPosition = Vector3.Lerp(this.camTransform.localPosition, Vector3.zero, Time.deltaTime * 4f);
			this.cam.fieldOfView = this.smoothedFOV;
		}
		this.cameraPosition = this.camTransform.position;
		this.cameraRotation = this.camTransform.rotation;
		this.softParent.UpdateRotationInfo();
	}

	// Token: 0x0600029C RID: 668 RVA: 0x0001A104 File Offset: 0x00018304
	private void InterpolateCamera()
	{
		Vector3 vector = Vector3.zero;
		if (this.lastFixedPlayerPosition != base.transform.position)
		{
			vector = base.transform.position - this.lastFixedPlayerPosition;
			Action<string, string> updateDebugText = DebugMenu.UpdateDebugText;
			string text = "interpolationOffset";
			string text2 = "Interpolation Offset: ";
			Vector3 vector2 = vector;
			updateDebugText(text, text2 + vector2.ToString());
		}
		float num = Mathf.Clamp01((Time.time - Time.fixedTime) / Time.fixedDeltaTime);
		if (1f / Time.deltaTime > 50f)
		{
			this.camRoot.transform.position = Vector3.Lerp(this.previousFixedCameraPosition, this.currentFixedCameraPosition, num) + (1f - num) * vector;
			return;
		}
		this.camRoot.transform.position = this.currentFixedCameraPosition;
	}

	// Token: 0x0600029D RID: 669 RVA: 0x0001A1E4 File Offset: 0x000183E4
	private void Actions()
	{
		Physics.queriesHitBackfaces = false;
		if (this.hands[0].interactState == ENT_Player.InteractType.hanging && this.hands[1].interactState == ENT_Player.InteractType.hanging)
		{
			this.gripStrengthLossRate = 0.5f;
		}
		else
		{
			this.gripStrengthLossRate = 1f;
		}
		this.aimCircle.gameObject.SetActive(false);
		if (!this.gMan.isPaused)
		{
			this.InteractCheck(0);
			this.InteractCheck(1);
		}
		UT_AudioClipHandler.AudioSet set = this.clipHandler.GetGroup("fatigue").GetSet("level");
		if (this.IsHanging())
		{
			set.SetVolume(Mathf.Lerp(set.GetCurrentVolume(), (1f - this.GetLowestGripStrength() / this.gripStrengthTimer - 0.5f) * 2f * set.maxVolume, Time.deltaTime * 3f));
			return;
		}
		set.SetVolume(Mathf.Lerp(set.GetCurrentVolume(), 0f, Time.deltaTime * 3f));
	}

	// Token: 0x0600029E RID: 670 RVA: 0x0001A2E4 File Offset: 0x000184E4
	private void InteractCheck(int hand)
	{
		ENT_Player.Hand hand2 = this.hands[hand];
		if (hand2.lockHand)
		{
			return;
		}
		if (this.camLocked)
		{
			hand2.uiInteract.gameObject.SetActive(false);
		}
		Vector3 vector = base.transform.right * 2f * (float)hand - base.transform.right;
		if (hand2.memoryTime > 0f)
		{
			hand2.memoryTime -= Time.deltaTime;
		}
		if (hand2.grabWait >= 0f)
		{
			hand2.grabWait -= Time.deltaTime;
		}
		bool flag = true;
		string text = "";
		if (hand2.interactState == ENT_Player.InteractType.item)
		{
			if (hand2.inventoryHand.currentItem.interactType != Item.InteractType.none)
			{
				text += "Item Has Interactor";
				flag = true;
			}
			else
			{
				text += "Is Holding Item ";
				flag = false;
			}
		}
		if (hand2.IsLocked())
		{
			text += "Is Locked ";
			flag = false;
		}
		if (OS_Manager.activeComputer && OS_Manager.activeComputer.canControl)
		{
			text += "Is In Computer ";
			flag = false;
		}
		if (DebugMenu.visible)
		{
			if (text == "")
			{
				text = "N/A";
			}
			DebugMenu.UpdateDebugText(string.Format("hand{0}CanInteract", hand), string.Format("Hand {0} Can Interact: {1} - Reason: {2}", hand, flag, text));
		}
		bool flag2 = hand2.currentInteract != null || hand2.grabTarget != null;
		string fireButton = hand2.fireButton;
		if (DebugMenu.visible)
		{
			string text2 = "";
			if (hand2.grabTarget != null)
			{
				text2 = "- " + hand2.grabTarget.name;
			}
			else if (hand2.currentInteract != null)
			{
				text2 = "- " + hand2.currentInteract.GetGameObject().name;
			}
			DebugMenu.UpdateDebugText(string.Format("hand{0}Interacting", hand), string.Format("Hand {0} Is Interacting: {1} {2}", hand, flag2, text2));
		}
		if (Inventory.IsShowingInventory() && hand2.interactState == ENT_Player.InteractType.item && InputManager.GetButton(fireButton).Down)
		{
			this.inventory.DropItemFromHand(this.camTransform.position + this.camTransform.forward, hand, false);
			return;
		}
		if (hand2.cooldown > 0f)
		{
			hand2.cooldown -= Time.deltaTime;
			return;
		}
		float num = this.interactDistance * (1f + this.curBuffs.GetBuff("addReach"));
		InteractHit interactHit = new InteractHit();
		bool flag3 = false;
		bool flag4 = false;
		if (!flag2 && !this.camLocked)
		{
			float num2 = Mathf.Clamp(this.cCon.velocity.magnitude * 0.03f, 0.15f, 0.3f);
			if (InputManager.IsGamepad())
			{
				num2 *= 1.5f;
			}
			Physics.queriesHitBackfaces = true;
			RaycastHit raycastHit;
			RaycastHit raycastHit2;
			if (Inventory.IsShowingInventory() && flag)
			{
				hand2.uiInteract.sprite = this.uiSpriteDict["hand-point"].sprite;
				hand2.uiInteract.gameObject.SetActive(false);
				if (Physics.Raycast(this.camTransform.position, this.camTransform.forward, out raycastHit, num * 2f, this.inventoryItemMask))
				{
					ObjectTagger component = raycastHit.collider.GetComponent<ObjectTagger>();
					if (hand2.interactState != ENT_Player.InteractType.item)
					{
						if (component != null)
						{
							if (raycastHit.collider.GetComponent<Clickable>().CanInteract(this, hand2))
							{
								hand2.uiInteract.gameObject.SetActive(true);
								this.interactRay = true;
								UT_HoverCursor component2 = raycastHit.collider.GetComponent<UT_HoverCursor>();
								if (component2 != null)
								{
									hand2.uiInteract.sprite = component2.GetCursor();
								}
								else if (component.HasTag("Pickupable"))
								{
									hand2.uiInteract.sprite = this.uiSpriteDict["hand-point"].sprite;
								}
							}
							else
							{
								hand2.uiInteract.gameObject.SetActive(false);
								this.interactRay = false;
							}
							if (InputManager.GetButton(fireButton).Down && this.interactRay && !flag2 && component.HasTag("Item"))
							{
								Item_Object component3 = raycastHit.collider.GetComponent<Item_Object>();
								if (component3.CanPickup())
								{
									if (component.HasTag("Prop"))
									{
										raycastHit.collider.GetComponent<CL_Prop>().Pickup();
									}
									this.inventory.AddItemToHand(component3.itemData, hand2);
									raycastHit.collider.gameObject.SetActive(false);
									hand2.uiInteract.gameObject.SetActive(false);
									hand2.cooldown += 0.15f;
									return;
								}
								hand2.uiInteract.gameObject.SetActive(false);
							}
						}
						else
						{
							hand2.uiInteract.gameObject.SetActive(false);
						}
					}
				}
			}
			else if ((Physics.Raycast(this.camTransform.position + vector * 0.01f, this.camTransform.forward, out raycastHit2, num, this.interactMask) | Physics.SphereCast(this.camTransform.position + vector * 0.01f, num2, this.camTransform.forward, out raycastHit, num, this.interactMask)) && !Inventory.IsShowingInventory())
			{
				flag4 = true;
				if (raycastHit.transform != null)
				{
					interactHit.Initialize(raycastHit);
				}
				else
				{
					interactHit.Initialize(raycastHit2);
				}
				if (flag)
				{
					ObjectTagger objectTagger = interactHit.collider.GetComponent<ObjectTagger>();
					hand2.uiInteract.sprite = this.uiSpriteDict["hand-open"].sprite;
					if (raycastHit2.collider != null)
					{
						Collider[] array = Physics.OverlapSphere(raycastHit2.point, 0.1f, this.interactMask);
						ObjectTagger objectTagger2 = raycastHit2.collider.GetComponent<ObjectTagger>();
						if (objectTagger2 != null && (objectTagger2.HasTag("Button") || objectTagger2.HasTag("Item") || objectTagger2.HasTag("Handhold")))
						{
							interactHit.Initialize(raycastHit2);
							interactHit.collider = raycastHit2.collider;
							if (!raycastHit2.collider.GetComponent<MeshCollider>())
							{
								interactHit.point = raycastHit2.collider.ClosestPoint(interactHit.point);
							}
							objectTagger = objectTagger2;
							if (CL_UIManager.debug)
							{
								CL_DebugView.draw.SphereOutline(raycastHit2.point, 0.1f, Color.green);
							}
						}
						else
						{
							Collider[] array2 = array;
							int i = 0;
							while (i < array2.Length)
							{
								Collider collider = array2[i];
								objectTagger2 = collider.GetComponent<ObjectTagger>();
								if (objectTagger2 != null && objectTagger2.HasTag("Handhold"))
								{
									interactHit.Initialize(raycastHit2);
									interactHit.collider = collider;
									objectTagger = objectTagger2;
									if (CL_UIManager.debug)
									{
										CL_DebugView.draw.SphereOutline(raycastHit2.point, 0.1f, Color.green);
										break;
									}
									break;
								}
								else
								{
									i++;
								}
							}
						}
					}
					if (CL_UIManager.debug && hand == 0)
					{
						CL_DebugView.draw.Label2D(interactHit.point, interactHit.collider.name, Color.green);
					}
					if (hand2.grabWait <= 0f)
					{
						if (objectTagger != null)
						{
							Clickable component4 = interactHit.collider.GetComponent<Clickable>();
							if (component4 != null)
							{
								Sprite sprite = this.uiSpriteDict["hand-open"].sprite;
								if (hand2.interactState != ENT_Player.InteractType.item)
								{
									this.Interact(hand2, ref flag2, fireButton, ref interactHit, out sprite, interactHit.collider, objectTagger, component4);
									flag3 = true;
								}
								else
								{
									hand2.inventoryHand.currentItem.interactor.Interact(hand2, ref flag2, fireButton, ref interactHit, out sprite, interactHit.collider, objectTagger, component4, hand2.GetHandItem().item);
									flag3 = true;
								}
								if (sprite == null)
								{
									sprite = this.uiSpriteDict["hand-open"].sprite;
								}
								hand2.uiInteract.sprite = sprite;
							}
						}
						else
						{
							hand2.uiInteract.gameObject.SetActive(false);
						}
						if (this.curBuffs.GetBuff("grabAnything") > 0f && hand2.grabWait <= 0f && !flag2 && !flag3 && hand2.interactState == ENT_Player.InteractType.none && hand2.handhold != this.grabAnywhereFakeHandholds[hand])
						{
							this.grabAnywhereFakeHandholds[hand].gameObject.SetActive(true);
							this.grabAnywherePosition[hand] = interactHit.transform.InverseTransformPoint(interactHit.point);
							objectTagger = this.grabAnywhereFakeHandholds[hand].GetTagger();
							this.grabAnywhereHit[hand] = interactHit.transform;
							Sprite sprite2 = this.uiSpriteDict["hand-open"].sprite;
							this.Interact(hand2, ref flag2, fireButton, ref interactHit, out sprite2, null, objectTagger, this.grabAnywhereFakeHandholds[hand]);
							hand2.uiInteract.sprite = sprite2;
							hand2.holdPosition = Vector3.zero;
						}
						else
						{
							this.grabAnywhereFakeHandholds[hand].gameObject.SetActive(false);
						}
					}
				}
			}
			else
			{
				hand2.uiInteract.gameObject.SetActive(false);
				this.interactRay = false;
				this.aimCircle.gameObject.SetActive(false);
			}
		}
		if (flag4)
		{
			this.AimCircle(interactHit, hand2);
		}
		Physics.queriesHitBackfaces = false;
		if (flag4)
		{
			if (InputManager.GetButton(hand2.throwButton).Down && hand2.interactState == ENT_Player.InteractType.item)
			{
				this.inventory.DropItemFromHand(interactHit.point - this.camTransform.forward * 0.3f, hand, false);
				return;
			}
		}
		else if (InputManager.GetButton(hand2.throwButton).Down && hand2.interactState == ENT_Player.InteractType.item)
		{
			this.inventory.DropItemFromHand(this.camTransform.position + this.camTransform.forward, hand, false);
			return;
		}
		if (hand2.memoryTime > 0f && hand2.grabWait < 0f && !ENT_Player.IsNullOrDestroyed(hand2.holdMemory) && hand2.interactState != ENT_Player.InteractType.item && flag)
		{
			Clickable holdMemory = hand2.holdMemory;
			ObjectTagger tagger = holdMemory.GetTagger();
			if (holdMemory.CanInteract(this, hand2))
			{
				hand2.uiInteract.gameObject.SetActive(true);
				this.interactRay = true;
				UT_HoverCursor component5 = holdMemory.GetGameObject().GetComponent<UT_HoverCursor>();
				if (component5 != null)
				{
					hand2.uiInteract.sprite = component5.GetCursor();
				}
				else if (tagger.HasTag("Pickupable"))
				{
					hand2.uiInteract.sprite = this.uiSpriteDict["hand-open"].sprite;
				}
				else if (tagger.HasTag("Door"))
				{
					hand2.uiInteract.sprite = this.uiSpriteDict["hand-point"].sprite;
				}
				else if (tagger.HasTag("Locked"))
				{
					hand2.uiInteract.sprite = this.uiSpriteDict["lock"].sprite;
				}
				else if (tagger.HasTag("NPC"))
				{
					hand2.uiInteract.sprite = this.uiSpriteDict["hand-open"].sprite;
				}
				else if (tagger.HasTag("Button"))
				{
					hand2.uiInteract.sprite = this.uiSpriteDict["hand-point"].sprite;
				}
			}
			else
			{
				hand2.uiInteract.gameObject.SetActive(false);
				this.interactRay = false;
			}
			if (InputManager.GetButton(fireButton).Pressed && !flag2 && holdMemory.CanInteract(this, hand2))
			{
				flag2 = true;
				hand2.interactState = ENT_Player.InteractType.hanging;
				if (this.curBuffs.GetBuff("grabAnything") > 0f && holdMemory == this.grabAnywhereFakeHandholds[hand])
				{
					hand2.GrabHold(holdMemory.GetGameObject().transform, holdMemory.GetGameObject().transform.position);
				}
				else
				{
					hand2.GrabHold(holdMemory.GetGameObject().transform, holdMemory.GetGameObject().transform.TransformPoint(hand2.holdMemoryPosition));
				}
				hand2.currentInteract = holdMemory;
				hand2.currentInteract.Interact(this, hand2);
				CL_Handhold component6 = holdMemory.GetGameObject().GetComponent<CL_Handhold>();
				hand2.dragMult = component6.dragMult;
				this.clipHandler.GetGroup("climbing").GetSet("handhold").Play(1f, this.audioChannels[3]);
			}
		}
		float currentGripStrengthTimer = this.GetCurrentGripStrengthTimer();
		DebugMenu.UpdateDebugText("gripstrengthtimer", "Max Grip Strength Timer: " + currentGripStrengthTimer.ToString());
		DebugMenu.UpdateDebugText("gripstrength", "Current Grip Strength: " + hand2.gripStrength.ToString());
		float num3 = Mathf.Clamp01(1f - hand2.gripStrength / this.gripStrengthTimer);
		if (this.infiniteStamina)
		{
			hand2.gripStrength = currentGripStrengthTimer;
		}
		if (flag2 && flag)
		{
			if (hand2.interactState == ENT_Player.InteractType.hanging && !ENT_Player.IsNullOrDestroyed(hand2.currentInteract))
			{
				hand2.holdTime += Time.deltaTime;
				if (num3 > 0.92f)
				{
					CL_CameraControl.Shake(0.0005f * num3);
				}
				hand2.uiInteract.sprite = this.uiSpriteDict["hand-grabbed"].sprite;
				Vector3 vector2 = global::UnityEngine.Random.insideUnitSphere * (num3 + hand2.GetShake() * 10f);
				if (!hand2.IsLocked())
				{
					hand2.handModel.position = hand2.currentInteract.GetGameObject().transform.TransformPoint(hand2.holdPosition) + vector2 * 0.02f;
					hand2.handModel.rotation = Quaternion.LookRotation(hand2.handModel.position - this.cam.transform.position, this.cam.transform.up);
					hand2.handSprite.sprite = hand2.grabSprite;
				}
				if (!this.isGrounded || this.grappled)
				{
					float num4 = 1f;
					if (hand2.handhold != null)
					{
						num4 *= hand2.handhold.strainRate;
					}
					hand2.gripStrength = Mathf.Clamp(hand2.gripStrength - Time.deltaTime * (this.gripStrengthLossRate / (this.curBuffs.GetBuff("addGripStrength") + 1f)) * num4, 0f, currentGripStrengthTimer);
				}
				else
				{
					hand2.gripStrength = Mathf.Clamp(hand2.gripStrength + Time.deltaTime * 0.75f, 0f, currentGripStrengthTimer);
				}
			}
			else if ((hand2.interactState == ENT_Player.InteractType.grab || hand2.interactState == ENT_Player.InteractType.hold) && !ENT_Player.IsNullOrDestroyed(hand2.currentInteract))
			{
				hand2.holdTime = 0f;
				if (!hand2.IsLocked())
				{
					hand2.handModel.position = hand2.currentInteract.GetGameObject().transform.TransformPoint(hand2.holdPosition);
					hand2.handModel.rotation = Quaternion.LookRotation(hand2.handModel.position - this.cam.transform.position, this.cam.transform.up);
					hand2.handSprite.sprite = hand2.grabSprite;
					hand2.handModel.localRotation = Quaternion.identity;
				}
				hand2.gripStrength = Mathf.Clamp(hand2.gripStrength + Time.deltaTime * 0.5f, 0f, currentGripStrengthTimer);
				hand2.handSprite.sprite = hand2.grabSprite;
				if (hand2.interactState == ENT_Player.InteractType.grab)
				{
					this.GrabPropUpdate(hand);
				}
				else if (hand2.interactState == ENT_Player.InteractType.hold && hand2.holdTarget != null)
				{
					float num5 = Vector3.Distance(base.transform.position, hand2.holdTarget.position);
					if (num5 > num * 1.1f)
					{
						this.StopInteraction(hand, "", true);
					}
					else if (num5 > 1.1f)
					{
						this.AddForce((hand2.holdTarget.position - base.transform.position) * Time.deltaTime * 1.1f);
					}
				}
			}
			if (InputManager.GetButton(fireButton).Up && (hand2.currentInteract != null || hand2.grabTarget != null) && (hand2.interactState == ENT_Player.InteractType.hold || hand2.interactState == ENT_Player.InteractType.grab || hand2.interactState == ENT_Player.InteractType.hanging))
			{
				this.StopInteraction(hand, "", true);
			}
		}
		else
		{
			hand2.ShakeHand(num3 * Time.deltaTime * 0.04f);
			hand2.holdTime = 0f;
			if (!hand2.IsLocked())
			{
				hand2.handModel.localRotation = Quaternion.identity;
			}
			if (flag)
			{
				if (InputManager.GetButton(fireButton).Pressed)
				{
					hand2.handSprite.sprite = hand2.openSprite;
				}
				else
				{
					hand2.handSprite.sprite = hand2.normalSprite;
				}
			}
			float num6 = 1f + this.curBuffs.GetBuff("addStaminaRegen");
			if (this.isGrounded)
			{
				hand2.gripStrength = Mathf.Clamp(hand2.gripStrength + Time.deltaTime * 0.9f * num6, 0f, currentGripStrengthTimer);
				hand2.gripStrength += this.curBuffs.GetBuff("groundedRegeneration") * Time.deltaTime;
			}
			else if (hand2.CanRegenerate())
			{
				hand2.gripStrength = Mathf.Clamp(hand2.gripStrength + Time.deltaTime * 0.75f * num6, 0f, currentGripStrengthTimer);
			}
		}
		if (hand2.gripStrength <= 0.1f && hand2.grabWait < 0.1f)
		{
			this.clipHandler.GetGroup("fatigue").GetSet("drop").Play(1f, this.audioChannels[1]);
			if (hand2.interactState != ENT_Player.InteractType.item)
			{
				this.StopInteraction(hand, "", true);
				hand2.grabWait = 1f;
			}
			return;
		}
		Color color = Color.white;
		if (this.curBuffs.GetBuff("grabAnything") > 0f)
		{
			color = Color.Lerp(Color.yellow, color, 1f - this.curBuffs.GetBuff("gooped"));
		}
		color = Color.Lerp(new Color(0f, 0.4812922f, 1f), color, 1f - this.curBuffs.GetBuff("pilled") * 0.5f * (Mathf.Sin(Time.time * 6f) / 2f + 0.5f));
		Color color2 = Color.Lerp(Color.red, color, Mathf.Round(hand2.gripStrength / this.gripStrengthTimer * 6f) / 6f);
		hand2.uiInteract.color = color2;
		hand2.handSprite.color = color2;
		if (hand2.interactState == ENT_Player.InteractType.item)
		{
			hand2.GetHandItem().SetHandColor(color2);
		}
		hand2.gripStrength = Mathf.Clamp(hand2.gripStrength + Time.deltaTime * this.curBuffs.GetBuff("regenerateGripStrength"), 0f, currentGripStrengthTimer);
	}

	// Token: 0x0600029F RID: 671 RVA: 0x0001B6B4 File Offset: 0x000198B4
	private void Interact(ENT_Player.Hand curhand, ref bool interacting, string fireButton, ref InteractHit hit, out Sprite interactSprite, Collider hitCollider, ObjectTagger tagger, Clickable clickable)
	{
		if ((this.useInteractWhitelist && !tagger.HasTagInList(this.interactWhitelist.ToArray())) || curhand.IsLocked())
		{
			interactSprite = null;
			curhand.uiInteract.gameObject.SetActive(false);
			this.interactRay = false;
			interacting = false;
			return;
		}
		float num = this.interactDistance * (1f + this.curBuffs.GetBuff("addReach"));
		Sprite sprite = null;
		if (clickable.CanInteract(this, curhand))
		{
			if (tagger.HasTag("Handhold"))
			{
				curhand.memoryTime = this.grabForgive;
				curhand.holdMemory = clickable;
				curhand.holdMemoryPosition = clickable.GetGameObject().transform.InverseTransformPoint(hit.point);
			}
			else if (tagger.HasTag("Button") && hit.distance > num * 0.85f)
			{
				sprite = null;
				interactSprite = sprite;
				curhand.uiInteract.gameObject.SetActive(false);
				this.interactRay = false;
				interacting = false;
				return;
			}
			curhand.uiInteract.gameObject.SetActive(true);
			this.interactRay = true;
			UT_HoverCursor ut_HoverCursor = null;
			if (hitCollider)
			{
				ut_HoverCursor = hitCollider.GetComponent<UT_HoverCursor>();
			}
			if (ut_HoverCursor != null)
			{
				sprite = ut_HoverCursor.GetCursor();
			}
			else if (tagger.HasTag("Pickupable"))
			{
				sprite = this.uiSpriteDict["hand-open"].sprite;
			}
			else if (tagger.HasTag("Handhold"))
			{
				sprite = this.uiSpriteDict["hand-open"].sprite;
			}
			else if (tagger.HasTag("Door"))
			{
				sprite = this.uiSpriteDict["hand-point"].sprite;
			}
			else if (tagger.HasTag("Locked"))
			{
				sprite = this.uiSpriteDict["lock"].sprite;
			}
			else if (tagger.HasTag("NPC"))
			{
				sprite = this.uiSpriteDict["hand-open"].sprite;
			}
			else if (tagger.HasTag("Button"))
			{
				sprite = this.uiSpriteDict["hand-point"].sprite;
			}
			else if (tagger.HasTag("Item"))
			{
				sprite = this.uiSpriteDict["hand-item"].sprite;
			}
		}
		else
		{
			curhand.uiInteract.gameObject.SetActive(false);
			this.interactRay = false;
		}
		if (InputManager.GetButton(fireButton).Down && this.interactRay && !interacting)
		{
			if (tagger.HasTag("Item"))
			{
				Item_Object component = hit.collider.GetComponent<Item_Object>();
				if (component.CanPickup())
				{
					component.Pickup();
					if (tagger.HasTag("Prop"))
					{
						hitCollider.GetComponent<CL_Prop>().Pickup();
					}
					this.inventory.AddItemToHand(component.itemData, curhand);
					hitCollider.gameObject.SetActive(false);
					curhand.uiInteract.gameObject.SetActive(false);
				}
				else
				{
					curhand.uiInteract.gameObject.SetActive(false);
				}
			}
			else if (tagger.HasTag("Pickupable"))
			{
				curhand.grabTarget = hit.rigidbody.GetComponent<CL_Prop>();
				sprite = this.uiSpriteDict["hand-grabbed"].sprite;
				interacting = true;
				curhand.holdPosition = clickable.GetGameObject().transform.InverseTransformPoint(hit.point);
				curhand.currentInteract = clickable;
				curhand.currentInteract.Interact(this, "");
				if (curhand.grabTarget != null)
				{
					this.holdDistance = curhand.grabTarget.holdDistance;
				}
				else
				{
					this.holdDistance = 1.8f;
				}
				curhand.interactState = ENT_Player.InteractType.grab;
				curhand.grabTarget.anchorPoint = curhand.grabTarget.transform.InverseTransformPoint(hit.point);
			}
			else if (!tagger.HasTag("Door"))
			{
				if (tagger.HasTag("NPC"))
				{
					if (clickable.CanInteract(this, curhand))
					{
						clickable.Interact(this, "");
						curhand.uiInteract.gameObject.SetActive(false);
					}
				}
				else if (tagger.HasTag("Button"))
				{
					if (clickable.CanInteract(this, curhand))
					{
						clickable.Interact(this, "");
						curhand.uiInteract.gameObject.SetActive(false);
						curhand.interactState = ENT_Player.InteractType.none;
					}
				}
				else if (tagger.HasTag("HoldButton"))
				{
					if (clickable.CanInteract(this, curhand))
					{
						curhand.currentInteract = clickable;
						curhand.currentInteract.Interact(this, curhand);
						curhand.GrabTarget(clickable.GetGameObject().transform, hit.point);
						interacting = true;
						curhand.interactState = ENT_Player.InteractType.hold;
						curhand.holdTarget = clickable.GetGameObject().transform;
					}
				}
				else if (tagger.HasTag("Handhold") && clickable.CanInteract(this, curhand))
				{
					interacting = true;
					curhand.interactState = ENT_Player.InteractType.hanging;
					curhand.currentInteract = clickable;
					curhand.currentInteract.Interact(this, curhand);
					if (hitCollider)
					{
						curhand.GrabHold(hitCollider.transform, hit.point);
					}
					else
					{
						curhand.GrabHold(clickable.GetGameObject().transform, hit.point);
					}
					CL_Handhold component2 = clickable.GetGameObject().GetComponent<CL_Handhold>();
					curhand.dragMult = component2.dragMult;
					this.clipHandler.GetGroup("climbing").GetSet("handhold").Play(1f, base.transform.position);
					sprite = this.uiSpriteDict["hand-grabbed"].sprite;
				}
			}
		}
		interactSprite = sprite;
	}

	// Token: 0x060002A0 RID: 672 RVA: 0x0001BC88 File Offset: 0x00019E88
	private void AimCircle(InteractHit hit, ENT_Player.Hand curhand)
	{
		if (curhand.interactState == ENT_Player.InteractType.item && curhand.inventoryHand.currentItem.handItemAsset.ShowAimCircle())
		{
			RaycastHit aimCircleHit = curhand.inventoryHand.currentItem.handItemAsset.GetAimCircleHit();
			if (CL_UIManager.debug)
			{
				CL_DebugView.draw.SphereOutline(aimCircleHit.point, 0.3f);
			}
			this.aimCircle.gameObject.SetActive(true);
			this.aimCircle.transform.position = aimCircleHit.point + aimCircleHit.normal * 0.01f;
			this.aimCircle.transform.rotation = Quaternion.LookRotation(aimCircleHit.normal);
		}
	}

	// Token: 0x060002A1 RID: 673 RVA: 0x0001BD50 File Offset: 0x00019F50
	public void StopInteraction(int hand, string type = "", bool addWait = true)
	{
		ENT_Player.Hand hand2 = this.hands[hand];
		hand2.SetRegenWait(0.17f);
		this.camLocked = false;
		if (!ENT_Player.IsNullOrDestroyed(hand2.currentInteract))
		{
			hand2.currentInteract.StopInteract(this, hand2, type);
			if (hand2.currentInteract.GetTagger() != null)
			{
				if (hand2.currentInteract.GetTagger().HasTag("Handhold"))
				{
					hand2.holdTarget = null;
					if (addWait)
					{
						hand2.grabWait += 0.25f;
					}
					this.clipHandler.GetGroup("climbing").GetSet("drop").Play(1f, this.audioChannels[0]);
					if (hand2.handhold != null)
					{
						hand2.handhold.LetGo(hand2);
						hand2.handhold = null;
					}
					hand2.holdMemory = null;
					hand2.memoryTime = 0f;
				}
				else if (hand2.currentInteract.GetTagger().HasTag("HoldButton"))
				{
					CL_HoldButton component = hand2.holdTarget.GetComponent<CL_HoldButton>();
					hand2.holdTarget = null;
					hand2.grabWait += 0.25f;
					this.clipHandler.GetGroup("climbing").GetSet("drop").Play(1f, this.audioChannels[0]);
					if (component != null)
					{
						component.StopInteract(this, "");
					}
					hand2.holdMemory = null;
					hand2.memoryTime = 0f;
				}
			}
		}
		hand2.currentInteract = null;
		this.DropGrabbedProp(hand);
		hand2.grabTarget = null;
		hand2.currentInteract = null;
		hand2.uiInteract.sprite = this.uiSpriteDict["hand-open"].sprite;
		if (hand2.interactState != ENT_Player.InteractType.item)
		{
			hand2.interactState = ENT_Player.InteractType.none;
		}
	}

	// Token: 0x060002A2 RID: 674 RVA: 0x0001BF2C File Offset: 0x0001A12C
	public void GrabPropUpdate(int hand)
	{
		string fireButton = this.hands[hand].fireButton;
		ENT_Player.Hand hand2 = this.hands[hand];
		if (hand2.grabTarget == null || !hand2.grabTarget.gameObject.activeInHierarchy)
		{
			this.DropGrabbedProp(hand);
			return;
		}
		if (hand2.interactState == ENT_Player.InteractType.grab)
		{
			Rigidbody rigidbody = hand2.grabTarget.GetRigidbody();
			hand2.grabTarget.gameObject.GetComponent<ObjectTagger>();
			Vector3 vector = Vector3.Lerp(hand2.grabTarget.transform.TransformPoint(hand2.grabTarget.anchorPoint), hand2.grabTarget.transform.position, 0.01f);
			Vector3 vector2 = this.camTransform.position + this.camTransform.forward * this.holdDistance - vector;
			Vector3 holdOffset = hand2.grabTarget.holdOffset;
			if (hand == 1)
			{
				holdOffset.x = -holdOffset.x;
			}
			vector2 += this.camTransform.TransformVector(holdOffset);
			Vector3 vector3 = 200f * Time.deltaTime * (this.camTransform.position + this.camTransform.forward * this.holdDistance - hand2.grabTarget.transform.TransformPoint(hand2.grabTarget.anchorPoint));
			bool flag = false;
			if (hand2.grabTarget.GetColliders().Contains(this.cConHit.collider) && base.transform.position.y - 0.25f > vector.y)
			{
				flag = true;
			}
			if (hand2.grabTarget.stuck)
			{
				hand2.grabTarget.AddBreakingVelocity(vector2 / rigidbody.mass);
				CL_CameraControl.Shake((vector2 / rigidbody.mass).magnitude * 0.0007f);
				hand2.gripStrength -= Time.deltaTime * 2f;
			}
			else if (!flag)
			{
				rigidbody.velocity = vector2 / rigidbody.mass * 8f + Vector3.down * rigidbody.mass;
				rigidbody.AddForceAtPosition(vector3, hand2.grabTarget.transform.TransformPoint(hand2.grabTarget.anchorPoint));
			}
			float num = 0f;
			if (hand2.grabTarget.stuck)
			{
				num = hand2.grabTarget.stuckStrength * 0.25f;
			}
			this.camSpeed = Mathf.Clamp(1f - rigidbody.mass * 0.25f - num, 0.1f, 1f);
			if (InputManager.GetButton("Rotate").Pressed)
			{
				rigidbody.AddTorque((InputManager.GetVector("Look").vector.y * base.transform.right + base.transform.up * -InputManager.GetVector("Look").vector.x) / (rigidbody.mass * 6f), ForceMode.VelocityChange);
				this.camLocked = true;
			}
			else
			{
				this.camLocked = false;
			}
			if (InputManager.GetButton(hand2.throwButton).Down)
			{
				rigidbody.AddForce(this.camTransform.forward * 150f);
				this.StopInteraction(hand, "", true);
				return;
			}
			if (InputManager.GetButton(fireButton).Up || base.transform.position.y - 1.8f > vector.y || Vector3.Distance(base.transform.position, hand2.grabTarget.transform.TransformPoint(hand2.grabTarget.anchorPoint)) > this.interactDistance * 1.5f)
			{
				this.DropGrabbedProp(hand);
				return;
			}
		}
	}

	// Token: 0x060002A3 RID: 675 RVA: 0x0001C314 File Offset: 0x0001A514
	private void DropGrabbedProp(int hand)
	{
		this.camSpeed = 1f;
		if (this.hands[hand].grabTarget != null)
		{
			this.hands[hand].grabTarget.GetRigidbody().velocity = Vector3.ClampMagnitude(this.hands[hand].grabTarget.GetRigidbody().velocity, Mathf.Clamp(10f - this.hands[hand].grabTarget.GetRigidbody().mass, 1f, 10f));
			this.hands[hand].grabTarget.Drop();
		}
		this.hands[hand].interactState = ENT_Player.InteractType.none;
		this.hands[hand].grabTarget = null;
		this.hands[hand].currentInteract = null;
		this.camLocked = false;
	}

	// Token: 0x060002A4 RID: 676 RVA: 0x0001C3E4 File Offset: 0x0001A5E4
	public override bool Damage(float amount, string type)
	{
		if (this.godmode)
		{
			return false;
		}
		type = type.ToLower();
		this.lastHitSource = type;
		CL_CameraControl.Shake(0.1f);
		this.timeSinceLastHit = 0f;
		this.gMan.uiMan.Hurt();
		amount *= 1f - Mathf.Clamp(this.curBuffs.GetBuff("damageResist"), 0f, 0.9f);
		amount *= 1f + this.curBuffs.GetBuff("damageMult");
		if (type == "teeth" && this.health - amount <= 0f)
		{
			return true;
		}
		if (type.ToLower() == "bloodbug" || type.ToLower() == "gasbag" || type.ToLower() == "turret" || type == "steam")
		{
			float buff = this.curBuffs.GetBuff("gripResist");
			if (global::UnityEngine.Random.value > buff)
			{
				this.DropHang(0.1f);
			}
		}
		if (type == "rebarexplosion")
		{
			CL_AchievementManager.SetAchievementValue("ACH_ROCKETJUMP", true);
		}
		if (type == "steam" || type == "heat")
		{
			this.clipHandler.GetGroup("player").GetSet("hurt-heat").Play(1f, this.audioChannels[1]);
			ENT_Player.Hand[] array = this.hands;
			for (int i = 0; i < array.Length; i++)
			{
				array[i].ShakeHand(0.05f);
			}
		}
		else if (type == "falling")
		{
			this.clipHandler.GetGroup("player").GetSet("hurt-falling").Play(1f, this.audioChannels[1]);
		}
		else if (!(type == "poison"))
		{
			if (SettingsManager.settings.voice == 1)
			{
				this.clipHandler.GetGroup("player").GetSet("hurt").Play(1f, this.audioChannels[1]);
			}
			else
			{
				this.clipHandler.GetGroup("player").GetSet("fem-hurt").Play(1f, this.audioChannels[1]);
			}
		}
		if (type.ToLower() == "nonlethal")
		{
			this.clipHandler.GetGroup("movement").GetSet("fem-land-heavy").Play(1f, this.audioChannels[1]);
			return false;
		}
		if (base.Damage(amount, type))
		{
			this.cCon.enabled = false;
			return true;
		}
		return false;
	}

	// Token: 0x060002A5 RID: 677 RVA: 0x0001C6A0 File Offset: 0x0001A8A0
	public void Revive()
	{
		this.dead = false;
		this.windAudio.Play();
		this.cCon.enabled = true;
		this.inventoryCam.gameObject.SetActive(true);
		this.vel = Vector3.zero;
		this.health = this.maxHealth;
		this.grappled = false;
		this.gravityMult = 1f;
		this.falling = false;
		this.DropHang(0.1f);
		this.UnLock();
	}

	// Token: 0x060002A6 RID: 678 RVA: 0x0001C720 File Offset: 0x0001A920
	public override void Kill(string type = "")
	{
		if (this.dead || this.gMan.IsReviving() || this.godmode)
		{
			return;
		}
		this.dead = true;
		type = type.ToLower();
		Debug.Log("The Player has died.");
		if (type == "deathfloor" && this.timeSinceLastHit < 3f)
		{
			Debug.Log(this.lastHitSource);
		}
		else
		{
			this.lastHitSource = type;
		}
		this.windAudio.Stop();
		this.cCon.enabled = false;
		this.inventoryCam.gameObject.SetActive(false);
		this.vel = Vector3.zero;
		this.KillAchievements(this.lastHitSource);
		base.Kill("");
		this.DropHang(0.1f);
		this.gMan.Die(type);
	}

	// Token: 0x060002A7 RID: 679 RVA: 0x0001C7F4 File Offset: 0x0001A9F4
	private void KillAchievements(string type)
	{
		uint num = <PrivateImplementationDetails>.ComputeStringHash(type);
		if (num <= 2225505087U)
		{
			if (num != 235577587U)
			{
				if (num != 915269214U)
				{
					if (num != 2225505087U)
					{
						return;
					}
					if (!(type == "barnacle"))
					{
						return;
					}
					CL_AchievementManager.SetAchievementValue("ACH_BARNACLED", true);
					return;
				}
				else
				{
					if (!(type == "ventthing"))
					{
						return;
					}
					CL_AchievementManager.SetAchievementValue("ACH_THINGKILL", true);
					return;
				}
			}
			else
			{
				if (!(type == "steam"))
				{
					return;
				}
				CL_AchievementManager.SetAchievementValue("ACH_STEAMDEATH", true);
				return;
			}
		}
		else if (num <= 2834823794U)
		{
			if (num != 2462491675U)
			{
				if (num != 2834823794U)
				{
					return;
				}
				if (!(type == "fan"))
				{
					return;
				}
				CL_AchievementManager.SetAchievementValue("ACH_FANKILL", true);
				return;
			}
			else
			{
				if (!(type == "bloodbug"))
				{
					return;
				}
				CL_AchievementManager.SetAchievementValue("ACH_BLOODBUGKILL", true);
				return;
			}
		}
		else if (num != 3376736801U)
		{
			if (num != 3700694853U)
			{
				return;
			}
			if (!(type == "crushed"))
			{
				return;
			}
			CL_AchievementManager.SetAchievementValue("ACH_CRUSHED", true);
			return;
		}
		else
		{
			if (!(type == "teeth"))
			{
				return;
			}
			CL_AchievementManager.SetAchievementValue("ACH_TEETHKILL", true);
			return;
		}
	}

	// Token: 0x060002A8 RID: 680 RVA: 0x0001C90C File Offset: 0x0001AB0C
	public void KillEffects(string type)
	{
		if (type == "falling")
		{
			this.clipHandler.GetGroup("player").GetSet("death-splatter").Play(1f, null);
		}
		this.clipHandler.GetGroup("player").GetSet("death").Play(1f, null);
		this.dead = true;
	}

	// Token: 0x060002A9 RID: 681 RVA: 0x0001C978 File Offset: 0x0001AB78
	public static ENT_Player GetPlayer()
	{
		if (ENT_Player.playerObject == null)
		{
			GameObject gameObject = GameObject.Find("ENT_Player");
			if (gameObject == null)
			{
				return null;
			}
			ENT_Player.playerObject = gameObject.GetComponent<ENT_Player>();
		}
		return ENT_Player.playerObject;
	}

	// Token: 0x060002AA RID: 682 RVA: 0x0001C9B8 File Offset: 0x0001ABB8
	public static Inventory GetInventory()
	{
		if (ENT_Player.playerObject == null)
		{
			ENT_Player.GetPlayer();
		}
		if (ENT_Player.playerObject.inventory == null)
		{
			ENT_Player.playerObject.inventory = ENT_Player.playerObject.gameObject.GetComponent<Inventory>();
			ENT_Player.playerObject.inventory.Initialize(ENT_Player.playerObject);
			Debug.Log("Initializing Inventory");
			return ENT_Player.playerObject.inventory;
		}
		return ENT_Player.playerObject.inventory;
	}

	// Token: 0x060002AB RID: 683 RVA: 0x0001CA36 File Offset: 0x0001AC36
	public Vector3 GetCameraTargetPosition()
	{
		return this.cameraPosition;
	}

	// Token: 0x060002AC RID: 684 RVA: 0x0001CA3E File Offset: 0x0001AC3E
	public Quaternion GetCameraTargetRotation()
	{
		return this.cameraRotation;
	}

	// Token: 0x060002AD RID: 685 RVA: 0x0001CA46 File Offset: 0x0001AC46
	public float GetCurrentFOV()
	{
		return this.curFOV;
	}

	// Token: 0x060002AE RID: 686 RVA: 0x0001CA50 File Offset: 0x0001AC50
	private bool CanUncrouch()
	{
		Vector3 vector = base.transform.position + this.cCon.center + Vector3.up * (this.crouchHeight / 2f);
		Vector3 vector2 = vector + Vector3.up * (this.standingHeight - this.crouchHeight);
		float num = this.cCon.radius * 0.9f;
		bool flag = Physics.CheckCapsule(vector, vector2, num, this.jumpMask);
		return !this.gMan.lockPlayer && !flag;
	}

	// Token: 0x060002AF RID: 687 RVA: 0x0001CAE9 File Offset: 0x0001ACE9
	private IEnumerator ChangeHeight(float targetHeight)
	{
		float elapsedTime = 0f;
		float currentHeight = this.cCon.height;
		while (elapsedTime < this.crouchTransitionDuration)
		{
			elapsedTime += Time.deltaTime;
			float num = elapsedTime / this.crouchTransitionDuration;
			float num2 = Mathf.Lerp(currentHeight, targetHeight, num);
			float height = this.cCon.height;
			this.cCon.height = num2;
			this.cCon.center = Vector3.up * (num2 - this.standingHeight) * 0.5f;
			float num3 = targetHeight / this.standingHeight;
			this.mainCamTarget.localPosition = Vector3.Lerp(this.mainCamTarget.localPosition, new Vector3(0f, 0.45f + (num2 - this.standingHeight), 0f), num);
			yield return null;
		}
		yield break;
	}

	// Token: 0x060002B0 RID: 688 RVA: 0x0001CAFF File Offset: 0x0001ACFF
	public void SetGravityMult(float g)
	{
		this.gravityMult = g;
	}

	// Token: 0x060002B1 RID: 689 RVA: 0x0001CB08 File Offset: 0x0001AD08
	public static bool IsNullOrDestroyed(object obj)
	{
		return obj == null || obj.Equals(null);
	}

	// Token: 0x060002B2 RID: 690 RVA: 0x0001CB16 File Offset: 0x0001AD16
	public bool IsLocked()
	{
		return this.camLocked || this.isFrozen();
	}

	// Token: 0x060002B3 RID: 691 RVA: 0x0001CB28 File Offset: 0x0001AD28
	public bool IsMoveLocked()
	{
		return this.isFrozen() || this.moveLocked || this.gMan.lockPlayer || this.gMan.IsReviving();
	}

	// Token: 0x060002B4 RID: 692 RVA: 0x0001CB54 File Offset: 0x0001AD54
	public bool IsInputLocked()
	{
		return this.isFrozen() || this.gMan.lockPlayerInput;
	}

	// Token: 0x060002B5 RID: 693 RVA: 0x0001CB6B File Offset: 0x0001AD6B
	public bool isFrozen()
	{
		return this.paused || this.gMan.freecam || this.dead || this.gMan.isPaused;
	}

	// Token: 0x060002B6 RID: 694 RVA: 0x0001CB97 File Offset: 0x0001AD97
	public bool IsInventoryLocked()
	{
		return this.invLocked;
	}

	// Token: 0x060002B7 RID: 695 RVA: 0x0001CB9F File Offset: 0x0001AD9F
	public void FOVShock(float s)
	{
		this.smoothedFOV += s;
	}

	// Token: 0x060002B8 RID: 696 RVA: 0x0001CBAF File Offset: 0x0001ADAF
	public void LockCamera()
	{
		this.camLocked = true;
	}

	// Token: 0x060002B9 RID: 697 RVA: 0x0001CBB8 File Offset: 0x0001ADB8
	public void UnlockCamera()
	{
		this.camLocked = false;
		this.mouseInteract = true;
		this.vel = Vector3.zero;
	}

	// Token: 0x060002BA RID: 698 RVA: 0x0001CBD3 File Offset: 0x0001ADD3
	public void Lock()
	{
		this.mouseInteract = false;
		this.camLocked = true;
		this.moveLocked = true;
		Cursor.lockState = CursorLockMode.Locked;
		this.invLocked = true;
		this.vel = Vector3.zero;
	}

	// Token: 0x060002BB RID: 699 RVA: 0x0001CC02 File Offset: 0x0001AE02
	public void UnLock()
	{
		this.mouseInteract = true;
		this.camLocked = false;
		this.moveLocked = false;
		this.invLocked = false;
		Cursor.lockState = CursorLockMode.Locked;
	}

	// Token: 0x060002BC RID: 700 RVA: 0x0001CC26 File Offset: 0x0001AE26
	public void LockMovementInput()
	{
		this.moveLocked = true;
		Cursor.lockState = CursorLockMode.Locked;
	}

	// Token: 0x060002BD RID: 701 RVA: 0x0001CC38 File Offset: 0x0001AE38
	internal override void ClampVelocityToAxis(Vector3 up)
	{
		this.vel = Vector3.ProjectOnPlane(this.vel, up);
		this.isGrounded = false;
		this.lastVel = this.vel;
		this.cCon.Move(-this.cCon.velocity * Time.fixedDeltaTime);
	}

	// Token: 0x060002BE RID: 702 RVA: 0x0001CC90 File Offset: 0x0001AE90
	public override void AddForce(Vector3 v)
	{
		v /= 10f;
		this.vel += v;
	}

	// Token: 0x060002BF RID: 703 RVA: 0x0001CCB4 File Offset: 0x0001AEB4
	public override void AddForce(Vector3 v, float maxVel)
	{
		v /= 10f;
		if ((this.vel + v).magnitude < maxVel || Vector3.Dot(this.vel.normalized, v.normalized) < 0f)
		{
			Vector3 vector = this.vel + v;
			vector = Vector3.ClampMagnitude(vector, maxVel);
			this.vel = vector;
			this.cCon.Move(this.vel * Time.deltaTime);
		}
	}

	// Token: 0x060002C0 RID: 704 RVA: 0x0001CD3B File Offset: 0x0001AF3B
	public override void AddForceAtPosition(Vector3 v, Vector3 p)
	{
		this.AddForce(v);
	}

	// Token: 0x060002C1 RID: 705 RVA: 0x0001CD44 File Offset: 0x0001AF44
	public void AddRotationVelocity(Vector3 v)
	{
		this.cameraRotationVelocity += v;
	}

	// Token: 0x060002C2 RID: 706 RVA: 0x0001CD58 File Offset: 0x0001AF58
	public override void TonguePull(Vector3 v)
	{
		base.TonguePull(v);
		this.grappled = true;
		Vector3 vector = new Vector3(InputManager.GetVector("Move").vector.x, InputManager.GetVector("Move").vector.y);
		v += base.transform.forward * vector.y * Time.deltaTime * 0.01f;
		v += base.transform.right * vector.x * Time.deltaTime * 0.01f;
		if (v.y > 0f && this.cCon.isGrounded)
		{
			this.vel += Vector3.up * 0.01f;
		}
		if (v.y > 0f)
		{
			v.y *= 2f;
		}
		this.vel += v;
		this.cCon.Move(v);
	}

	// Token: 0x060002C3 RID: 707 RVA: 0x0001CE7C File Offset: 0x0001B07C
	public override void Teleport(Vector3 pos)
	{
		this.ClearGrapple();
		base.Teleport(pos);
		if (this.softParent != null)
		{
			this.softParent.Unparent();
		}
	}

	// Token: 0x060002C4 RID: 708 RVA: 0x0001CEA0 File Offset: 0x0001B0A0
	public override void Teleport(Vector3 pos, Quaternion rot, bool keepVelocity = false)
	{
		this.ClearGrapple();
		if (this.cCon.enabled)
		{
			this.cCon.enabled = false;
			this.falling = false;
			base.Teleport(pos, rot, false);
			this.fallStartHeight = pos.y;
			this.cCon.enabled = true;
		}
		else
		{
			base.Teleport(pos, rot, false);
			this.fallStartHeight = pos.y;
			this.falling = false;
		}
		if (!keepVelocity)
		{
			this.vel = Vector3.zero;
		}
		if (this.softParent != null)
		{
			this.softParent.Unparent();
		}
	}

	// Token: 0x060002C5 RID: 709 RVA: 0x0001CF34 File Offset: 0x0001B134
	public bool IsHanging()
	{
		bool flag = false;
		ENT_Player.Hand[] array = this.hands;
		for (int i = 0; i < array.Length; i++)
		{
			if (array[i].interactState == ENT_Player.InteractType.hanging)
			{
				flag = true;
			}
		}
		return flag;
	}

	// Token: 0x060002C6 RID: 710 RVA: 0x0001CF68 File Offset: 0x0001B168
	public void DropHang(float grabCooldown = 0.1f)
	{
		foreach (ENT_Player.Hand hand in this.hands)
		{
			if (hand.interactState == ENT_Player.InteractType.hanging)
			{
				this.StopInteraction(hand.id, "jump", true);
				hand.gripStrength -= this.climbJumpDrain / (this.curBuffs.GetBuff("addGripStrength") + 1f);
			}
			if (hand.grabWait < grabCooldown)
			{
				hand.grabWait = grabCooldown;
			}
		}
	}

	// Token: 0x060002C7 RID: 711 RVA: 0x0001CFE3 File Offset: 0x0001B1E3
	public void SetInteractWhitelist(List<string> whitelistTags)
	{
		this.interactWhitelist = whitelistTags;
		this.useInteractWhitelist = true;
	}

	// Token: 0x060002C8 RID: 712 RVA: 0x0001CFF3 File Offset: 0x0001B1F3
	public void StopInteractWhitelist()
	{
		this.useInteractWhitelist = false;
	}

	// Token: 0x060002C9 RID: 713 RVA: 0x0001CFFC File Offset: 0x0001B1FC
	public void SetGrappled(bool b, AIGameEntity.Grappler grappler = null)
	{
		this.grappled = b;
		if (grappler != null)
		{
			if (b && !this.grapplers.Contains(grappler))
			{
				this.grapplers.Add(grappler);
				return;
			}
			if (!b && this.grapplers.Contains(grappler))
			{
				this.grapplers.Remove(grappler);
			}
		}
	}

	// Token: 0x060002CA RID: 714 RVA: 0x0001D050 File Offset: 0x0001B250
	public void ClearGrapple()
	{
		for (int i = this.grapplers.Count - 1; i >= 0; i--)
		{
			this.grapplers[i].ReleaseGrapple();
		}
	}

	// Token: 0x060002CB RID: 715 RVA: 0x0001D086 File Offset: 0x0001B286
	public void SetGravity(bool b)
	{
		this.hasGravity = b;
	}

	// Token: 0x060002CC RID: 716 RVA: 0x0001D08F File Offset: 0x0001B28F
	public void FreezeInputForTime(float f)
	{
		base.StartCoroutine(this.<FreezeInputForTime>g__FreezeCoroutine|209_0(f));
	}

	// Token: 0x060002CD RID: 717 RVA: 0x0001D09F File Offset: 0x0001B29F
	public void FreezeInputForFrame()
	{
		base.StartCoroutine(ENT_Player.<FreezeInputForFrame>g__FreezeCoroutine|210_0());
	}

	// Token: 0x060002CE RID: 718 RVA: 0x0001D0AD File Offset: 0x0001B2AD
	public float GetCurrentFallDistance()
	{
		return this.curFallDistance;
	}

	// Token: 0x060002CF RID: 719 RVA: 0x0001D0B8 File Offset: 0x0001B2B8
	private void OnControllerColliderHit(ControllerColliderHit hit)
	{
		this.hitNormal = hit.normal;
		this.hasHit = true;
		this.cConHit = hit;
		this.hitTime = 0.1f;
		float num = Vector3.Angle(Vector3.up, this.hitNormal);
		if (Vector3.Dot(this.vel.normalized, this.hitNormal) <= 0f && num > this.slopeLimit)
		{
			Vector3 vector = Vector3.ProjectOnPlane(this.vel, this.hitNormal);
			this.vel = vector;
			this.verticalSlide = true;
		}
	}

	// Token: 0x060002D0 RID: 720 RVA: 0x0001D144 File Offset: 0x0001B344
	public void AddGripStrength(float amount, bool onlyToGrabbingHands = false)
	{
		foreach (ENT_Player.Hand hand in this.hands)
		{
			if (!onlyToGrabbingHands || hand.interactState == ENT_Player.InteractType.hanging)
			{
				hand.AddGripStrength(amount);
			}
		}
	}

	// Token: 0x060002D1 RID: 721 RVA: 0x0001D180 File Offset: 0x0001B380
	public float GetLowestGripStrength()
	{
		float num = this.gripStrengthTimer;
		foreach (ENT_Player.Hand hand in this.hands)
		{
			num = Mathf.Min(num, hand.gripStrength);
		}
		return num;
	}

	// Token: 0x060002D2 RID: 722 RVA: 0x0001D1BB File Offset: 0x0001B3BB
	public float GetCurrentGripStrengthTimer()
	{
		return this.gripStrengthTimer * (1f + this.curBuffs.GetBuff("addStamina")) - this.curBuffs.GetBuff("limitGripStrength") * this.gripStrengthTimer;
	}

	// Token: 0x060002D3 RID: 723 RVA: 0x0001D1F2 File Offset: 0x0001B3F2
	public void Buff(BuffContainer buff)
	{
		this.curBuffs.AddBuff(buff);
	}

	// Token: 0x060002D4 RID: 724 RVA: 0x0001D200 File Offset: 0x0001B400
	private void ApplyBuffEffects()
	{
		this.gMan.uiMan.SetVignette("roids", this.curBuffs.GetBuff("roided"));
		this.gMan.uiMan.SetVignette("goop", this.curBuffs.GetBuff("gooped"));
		this.gMan.uiMan.SetVignette("pills", this.curBuffs.GetBuff("pilled"));
		this.gMan.uiMan.SetVignette("freezing", this.curBuffs.GetBuff("freezing"));
		this.gMan.uiMan.SetVignette("warming", this.curBuffs.GetBuff("warming"));
	}

	// Token: 0x060002D5 RID: 725 RVA: 0x0001D2C8 File Offset: 0x0001B4C8
	public void AddPerk(Perk perk, int stackAmount = 1)
	{
		foreach (Perk perk2 in this.perks)
		{
			if (perk.id == perk2.id)
			{
				perk2.AddStack(1);
				return;
			}
		}
		Perk perk3 = Object.Instantiate<Perk>(perk);
		this.perks.Add(perk3);
		perk3.Initialize(this);
		perk3.stackAmount = stackAmount;
		Object.Instantiate<UI_Perk_Icon>(this.perkIconAsset, this.perkIconHolder).SetPerk(perk3);
	}

	// Token: 0x060002D6 RID: 726 RVA: 0x0001D368 File Offset: 0x0001B568
	public void RemoveAllPerks()
	{
		if (this.perks == null || this.perks.Count == 0)
		{
			return;
		}
		for (int i = this.perks.Count - 1; i >= 0; i--)
		{
			this.perks[i].RemoveFromPlayer();
		}
	}

	// Token: 0x060002D7 RID: 727 RVA: 0x0001D3B4 File Offset: 0x0001B5B4
	public void RemovePerk(Perk perk, bool removeAll = true)
	{
		if (this.perks.Contains(perk) && !removeAll && perk.stackAmount > 1)
		{
			perk.RemoveStack(1);
			return;
		}
		perk.ClearPerkEffects();
		this.perks.Remove(perk);
	}

	// Token: 0x060002D8 RID: 728 RVA: 0x0001D3EC File Offset: 0x0001B5EC
	public bool HasPerk(string perkID)
	{
		using (List<Perk>.Enumerator enumerator = this.perks.GetEnumerator())
		{
			while (enumerator.MoveNext())
			{
				if (enumerator.Current.id == perkID)
				{
					return true;
				}
			}
		}
		return false;
	}

	// Token: 0x060002D9 RID: 729 RVA: 0x0001D44C File Offset: 0x0001B64C
	public Perk GetPerk(string perkID)
	{
		foreach (Perk perk in this.perks)
		{
			if (perk.id == perkID)
			{
				return perk;
			}
		}
		return null;
	}

	// Token: 0x060002DA RID: 730 RVA: 0x0001D4B0 File Offset: 0x0001B6B0
	public string GetLastHitSource()
	{
		return this.lastHitSource;
	}

	// Token: 0x060002DB RID: 731 RVA: 0x0001D4B8 File Offset: 0x0001B6B8
	public static Vector3 MostYFacingPerpendicular(Vector3 inputVector)
	{
		inputVector.Normalize();
		return Vector3.ProjectOnPlane(Vector3.up, inputVector).normalized;
	}

	// Token: 0x060002DC RID: 732 RVA: 0x0001D4E0 File Offset: 0x0001B6E0
	private void FootstepAudio(Vector3 moveAxis, float curSpeed)
	{
		if (moveAxis.magnitude > 0.5f && this.timeSinceGrounded < 0.2f)
		{
			this.footstepTime -= Mathf.Max(curSpeed, 1.2f) * Time.deltaTime;
			if (this.footstepTime <= 0f)
			{
				int num;
				for (num = global::UnityEngine.Random.Range(0, this.curFootstep.audio.Count); num == this.lastFootstep; num = global::UnityEngine.Random.Range(0, this.curFootstep.audio.Count))
				{
				}
				this.lastFootstep = num;
				AudioManager.PlaySound(this.curFootstep.audio[num], base.transform.position, this.curFootstep.volume, global::UnityEngine.Random.Range(this.curFootstep.minPitch, this.curFootstep.maxPitch), 0f, false, 1f, null);
				if (this.crouching)
				{
					this.footstepTime = this.footstepCrouchSpeed;
					return;
				}
				if (this.sprinting)
				{
					this.footstepTime = this.footstepSprintSpeed;
					return;
				}
				this.footstepTime = this.footstepSpeed;
			}
		}
	}

	// Token: 0x060002DD RID: 733 RVA: 0x0001D608 File Offset: 0x0001B808
	private void PoisonCheck()
	{
		float buff = this.curBuffs.GetBuff("poisoned");
		if (buff > 0f)
		{
			float num = 5f + this.curBuffs.GetBuff("poisonTimer");
			float buff2 = this.curBuffs.GetBuff("poisonDamage");
			float buff3 = this.curBuffs.GetBuff("poisonStaminaDamage");
			bool flag = this.curBuffs.GetBuff("poisonDropHands") > global::UnityEngine.Random.value;
			this.poisonedHurtTime -= Time.deltaTime;
			if (this.poisonedHurtTime <= 0f)
			{
				this.poisonedHurtTime = num;
				if (buff2 > 0f)
				{
					this.Damage(buff, "poison");
				}
				if (buff3 > 0f)
				{
					this.AddGripStrength(-buff3, false);
				}
				if (flag)
				{
					if (this.IsHanging())
					{
						this.camRoot.ShakeCamera(0.08f);
					}
					this.DropHang(0.1f);
				}
				this.camRoot.ShakeCamera(0.05f);
				if (SettingsManager.settings.voice == 1)
				{
					this.clipHandler.GetGroup("player").GetSet("cough").Play(1f, this.audioChannels[1]);
					return;
				}
				this.clipHandler.GetGroup("player").GetSet("fem-cough").Play(1f, this.audioChannels[1]);
			}
		}
	}

	// Token: 0x060002DE RID: 734 RVA: 0x0001D778 File Offset: 0x0001B978
	private ENT_Player.Footsteps GetFootstepMaterial()
	{
		ENT_Player.Footsteps footsteps = this.footsteps[0];
		RaycastHit raycastHit;
		if (Physics.SphereCast(base.transform.position, this.cCon.radius * 0.8f, -base.transform.up, out raycastHit, this.cCon.height + 0.5f, this.stepMask))
		{
			MeshCollider meshCollider = raycastHit.collider as MeshCollider;
			this.curFootstepRaycast = raycastHit;
			if (meshCollider == null)
			{
				return footsteps;
			}
			ObjectMaterial component = meshCollider.GetComponent<ObjectMaterial>();
			if (component == null)
			{
				return this.footsteps[0];
			}
			for (int i = 0; i < this.footsteps.Count; i++)
			{
				if (this.footsteps[i].material == component.GetMaterialType())
				{
					return this.footsteps[i];
				}
			}
		}
		return footsteps;
	}

	// Token: 0x060002DF RID: 735 RVA: 0x0001D864 File Offset: 0x0001BA64
	private void CreateCommands()
	{
		CommandConsole.AddCommand("noclip", new Action<string[]>(this.Noclip), true);
		CommandConsole.AddCommand("kill", new Action<string[]>(this.KillCommand), false);
		CommandConsole.AddCommand("addforcetoplayer", new Action<string[]>(this.AddForceCommand), true);
		CommandConsole.AddCommand("infiniteStamina", new Action<string[]>(this.InfiniteStaminaCommand), true);
		CommandConsole.AddCommand("addperk", new Action<string[]>(this.AddPerk), true);
		CommandConsole.AddCommand("listperks", new Action<string[]>(this.ListPerks), true);
		CommandConsole.AddCommand("godmode", new Action<string[]>(this.GodMode), true);
		CommandConsole.AddCommand("hands-mobile", new Action<string[]>(this.SetHandsToMobilePosition), false);
	}

	// Token: 0x060002E0 RID: 736 RVA: 0x0001D92C File Offset: 0x0001BB2C
	public void ListPerks(string[] args)
	{
		foreach (Perk perk in CL_AssetManager.GetFullCombinedAssetDatabase().perkAssets)
		{
			CommandConsole.Log("Perk: " + perk.id.ToLower(), false);
		}
	}

	// Token: 0x060002E1 RID: 737 RVA: 0x0001D998 File Offset: 0x0001BB98
	public void AddPerk(string[] args)
	{
		this.AddPerk(CL_AssetManager.GetPerkAsset(args[0], ""), 1);
	}

	// Token: 0x060002E2 RID: 738 RVA: 0x0001D9AE File Offset: 0x0001BBAE
	public void KillCommand(string[] args)
	{
		this.Kill("");
	}

	// Token: 0x060002E3 RID: 739 RVA: 0x0001D9BB File Offset: 0x0001BBBB
	public void AddForceCommand(string[] args)
	{
		this.AddForce(new Vector3(float.Parse(args[0]), float.Parse(args[1]), float.Parse(args[2])));
	}

	// Token: 0x060002E4 RID: 740 RVA: 0x0001D9E0 File Offset: 0x0001BBE0
	public void InfiniteStaminaCommand(string[] args)
	{
		if (args.Length == 0)
		{
			this.infiniteStamina = !this.infiniteStamina;
		}
		else
		{
			bool flag;
			if (!bool.TryParse(args[0], out flag))
			{
				CommandConsole.Log("Unable to parse " + args[0] + " arg needs to be a boolean (true/false/0/1).", false);
				return;
			}
			this.infiniteStamina = Convert.ToBoolean(args[0]);
		}
		CommandConsole.Log("Infinite Stamina set to " + this.infiniteStamina.ToString(), false);
	}

	// Token: 0x060002E5 RID: 741 RVA: 0x0001DA54 File Offset: 0x0001BC54
	public void Noclip(string[] args)
	{
		if (args.Length == 0)
		{
			this.noclip = !this.noclip;
		}
		else
		{
			bool flag;
			if (!bool.TryParse(args[0], out flag))
			{
				CommandConsole.Log("Unable to parse " + args[0] + " arg needs to be a boolean (true/false/0/1).", false);
				return;
			}
			this.noclip = Convert.ToBoolean(args[0]);
		}
		CommandConsole.Log("Noclip set to " + this.noclip.ToString(), false);
	}

	// Token: 0x060002E6 RID: 742 RVA: 0x0001DAC6 File Offset: 0x0001BCC6
	public void SetGodMode(bool b)
	{
		this.godmode = b;
	}

	// Token: 0x060002E7 RID: 743 RVA: 0x0001DAD0 File Offset: 0x0001BCD0
	public void GodMode(string[] args)
	{
		if (args.Length == 0)
		{
			this.godmode = !this.godmode;
			CommandConsole.Log("Godmode set to " + this.godmode.ToString(), false);
			return;
		}
		this.godmode = Convert.ToBoolean(args[0]);
		CommandConsole.Log("Godmode set to " + this.godmode.ToString(), false);
	}

	// Token: 0x060002E8 RID: 744 RVA: 0x0001DB38 File Offset: 0x0001BD38
	internal void SetHandsToMobilePosition(string[] args)
	{
		float num = 0.12f;
		this.hands[0].SetHandOffset(Vector3.right * num);
		this.hands[1].SetHandOffset(Vector3.right * -num);
		this.inventory.itemHands[0].handInventoryRoot.transform.localPosition += Vector3.right * num;
		this.inventory.itemHands[1].handInventoryRoot.transform.localPosition += Vector3.right * -num;
	}

	// Token: 0x060002E9 RID: 745 RVA: 0x0001DBE1 File Offset: 0x0001BDE1
	public SoftParent GetSoftParent()
	{
		return this.softParent;
	}

	// Token: 0x060002EA RID: 746 RVA: 0x0001DBEC File Offset: 0x0001BDEC
	private void AchievementCheck()
	{
		if (CL_GameManager.gamemode.allowHeightAchievements)
		{
			if (CL_GameManager.gMan.GetPlayerCorrectedHeight() > 250f)
			{
				CL_AchievementManager.SetAchievementValue("ACH_250_METERS", true);
			}
			if (CL_GameManager.gMan.GetPlayerCorrectedHeight() > 500f)
			{
				CL_AchievementManager.SetAchievementValue("ACH_500_METERS", true);
			}
			if (CL_GameManager.gMan.GetPlayerCorrectedHeight() > 1000f)
			{
				CL_AchievementManager.SetAchievementValue("ACH_1000_METERS", true);
			}
			if (CL_GameManager.gMan.GetPlayerCorrectedHeight() > 2000f)
			{
				CL_AchievementManager.SetAchievementValue("ACH_2000_METERS", true);
			}
			if (CL_GameManager.gMan.GetPlayerCorrectedHeight() > 5000f)
			{
				CL_AchievementManager.SetAchievementValue("ACH_5000_METERS", true);
			}
			if (CL_GameManager.gMan.GetPlayerCorrectedHeight() > 10000f)
			{
				CL_AchievementManager.SetAchievementValue("ACH_10000_METERS", true);
			}
		}
	}

	// Token: 0x060002EB RID: 747 RVA: 0x0001DCB0 File Offset: 0x0001BEB0
	internal void DropEverything()
	{
		this.DropHang(5f);
		this.hands[0].DestroyItem();
		this.hands[1].DestroyItem();
		this.hands[0].uiInteract.gameObject.SetActive(false);
		this.hands[1].uiInteract.gameObject.SetActive(false);
	}

	// Token: 0x060002EC RID: 748 RVA: 0x0001DD12 File Offset: 0x0001BF12
	internal void LetGo()
	{
		this.DropHang(0.5f);
		this.hands[0].uiInteract.gameObject.SetActive(false);
		this.hands[1].uiInteract.gameObject.SetActive(false);
	}

	// Token: 0x060002ED RID: 749 RVA: 0x0001DD4F File Offset: 0x0001BF4F
	internal void StashItemsInHand()
	{
		this.inventory.DropItemFromHand(this.inventory.inventoryCenterLocator.position, 0, true);
		this.inventory.DropItemFromHand(this.inventory.inventoryCenterLocator.position, 1, true);
	}

	// Token: 0x060002EE RID: 750 RVA: 0x0001DD8C File Offset: 0x0001BF8C
	private bool IsSprintHeld()
	{
		return ((InputManager.GetButton("Sprint").Pressed && !SettingsManager.settings.alwaysSprint) || (SettingsManager.settings.alwaysSprint && !InputManager.GetButton("Sprint").Pressed)) && this.inputAxis.magnitude > 0.8f && !this.crouching;
	}

	// Token: 0x060002EF RID: 751 RVA: 0x0001DDF0 File Offset: 0x0001BFF0
	internal void SetCrouched(bool v)
	{
		this.crouching = true;
		this.cCon.height = this.crouchHeight;
	}

	// Token: 0x060002F0 RID: 752 RVA: 0x0001DE0C File Offset: 0x0001C00C
	public override void OffsetEntity(float amount)
	{
		base.OffsetEntity(amount);
		ENT_Player.Hand[] array = this.hands;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].OffsetLastHoldPosition(Vector3.up * amount);
		}
	}

	// Token: 0x060002F2 RID: 754 RVA: 0x0001E09A File Offset: 0x0001C29A
	[CompilerGenerated]
	private IEnumerator <SoftParentWait>g__WaitFunc|155_0()
	{
		if (this.softParent.IsParented())
		{
			Vector3 vector = base.transform.position - this.lastPlayerPosition;
			Action<string, string> updateDebugText = DebugMenu.UpdateDebugText;
			string text = "trueChangeVector";
			string text2 = "True Change Vector : ";
			Vector3 vector2 = vector;
			updateDebugText(text, text2 + vector2.ToString());
			base.transform.position += this.softParent.GetPositionDeltaWorldDifference();
			this.lastFixedPlayerPosition += this.softParent.GetPositionDeltaWorldDifference();
			this.currentFixedCameraPosition += this.softParent.GetPositionDeltaWorldDifference();
			this.previousFixedCameraPosition += this.softParent.GetPositionDeltaWorldDifference();
			this.softParent.UpdatePositionInfo();
			this.softParent.UpdateRotationInfo();
		}
		this.lastPlayerPosition = base.transform.position;
		yield return new WaitForEndOfFrame();
		yield break;
	}

	// Token: 0x060002F3 RID: 755 RVA: 0x0001E0AC File Offset: 0x0001C2AC
	[CompilerGenerated]
	private void <Movement>g__Unparent|156_0()
	{
		if (!this.softParent.IsParented())
		{
			return;
		}
		Vector3 deltaWorldVelocity = this.softParent.GetDeltaWorldVelocity();
		this.softParent.Unparent();
		float num = 1f;
		if (this.IsHanging())
		{
			num *= 0.05f;
		}
		this.vel += deltaWorldVelocity * 0.005f * num;
	}

	// Token: 0x060002F4 RID: 756 RVA: 0x0001E116 File Offset: 0x0001C316
	[CompilerGenerated]
	private IEnumerator <FreezeInputForTime>g__FreezeCoroutine|209_0(float f)
	{
		this.LockMovementInput();
		yield return new WaitForSeconds(f);
		this.UnLock();
		yield break;
	}

	// Token: 0x060002F5 RID: 757 RVA: 0x0001E12C File Offset: 0x0001C32C
	[CompilerGenerated]
	internal static IEnumerator <FreezeInputForFrame>g__FreezeCoroutine|210_0()
	{
		InputManager.DeactivateMap(0);
		yield return null;
		InputManager.ActivateMap(0);
		yield break;
	}

	// Token: 0x04000391 RID: 913
	private CL_GameManager gMan;

	// Token: 0x04000392 RID: 914
	public CharacterController cCon;

	// Token: 0x04000393 RID: 915
	public Transform effectsRoot;

	// Token: 0x04000394 RID: 916
	private Dictionary<string, ParticleSystem> playerEffects = new Dictionary<string, ParticleSystem>();

	// Token: 0x04000395 RID: 917
	public static ENT_Player playerObject;

	// Token: 0x04000396 RID: 918
	private Inventory inventory;

	// Token: 0x04000397 RID: 919
	private float tick;

	// Token: 0x04000398 RID: 920
	[Header("Player stats")]
	public float speed = 1.5f;

	// Token: 0x04000399 RID: 921
	public float sprintSpeed = 2.5f;

	// Token: 0x0400039A RID: 922
	public float crouchSpeed = 0.75f;

	// Token: 0x0400039B RID: 923
	public float jumpHeight = 0.5f;

	// Token: 0x0400039C RID: 924
	public int extraJumps;

	// Token: 0x0400039D RID: 925
	private int extraJumpsRemaining;

	// Token: 0x0400039E RID: 926
	private float timeSinceJumpBoost;

	// Token: 0x0400039F RID: 927
	private bool sprinting;

	// Token: 0x040003A0 RID: 928
	private bool crouching;

	// Token: 0x040003A1 RID: 929
	private Vector3 vel;

	// Token: 0x040003A2 RID: 930
	private Vector3 curVel;

	// Token: 0x040003A3 RID: 931
	private Vector3 wishVel;

	// Token: 0x040003A4 RID: 932
	[Space(10f)]
	public float standingHeight = 1.5f;

	// Token: 0x040003A5 RID: 933
	public float crouchHeight = 0.75f;

	// Token: 0x040003A6 RID: 934
	public float crouchTransitionDuration = 0.25f;

	// Token: 0x040003A7 RID: 935
	private bool crouchPressed;

	// Token: 0x040003A8 RID: 936
	private ENT_Player.PlayerState playerState = ENT_Player.PlayerState.airborne;

	// Token: 0x040003A9 RID: 937
	private Vector2 inputAxis;

	// Token: 0x040003AA RID: 938
	public float gripStrengthTimer = 10f;

	// Token: 0x040003AB RID: 939
	private float gripStrengthLossRate = 1f;

	// Token: 0x040003AC RID: 940
	public float climbJumpDrain = 0.5f;

	// Token: 0x040003AD RID: 941
	[Header("Camera")]
	public Camera cam;

	// Token: 0x040003AE RID: 942
	public Camera inventoryCam;

	// Token: 0x040003AF RID: 943
	public Transform camTransform;

	// Token: 0x040003B0 RID: 944
	public Transform invRoot;

	// Token: 0x040003B1 RID: 945
	public Transform mainCamTarget;

	// Token: 0x040003B2 RID: 946
	public CL_CameraControl camRoot;

	// Token: 0x040003B3 RID: 947
	public float camMaxY = 88f;

	// Token: 0x040003B4 RID: 948
	public float camMinY = -80f;

	// Token: 0x040003B5 RID: 949
	private float curFOV = 90f;

	// Token: 0x040003B6 RID: 950
	private float sprintFOV = 100f;

	// Token: 0x040003B7 RID: 951
	private float camY;

	// Token: 0x040003B8 RID: 952
	private float camSpeed = 1f;

	// Token: 0x040003B9 RID: 953
	private float smoothedFOV = 90f;

	// Token: 0x040003BA RID: 954
	private float camSway;

	// Token: 0x040003BB RID: 955
	private Vector3 currentFixedCameraPosition;

	// Token: 0x040003BC RID: 956
	private Vector3 previousFixedCameraPosition;

	// Token: 0x040003BD RID: 957
	private Vector3 softParentPreviousOffset;

	// Token: 0x040003BE RID: 958
	private Vector3 lastFixedPlayerPosition;

	// Token: 0x040003BF RID: 959
	private Vector3 lastFixedPlayerParentedPosition;

	// Token: 0x040003C0 RID: 960
	private Vector3 lastPlayerPosition;

	// Token: 0x040003C1 RID: 961
	private Vector3 cameraRotationVelocity;

	// Token: 0x040003C2 RID: 962
	public float headbob = 0.5f;

	// Token: 0x040003C3 RID: 963
	public float headbobFrequency = 5f;

	// Token: 0x040003C4 RID: 964
	private float bobTime;

	// Token: 0x040003C5 RID: 965
	public float dragCoefficient = 0.1f;

	// Token: 0x040003C6 RID: 966
	public float frictionMultiplier = 50f;

	// Token: 0x040003C7 RID: 967
	public float wallfrictionMultiplier = 10f;

	// Token: 0x040003C8 RID: 968
	public float gravity = -9.81f;

	// Token: 0x040003C9 RID: 969
	private float gravityMult = 1f;

	// Token: 0x040003CA RID: 970
	public float wallPushForce = 0.0001f;

	// Token: 0x040003CB RID: 971
	public float coyoteTime = 0.12f;

	// Token: 0x040003CC RID: 972
	private bool wasGrounded = true;

	// Token: 0x040003CD RID: 973
	private bool isGrounded;

	// Token: 0x040003CE RID: 974
	private bool isSliding;

	// Token: 0x040003CF RID: 975
	private float timeSinceGrounded;

	// Token: 0x040003D0 RID: 976
	private Vector3 climbDir = Vector3.up;

	// Token: 0x040003D1 RID: 977
	private Vector3 jumpDir = Vector3.up;

	// Token: 0x040003D2 RID: 978
	private float hitTime;

	// Token: 0x040003D3 RID: 979
	private bool grappled;

	// Token: 0x040003D4 RID: 980
	private List<AIGameEntity.Grappler> grapplers = new List<AIGameEntity.Grappler>();

	// Token: 0x040003D5 RID: 981
	private bool hasGravity = true;

	// Token: 0x040003D6 RID: 982
	private Vector3 moveAxis = Vector3.zero;

	// Token: 0x040003D7 RID: 983
	private Vector3 hitNormal;

	// Token: 0x040003D8 RID: 984
	private bool hasHit;

	// Token: 0x040003D9 RID: 985
	private ControllerColliderHit cConHit;

	// Token: 0x040003DA RID: 986
	private bool verticalSlide;

	// Token: 0x040003DB RID: 987
	[Space(10f)]
	public float slopeLimit = 45f;

	// Token: 0x040003DC RID: 988
	public float upSlopeLimit = 91f;

	// Token: 0x040003DD RID: 989
	public float slideFriction = 0.3f;

	// Token: 0x040003DE RID: 990
	public float slideSpeed = 0.3f;

	// Token: 0x040003DF RID: 991
	private Vector3 lastVel;

	// Token: 0x040003E0 RID: 992
	private SoftParent softParent;

	// Token: 0x040003E1 RID: 993
	private bool softParentHasUpdatedPosition;

	// Token: 0x040003E2 RID: 994
	public LayerMask interactMask;

	// Token: 0x040003E3 RID: 995
	public LayerMask inventoryItemMask;

	// Token: 0x040003E4 RID: 996
	public LayerMask jumpMask;

	// Token: 0x040003E5 RID: 997
	public LayerMask stepMask;

	// Token: 0x040003E6 RID: 998
	private bool interactRay;

	// Token: 0x040003E7 RID: 999
	private bool mouseInteract;

	// Token: 0x040003E8 RID: 1000
	private bool paused;

	// Token: 0x040003E9 RID: 1001
	private bool moveLocked;

	// Token: 0x040003EA RID: 1002
	private bool camLocked;

	// Token: 0x040003EB RID: 1003
	private bool invLocked;

	// Token: 0x040003EC RID: 1004
	private float holdDistance = 1.8f;

	// Token: 0x040003ED RID: 1005
	[Space(10f)]
	public float interactDistance = 2f;

	// Token: 0x040003EE RID: 1006
	public float grabForgive = 0.3f;

	// Token: 0x040003EF RID: 1007
	private bool hasJumped;

	// Token: 0x040003F0 RID: 1008
	[Space(10f)]
	public ENT_Player.Hand[] hands = new ENT_Player.Hand[]
	{
		new ENT_Player.Hand(),
		new ENT_Player.Hand()
	};

	// Token: 0x040003F1 RID: 1009
	[Space(10f)]
	private bool grabAnywhere;

	// Token: 0x040003F2 RID: 1010
	public CL_Handhold[] grabAnywhereFakeHandholds;

	// Token: 0x040003F3 RID: 1011
	private Vector3[] grabAnywherePosition = new Vector3[2];

	// Token: 0x040003F4 RID: 1012
	private Transform[] grabAnywhereHit = new Transform[2];

	// Token: 0x040003F5 RID: 1013
	[Header("UI")]
	public List<ENT_Player.InteractionSprite> interactionSprites;

	// Token: 0x040003F6 RID: 1014
	public Dictionary<string, ENT_Player.InteractionSprite> uiSpriteDict;

	// Token: 0x040003F7 RID: 1015
	private bool useInteractWhitelist;

	// Token: 0x040003F8 RID: 1016
	private List<string> interactWhitelist = new List<string>();

	// Token: 0x040003F9 RID: 1017
	public SpriteRenderer aimCircle;

	// Token: 0x040003FA RID: 1018
	private float transitionTimer;

	// Token: 0x040003FB RID: 1019
	private bool transitioning;

	// Token: 0x040003FC RID: 1020
	public UT_AudioClipHandler clipHandler;

	// Token: 0x040003FD RID: 1021
	public float softLandCutoff = 1.3f;

	// Token: 0x040003FE RID: 1022
	public float mediumLandCutoff = 5f;

	// Token: 0x040003FF RID: 1023
	public float heavyLandCutoff = 20f;

	// Token: 0x04000400 RID: 1024
	[Space(10f)]
	public AudioSource windAudio;

	// Token: 0x04000401 RID: 1025
	public float windAudioVelocityMultiplier;

	// Token: 0x04000402 RID: 1026
	private bool fallingAudio;

	// Token: 0x04000403 RID: 1027
	private List<AudioSource> audioChannels;

	// Token: 0x04000404 RID: 1028
	[Space(10f)]
	public float footstepSpeed = 1f;

	// Token: 0x04000405 RID: 1029
	public float footstepCrouchSpeed = 1f;

	// Token: 0x04000406 RID: 1030
	public float footstepSprintSpeed = 1f;

	// Token: 0x04000407 RID: 1031
	public List<ENT_Player.Footsteps> footsteps;

	// Token: 0x04000408 RID: 1032
	private ENT_Player.Footsteps curFootstep;

	// Token: 0x04000409 RID: 1033
	private RaycastHit curFootstepRaycast;

	// Token: 0x0400040A RID: 1034
	private float footstepTime;

	// Token: 0x0400040B RID: 1035
	private int lastFootstep = -1;

	// Token: 0x0400040C RID: 1036
	[Header("Debug")]
	public bool noclip;

	// Token: 0x0400040D RID: 1037
	private bool hideInv;

	// Token: 0x0400040E RID: 1038
	private bool hideUI;

	// Token: 0x0400040F RID: 1039
	private bool infiniteStamina;

	// Token: 0x04000410 RID: 1040
	private bool godmode;

	// Token: 0x04000411 RID: 1041
	private float curFallDistance;

	// Token: 0x04000412 RID: 1042
	private bool falling;

	// Token: 0x04000413 RID: 1043
	private float fallStartHeight;

	// Token: 0x04000414 RID: 1044
	private float fallHurtTime;

	// Token: 0x04000415 RID: 1045
	private float timeSinceLastHit;

	// Token: 0x04000416 RID: 1046
	private string lastHitSource = "";

	// Token: 0x04000417 RID: 1047
	private Vector3 cameraPosition;

	// Token: 0x04000418 RID: 1048
	private Quaternion cameraRotation;

	// Token: 0x04000419 RID: 1049
	private float poisonedHurtTime;

	// Token: 0x0400041A RID: 1050
	[Header("Buffs")]
	public ENT_Player.PlayerBuff curBuffs;

	// Token: 0x0400041B RID: 1051
	public List<Perk> perks;

	// Token: 0x0400041C RID: 1052
	public UI_Perk_Icon perkIconAsset;

	// Token: 0x0400041D RID: 1053
	public Transform perkIconHolder;

	// Token: 0x0200022B RID: 555
	[Serializable]
	public class SaveData
	{
		// Token: 0x06000D2C RID: 3372 RVA: 0x00051A4C File Offset: 0x0004FC4C
		public void SaveDataIntoClass(ENT_Player player)
		{
			this.perks = new List<Perk.PerkSaveData>();
			foreach (Perk perk in player.perks)
			{
				this.perks.Add(Perk.PerkSaveData.GetPerkSaveData(perk));
			}
			this.buffs = new List<BuffContainer>();
			foreach (BuffContainer buffContainer in player.curBuffs.currentBuffs)
			{
				this.buffs.Add(buffContainer);
			}
			this.inventory = new Item[player.inventory.bagItems.Count];
			for (int i = 0; i < player.inventory.bagItems.Count; i++)
			{
				player.inventory.bagItems[i].Save();
				this.inventory[i] = player.inventory.bagItems[i].GetClone();
			}
			this.handInventory = new Item[2];
			if (player.inventory.itemHands[0].currentItem != null)
			{
				player.inventory.itemHands[0].currentItem.Save();
				this.handInventory[0] = player.inventory.itemHands[0].currentItem.GetClone();
			}
			if (player.inventory.itemHands[1].currentItem != null)
			{
				player.inventory.itemHands[1].currentItem.Save();
				this.handInventory[1] = player.inventory.itemHands[1].currentItem.GetClone();
			}
			if (WorldLoader.initialized)
			{
				Transform transform = WorldLoader.GetCurrentLevelFromBounds().level.transform;
				this.positionRelativeToLevel = transform.InverseTransformPoint(player.transform.position);
				this.playerRotation = Quaternion.Inverse(transform.rotation) * player.transform.rotation;
				this.playerCameraRotation = player.camTransform.localRotation;
				return;
			}
			this.playerRotation = player.transform.rotation;
			this.playerCameraRotation = player.camTransform.localRotation;
			this.positionRelativeToLevel = player.transform.position;
		}

		// Token: 0x06000D2D RID: 3373 RVA: 0x00051CB4 File Offset: 0x0004FEB4
		public void LoadDataIntoPlayer(ENT_Player player, bool loadInventory = true, bool loadPerks = false, bool loadBuffs = false, string type = "disk")
		{
			if (loadBuffs)
			{
				player.curBuffs.currentBuffs = this.buffs;
			}
			if (loadInventory)
			{
				if (!player.HasPerk("Perk_AnomalousBonds"))
				{
					player.inventory.ClearInventory();
					List<Item> list = new List<Item>();
					for (int i = 0; i < this.inventory.Length; i++)
					{
						this.inventory[i].Load();
						this.inventory[i].CopyNonDataFromItemAsset();
						list.Add(this.inventory[i].GetClone());
					}
					player.inventory.LoadItemsIntoBag(list);
					for (int j = 0; j < 2; j++)
					{
						if (this.handInventory[j] != null && this.handInventory[j].prefabName != "")
						{
							this.handInventory[j].Load();
							this.handInventory[j].CopyNonDataFromItemAsset();
							player.inventory.AddItemToHand(this.handInventory[j].GetClone(), player.hands[j]);
						}
					}
				}
				else if (type == "disk")
				{
					player.inventory.ClearItemTagFromInventory("disk");
				}
			}
			player.Revive();
			if (loadPerks)
			{
				player.RemoveAllPerks();
				foreach (Perk.PerkSaveData perkSaveData in this.perks)
				{
					Perk perk = Object.Instantiate<Perk>(CL_AssetManager.GetPerkAsset(perkSaveData.id, ""));
					Perk.PerkSaveData.SetPerkSaveData(perk, perkSaveData);
					player.AddPerk(perk, perkSaveData.stackAmount);
				}
			}
		}

		// Token: 0x04000E91 RID: 3729
		public List<Perk.PerkSaveData> perks;

		// Token: 0x04000E92 RID: 3730
		public List<BuffContainer> buffs;

		// Token: 0x04000E93 RID: 3731
		public Item[] inventory;

		// Token: 0x04000E94 RID: 3732
		public Item[] handInventory;

		// Token: 0x04000E95 RID: 3733
		public Vector3 positionRelativeToLevel;

		// Token: 0x04000E96 RID: 3734
		public Quaternion playerRotation;

		// Token: 0x04000E97 RID: 3735
		public Quaternion playerCameraRotation;
	}

	// Token: 0x0200022C RID: 556
	[Serializable]
	public class Footsteps
	{
		// Token: 0x04000E98 RID: 3736
		public string name;

		// Token: 0x04000E99 RID: 3737
		public float volume = 0.15f;

		// Token: 0x04000E9A RID: 3738
		[Range(0f, 2f)]
		public float minPitch = 1f;

		// Token: 0x04000E9B RID: 3739
		[Range(0f, 2f)]
		public float maxPitch = 1f;

		// Token: 0x04000E9C RID: 3740
		public ObjectMaterial.MaterialType material;

		// Token: 0x04000E9D RID: 3741
		public List<AudioClip> audio;

		// Token: 0x04000E9E RID: 3742
		public bool useLandingAudio;

		// Token: 0x04000E9F RID: 3743
		public List<AudioClip> landingAudio;

		// Token: 0x04000EA0 RID: 3744
		public float landingAudioVolume = 1f;

		// Token: 0x04000EA1 RID: 3745
		public ParticleSystem landingEffect;

		// Token: 0x04000EA2 RID: 3746
		public float friction = 1f;

		// Token: 0x04000EA3 RID: 3747
		public float moveLerp = 1f;

		// Token: 0x04000EA4 RID: 3748
		public float moveSpeed = 1f;
	}

	// Token: 0x0200022D RID: 557
	[Serializable]
	public class PlayerAudioList
	{
		// Token: 0x04000EA5 RID: 3749
		public string name;

		// Token: 0x04000EA6 RID: 3750
		public string group;

		// Token: 0x04000EA7 RID: 3751
		public string set;
	}

	// Token: 0x0200022E RID: 558
	[Serializable]
	public class InteractionSprite
	{
		// Token: 0x04000EA8 RID: 3752
		public string name;

		// Token: 0x04000EA9 RID: 3753
		public Sprite sprite;
	}

	// Token: 0x0200022F RID: 559
	public enum InteractType
	{
		// Token: 0x04000EAB RID: 3755
		none,
		// Token: 0x04000EAC RID: 3756
		press,
		// Token: 0x04000EAD RID: 3757
		grab,
		// Token: 0x04000EAE RID: 3758
		hold,
		// Token: 0x04000EAF RID: 3759
		hanging,
		// Token: 0x04000EB0 RID: 3760
		item
	}

	// Token: 0x02000230 RID: 560
	public enum PlayerState
	{
		// Token: 0x04000EB2 RID: 3762
		grounded,
		// Token: 0x04000EB3 RID: 3763
		airborne,
		// Token: 0x04000EB4 RID: 3764
		hanging
	}

	// Token: 0x02000231 RID: 561
	[Serializable]
	public class Hand
	{
		// Token: 0x06000D32 RID: 3378 RVA: 0x00051ECC File Offset: 0x000500CC
		public bool IsLocked()
		{
			return this.moveLocked;
		}

		// Token: 0x06000D33 RID: 3379 RVA: 0x00051ED4 File Offset: 0x000500D4
		public void SetLocked(bool b)
		{
			this.moveLocked = b;
			if (this.moveLocked)
			{
				this.handSprite.gameObject.layer = LayerMask.NameToLayer("Default");
				return;
			}
			this.handSprite.gameObject.layer = LayerMask.NameToLayer("Player");
		}

		// Token: 0x06000D34 RID: 3380 RVA: 0x00051F28 File Offset: 0x00050128
		public void MoveTo(Vector3 pos)
		{
			this.handModel.position = pos;
			this.handModel.rotation = Quaternion.LookRotation(this.handModel.position - this.player.cam.transform.position, this.player.cam.transform.up);
		}

		// Token: 0x06000D35 RID: 3381 RVA: 0x00051F8B File Offset: 0x0005018B
		public void DropHand(bool skipWait = false)
		{
			this.holdMemory = null;
			ENT_Player.playerObject.StopInteraction(this.id, "", !skipWait);
		}

		// Token: 0x06000D36 RID: 3382 RVA: 0x00051FAD File Offset: 0x000501AD
		public void AddHandWait(float amount = 1f)
		{
			this.grabWait += amount;
		}

		// Token: 0x06000D37 RID: 3383 RVA: 0x00051FBD File Offset: 0x000501BD
		public void SetSprite(Sprite s)
		{
			this.curSprite = s;
			this.handSprite.sprite = s;
		}

		// Token: 0x06000D38 RID: 3384 RVA: 0x00051FD2 File Offset: 0x000501D2
		public ENT_Player GetPlayer()
		{
			return this.player;
		}

		// Token: 0x06000D39 RID: 3385 RVA: 0x00051FDA File Offset: 0x000501DA
		public void Initialize(ENT_Player p)
		{
			this.player = p;
			this.handSway = this.handModel.gameObject.GetComponent<ViewSway>();
			this.handSway.Initialize(this);
		}

		// Token: 0x06000D3A RID: 3386 RVA: 0x00052005 File Offset: 0x00050205
		public void ShakeHand(float amount)
		{
			this.handSway.Shake(amount);
		}

		// Token: 0x06000D3B RID: 3387 RVA: 0x00052013 File Offset: 0x00050213
		public float GetShake()
		{
			return this.handSway.GetShake();
		}

		// Token: 0x06000D3C RID: 3388 RVA: 0x00052020 File Offset: 0x00050220
		public void RockHand(float amount)
		{
			this.handSway.Rock(amount);
		}

		// Token: 0x06000D3D RID: 3389 RVA: 0x0005202E File Offset: 0x0005022E
		public void AddGripStrength(float amount)
		{
			this.gripStrength = Mathf.Clamp(this.gripStrength + amount, 0f, this.player.GetCurrentGripStrengthTimer());
		}

		// Token: 0x06000D3E RID: 3390 RVA: 0x00052053 File Offset: 0x00050253
		public void SetGripStrength(float amount)
		{
			this.gripStrength = Mathf.Clamp(amount, 0f, this.player.GetCurrentGripStrengthTimer());
		}

		// Token: 0x06000D3F RID: 3391 RVA: 0x00052071 File Offset: 0x00050271
		public void DropItem(Vector3 pos)
		{
			if (this.inventoryHand.HasItem())
			{
				Inventory.instance.DropItemFromHand(pos, this.id, false);
			}
		}

		// Token: 0x06000D40 RID: 3392 RVA: 0x00052092 File Offset: 0x00050292
		public void DestroyItem()
		{
			if (this.inventoryHand.HasItem())
			{
				Inventory.instance.DestroyItemInHand(this.id);
			}
		}

		// Token: 0x06000D41 RID: 3393 RVA: 0x000520B1 File Offset: 0x000502B1
		public HandItem GetHandItem()
		{
			return this.inventoryHand.currentItem.GetHandItem();
		}

		// Token: 0x06000D42 RID: 3394 RVA: 0x000520C3 File Offset: 0x000502C3
		public void GrabTarget(Transform target, Vector3 worldspacePos)
		{
			this.lastHoldPosition = worldspacePos;
			this.holdTarget = target;
			this.holdPosition = this.holdTarget.InverseTransformPoint(worldspacePos);
			this.holdVelocity = Vector3.zero;
		}

		// Token: 0x06000D43 RID: 3395 RVA: 0x000520F0 File Offset: 0x000502F0
		public void GrabHold(Transform target, Vector3 worldspacePos)
		{
			this.lastHoldPosition = worldspacePos;
			this.holdTarget = target;
			this.holdPosition = this.holdTarget.InverseTransformPoint(worldspacePos);
			this.holdVelocity = Vector3.zero;
			this.handhold = target.GetComponent<CL_Handhold>();
		}

		// Token: 0x06000D44 RID: 3396 RVA: 0x00052129 File Offset: 0x00050329
		public void GrabHold(Transform target)
		{
			this.holdTarget = target;
			this.lastHoldPosition = this.holdTarget.TransformPoint(this.holdPosition);
			this.holdVelocity = Vector3.zero;
			this.handhold = target.GetComponent<CL_Handhold>();
		}

		// Token: 0x06000D45 RID: 3397 RVA: 0x00052160 File Offset: 0x00050360
		public void FixedUpdate()
		{
			if (this.holdTarget != null)
			{
				Vector3 vector = this.holdTarget.TransformPoint(this.holdPosition);
				this.holdVelocity = (this.lastHoldPosition - vector) / Time.fixedDeltaTime;
				if (this.handhold != null)
				{
					this.holdVelocity *= this.handhold.velocityMult;
				}
				this.lastHoldPosition = vector;
				DebugMenu.UpdateDebugText("handvel" + this.id.ToString(), string.Format("Hand {0} Velocity: {1}", this.id, this.holdVelocity * 10f));
			}
			if (!this.IsLocked())
			{
				this.handModel.localScale = Vector3.Lerp(this.handModel.localScale, Vector3.one, Time.deltaTime * 5f);
			}
			if (this.regenWait > 0f)
			{
				this.regenWait -= Time.fixedDeltaTime;
			}
		}

		// Token: 0x06000D46 RID: 3398 RVA: 0x00052278 File Offset: 0x00050478
		public void OffsetHoldPosition(Vector3 offset)
		{
			Vector3 vector = this.holdTarget.TransformPoint(this.holdPosition) + offset;
			this.holdPosition = this.holdTarget.InverseTransformPoint(vector);
		}

		// Token: 0x06000D47 RID: 3399 RVA: 0x000522AF File Offset: 0x000504AF
		public void SetHoldPosition(Vector3 relativePosition)
		{
			this.holdPosition = relativePosition;
		}

		// Token: 0x06000D48 RID: 3400 RVA: 0x000522B8 File Offset: 0x000504B8
		public void OffsetLastHoldPosition(Vector3 offset)
		{
			this.lastHoldPosition += offset;
		}

		// Token: 0x06000D49 RID: 3401 RVA: 0x000522CC File Offset: 0x000504CC
		public Vector3 GetHoldPosition()
		{
			return this.holdPosition;
		}

		// Token: 0x06000D4A RID: 3402 RVA: 0x000522D4 File Offset: 0x000504D4
		public Vector3 GetVelocity()
		{
			if (this.holdTarget == null)
			{
				return Vector3.zero;
			}
			return this.holdVelocity;
		}

		// Token: 0x06000D4B RID: 3403 RVA: 0x000522F0 File Offset: 0x000504F0
		public void SetHandOffset(Vector3 offset)
		{
			this.handSway.OffsetInitialPosition(offset);
		}

		// Token: 0x06000D4C RID: 3404 RVA: 0x000522FE File Offset: 0x000504FE
		internal bool CanRegenerate()
		{
			return this.regenWait <= 0f;
		}

		// Token: 0x06000D4D RID: 3405 RVA: 0x00052310 File Offset: 0x00050510
		internal void SetRegenWait(float v)
		{
			this.regenWait = v;
		}

		// Token: 0x04000EB5 RID: 3765
		public int id;

		// Token: 0x04000EB6 RID: 3766
		private bool moveLocked;

		// Token: 0x04000EB7 RID: 3767
		[Header("Hand Components")]
		public Transform handModel;

		// Token: 0x04000EB8 RID: 3768
		private ViewSway handSway;

		// Token: 0x04000EB9 RID: 3769
		[Space(10f)]
		public Image uiInteract;

		// Token: 0x04000EBA RID: 3770
		[Header("Sprites")]
		public SpriteRenderer handSprite;

		// Token: 0x04000EBB RID: 3771
		public Sprite openSprite;

		// Token: 0x04000EBC RID: 3772
		public Sprite grabSprite;

		// Token: 0x04000EBD RID: 3773
		public Sprite normalSprite;

		// Token: 0x04000EBE RID: 3774
		[Header("Controls")]
		public string throwButton = "q";

		// Token: 0x04000EBF RID: 3775
		[Header("Controls")]
		public string fireButton = "Fire1";

		// Token: 0x04000EC0 RID: 3776
		[Header("Interactions")]
		public ENT_Player.InteractType interactState;

		// Token: 0x04000EC1 RID: 3777
		public Clickable currentInteract;

		// Token: 0x04000EC2 RID: 3778
		public Clickable holdMemory;

		// Token: 0x04000EC3 RID: 3779
		public Vector3 holdMemoryPosition;

		// Token: 0x04000EC4 RID: 3780
		public Transform holdTarget;

		// Token: 0x04000EC5 RID: 3781
		public float memoryTime;

		// Token: 0x04000EC6 RID: 3782
		public float grabWait;

		// Token: 0x04000EC7 RID: 3783
		public float holdTime;

		// Token: 0x04000EC8 RID: 3784
		private float regenWait;

		// Token: 0x04000EC9 RID: 3785
		public Vector3 holdPosition = Vector3.zero;

		// Token: 0x04000ECA RID: 3786
		private Vector3 holdVelocity;

		// Token: 0x04000ECB RID: 3787
		private Vector3 lastHoldPosition;

		// Token: 0x04000ECC RID: 3788
		[Header("Status")]
		public float dragMult;

		// Token: 0x04000ECD RID: 3789
		public float gripStrength = 10f;

		// Token: 0x04000ECE RID: 3790
		public CL_Prop grabTarget;

		// Token: 0x04000ECF RID: 3791
		[HideInInspector]
		public Inventory.ItemHand inventoryHand;

		// Token: 0x04000ED0 RID: 3792
		[HideInInspector]
		public CL_Handhold handhold;

		// Token: 0x04000ED1 RID: 3793
		[HideInInspector]
		public Sprite curSprite;

		// Token: 0x04000ED2 RID: 3794
		[HideInInspector]
		public bool lockHand;

		// Token: 0x04000ED3 RID: 3795
		[HideInInspector]
		public float cooldown;

		// Token: 0x04000ED4 RID: 3796
		private ENT_Player player;
	}

	// Token: 0x02000232 RID: 562
	[Serializable]
	public class PlayerBuff
	{
		// Token: 0x06000D4F RID: 3407 RVA: 0x0005234D File Offset: 0x0005054D
		public void Initialize()
		{
			this.finalBuffs = new Dictionary<string, BuffContainer.Buff>();
			this.currentBuffs = new List<BuffContainer>();
		}

		// Token: 0x06000D50 RID: 3408 RVA: 0x00052368 File Offset: 0x00050568
		public void UpdateBuffs()
		{
			this.finalBuffs.Clear();
			for (int i = 0; i < this.currentBuffs.Count; i++)
			{
				bool flag = this.currentBuffs[i].Update();
				foreach (BuffContainer.Buff buff in this.currentBuffs[i].buffs)
				{
					if (!this.finalBuffs.ContainsKey(buff.id))
					{
						BuffContainer.Buff buff2 = new BuffContainer.Buff();
						buff2.id = buff.id;
						buff2.amount = 0f;
						this.finalBuffs.Add(buff.id, buff2);
					}
					this.finalBuffs[buff.id].amount += buff.amount * this.currentBuffs[i].GetMultiplier();
					DebugMenu.UpdateDebugText("buff" + buff.id, "BUFF: " + buff.id + " " + this.finalBuffs[buff.id].amount.ToString());
				}
				if (flag)
				{
					this.currentBuffs.Remove(this.currentBuffs[i]);
					return;
				}
			}
		}

		// Token: 0x06000D51 RID: 3409 RVA: 0x000524E0 File Offset: 0x000506E0
		public List<BuffContainer> GetBuffContainers(string id)
		{
			List<BuffContainer> list = new List<BuffContainer>();
			foreach (BuffContainer buffContainer in this.currentBuffs)
			{
				if (buffContainer.id == id)
				{
					list.Add(buffContainer);
				}
			}
			return list;
		}

		// Token: 0x06000D52 RID: 3410 RVA: 0x00052548 File Offset: 0x00050748
		public BuffContainer GetBuffContainer(string id)
		{
			foreach (BuffContainer buffContainer in this.currentBuffs)
			{
				if (buffContainer.id == id)
				{
					return buffContainer;
				}
			}
			return null;
		}

		// Token: 0x06000D53 RID: 3411 RVA: 0x000525AC File Offset: 0x000507AC
		public void RemoveBuffContainer(string id)
		{
			bool flag = false;
			for (int i = this.currentBuffs.Count - 1; i >= 0; i--)
			{
				if (this.currentBuffs[i].id == id)
				{
					Debug.Log("Removing Buff: " + id);
					flag = true;
					foreach (BuffContainer.Buff buff in this.currentBuffs[i].buffs)
					{
						DebugMenu.DeleteDebugText("buff" + buff.id);
					}
					this.currentBuffs.RemoveAt(i);
				}
			}
			if (!flag)
			{
				Debug.Log("Attempted to find buff: " + id + ",  Buff not found.");
			}
		}

		// Token: 0x06000D54 RID: 3412 RVA: 0x0005268C File Offset: 0x0005088C
		public void RemoveBuffContainer(BuffContainer b)
		{
			if (this.currentBuffs.Contains(b))
			{
				foreach (BuffContainer.Buff buff in b.buffs)
				{
					DebugMenu.DeleteDebugText("buff" + buff.id);
				}
				this.currentBuffs.Remove(b);
			}
		}

		// Token: 0x06000D55 RID: 3413 RVA: 0x00052710 File Offset: 0x00050910
		public bool HasBuffContainer(string id)
		{
			using (List<BuffContainer>.Enumerator enumerator = this.currentBuffs.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					if (enumerator.Current.id == id)
					{
						return true;
					}
				}
			}
			return false;
		}

		// Token: 0x06000D56 RID: 3414 RVA: 0x00052770 File Offset: 0x00050970
		public float GetBuff(string id)
		{
			if (this.finalBuffs == null)
			{
				return 0f;
			}
			if (!this.finalBuffs.ContainsKey(id))
			{
				return 0f;
			}
			return this.finalBuffs[id].amount;
		}

		// Token: 0x06000D57 RID: 3415 RVA: 0x000527A8 File Offset: 0x000509A8
		public void AddBuff(BuffContainer buffContainer)
		{
			this.currentBuffs.Add(buffContainer);
			foreach (BuffContainer.Buff buff in buffContainer.buffs)
			{
				if (buff.id == "food")
				{
					ENT_Player.playerObject.AddGripStrength(buff.maxAmount, false);
				}
				else if (!this.finalBuffs.ContainsKey(buff.id))
				{
					BuffContainer.Buff buff2 = new BuffContainer.Buff();
					buff2.id = buff.id;
					buff2.amount = 0f;
				}
			}
		}

		// Token: 0x04000ED5 RID: 3797
		public string name;

		// Token: 0x04000ED6 RID: 3798
		public List<BuffContainer> currentBuffs;

		// Token: 0x04000ED7 RID: 3799
		private Dictionary<string, BuffContainer.Buff> finalBuffs;
	}
}
