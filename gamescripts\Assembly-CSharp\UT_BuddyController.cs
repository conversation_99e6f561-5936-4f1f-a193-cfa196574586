﻿using System;
using UnityEngine;

// Token: 0x020001A5 RID: 421
public class UT_BuddyController : MonoBehaviour
{
	// Token: 0x06000B23 RID: 2851 RVA: 0x000480AC File Offset: 0x000462AC
	public void SetBuddy(Transform t)
	{
		this.buddy = t.GetComponent<DEN_Buddy>();
	}

	// Token: 0x06000B24 RID: 2852 RVA: 0x000480BA File Offset: 0x000462BA
	public void SetBuddyHangoutSpot(Transform t)
	{
		if (this.buddy == null)
		{
			return;
		}
		this.buddy.SetHangoutSpot(t.position);
	}

	// Token: 0x06000B25 RID: 2853 RVA: 0x000480DC File Offset: 0x000462DC
	public void PlayBuddyEvent()
	{
		this.buddy.PlayEvent(this.buddyEvent);
	}

	// Token: 0x04000C28 RID: 3112
	private DEN_Buddy buddy;

	// Token: 0x04000C29 RID: 3113
	public DEN_Buddy.BuddyEvent buddyEvent;
}
