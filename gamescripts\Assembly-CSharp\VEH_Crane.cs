﻿using System;
using Drawing;
using UnityEngine;

// Token: 0x020001B6 RID: 438
public class VEH_Crane : MonoBehaviourGizmos
{
	// Token: 0x06000B6A RID: 2922 RVA: 0x00048B80 File Offset: 0x00046D80
	private void Start()
	{
		float num = Vector3.Distance(this.tether.transform.position, this.tether.connectedBody.transform.position);
		this.tether.minDistance = num;
		this.tether.maxDistance = num;
		this.tetherDistanceTarget = num;
		this.startAngle = base.transform.localRotation.eulerAngles.z;
		this.startRot = base.transform.localRotation;
		this.curAngle = 0f;
	}

	// Token: 0x06000B6B RID: 2923 RVA: 0x00048C14 File Offset: 0x00046E14
	private void Update()
	{
		this.turnVelocity = Mathf.Lerp(this.turnVelocity, this.curTurn, Time.deltaTime * 2f);
		this.curAngle += this.turnVelocity * this.turnSpeed * Time.deltaTime;
		if (this.limitTurn)
		{
			this.curAngle = Mathf.Clamp(this.curAngle, this.minTurn, this.maxTurn);
		}
		Quaternion quaternion = Quaternion.Euler(0f, 0f, this.curAngle);
		base.transform.localRotation = this.startRot * quaternion;
		this.tetherDistanceTarget += this.curReel * Time.deltaTime * this.reelSpeed;
		this.tetherDistanceTarget = Mathf.Clamp(this.tetherDistanceTarget, this.minDistance, this.maxDistance);
		this.tether.minDistance = this.tetherDistanceTarget;
		this.tether.maxDistance = this.tetherDistanceTarget;
		this.reelAudio.volume = Mathf.Lerp(this.reelAudio.volume, Mathf.Abs(this.curReel), Time.deltaTime);
		this.turnAudio.volume = Mathf.Lerp(this.turnAudio.volume, Mathf.Abs(this.curTurn), Time.deltaTime);
	}

	// Token: 0x06000B6C RID: 2924 RVA: 0x00048D6A File Offset: 0x00046F6A
	public void SetTurn(float f)
	{
		this.curTurn += f;
	}

	// Token: 0x06000B6D RID: 2925 RVA: 0x00048D7A File Offset: 0x00046F7A
	public void SetReel(float f)
	{
		this.curReel += f;
	}

	// Token: 0x06000B6E RID: 2926 RVA: 0x00048D8C File Offset: 0x00046F8C
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this) && this.limitTurn)
		{
			float num = Mathf.Cos(this.minTurn * 2f * 0.00872665f);
			float num2 = Mathf.Sin(this.minTurn * 2f * 0.00872665f);
			float num3 = Mathf.Cos(this.maxTurn * 2f * 0.00872665f);
			float num4 = Mathf.Sin(this.maxTurn * 2f * 0.00872665f);
			Draw.Line(base.transform.position, base.transform.position + base.transform.forward * 10f, Color.green);
			float num5 = 0.25f;
			Draw.DashedLine(base.transform.position, base.transform.position + (-base.transform.up * num + base.transform.right * num2).normalized * 10f, num5, num5, Color.yellow);
			Draw.DashedLine(base.transform.position, base.transform.position + (-base.transform.up * num3 + base.transform.right * num4).normalized * 10f, num5, num5, Color.yellow);
		}
	}

	// Token: 0x04000C50 RID: 3152
	public float turnSpeed = 1f;

	// Token: 0x04000C51 RID: 3153
	private float curTurn;

	// Token: 0x04000C52 RID: 3154
	private float turnVelocity;

	// Token: 0x04000C53 RID: 3155
	public SpringJoint tether;

	// Token: 0x04000C54 RID: 3156
	public float reelSpeed = 2f;

	// Token: 0x04000C55 RID: 3157
	public float minDistance;

	// Token: 0x04000C56 RID: 3158
	public float maxDistance;

	// Token: 0x04000C57 RID: 3159
	private float curReel;

	// Token: 0x04000C58 RID: 3160
	private float tetherDistanceTarget;

	// Token: 0x04000C59 RID: 3161
	public AudioSource reelAudio;

	// Token: 0x04000C5A RID: 3162
	public AudioSource turnAudio;

	// Token: 0x04000C5B RID: 3163
	public bool limitTurn;

	// Token: 0x04000C5C RID: 3164
	public float minTurn;

	// Token: 0x04000C5D RID: 3165
	public float maxTurn;

	// Token: 0x04000C5E RID: 3166
	private float startAngle;

	// Token: 0x04000C5F RID: 3167
	private float curAngle;

	// Token: 0x04000C60 RID: 3168
	private Quaternion startRot;
}
