﻿using System;
using System.Collections;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using Drawing;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000136 RID: 310
public class UI_ProgressionPopup : MonoBehaviourGizmos
{
	// Token: 0x06000907 RID: 2311 RVA: 0x0003F1A4 File Offset: 0x0003D3A4
	private void Awake()
	{
		this.rectTransform = base.GetComponent<RectTransform>();
		this.rootPositon = this.rectTransform.anchoredPosition;
		if (this.animateOnPopup)
		{
			this.rectTransform.anchoredPosition = this.rootPositon + this.hidePositionOffset;
		}
	}

	// Token: 0x06000908 RID: 2312 RVA: 0x0003F1F2 File Offset: 0x0003D3F2
	public void Popup(Sprite i, string title, string desc)
	{
		base.StartCoroutine(this.PopupAnimation(i, title, desc));
	}

	// Token: 0x06000909 RID: 2313 RVA: 0x0003F204 File Offset: 0x0003D404
	private IEnumerator PopupAnimation(Sprite i, string title, string desc)
	{
		if (this.showingAnimation)
		{
			while (this.showingAnimation)
			{
				yield return null;
			}
		}
		this.UpdateInformation(i, title, desc);
		this.showingAnimation = true;
		this.rectTransform.DOAnchorPos(this.rootPositon, this.openSpeed, false).SetUpdate(true);
		yield return new WaitForSecondsRealtime(this.openSpeed);
		base.transform.DOPunchScale(Vector3.one * 0.05f, 0.5f, 3, 1.2f);
		yield return new WaitForSecondsRealtime(this.holdTime);
		this.rectTransform.DOAnchorPos(this.rootPositon + this.hidePositionOffset, this.openSpeed, false).SetUpdate(true);
		yield return new WaitForSecondsRealtime(this.openSpeed);
		this.showingAnimation = false;
		yield break;
	}

	// Token: 0x0600090A RID: 2314 RVA: 0x0003F228 File Offset: 0x0003D428
	public void UpdateInformation(Sprite i, string title, string desc)
	{
		this.icon.sprite = i;
		this.text.text = string.Concat(new string[]
		{
			"<size=20>",
			title.ToUpper(),
			"</size>\n<color=grey>",
			desc,
			"</color>"
		});
	}

	// Token: 0x0600090B RID: 2315 RVA: 0x0003F27C File Offset: 0x0003D47C
	public override void DrawGizmos()
	{
		if (GizmoContext.InSelection(this))
		{
			if (this.rectTransform == null)
			{
				this.rectTransform = base.GetComponent<RectTransform>();
			}
			Draw.Cross(this.rectTransform.position + this.hidePositionOffset, 20f, new Color(1f, 0.561f, 0f, 0.25f));
		}
	}

	// Token: 0x04000A54 RID: 2644
	public Image icon;

	// Token: 0x04000A55 RID: 2645
	public TMP_Text text;

	// Token: 0x04000A56 RID: 2646
	public bool animateOnPopup = true;

	// Token: 0x04000A57 RID: 2647
	public Vector2 hidePositionOffset;

	// Token: 0x04000A58 RID: 2648
	private Vector2 rootPositon = Vector2.zero;

	// Token: 0x04000A59 RID: 2649
	public float openSpeed = 1f;

	// Token: 0x04000A5A RID: 2650
	public float holdTime = 2f;

	// Token: 0x04000A5B RID: 2651
	private bool showingAnimation;

	// Token: 0x04000A5C RID: 2652
	private RectTransform rectTransform;
}
